'use client'

import { useEffect } from 'react'
import { EventName, EventType } from '../const'
import { isBrowser, isProd } from '@/lib/utils'
import { statistic } from '..'
import { watchClick } from './watchClick'
import { watchRouteChange } from './watchRouteChange'

export const StatisticWatcher = () => {
  useEffect(() => {
    if (isBrowser()) {
      if (isProd()) {
        console.log = () => {}
      }

      statistic({ eventType: EventType.View })

      watchClick(data => statistic(JSON.parse(data)))

      watchRouteChange(path =>
        statistic({
          eventType: EventType.View,
          path: window.location.origin + path
        })
      )

      document.addEventListener('visibilitychange', () => {
        statistic({
          eventName:
            document.visibilityState === 'hidden'
              ? EventName.leave
              : EventName.return
        })
      })
    }
  }, [])

  return <div uchoice-event-watcher="👀"></div>
}
