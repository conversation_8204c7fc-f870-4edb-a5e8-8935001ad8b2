import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'

export const watchRouteChange = (fn: (path: string) => void) => {
  // TODO:Warning: useInsertionEffect must not schedule updates.
  const oldPushState = window.history.pushState
  window.history.pushState = function () {
    fn(arguments[2])
    // @ts-ignore
    return oldPushState.apply(this, arguments)
  }

  const oldReplaceState = window.history.replaceState
  window.history.replaceState = function () {
    fn(arguments[2])
    // @ts-ignore
    return oldReplaceState.apply(this, arguments)
  }
}
