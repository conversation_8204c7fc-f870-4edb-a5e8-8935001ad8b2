import { isShowFirstLinkPop } from '@/app/api/api-uchoice/showcase/isShowFirstLinkPop'
import { loading } from './client/loading'
import { webview } from './client/webview'
import { WebviewEvents } from './client/webview/events'
import { FirstAddToShowcaseAlertManager } from '@/components/FirstAddToShowcaseAlert'
import { getToken, makeSureInApp } from './client/utils'

const checkAuth = async (action: Function) => {
  if (webview) {
    await webview?.send(WebviewEvents.makeSureTikTokAuthed)
    // 因为登录成功后app会刷新网页，防止在刷新前弹sku弹窗，弹到一半刷新网页让人觉得网页出问题了
    if (await getToken()) action()
  } else {
    action()
  }
}

const _addToShowCase = async (
  { link, id },
  statisticFrom?: string,
  afterJumped?: () => void
) => {
  if (
    !!(await webview?.send(WebviewEvents.hasJavaScriptHandler, {
      handlerName: WebviewEvents.addToShowcase
    }))
  ) {
    await webview?.send(WebviewEvents.addToShowcase, {
      itemId: id,
      statisticFrom
    })
  } else {
    webview?.send(WebviewEvents.launch, {
      url: link
    })
  }

  afterJumped?.()
}

const addToShowCase = async (
  { link, id },
  statisticFrom?: string,
  afterJumped?: () => void
) => {
  if (webview) {
    loading.show()
    const { result } = await isShowFirstLinkPop()
    loading.hide()
    if (result) {
      FirstAddToShowcaseAlertManager.show(() => {
        _addToShowCase({ link, id }, statisticFrom, afterJumped)
      })
    } else {
      _addToShowCase({ link, id }, statisticFrom, afterJumped)
    }

    if (
      id &&
      !!(await webview?.send(WebviewEvents.hasJavaScriptHandler, {
        handlerName: WebviewEvents.trackTiktokBusinessEvent
      }))
    ) {
      webview?.send(WebviewEvents.trackTiktokBusinessEvent, {
        name: 'addToShowcase',
        params: {
          id: `${id}`
        }
      })
    }
  } else {
    afterJumped?.()
    window.location.href = link
  }
}

const launchShopAuthURL = async ({
  memberAccountId,
  itemId,
  statisticFrom
}: {
  memberAccountId?: string
  itemId?: string
  statisticFrom?: string
}) => {
  if (
    !!(await webview?.send(WebviewEvents.hasJavaScriptHandler, {
      handlerName: WebviewEvents.launchShopAuthURL
    }))
  ) {
    await webview?.send(WebviewEvents.launchShopAuthURL, {
      memberAccountId,
      itemId,
      statisticFrom
    })
  } else {
    await makeSureInApp()
  }
}

const toLogin = async () => {
  if (webview) {
    if (!(await getToken())) {
      await webview?.send(WebviewEvents.makeSureLogined)
      window.location.reload()
      return
    }
    await webview?.send(WebviewEvents.makeSureTikTokAuthed)
    window.location.reload()
  } else {
    makeSureInApp()
  }
}

export { addToShowCase, checkAuth, launchShopAuthURL, toLogin }
