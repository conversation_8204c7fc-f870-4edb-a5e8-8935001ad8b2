import { useEffect, useState, useRef } from 'react'

export const useIntersectionObserver = () => {
  const ref = useRef<any>()
  const [isLeave, setIsLeave] = useState(false)

  useEffect(() => {
    let intersectionObserver: IntersectionObserver | null = null
    let mutationObserver: MutationObserver | null = null

    const initIntersectionObserver = () => {
      try {
        if (ref.current) {
          if (intersectionObserver) {
            intersectionObserver.disconnect()
          }

          intersectionObserver = new IntersectionObserver(
            entries => {
              try {
                entries.forEach(entry => {
                  setIsLeave(!entry.isIntersecting)
                })
              } catch (error) {
                console.error('Error in IntersectionObserver callback:', error)
              }
            },
            {
              threshold: 0
            }
          )

          intersectionObserver.observe(ref.current)
        }
      } catch (error) {
        console.error('Error initializing IntersectionObserver:', error)
      }
    }

    try {
      // 初次执行
      initIntersectionObserver()

      // 监听 DOM 变化
      mutationObserver = new MutationObserver(mutations => {
        try {
          mutations.forEach(() => {
            if (ref.current) {
              initIntersectionObserver()
              if (mutationObserver) {
                mutationObserver.disconnect()
              }
            }
          })
        } catch (error) {
          console.error('Error in MutationObserver callback:', error)
        }
      })

      mutationObserver.observe(document.body, {
        childList: true,
        subtree: true
      })
    } catch (error) {
      console.error('Error setting up observers:', error)
    }

    return () => {
      try {
        if (intersectionObserver) {
          intersectionObserver.disconnect()
        }
        if (mutationObserver) {
          mutationObserver.disconnect()
        }
      } catch (error) {
        console.error('Error cleaning up observers:', error)
      }
    }
  }, [ref.current])

  return {
    ref,
    isLeave
  }
}
