import { useEffect } from 'react'
import { webview } from '../client/webview'
import { WebviewEvents } from '../client/webview/events'

export const useWebviewBackground = (color: string) => {
  useEffect(() => {
    webview?.send(WebviewEvents.setWebviewBackgroundColor, {
      color
    })

    return () => {
      webview?.send(WebviewEvents.setWebviewBackgroundColor, {
        color: '#FFFFFF'
      })
    }
  }, [])
}
