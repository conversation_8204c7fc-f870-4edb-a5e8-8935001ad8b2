import { getGoAppBarHeight } from '@/components/GoAppBar'
import { getNavHeight } from '@/components/TransparentNavPage'
import { useEffect } from 'react'

interface Props {
  onSectionIndexChange: (index: number) => void
}

let positions = []

export const useSections = ({ onSectionIndexChange }: Props) => {
  const scrollToSectionIndex = index => {
    // 不滚动到0的话，位置会有点不太精准
    if (index === 0) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
      return
    }

    // +1是为了点击tab的时候，往下滚动一点，让tab的选中index更精准点
    window.scrollTo({
      top: positions[index] + 1,
      behavior: 'smooth'
    })
  }

  function findClosestCount(arr, target) {
    let maxIndex = 0

    for (let i = 0; i < arr.length; i++) {
      if (target >= arr[i]) {
        maxIndex = i
      }
    }

    return maxIndex
  }

  const calculatePostions = () => {
    const stickyHeader = document.getElementById('sticky-header')
    if (stickyHeader) {
      positions = []

      const elements = document.getElementsByClassName('tab-section')
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i]
        positions.push(
          // @ts-ignore
          element!.offsetTop! -
            getGoAppBarHeight() -
            getNavHeight() -
            stickyHeader.offsetHeight || 0
        )
      }
    }
  }

  useEffect(() => {
    const handleScroll = () => {
      calculatePostions()

      let scrollPosition = window.pageYOffset
      let currentTabIndex = 0

      let documentHeight = document.documentElement.scrollHeight
      let windowHeight = window.innerHeight

      // 滚动到底部
      if (scrollPosition + windowHeight >= documentHeight) {
        onSectionIndexChange(
          document.getElementsByClassName('tab-section').length - 1
        )
      } else {
        currentTabIndex = findClosestCount(positions, scrollPosition)
        if (currentTabIndex != -1) {
          onSectionIndexChange(currentTabIndex)
        }
      }
    }

    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  return scrollToSectionIndex
}
