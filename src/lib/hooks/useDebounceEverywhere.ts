import { useLayoutEffect } from 'react'

const key = 'debounce_at'

export const useDebounceEverywhere = (delay = 200) => {
  useLayoutEffect(() => {
    const onClick = e => {
      let element = e.target
      const debounceAt = Date.now()

      element.style.pointerEvents = 'none'
      if (!element.hasAttribute(key)) {
        element.setAttribute(key, debounceAt)
      }

      let currentElement = element
      while (currentElement.parentElement) {
        currentElement.parentElement.style.pointerEvents = 'none'
        currentElement = currentElement.parentElement
        if (!currentElement.hasAttribute(key)) {
          currentElement.setAttribute(key, debounceAt)
        }
      }

      setTimeout(() => {
        document.querySelectorAll(`[${key}]`).forEach((element: any) => {
          const timestamp = parseInt(element.getAttribute(key))
          if (Date.now() - timestamp >= delay) {
            element.style.pointerEvents = ''
            element.removeAttribute(key)
          }
        })
      }, delay)
    }

    document.addEventListener('click', onClick)

    return () => {
      document.removeEventListener('click', onClick)
    }
  }, [])
}
