import { useEffect, useState } from 'react'
import { getToken } from '../client/utils'

class LoginStatusCache {
  private static instance: LoginStatusCache
  private isLogined: boolean = false

  private constructor() {}

  public static getInstance(): LoginStatusCache {
    if (!LoginStatusCache.instance) {
      LoginStatusCache.instance = new LoginStatusCache()
    }
    return LoginStatusCache.instance
  }

  public getLoginStatus(): boolean {
    return this.isLogined
  }

  public setLoginStatus(status: boolean): void {
    this.isLogined = status
  }
}

const loginStatusCache = LoginStatusCache.getInstance()

export const useLogined = () => {
  const [isLogined, setIsLogined] = useState(loginStatusCache.getLoginStatus())

  useEffect(() => {
    const checkLoginStatus = async () => {
      const tokenExists = await getToken()
      setIsLogined(!!tokenExists)
      loginStatusCache.setLoginStatus(!!tokenExists)
    }

    checkLoginStatus()
  }, [])

  return isLogined
}
