import { useEffect, useState } from 'react'
import { hasNormalApiShowcaseAccount } from '@/app/api/api-uchoice/uChoice/account/hasNormalApiShowcaseAccount/request'
import { TikTokApiShowcaseAccountStatus } from '@/app/api/api-uchoice/uChoice/account/hasNormalApiShowcaseAccount/dtos'

export const useShowcasePermission = () => {
  const [showcasePermission, setShowcasePermission] = useState<boolean>(false)

  useEffect(() => {
    const checkShowcasePermission = async () => {
      const res = await hasNormalApiShowcaseAccount()
      if (
        res.code === 200 &&
        res.result.status === TikTokApiShowcaseAccountStatus.Normal
      ) {
        setShowcasePermission(true)
      } else {
        setShowcasePermission(false)
      }
    }

    checkShowcasePermission()
  }, [])

  return showcasePermission
}
