/*
 * @Author: <PERSON>
 * @Date: 2024-04-10 12:50:29
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-12 14:34:28
 * @Desc 倒计时
 */
import { useEffect, useRef, useState } from 'react'

const useCounter = (_time: number = 60) => {
  const [time, setTime] = useState(_time)
  const timer = useRef<NodeJS.Timer | null>(null)

  useEffect(() => {
    return () => {
      if (timer.current) {
        clearInterval(timer.current)
      }
    }
  }, [])

  useEffect(() => {
    if (time === 0 && timer.current) {
      clearInterval(timer.current)
      setTime(_time)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [time])

  const start = () => {
    timer.current = setInterval(() => {
      setTime(t => --t)
    }, 1000)
  }

  const reset = () => {
    if (timer.current) {
      clearInterval(timer.current)
    }
    if (time !== _time) {
      setTime(_time)
    }
  }

  return { time, start, reset }
}

export default useCounter
