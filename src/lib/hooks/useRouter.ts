import { NavigateOptions } from 'next/dist/shared/lib/app-router-context'
import { useRouter as _useRouter } from 'next/navigation'
import { webview } from '../client/webview'
import { WebViewToPages, WebviewEvents } from '../client/webview/events'

export const useRouter = () => {
  const _router = _useRouter()

  const appendParams = (url: string) => {
    const currentUrl = new URL(window.location.href)
    const newUrl = new URL(url, window.location.origin)

    const region = currentUrl.searchParams.get('region')
    if (region) {
      newUrl.searchParams.set('region', region)
    }

    const language = currentUrl.searchParams.get('language')
    if (language) {
      newUrl.searchParams.set('language', language)
    }

    return newUrl.href
  }

  return {
    push: (href: string, options?: NavigateOptions) => {
      const url = appendParams(href)

      webview?.send(WebviewEvents.toPage, {
        name: WebViewToPages.webview,
        params: {
          url
        }
      })
      !webview && _router.push(url, options)
    },
    back: () => {
      webview?.send(WebviewEvents.back)
      !webview && _router.back()
    }
  }
}
