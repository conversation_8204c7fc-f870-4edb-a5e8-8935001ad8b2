import { loading } from '../client/loading'
import { passwordLogin } from '@/app/api/api-uaa/oauth/token'

const usePasswordLogin = () => {
  const onPasswordLogin = async (phone: string, pwd: string) => {
    try {
      loading.show()
      const form = `grant_type=tt_mobile_password&mobile=${phone}&password=${pwd}`
      const data = await passwordLogin(form)
      loading.hide()
      if (data.code != 200) {
        return Promise.reject(new Error(data.message))
      }
      return Promise.resolve(data.result.access_token)
    } catch (error) {
      loading.hide()
      return Promise.reject(error)
    }
  }

  return onPasswordLogin
}

export default usePasswordLogin
