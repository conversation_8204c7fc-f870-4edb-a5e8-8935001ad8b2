import { customAlphabet } from 'nanoid'
import { cache } from 'react'
import { clientLanguage, clientRegion } from './client/utils'
import { serverLanguage, serverRegion } from './server/utils'

export const getBaseUrl = () =>
  isRegionTH()
    ? process.env.NEXT_PUBLIC_TH_API_BASE_URL
    : process.env.NEXT_PUBLIC_VN_API_BASE_URL
export const getBaseH5Url = () => process.env.NEXT_PUBLIC_BASE_H5_URL
export const getBaseH5OldUrl = () => process.env.NEXT_PUBLIC_BASE_H5_OLD_URL

export const isProd = () =>
  process.env.NEXT_PUBLIC_TH_API_BASE_URL === 'https://api.th.uchoice.pro' ||
  process.env.NEXT_PUBLIC_VN_API_BASE_URL === 'https://api.vn.uchoice.pro'

export const isDEV = cache(() => process.env.NODE_ENV === 'development')

export const getOldH5Url = () => {
  return process.env.NEXT_PUBLIC_OLD_URL
}

export const nanoid = customAlphabet(
  'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',
  8
)

export const isBrowser = () => typeof window !== 'undefined'

export const delayTime = time => {
  return new Promise<void>(resolve => {
    setTimeout(() => {
      resolve()
    }, time)
  })
}

export function formatNumberWithCommas(number) {
  // 将数字转换为字符串
  var numStr = number.toString()
  // 使用正则表达式在字符串中每三位插入逗号
  return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export function formatNumberWithCommasFixed2(number) {
  if (!number) {
    return '0.00'
  }
  // 将数字转换为字符串
  var numStr = parseFloat(number).toFixed(2)
  // 使用正则表达式在字符串中每三位插入逗号
  return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 获取之前某一天的0点0分0秒的时间戳。
 * @param daysAgo 天数，表示多少天之前。
 * @return {number} 时间戳（毫秒）。
 */
export function getTimestampOfDaysAgo(daysAgo) {
  const today = new Date()
  // 将时间调整到0点0分0秒
  today.setHours(0, 0, 0, 0)
  // 计算目标日期
  const targetDate = new Date(today.getTime() - daysAgo * 24 * 60 * 60 * 1000)
  return targetDate.getTime()
}

export function formatNumberStr(num) {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 10000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return formatNumberWithCommas(num)
}

export function debounce(func, wait = 200) {
  let timeout
  return function () {
    // @ts-ignore
    const context = this
    const args = arguments
    clearTimeout(timeout)
    timeout = setTimeout(() => {
      func.apply(context, args)
    }, wait)
  }
}

export const language = () =>
  isBrowser() ? clientLanguage() : serverLanguage()
export const isLanguageEN = () => language() === 'en'
export const isLanguageTH = () => language() === 'th'
export const isLanguageVI = () => language() === 'vi'

export const region = () => (isBrowser() ? clientRegion() : serverRegion())
export const isRegionTH = () => region() === 'TH'
export const isRegionVN = () => region() === 'VN'

export const matchLanguage = ({
  en,
  th,
  vi
}: {
  en: any
  th: any
  vi: any
}) => {
  if (isLanguageEN()) {
    return en
  }
  if (isLanguageTH()) {
    return th
  }
  if (isLanguageVI()) {
    return vi
  }
}
