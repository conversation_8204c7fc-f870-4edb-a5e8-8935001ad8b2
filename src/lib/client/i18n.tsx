import i18next, { TOptions } from 'i18next'
import { initReactI18next } from 'react-i18next'
import { en, th, vi } from '@/resources/translations'
import qs from 'qs'
import { webview } from './webview'

// export const changeLanguage = (langauge: string) => {
//   localStorage.setItem('language', langauge)
//   window.location.reload()
// }

// 防止循环引入
const language = () => {
  if (typeof window !== 'undefined') {
    if (webview) {
      return webview.getData().language
    }

    const query = window.location.search.replace('?', '')
    const region = qs.parse(query)['region']

    const lan = qs.parse(query)['language']
    if (lan === 'th' && region === 'TH') {
      return 'th'
    }
    if (lan === 'vi' && region === 'VN') {
      return 'vi'
    }
    if (lan === 'en') {
      return 'en'
    }

    if (region === 'VN') {
      return 'vi'
    }
    if (region === 'TH') {
      return 'th'
    }

    const cacheLanguage = localStorage.getItem('language')
    if (cacheLanguage) {
      return cacheLanguage
    }

    if (
      window.navigator.language &&
      ['th', 'vi', 'en'].includes(window.navigator.language.substring(0, 2))
    ) {
      return window.navigator.language.substring(0, 2)
    }

    return 'en'
  }
}

let inited = false

const i18n = {
  t: (key: string, options?: TOptions<any> | string): any => {
    if (!inited) {
      inited = true
      i18next.use(initReactI18next).init({
        lng: language(),
        debug: false,
        resources: {
          en: {
            translation: en
          },
          th: {
            translation: th
          },
          vi: {
            translation: vi
          }
        }
      })
    }

    return i18next.t(key, options)
  },
  changeLanguage: (lang: 'en' | 'th' | 'vi') => {
    i18next.changeLanguage(lang, (err, t) => {
      if (err) return console.log('something went wrong loading', err)
      t('key')
    })
  }
}

export { i18n }
