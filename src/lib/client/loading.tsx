'use client'

import { useState } from 'react'
import { Mask, SpinLoading } from 'antd-mobile'
import { webview } from './webview'
import { WebviewEvents } from './webview/events'

let toggle: (value: boolean, userInteractive?: boolean) => void

export const Loading = () => {
  const [visible, setVisble] = useState(false)

  toggle = (value, userInteractive) => {
    if (webview) {
      if (value) {
        webview.send(WebviewEvents.showLoading, {
          userInteractive: userInteractive ? true : false
        })
      } else {
        webview.send(WebviewEvents.hideLoading)
      }
    } else {
      setVisble(value)
    }
  }

  return (
    <Mask visible={visible} color="white">
      <div className="flex h-screen w-full items-center justify-center">
        <SpinLoading color="rgb(220,220,220)" />
      </div>
    </Mask>
  )
}
interface Params {
  userInteractive?: boolean
}

export const loading = {
  show(params?: Params) {
    toggle(true, params?.userInteractive || false)
  },
  hide() {
    toggle(false)
  }
}
