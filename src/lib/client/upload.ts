import { bucketPolicy } from '@/app/api/api-base/upload/bucketPolicy/request'
import { loading } from '@/lib/client/loading'
import { toast } from './toast'
const OSS = require('ali-oss')

class CredentialsCache {
  private static credentials: any

  static put(credentials: any) {
    this.credentials = credentials
  }

  static get() {
    return this.credentials
  }

  static isCredentialsExpired() {
    if (!this.credentials) {
      return true
    }
    const expireDate = new Date(this.credentials.expiration)
    const now = new Date()
    // 如果有效期不足一分钟，视为过期。
    return expireDate.getTime() - now.getTime() <= 60000
  }
}

export const uploadFile = async (application: string, file: File) => {
  if (CredentialsCache.isCredentialsExpired()) {
    loading.show()
    const res = await bucketPolicy(application)
    loading.hide()
    if (res.code === 200) {
      CredentialsCache.put(res.result)
    } else {
      return null
    }
  }

  const client = new OSS({
    region: CredentialsCache.get().region,
    accessKeyId: CredentialsCache.get().accessKeyId,
    accessKeySecret: CredentialsCache.get().accessKeySecret,
    bucket: CredentialsCache.get().bucket,
    stsToken: CredentialsCache.get().stsToken
  })

  loading.show()
  try {
    const result = await client.put(
      `${CredentialsCache.get().path}${file.name}`,
      file,
      {
        callback: CredentialsCache.get().callback
      }
    )
    loading.hide()
    if (result.res.status === 200) {
      return result.data.result
    }
  } catch (e) {
    loading.hide()
    toast('Upload failed')
  }

  return null
}
