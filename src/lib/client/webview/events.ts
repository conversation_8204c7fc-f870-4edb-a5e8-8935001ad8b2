export enum WebviewEvents {
  back = 'back',
  canLaunch = 'canLaunch',
  launch = 'launch',
  makeSureTikTokAuthed = 'makeSureTikTokAuthed',
  toPage = 'toPage',
  shareUrl = 'shareUrl',
  contactUs = 'contactUs',
  showLoading = 'showLoading',
  hideLoading = 'hideLoading',
  toggleCollect = 'toggleCollect',
  toggleTopicCollect = 'toggleTopicCollect',
  toast = 'toast',
  accountDeact = 'accountDeact',
  goBackHome = 'goBackHome',
  switchTiktokAccount = 'switchTiktokAccount',
  hasJavaScriptHandler = 'hasJavaScriptHandler',
  getToken = 'getToken',
  statistic = 'statistic',
  setWebviewBackgroundColor = 'setWebviewBackgroundColor',
  makeSureLogined = 'makeSureLogined',
  freeSampleFakeExpireAt = 'freeSampleFakeExpireAt',
  previewImages = 'previewImages',
  getUserInfo = 'getUserInfo',
  trackTiktokBusinessEvent = 'trackTiktokBusinessEvent',
  addToShowcase = 'addToShowcase',
  downloadFiles = 'downloadFiles',
  launchShopAuthURL = 'launchShopAuthURL',
  tiktokAuthComplete = 'tiktokAuthComplete',
  copy = 'copy',
  pdpLoaded = 'pdpLoaded',
  setStorage = 'setStorage',
  getStorage = 'getStorage'
}

export enum WebViewToPages {
  login = '/login',
  webview = '/webview',
  sampleApply = '/sample_apply',
  sampleApplyRule = '/sample_apply_rule',
  video_paging = '/video_paging',
  sampleOrderLogistics = '/sample_order_logistics',
  addressList = '/address_list'
}
