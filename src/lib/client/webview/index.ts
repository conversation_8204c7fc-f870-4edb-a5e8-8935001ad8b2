import { WebviewEvents } from './events'
import qs from 'qs'

const getData = () => {
  const query = window.location.search.replace('?', '')
  return window.JSON.parse(qs.parse(query)['app-data'] as string)
}

const send = async (
  action: WebviewEvents,
  params: { name: string; params: any } = { name: '', params: {} }
) => {
  return await window.flutter_inappwebview.callHandler(action, params)
}

export const webview =
  typeof window !== 'undefined' && window.flutter_inappwebview
    ? {
        send,
        getData
      }
    : null
