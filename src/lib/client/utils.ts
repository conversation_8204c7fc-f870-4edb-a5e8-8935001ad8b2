import { isBrowser } from '../utils'
import { webview } from './webview'
import qs from 'qs'
import { WebviewEvents } from './webview/events'
import { MemberVo } from '@/app/api/api-uchoice/uChoice/account/current/dtos'
import { getUserInfo as getUserInfoApi } from '@/app/api/api-uchoice/uChoice/account/current/request'
import { toast } from './toast'

// 使用动态导入i18n的方式来避免循环引用
let i18nInstance: any = null

// 异步获取i18n实例
const getI18n = async () => {
  if (!i18nInstance) {
    const { i18n } = await import('./i18n')
    i18nInstance = i18n
  }
  return i18nInstance
}

// 异步翻译函数
export const tAsync = async (key: string, options?: any) => {
  const i18n = await getI18n()
  return i18n.t(key, options)
}

export const clientLanguage = () => {
  if (typeof window !== 'undefined') {
    if (webview) {
      return webview.getData().language
    }

    const query = window.location.search.replace('?', '')
    const region = qs.parse(query)['region']

    const lan = qs.parse(query)['language']
    if (lan === 'th' && region === 'TH') {
      return 'th'
    }
    if (lan === 'vi' && region === 'VN') {
      return 'vi'
    }
    if (lan === 'en') {
      return 'en'
    }

    if (region === 'VN') {
      return 'vi'
    }
    if (region === 'TH') {
      return 'th'
    }

    const cacheLanguage = localStorage.getItem('language')
    if (cacheLanguage) {
      return cacheLanguage
    }

    if (
      window.navigator.language &&
      ['th', 'vi', 'en'].includes(window.navigator.language.substring(0, 2))
    ) {
      return window.navigator.language.substring(0, 2)
    }

    return 'en'
  }
}

export const clientRegion = () => {
  if (typeof window !== 'undefined') {
    if (webview) {
      // 20240814: 为了兼容老版本app，老版本app没有region字段
      return webview.getData().region || 'TH'
    }

    const query = window.location.search.replace('?', '')
    const region = qs.parse(query)['region']
    if (region === 'VN') {
      return 'VN'
    }

    return 'TH'
  }
}

export const getToken = async () => {
  if (!webview && process.env.NEXT_PUBLIC_TOKEN) {
    return process.env.NEXT_PUBLIC_TOKEN
  }

  let token = localStorage.getItem('access_token')
  !token && (token = await webview?.send(WebviewEvents.getToken))

  if (token === '') {
    token = null
  }

  return token
}

export const getUserInfo = async () => {
  if (!(await getToken())) {
    return null
  }

  if (
    !!(await webview?.send(WebviewEvents.hasJavaScriptHandler, {
      handlerName: WebviewEvents.getUserInfo
    }))
  ) {
    const userInfo = JSON.parse(
      await webview?.send(WebviewEvents.getUserInfo)
    ) as MemberVo
    if (userInfo?.id) {
      return userInfo
    }
  }

  const { code, result } = await getUserInfoApi()
  if (code === 200) {
    return result
  }

  return null
}

export const px2rem = (px: number) => {
  return `${px / 40}rem`

  return `${(px * (window.innerWidth / 375)) / 40}rem`
}

export const scaledPx = (px: number) => {
  return (window.innerWidth / 375) * px
}

/**
 * 转换
 * @param rpx 以宽度为750px的ui稿对应的大小
 */
export const rpxToPx = (rpx: number) => {
  return typeof window === 'undefined' ? 0 : (window.innerWidth / 750) * rpx
}

export const wait = async (delay = 1000) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(true)
    }, delay)
  })
}

export const isIOS = () => {
  return isBrowser()
    ? /iPad|iPhone|iPod/.test(window.navigator.userAgent)
    : true
}

export const inApp = () => {
  return !!webview
}

export const makeSureInApp = async (
  args?: { route: string; params: any; desc?: string },
  noTarget: boolean = false
) => {
  return new Promise(async resolve => {
    if (!webview) {
      if (!noTarget) {
        // 优先尝试使用i18n，如果失败则回退到内联翻译
        const copyMessage = await tAsync(
          'Common.点击链接直接打开 或者 复制本条信息直接打开app'
        )
        // 剪切板跳转
        copyText(
          `【uChoice Pro】${
            args?.params?.initialURL || window.location.href
          }\n${args?.desc ? `${args.desc}` : ''} ${copyMessage}`,
          () => {}
        )
      }

      let url = isIOS()
        ? 'https://uchoicepro.onelink.me/ujUP?af_xp=social&pid=Social_all&af_dp=com.uChoice.youpik%3A%2F%2F'
        : 'https://uchoicepro-android.onelink.me/e8Xg?af_xp=social&pid=Social_all&af_dp=ypk%3A%2F%2Fuceapp%3A8888%2Findex'

      if (!noTarget) {
        if (args && args.desc) {
          delete args.desc
        }
        let deepLinkParams = args || {
          route: 'Web',
          params: {
            initialURL: window.location.href
          }
        }
        if (/\.[a-zA-Z]+\/product\/\d+/.test(window.location.pathname)) {
          deepLinkParams = {
            route: 'Pdp',
            params: {
              id: window.location.pathname.split('/product/')[1]
            }
          }
        }

        url = `${url}&deep_link_value=${encodeURIComponent(
          JSON.stringify(deepLinkParams)
        )}`
      }

      window.location.href = url
    } else {
      resolve(true)
    }
  })
}

export const copyText = async (text: string, copied?: () => void) => {
  if (
    !!(await webview?.send(WebviewEvents.hasJavaScriptHandler, {
      handlerName: WebviewEvents.copy
    }))
  ) {
    webview?.send(WebviewEvents.copy, {
      text,
      withToast: copied ? false : true
    })
    return
  }

  try {
    await navigator.clipboard.writeText(text)
    if (copied) {
      copied?.()
    } else {
      const successMsg = await tAsync('Common.Copied')
      toast.success(successMsg)
    }
  } catch (error) {
    console.log('copy failed')
    // 如果Clipboard API失败，回退到旧方法
    const input: any = document.createElement('textarea')
    try {
      input.value = text
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      if (copied) {
        copied?.()
      } else {
        const successMsg = await tAsync('Common.Copied')
        toast.success(successMsg)
      }
    } catch (fallbackError) {
    } finally {
      document.body.removeChild(input)
    }
  }
}

export const resizeImageUrl = (url: string, width: number) => {
  if (!url) {
    return ''
  }

  return url
  // const size = (width / 2) * window.devicePixelRatio
  // const pattern = /-resize-jpeg:(\d+):(\d+)/
  // return url.replace(pattern, `-resize-jpeg:${size}:${size}`)
}

export const isPreviewMode = () => {
  const query = window.location.search.replace('?', '')
  return qs.parse(query)['mode'] === 'preview'
}

export const launchUrl = (url: string, interceptTiktokLink = true) => {
  if (webview) {
    webview?.send(WebviewEvents.launch, {
      url: url,
      interceptTiktokLink
    })
  } else {
    window.location.href = url
  }
}

export const makeBodyScrollable = (scrollable: boolean) => {
  const body = document.body

  if (scrollable) {
    body.style.overflow = ''
  } else {
    body.style.overflow = 'hidden'
  }
}
