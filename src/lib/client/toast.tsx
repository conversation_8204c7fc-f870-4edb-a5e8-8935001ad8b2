import { toast as hotToast } from 'react-hot-toast'
import { AppColors } from '../const'
import { webview } from './webview'
import { WebviewEvents } from './webview/events'
import { getNavHeight } from '@/components/TransparentNavPage'
import { getGoAppBarHeight } from '@/components/GoAppBar'

const calculateDuration = (message: string) => {
  const length = message.length
  if (length <= 10) {
    return 1500
  } else {
    return 2500
  }
}

const toast = (message: string) => {
  if (webview) {
    webview.send(WebviewEvents.toast, { message })
    return
  }

  const options = {
    position: 'top-center' as const,
    style: {
      marginTop: `${getGoAppBarHeight() + getNavHeight()}px`,
      background: 'white',
      color: AppColors.black
    }
  }

  hotToast.dismiss()
  hotToast(
    t => (
      <p className="line-clamp-[0] w-full max-w-[500px] break-all text-center">
        {message}
      </p>
    ),
    {
      ...options,
      duration: calculateDuration(message)
    }
  )
}

toast.success = (message: string) => {
  toast(message)
}

toast.error = (message: string) => {
  toast(message)
}

export { toast }
