import { toast } from '@/lib/client/toast'
import axios, {
  AxiosError,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig
} from 'axios'
import { ApiResponse } from '../types/http'
import { loading } from './loading'
import { getToken, isIOS, makeSureInApp } from './utils'
import { getBaseUrl, language, region } from '../utils'
import { webview } from './webview'
import { WebviewEvents } from './webview/events'

const cancelTokens: Record<string, (message?: string) => void> = {}

// 取消特定请求
export const cancelRequest = (requestKey: string) => {
  if (cancelTokens[requestKey]) {
    cancelTokens[requestKey]()
    delete cancelTokens[requestKey]
  }
}

// 取消所有请求
export const cancelAllRequests = () => {
  Object.keys(cancelTokens).forEach(key => {
    cancelTokens[key]()
    delete cancelTokens[key]
  })
}

export const cancelGetUserInfoRequests = () => {
  Object.keys(cancelTokens).forEach(key => {
    if(key.indexOf('getUserInfo_') != -1) {
      cancelTokens[key]()
      delete cancelTokens[key]
    }
  })
}

// 是否自定义处理错误信息
export const request = <T>(
  config: AxiosRequestConfig & {
    isHandleErrorCustom?: boolean
    requestKey?: string
  }
): Promise<ApiResponse<T>> => {
  return new Promise(resolve => {
    const instance = axios.create({
      baseURL: getBaseUrl(),
      timeout: 30000,
      withCredentials: true,
      headers: {
        'x-version': 'dev',
        'device-os-type': isIOS() ? 'ios' : 'android',
        region: region(),
        language: {
          en: 'en-US',
          th: 'th-TH',
          vi: 'vi-VN'
        }[language()]
      }
    })

    instance.interceptors.request.use(async config => {
      let Authorization: string = ''
      if (config.url?.includes('api-uaa/oauth/token')) {
        Authorization = `Basic ${btoa('app:app')}`
      } else {
        const token = await getToken()
        Authorization = `Bearer ${token}`
      }
      config.headers.Authorization = Authorization
      // 添加取消令牌
      const { requestKey } = config as AxiosRequestConfig & {
        requestKey?: string
      }
      if (requestKey) {
        cancelRequest(requestKey) // 取消已有的同名请求
        console.log(config.cancelToken, cancelRequest(requestKey), 'll;jl;')
        config.cancelToken = new axios.CancelToken(cancel => {
          cancelTokens[requestKey] = cancel
        })
      }
      return config
    })

    instance.interceptors.response.use(
      response => {
        const { code, message } = response.data as ApiResponse<any>
        const configReponse = response.config as InternalAxiosRequestConfig & {
          isHandleErrorCustom?: boolean
        }
        if (code !== 200 && !configReponse.isHandleErrorCustom) {
          toast.error(message)
        }
        // 清理取消令牌
        const { requestKey } = config
        if (requestKey) {
          delete cancelTokens[requestKey]
        }
        return response
      },
      async error => {
        const { response } = error as AxiosError
        console.log('response', response)
        loading.hide()

        try {
          const { status, data } = response as AxiosResponse
          if (status !== 401 && data?.message) {
            toast.error(data.message)
          }

          if (status === 401) {
            if (webview) {
              await webview?.send(WebviewEvents.makeSureLogined)
              window.location.reload()
            } else {
              makeSureInApp()
            }
          }
        } catch (error) {}
      }
    )

    instance
      .request(config)
      .then((response: AxiosResponse<ApiResponse<T>>) => {
        if (response) {
          resolve(response.data)
        }
      })
      .catch(error => {
        console.log('request error:', error)
      })
  })
}
