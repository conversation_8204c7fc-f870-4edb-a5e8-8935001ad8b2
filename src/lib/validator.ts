class Validator {
  // 数字加小数点，如：100.
  static isAlmostPrice(value?: string | number) {
    value = value + ''

    const match = value.match(/\d+\./)
    return match ? match[0] === match.input : false
  }

  // 是否是价格，精确到分
  static isPrice(value?: string | number) {
    value = value + ''

    // 排除：0000
    let match = value.match(/00+/)
    if (match && match[0] === match.input) {
      return false
    }

    // 排除：0123
    match = value.match(/0\d+/)
    if (match && match[0] === match.input) {
      return false
    }

    const regex = /^\d+(\.\d{1,2})?$/
    return regex.test(value)
  }

  // 可以输入的价格
  static isInputablePrice(value?: string | number) {
    return (
      value === '' || Validator.isAlmostPrice(value) || Validator.isPrice(value)
    )
  }
}

export { Validator }
