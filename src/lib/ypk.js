export const Events = {
  goback: 'goback',
  share: 'share',
  goPDP: 'goPDP',
  copyItemLink: 'copyItemLink',
  setTitle: 'setTitle',
  goHome: 'goHome',
  openBrowser: 'openBrowser',
  addToShowcase: 'addToShowcase'
}

const postMessage = (action, params = {}) => {
  if (window.ReactNativeWebView) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({
        payFlag: action,
        ...params
      })
    )
  }
}

export const ypkClient = typeof window !== 'undefined' && window.ReactNativeWebView
  ? {
    postMessage,
  }
  : null

export const ypkH5 = typeof window !== 'undefined' && window.location.href.includes('h5-uc.ypkshop.com')