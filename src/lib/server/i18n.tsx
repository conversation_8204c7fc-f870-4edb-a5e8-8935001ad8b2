import i18next from 'i18next'
import { en, th, vi } from '@/resources/translations'
import { serverLanguage } from './utils'

export const i18nSInit = () => {
  i18next.init({
    lng: serverLanguage(),
    debug: false,
    resources: {
      en: {
        translation: en
      },
      th: {
        translation: th
      },
      vi: {
        translation: vi
      }
    }
  })
}

const i18nS = i18next

export { i18nS }
