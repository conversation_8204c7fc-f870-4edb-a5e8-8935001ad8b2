import { ApiResponse } from '../types/http'
import { getAppData, isIOS } from './utils'
import { getBaseUrl, language, region } from '../utils'

export async function fetcher<T>(
  url: RequestInfo,
  init: RequestInit = { cache: 'no-store' }
): Promise<ApiResponse<T>> {
  const defaultHeaders = {
    'x-version': 'dev',
    'device-os-type': isIOS() ? 'ios' : 'android',
    region: `${region()}`,
    language: {
      en: 'en-US',
      th: 'th-TH',
      vi: 'vi-VN'
    }[`${language()}`]
  }

  if (getAppData()?.token) {
    defaultHeaders['Authorization'] = `Bearer ${getAppData()?.token}`
  }

  const headers = {
    ...defaultHeaders,
    ...init.headers
  }

  const updatedInit: RequestInit = {
    ...init,
    headers
  }

  const res = await fetch(`${getBaseUrl()}/${url}`, updatedInit)

  if (!res.ok) {
    const json = await res.json()
    if (json.error) {
      const error = new Error(json.error) as Error & {
        status: number
      }
      error.status = res.status
      throw error
    } else {
      throw new Error('fetcher failed')
    }
  }

  return res.json() as Promise<ApiResponse<T>>
}
