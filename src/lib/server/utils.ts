import { headers } from 'next/dist/client/components/headers'
import { Metadata } from 'next'

export const inApp = () => {
  return headers().get('app-data')
}

export const isIOS = () => {
  return /iPad|iPhone|iPod/.test(headers().get('user-agent') || '')
}

export const getAppData: () => Omit<
  AppData,
  'userInfo' | 'deviceInfo'
> | null = () => {
  if (inApp()) {
    const appData = JSON.parse(headers().get('app-data') as string)
    return appData as Omit<AppData, 'userInfo' | 'deviceInfo'>
  }
  return null
}

export const serverLanguage = () => {
  if (inApp()) {
    return getAppData()?.language
  }

  const lan = headers().get('language')
  if (lan === 'th' && serverRegion() === 'VN') {
    return 'th'
  }
  if (lan === 'vi' && serverRegion() === 'VN') {
    return 'vi'
  }
  if (lan === 'en') {
    return 'en'
  }

  const region = headers().get('region')
  if (region === 'TH') {
    return 'th'
  }
  if (region === 'VN') {
    return 'vi'
  }

  const accpetLanguage = headers().get('accept-language')
  if (accpetLanguage === 'th-TH') {
    return 'th'
  }
  if (accpetLanguage === 'vi-VN') {
    return 'vi'
  }

  return 'en'
}

export const serverRegion = () => {
  if (inApp()) {
    // 20240814: 为了兼容老版本app，老版本app没有region字段
    return getAppData()?.region || 'TH'
  }

  const region = headers().get('region')
  if (region === 'TH') {
    return 'TH'
  }
  if (region === 'VN') {
    return 'VN'
  }

  const accpetLanguage = headers().get('accept-language')
  if (accpetLanguage === 'th-TH') {
    return 'TH'
  }
  if (accpetLanguage === 'vi-VN') {
    return 'VN'
  }

  return 'TH'
}

export const defaultMetadataTemplate = {
  title: 'TikToker เลือก uChoice!',
  description: 'uChoice pro for TikTok Creators.',
  icon: 'https://file.uchoice.pro/public/img/231219/uce2_512.png?x-oss-process=style/jpg',
  // App导航栏标题
  nativeNavBarTitle: '',
  // 是否展示App导航栏
  showNativeNavBar: false,
  // 是否展示App导航栏分享按钮
  showNativeNavBarShareButton: false,
  // 是否展示App导航栏客服按钮
  showNativeNavBarContactButton: false,
  // App导航栏的分享埋点时间名
  nativeNavBarShareEvent: ''
}

export const metadataTemplate = (
  args?: Partial<typeof defaultMetadataTemplate>
) => {
  const {
    title = defaultMetadataTemplate.title,
    description = defaultMetadataTemplate.description,
    icon = defaultMetadataTemplate.icon
  } = args || defaultMetadataTemplate

  return {
    title,
    description,
    icons: {
      icon: icon || '../favicon.ico',
      shortcut: icon
    },
    twitter: {
      card: 'summary',
      title,
      description,
      images: [icon]
    },
    openGraph: {
      title,
      description,
      images: [icon]
    },
    other: inApp()
      ? {
        nativeNavBarTitle:
          args?.nativeNavBarTitle !== undefined &&
            args?.nativeNavBarTitle !== null
            ? args.nativeNavBarTitle
            : title,
        showNativeNavBar: args?.showNativeNavBar ? 'true' : 'false',
        showNativeNavBarShareButton: args?.showNativeNavBarShareButton
          ? 'true'
          : 'false',
        showNativeNavBarContactButton: args?.showNativeNavBarContactButton
          ? 'true'
          : 'false',
        nativeNavBarShareEvent: args?.nativeNavBarShareEvent
      }
      : {}
  } as Metadata
}
