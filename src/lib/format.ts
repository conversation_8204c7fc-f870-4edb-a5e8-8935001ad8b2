import { isRegionTH, isRegionVN } from './utils'

export const showEarn = (minEarn: number, maxEarn: number) => {
  if (minEarn === maxEarn) {
    return `${formatPrice(minEarn || 0, true)}`
  }
  return `${formatPrice(minEarn || 0, true)}-${formatPrice(maxEarn || 0, true)}`
}

export const mintoMaxPrice = (minPrince, maxPrice) => {
  if (minPrince && maxPrice) {
    return `${formatPrice(minPrince, true)}~${formatPrice(maxPrice, true)}`
  } else if (minPrince == maxPrice) {
    return `${formatPrice(minPrince, true)}`
  } else {
    return '-'
  }
}
export const formatSales = (
  num?: number,
  placeholder = '',
  isNeedZero = false
) => {
  if (num === undefined && !isNeedZero) {
    return placeholder
  }
  if (num !== undefined || num === 0) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num?.toString()
  }
  return '0' // 当 num 为 0 且 isNeedZero 为 true 时，返回 '0'
}

export function formatPrice(val = 0, withUnit = false, forceTH = false) {
  const num = Number(val).toFixed(2).toString()
  let result = num
  const [zheng, decimals] = num.split('.')
  if (forceTH || isRegionTH()) {
    if (num.length > 5) {
      result = `${zheng.replace(/(\d)(?=(\d{3})+$)/g, '$1,')}.${decimals}`
    }
  } else {
    result = `${zheng.replace(/(\d)(?=(\d{3})+$)/g, '$1.')}`
  }

  if (withUnit) {
    if (forceTH || isRegionTH()) {
      result = `฿${result}`
    } else {
      result = `${result}₫`
    }
  }

  return result
}

export function formatCount(val) {
  if (`${val}`.includes(',')) {
    return val
  }
  return formatPrice(val, false, true).replace('.00', '').replace(/\./g, ',')
}

export function formatRate(val) {
  let valRate = val / 100
  return formatPrice(valRate, false, true).replace('.00', '')
}
export function formatThous(val) {
  if (!val) {
    return 0
  }
  let valThous = val
  return isRegionTH()
    ? formatPrice(valThous).replace('.00', '')
    : formatPrice(valThous)
}

export function formatPercentSign(val) {
  let valRate = val * 100
  return formatPrice(valRate).replace('.00', '%')
}

export function formatTime(timestamp, format = 'DD/MM/YYYY') {
  const date = new Date(timestamp)
  const day = ('0' + date.getDate()).slice(-2)
  const month = ('0' + (date.getMonth() + 1)).slice(-2)
  const year = date.getFullYear()

  return format
    .replace('DD', day)
    .replace('MM', month)
    .replace('YYYY', `${year}`)
}

export function formatMinMax(min, max) {
  if (!min || !max) {
    return ''
  }
  return min === max ? `${min}` : `${min}~${max}`
}
