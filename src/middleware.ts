import { NextResponse } from 'next/server'
import { NextRequest } from 'next/server'
import qs from 'qs'

export function middleware(request: NextRequest) {
  const url = request.nextUrl
  const query = url.searchParams.toString()

  const params = qs.parse(query)

  // 兼容老页面，这三个页面已经删掉了
  if (
    url.pathname.includes('/tiktok_data/anchor_list/page') ||
    url.pathname.includes('/tiktok_data/shop_list/page') ||
    url.pathname.includes('/tiktok_data/product_list/page')
  ) {
    const modifiedUrl = url.clone()
    modifiedUrl.pathname = '/tiktok_data/page'
    return NextResponse.redirect(modifiedUrl)
  }

  const headers = new Headers({
    ...Object.fromEntries(request.headers),
    region: `${params.region}`,
    language: `${params.language}`
  })

  if (params.region) {
    const modifiedRequest = new NextRequest(request.url, {
      headers,
      method: request.method,
      body: request.body,
      cache: request.cache,
      credentials: request.credentials,
      integrity: request.integrity,
      keepalive: request.keepalive,
      mode: request.mode,
      redirect: request.redirect,
      referrer: request.referrer,
      referrerPolicy: request.referrerPolicy,
      signal: request.signal
    })

    return NextResponse.next(modifiedRequest)
  }

  return NextResponse.next()
}
