export const showcase = {
  en: {
    申样流程: 'Sample application process',
    选择样品: 'Step 1 : Choose a Sample',
    选择想申请的样品: '>> Select the sample you want to apply for',
    进入商品详情页: '>> Go to the product details page',
    点击申请样品按钮: '>> Click the "Request Sample" button',
    授权: 'Step 2 : Tiktok Authorization',
    step2_1: '',
    如果您的账号没有授权:
      'If your account is not authorized, you need to authorize it before applying for samples.',
    则进入申请样品页面:
      'If your account is already authorized, proceed to the sample application page.',
    填写申请样品信息: 'Step 3 : Fill in Sample Application Information',
    '在样品申请页面，您需要：填写或者检查样品':
      'On the sample application page, you need to:',
    step3_2: ' Fill in or check the sample delivery address.',
    step3_3: 'Verify the number of samples you can apply for according to [',
    样品申请规则: 'the sample application rules',
    核对申请样品数量:
      'Confirm the TikTok account for the sample application (if you have multiple accounts linked).',
    step3_4:
      "Upload or update a screenshot of your sales from the last month to facilitate the platform's review of the number of samples you can apply for.",
    step3_5: 'Choose your fulfillment method.',
    等待审核: 'Step 4 : Pending Review',
    重新打开app:
      'When you re-open the app, you can view the sample review status on the "Account" page.',
    平台审核:
      'If your sample is approved by the platform, it will move to the "To be Shipped" status.',
    关闭状态: 'If it is not approved, it will move to the "Closed" status.',
    收样品进行履约: 'Step 5 : Receive Samples and Fulfill Commitments',
    商家发货之后:
      '>> After the seller packes and ships the product, the status of the sample will go to "Pending Fulfill."',
    达人收到样品后:
      '>> After receiving the sample, creators choose the fulfillment method and enter the fulfillment information.',
    注意: 'Note⚠️',
    获得视频投流奖励:
      ':Choosing "video" or "video & live" will qualify you for video streaming rewards',
    提交之后:
      'After submission, it will go to the "Fulfillment Pending Review" status.',
    履约审核成功:
      'If the fulfillment review is failed, it will go to the "Fullfillment failed"  Status in "Sample Management", creators need to re-enter the fullfillment information.',
    释放一个样品名额:
      'If the fulfillment review is successful, it will go to the "Completed"  Status in "Sample Management", and get a sample quota and the video promotion rewards.',
    查看详细流程: 'View Details >>',
    pdp_查看详细流程: 'Details for Newcomers',
    立即前往新人精选样品池: 'Go to Exclusive Sample Pool for New Comers',
    '新人95%+通过率': 'New User 95%+ Approval',
    'New Arrival': 'New Arrivals',
    '新品来袭 优先申样！': 'New arrivals, priority sampling!',
    '火爆热销中…': 'Hot-selling...',
    申样热度: 'Hot samples',
    '30天销量': '30-day sales',
    最新: 'Newest',
    店铺评分: 'Shop rating',
    '佣金率%': 'Commission%',
    'Free sample': 'Free sample',
    'Sold/Month': '{{sold}} sold/month'
  },
  th: {
    申样流程: 'ขั้นตอนการขอสินค้าตัวอย่าง',
    选择样品: 'ขั้นตอนที่ 1 : เลือกสินค้าตัวอย่าง',
    选择想申请的样品: '>> เลือกสินค้าตัวอย่างที่คุณต้องการขอ',
    进入商品详情页: '>> ไปที่หน้ารายละเอียดสินค้า',
    点击申请样品按钮: '>> คลิกที่ปุ่ม "ขอสินค้าตัวอย่าง"',
    授权: 'ขั้นตอนที่ 2 : การให้อนุญาตกับ Tiktok',
    step2_1: 'หากบัญชีของคุณไม่ได้อนุญาตในการเข้าถึงข้อมูล',
    如果您的账号没有授权: 'คุณจะต้องให้อนุญาตก่อนที่จะสมัครขอตัวอย่าง',
    则进入申请样品页面:
      'หากบัญชีของคุณได้ให้อนุญาตมในการเข้าถึงแล้ว ให้ไปที่หน้าการขอสินค้าตัวอย่าง',
    填写申请样品信息: 'ขั้นตอนที่ 3: กรอกข้อมูลการขอสินค้าตัวอย่าง',
    '在样品申请页面，您需要：填写或者检查样品':
      'ในหน้าใบขอสินค้าตัวอย่าง คุณต้อง:',
    step3_2: '-กรอกหรือตรวจสอบที่อยู่สำหรับจัดส่ง',
    step3_3: '-ตรวจสอบจำนวนสินค้าตัวอย่างที่คุณสามารถขอได้ตาม',
    样品申请规则: 'กฎการขอสินค้าตัวอย่าง',
    核对申请样品数量:
      '-ยืนยันบัญชี TikTok สำหรับสินค้าตัวอย่าง (หากคุณเชื่อมโยงหลายบัญชี)',
    step3_4:
      ' -อัปโหลดหรืออัปเดตภาพหน้าจอยอดขายของคุณจากเดือนที่แล้วเพื่อความสะดวกในการตรวจสอบจำนวนสินค้าตัวอย่างของแพลตฟอร์มที่คุณสามารถส่งคำขอได้',
    step3_5: '',
    等待审核: 'ขั้นตอนที่ 4: รอการตรวจสอบ',
    重新打开app:
      'เมื่อเปิดแอปอีกครั้ง คุณสามารถดูสถานะการตรวจสอบสินค้าได้ในหน้า "บัญชี"',
    平台审核:
      'หากสินค้าตัวอย่างของคุณได้รับการอนุมัติจากแพลตฟอร์ม สถานะของสินค้าตัวอย่างจะย้ายไปที่ "กำลังจัดส่ง"',
    关闭状态: 'หากไม่ได้รับการอนุมัติ สถานะของสินค้าตัวอย่างจะย้ายไปยัง "ปิด"',
    收样品进行履约: 'ขั้นตอนที่ 5 : รับสินค้าตัวอย่างและปฏิบัติตามข้อตกลง',
    商家发货之后:
      '>> หลังจากที่ผู้ขายบรรจุและจัดส่งสินค้าแล้ว สถานะของสินค้าตัวอย่างจะไปที่ "รอดำเนินการ"',
    达人收到样品后:
      '>> หลังจากได้รับสินค้าตัวอย่างแล้ว ครีเอเตอร์จะเลือกวิธีการกรอกข้อมูลและป้อนข้อมูลที่จำเป็น',
    注意: 'หมายเหตุ⚠️',
    获得视频投流奖励:
      ':การเลือก "วิดีโอ" หรือ "วิดีโอ & ไลฟ์" จะทำให้คุณมีสิทธิ์ได้รับรางวัลการสตรีมวิดีโอ',
    提交之后:
      'หลังจากส่งแล้ว มันจะย้ายไปอยู่ที่สถานะ "การดำเนินการรอการตรวจสอบ"',
    履约审核成功:
      'หากการตรวจสอบการดำเนินการล้มเหลว คำขอของคุณจะถูกย้ายไปที่สถานะ "การดำเนินการล้มเหลว" ใน "การจัดการสินค้าตัวอย่าง" ครีเอเตอร์ต้องป้อนข้อมูลการดำเนินการอีกครั้ง',
    释放一个样品名额:
      'หากการตรวจสอบการดำเนินการสำเร็จคำขอของคุณจะถูกย้ายไปที่สถานะ  "เสร็จสมบูรณ์" ใน "การจัดการสินค้าตัวอย่าง" และได้รับโควต้าสินค้าตัวอย่างและรางวัลสำหรับโปรโมตวิดีโอ',
    查看详细流程: 'ดูรายละเอียด >>',
    pdp_查看详细流程: 'รายละเอียดสำหรับผู้มาใหม่',
    立即前往新人精选样品池: 'ไปที่คลังสินค้าตัวอย่างพิเศษสำหรับผู้ใช้ใหม่',
    '新人95%+通过率': 'ผู้ใช้ใหม่มากกว่า 95% ได้ให้การอนุมัติ',
    'New Arrival': 'สินค้ามาใหม่',
    '新品来袭 优先申样！': 'สินค้าใหม่มาแล้ว ขอตัวอย่างได้ก่อนใคร!',
    '火爆热销中…': 'สินค้าขายดี...',
    申样热度: 'สินค้าตัวอย่างยอดฮิต',
    '30天销量': 'ยอดขาย 30 วันล่าสุด',
    最新: 'ใหม่ล่าสุด',
    店铺评分: 'คะแนนร้านค้า',
    '佣金率%': 'ค่าคอม%',
    'Free sample': 'Free sample',
    'Sold/Month': 'ยอดขาย 30 วัน {{sold}}'
  },
  vi: {
    申样流程: 'Quy trình đăng ký mẫu',
    选择样品: 'Bước 1: Chọn Mẫu',
    选择想申请的样品: '>> Chọn mẫu bạn muốn đăng ký',
    进入商品详情页: '>> Đi đến trang chi tiết sản phẩm',
    点击申请样品按钮: '>> Nhấp vào nút "Yêu cầu Mẫu"',
    授权: 'Bước 2: Xác thực TikTok',
    step2_1: '',
    如果您的账号没有授权:
      'Nếu tài khoản của bạn chưa được ủy quyền, bạn cần thực hiện ủy quyền trước khi đăng ký mẫu.',
    则进入申请样品页面:
      'Nếu tài khoản của bạn đã được xác thực, tiếp tục đến trang đăng ký mẫu.',
    填写申请样品信息: 'Bước 3: Điền thông tin đăng ký mẫu',
    '在样品申请页面，您需要：填写或者检查样品':
      'Trên trang đăng ký mẫu, bạn cần:',
    step3_2: ' Điền hoặc kiểm tra địa chỉ giao hàng của mẫu.',
    step3_3: 'Xác minh số lượng mẫu bạn có thể đăng ký theo [',
    样品申请规则: 'quy tắc đăng ký mẫu',
    核对申请样品数量:
      'Xác nhận tài khoản TikTok cho đơn đăng ký mẫu (nếu bạn có nhiều tài khoản liên kết).',
    step3_4:
      'Tải lên hoặc cập nhật ảnh chụp màn hình doanh thu của bạn từ tháng trước để giúp nền tảng xem xét số lượng mẫu bạn có thể đăng ký.',
    step3_5: 'Chọn phương thức thực hiện.',
    等待审核: 'Bước 4: Đang chờ xem xét',
    重新打开app:
      'Khi bạn mở lại ứng dụng, bạn có thể xem trạng thái xem xét mẫu trên trang "Tài khoản".',
    平台审核:
      'Nếu mẫu của bạn được nền tảng phê duyệt, nó sẽ chuyển sang trạng thái "Chờ Giao Hàng".',
    关闭状态:
      'Nếu không được phê duyệt, nó sẽ chuyển sang trạng thái "Đã Đóng".',
    收样品进行履约: 'Bước 5: Nhận mẫu và thực hiện cam kết',
    商家发货之后:
      '>> Sau khi người bán đóng gói và gửi sản phẩm, trạng thái của mẫu sẽ chuyển thành "Chờ Thực Hiện".',
    达人收到样品后:
      '>> Sau khi nhận mẫu, các nhà sáng tạo chọn phương thức thực hiện và nhập thông tin thực hiện.',
    注意: 'Lưu ý⚠️',
    获得视频投流奖励:
      ': Chọn "video" hoặc "video & live" sẽ đủ điều kiện nhận thưởng video streaming',
    提交之后:
      'Sau khi gửi, nó sẽ chuyển thành trạng thái "Chờ Xem Xét Thực Hiện".',
    履约审核成功:
      'Nếu xem xét thực hiện thất bại, nó sẽ chuyển thành trạng thái "Thực Hiện Thất Bại" trong "Quản Lý Mẫu", các nhà sáng tạo cần nhập lại thông tin thực hiện.',
    释放一个样品名额:
      'Nếu việc xem xét thực hiện thành công, nó sẽ chuyển thành trạng thái "Hoàn Thành" trong "Quản Lý Mẫu", và nhận một hạn ngạch mẫu cùng với phần thưởng video quảng cáo.',
    查看详细流程: 'Xem Chi Tiết >>',
    pdp_查看详细流程: 'Chi tiết cho Người mới',
    立即前往新人精选样品池: 'Đi đến khu vực mẫu dành cho Người mới',
    '新人95%+通过率': 'Tỷ lệ thông qua của người mới 95%+',
    'New Arrival': 'Hàng mới về',
    '新品来袭 优先申样！': 'Hàng mới về, ưu tiên lấy mẫu!',
    '火爆热销中…': 'Đang bán chạy...',
    申样热度: 'Mẫu bán chạy',
    '30天销量': 'Doanh số 30 ngày',
    最新: 'Mới nhất',
    店铺评分: 'Đánh giá cửa hàng',
    '佣金率%': 'Tỷ lệ hoa hồng%',
    'Free sample': 'Free sample',
    'Sold/Month': '{{sold}} Đã bán/tháng'
  }
}
