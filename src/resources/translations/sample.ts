export const sample = {
  en: {
    submitTips:
      'Some business data of you(active / sales /sample fulfillment)will be visible to sellers when they receive the sample requests from you.',
    RequestSubmitted: 'Request Submitted',
    requestSubTips:
      'Thanks for requesting a sample.The seller will respond within 7 days. And you can add the product manually.',
    ViewDetails: 'View details',
    Sample_Request: `Sample Request`,
    Pending_Review: `Pending Review`,
    To_be_shipped: `To be shipped`,
    To_be_received: `To be received`,
    Closed: `Closed`,
    You_Currently_have_no_Orders: `No orders here.`,
    Request_Sample: `Request Sample`,
    Request_Time: `Request Time`,
    Track_delivery: `Track delivery`,
    Sample: `Sample`,
    Please_authorize_tiktok_account: `Please authorize tiktok account`,
    conditions: `Conditions：
    1.This product requires the number of creator's fans ≥#;
    2.Average video views ≥#;
    3.GMV sales in the past month ≥ #;`,
    Select_the_creator: `Select the creator`,
    no_qualified_creator_account: `no qualified creator account`,
    Please_select_creator: `Please select creator`,
    You_can_request_up_to_x_samples: `You can request up to x samples`,
    If_the_sample_order_of_the_application_is___x__one_application_quota_will_be_restored_: `If the sample order of the application is ≥ x, one application quota will be restored;`,
    got_it: `got it`,
    After_receiving_the_free_sample_I_agree_to_post_a_video_or_LIVE_video_that_contains_the_product_link_I_ll_Keep_the_video_public_for_at_least_3_days_or_keep_the_product_link_in_my_LIVE_video_for_at_least_10_minutes_: `After receiving the free sample,I agree to post a video or LIVE video that contains the product link.I'll Keep the video public for at least 3 days,or keep the product link in my LIVE video for at least 10 minutes.`,
    Delivery_Information: `Delivery Information`,
    Please_fill_in_your_Line_ID_so_that_we_can_contact_you: `Please fill in your Line ID so that we can contact you`,
    Enter_Line_ID: `Enter Line ID`,
    Quit_process: `Quit process`,
    Your_information_will_not_be_saved: `Your information will not be saved`,
    Upload_Sales_screenshots_of_Tiktok_showcase_: `Upload Sales screenshots of Tiktok showcase `,
    To_facilitate_merchants_to_pass_your_sample_application_as_soon_as_possible__please_upload_a_screenshot_of_TikTOK_showcase_sales_data_: `To facilitate merchants to pass your sample application as soon as possible, please upload a screenshot of TikTOK showcase sales data;`,
    Please_ensure_the_authenticity_of_the_pictures__otherwise_your_permission_to_apply_for_samples_may_be_closed_: `Please ensure the authenticity of the pictures, otherwise your permission to apply for samples may be closed;`,
    Example: `Example`,
    Screenshots_Example: `Screenshots Example`,
    Please_go_to_the_Tiktok_store_to_capture_the_page_as_below_and_upload_it_: `Please go to the Tiktok store to capture the page as below and upload it;`,
    For_detailed_steps__refer_to_the__instructions_below___: `For detailed steps, refer to the  instructions below ↓;`,
    How_to_get: `How to get`,
    Step: `Step`,
    Uploaded_time: `Uploaded time`,
    Update_sales_of_showcase_screenshot: `Update sales of showcase screenshot`,
    bdata_notes: `Some business data of you(active / sales /sample fulfillment)will be visible to sellers when they receive the sample requests from you.  `,
    Request_Submitted: `Request Submitted`,
    Thanks_for_requesting_a_sample_The_seller_will_respond_within_7_days_And_you_can_add_the_product_manually_: `Thanks for requesting a sample.The seller will respond within 7 days.And you can add the product manually.`,
    View_details: `View details`,
    The_sales_screenshots_of_tiktok_showcase_: `The sales screenshots of tiktok showcase `,
    upload_: `upload `,
    update_: `update`,
    Request_detail: `Request detail`,
    We_will_finish_the_review_within_7_working_days__please_wait_patiently_: `We will finish the review within 7 working days, please wait patiently;`,
    Application_submitted: `Application submitted`,
    Application_approved_to_be_shipped_: `Application approved，to be shipped`,
    Shipped: `Shipped`,
    Transaction_closed: `Transaction closed`,
    Creator_s_account: `Creator's account`,
    Contact_us: `Contact us`,
    Logistics_: `Logistics `,
    Courier_Company: `Courier Company`,
    Tracking_number: `Tracking number`,
    Go_to_the_logistics_offical_website: `Go to the logistics offical website`,
    Fans: `Fans`,
    order: 'Order:',
    edit: 'Edit',
    add_address: 'Add Address',
    upload_image: 'Upload Image',
    sample_order_management: 'Sample Order Management',
    'x笔订单待上传履约视频/直播，履约后达到出单要求获得':
      '{{count}} orders pending Video/LIVE upload for',
    '฿投流奖励（先到先得）':
      ' ฿{{reward}} video promotion reward (first come, first served)',
    'x笔订单待上传履约视频/直播，履约后达到出单要求可获得额外免费样品数量。':
      '{{count}} orders are waiting for uploading fulfillment Video/LIVE. After fulfilling, you can receive extra free sample quantities.',
    '去查看>': 'Check Now >',
    投流奖励说明: 'Video Promote Reward',
    样品奖励说明: 'Sample Reward',
    发货待履约: `Pending Fulfill`,
    履约审核中: `Fulfill Reviewing`,
    履约失败: `Fullfillment failure`,
    已完成: `Completed`,
    直播: `LIVE`,
    视频: `Video`,
    投流奖励状态: `Reward Status`,
    未达标: `Not Met`,
    直播无投流奖励: `No reward for LIVE`,
    未上传视频ID: `Video Code not uploaded`,
    待发放: `Pending issuance`,
    已发放: `Issued`,
    无投流奖励: `No reward`,
    上传视频链接或直播截图: `Upload video links or screenshots of LIVE`,
    上传视频ID: 'Upload VIDEO Code',
    '已发货，进入履约流程': `Shipped, entered the fulfillment process`,
    '我们会在三个工作日内完成审核，请耐心等待': `We will complete checking within 3 working days, please be patient.`,
    履约待审核: `Fulfillments to be confirmed`,
    '提交成功 待审核': `Submitted successfully, pending checking`,
    审核拒绝: `Fullfillment checking rejected`,
    '审核被拒绝，请重新您的履约信息': `The fullfillment was rejected, please re-enter your information.`,
    履约成功后可以开放一个样品资格: `After the fullfillment is completed, a sample qualification can be opened for you.`,
    请点击下方按钮填写履约信息: `Please click the button below to fill in the fullfillment information`,
    审核通过: `Fullfillment checking approved`,
    履约流程: `Fulfillment process`,
    发货流程: `Shipping process`,
    填写履约信息: `Fill in fullfillment\ninformation`,
    请上传视频ID: `Please upload your VIDEO Code`,
    '选填，请复制视频ID，用于视频投放奖励': `Optional, ID for video promotional reward`,
    '如何获得视频ID？': `How to Get a Video Code?`,
    确认上传: `Confirm Upload`,
    '上传后不可更改，确认要继续吗？': `After uploading, it cannot be changed. Are you sure you want to continue?`,
    上一步: `Previous`,
    下一步: `Next`,
    视频和直播: `Video & Livestream`,
    履约方式: `Fulfill Ways`,
    请复制视频链接到此处: `Please copy the video link here`,
    必填: `Required`,
    选填: `Optional`,
    履约申请: `Fullfillment application`,
    '如何履约？': `How to fulfill?`,
    '1、履约完成后，只要您的销售件数超过x件，可以恢复一个申请样品的名额。': `1. After the fulfillment is completed, as long as the number of your sales exceeds {{count}} pieces, you can resume one sample application quota.`,
    '1、履约完成并审核通过后，可以恢复一个申请样品的名额。':
      '1. After completing and passing the fulfillment, one sample application quota can be opened for you.',
    '2、您可以选择视频/直播的履约形式。': `2. You can choose the video/livestream fulfillment method.`,
    '3、视频履约需要填写视频链接': `3. Video fulfillment requires filling in the video link.`,
    '4、直播履约需要填写直播截图，我们会为您提供示例图': `4. Livestream fullfillment requires uploading livestream screenshots, and we will provide you with sample images.`,
    '5、请填写真实的履约信息，我们会进行核实。': `5. Please fill in the true information and we will check it.`,
    我已知晓: `Got it`,
    '1. Go to creator tools in the TikTok profile page': `1. Go to creator tools in the TikTok profile page`,
    _2__Turn_on__Ad_settings__: `2. Turn on "Ad settings."`,
    '3. Select the TikTok post you want to create the ad with, and go to the ad settings page.': `3. Select the TikTok post you want to create the ad with, and go to the ad settings page.`,
    _4__Turn_on_the__Ad_authorization__and_generate_a_video_code__Please_select_the_longest_period_of_validity_for_the_code__: `4. Turn on the "Ad authorization" and generate a video code.（{{replaceme}}）`,
    'Please select the longest period of validity for the code.': `Please select the longest period of validity for the code.`,
    '您未达到投流奖励要求，请继续加油哦！': `You have not met the requirements for a {{replaceme}}. Keep up the good work!`,
    'video promote reward': '฿{{amount}} {{link}}',
    'video promote reward 文字部分': 'video promote reward',
    '您已达到投流奖励要求，投流视频ID：，请耐心等待运营人员联系商家为您投流': `You have met the requirements for a ฿{{amount}} video promote reward. Promote Video ID: {{videoId}}. Please wait for our operations team to contact the merchant to deliver it to you.`,
    '恭喜达到出单要求！投流奖励已发放给视频ID：': `Congratulations on meeting the order requirements! The ฿{{amount}} video promote reward has been issued to Video ID: {{videoId}}.`,
    '已完成，检测到您已销售超过件，本样品名额已释放': `Completed. We've known that you have sold more than {{count}} pieces. The quota for this sample has been released.`,
    '达人收到样品并发布履约视频/直播后，一个月内，达成以下要求，即可获得฿视频投流奖励。': `After you receive samples and publishes fulfillment Video/LIVE, meeting the promoted reward requirements within one month will earn  a <b>{{amount}} video promote reward</b>.`,
    '฿视频投流奖励。': `฿{{amount}} video promote reward`,
    商品出单量: `Orders of this product`,
    GMV: `GMV of this product`,
    视频播放量: `Video views`,
    数量: `Quantity`,
    最多申请件: `No more than {{count}}`,
    '收到样品且上传发布的视频/直播后，样品商品的订单销量＞单时，您的账号将恢复1个免费样品额度。': `After receiving samples and uploading Video/LIVE, when the order sales of sample products is >{{count}}, your account will be credited with 1 free sample quota.`,
    '商品出单数≥': `Product orders ≥ {{count}}`,
    '商品GMV≥฿': `GMV ≥ ฿{{amount}}`,
    '视频播放量≥': `Video views ≥ {{count}}`,
    '已完成，销售本商品达到件，就可以释放本样品名额': `Completed, if the sales of this product reaches {{count}} pieces, the sample quota can be released.`,
    '尽快提交视频/直播履约信息，达到出单要求获得中投流奖励（先到先得）': `Submit your Video/LIVE fulfillment information as soon as possible to meet the order requirements and receive a ฿{{amount}} video promote reward (first come, first served).`,
    '尽快提交视频/直播履约信息，达到出单要求可获得额外免费样品数量': `Submit your Video/LIVE fulfillment information as soon as possible to receive extra free sample quantities.`,
    '达到出单要求，获得฿投流奖励，先到先得': `Meet the order requirements, get ฿{{amount}} video promote reward, first come, first served`,
    该订单已上传视频ID: 'The sample order has uploaded the video promotion ID',
    'Promote Video ID': 'Promote Video Code',
    提示: 'เคล็ดลับ',
    '该tiktok账号仅能同时申请x个样品，请及时将历史申请履约，否则无法申请新的样品查看《样品申请规则》': `You can only apply for {{count}} samples at the same time for this TikTok account. Please ensure that you {{履约}} your previous sample requests in time, or you won't be able to apply for new samples.View {{样品申请规则}}`,
    履约: `fulfill`,
    样品申请规则: 'Sample Application Rules',
    '申请条件: 该商品要求达人粉丝数≥；点击查看《样品申请规则》': `Application conditions: This product requires the  creator's fans ≥ {{count}},  clicks to view the {{样品申请规则}}`,
    待平台审核: `Pending platform review`,
    待商家审核: `Pending seller review`,
    '平台审核通过，待商家审核': `The platform review approved, pending review by seller.`,
    '商家审核通过，待发货': `Seller review approved, waiting for shipment`,
    '注意：直播履约需要在直播时进行截圈，点击查看案例': `Note: Livestream fulfillment requires the screenshot while you are doing the livestreaming. {{点击查看案例}}.`,
    点击查看案例: `Click to view the case`,
    可履约方式: `How to Fulfill`,
    达人免费领养次数说明: `Explanation for the number of samples that creators can receive for free`,
    序号: `Number`,
    最近一个月橱窗销售额要求: `Requirements for last month’s showcase sales`,
    可同时免费领取样品数量: `Number of samples that can receive for free at the same time`,
    '注意:': `Note:`,
    '1.达人领取样品后，完成履约并审核通过，可以恢复一次达人免费领样次数。': `1. After the creator receives the sample, and the fulfillment is completed and approved, the creator can get a free sample quota.`,
    '2.部分商品领取样品要求以商家实际要求为准。': `2. The sample application requirements for some products are subject to the actual requirements of the seller.`,
    销售额范围: `Sales range`,
    '请上传TikTOK店铺橱窗销售数据截图，以方便商家尽快通过您的样品申请': `Please upload 1 Month sales`,
    '您上传的橱窗销售截图已过期，请重新上传': `The sales screenshort you uploaded has over 30 days ago, please upload again`,
    请选择您最近一个月的橱窗销量额范围: `Select the sales range for your showcase in the past month.`,
    及以上: ' and above',
    总销售额: `Total Sales`,
    'Please upload/update showcase sales screenshots for sample approval.':
      'Please upload/update showcase sales screenshots for sample approval.',
    提交时间: 'Submission Time',
    商家补发: `Seller Resend`,
    upload: 'upload',
    新人免费样品X1: 'New Comer Free Sample X1',
    即将失效: 'Expiring Soon',
    'Free samples': 'Free samples',
    免费样品额度: 'Free Sample Quota',
    升级免费样品额度: 'Upgrade free sample quota',
    及时履约: 'Timely fulfillment',
    '需要您在拿到样品后及时履约，否则免费样品额度将不再被返还。':
      'You need to fulfill the sample in a timely manner after receiving it, otherwise the free sample quota will not be returned.',
    提升GMV: 'Increase GMV',
    '通过提升自身的内容销售能力，可以根据Youpik uChoice的样品规则，获得更多免费样品资格！':
      "By improving your content sales ability, you can get more free sample qualifications according to Youpik uChoice's sample rules!",
    'Report level problem upload proof': 'Report level problem & upload proof',
    当前等级: 'Current',
    '30天成交件数': '30 Days Items Sold',
    提升成交件数: 'Improve Items Sold',
    '❗️出单送申样机会+出单教程❗️': 'Unlock free sample & sales tutorial',
    首次购样流程: 'First sample purchase process',
    '点击可查看购样教程，如不需要，点击【跳过】按钮即可。':
      'Click to view sample purchase tutorial, or click "Continue" if you don\'t need it.',
    继续购样: 'Continue',
    '查看购样流程>': 'How to buy >',
    '挂橱窗，解锁出单教程': 'Add showcase, unlock sales tutorial',
    '平台赠送您该商品的出单教程，助您内容销量的增长！请先将商品添加橱窗~':
      '<main>The platform provides a <button>sales tutorial</button> for this product to help you increase sales. Please <red>add the product to the showcase first</red>.</main>',
    '添加橱窗>': 'Add showcase >',
    已解锁回本教程: 'Sales tutorial unlocked',
    '平台赠送您该商品的出单教程，助您内容销量的增长！可前往“我的”页面查看~':
      'The platform provides a <span style="color: #FF0032;">sales tutorial</span> for this product to help you increase sales! You can check it on the "My" page.',
    '回本教程>': 'Sales tutorial >',
    '请认准【YOUPIK】高佣链接':
      'Please ensure the link contains "YOUPIK" with high commission',
    'DOs弹窗处点击add showcase': 'DOs: Click add showcase in the pop-up window',
    'DONTs弹窗处点击view more后add showcase':
      'DONTs: After clicking view more in the pop-up, click add showcase',
    成本购样: 'Buy sample',
    添加橱窗: 'Add showcase',
    解锁教程: 'Unlock sales tutorial',
    模仿带货: 'Imitate selling',
    轻松出单: 'Easy to get sales',
    免费样品: `Free Samples`,
    流程详情: 'Process details',
    购样流程: 'Buy sample process'
  },
  th: {
    submitTips:
      'Some business data of you(active / sales /sample fulfillment)will be visible to sellers when they receive the sample requests from you.',
    RequestSubmitted: 'Request Submitted',
    requestSubTips:
      'Thanks for requesting a sample.The seller will respond within 7 days. And you can add the product manually.',
    ViewDetails: 'View details',
    Sample_Request: `ขอสินค้าตัวอย่างฟรี`,
    Pending_Review: `รอตรวจสอบ`,
    To_be_shipped: `อยู่ระหว่างจัดส่ง`,
    To_be_received: `จัดส่งสำเร็จ`,
    Closed: `คำขอถูกปฏิเสธ`,
    You_Currently_have_no_Orders: `ยังไม่มีคำขอสินค้าตัวอย่าง`,
    Request_Sample: `ขอสินค้าตัวอย่าง`,
    Request_Time: `เวลาส่งคำขอ`,
    Track_delivery: `ติดตามการจัดส่ง`,
    Sample: `สินค้าตัวอย่าง`,
    Please_authorize_tiktok_account: `กรุณาอนุญาตการเข้าถึงบัญชี TikTok ของคุณ`,
    conditions: `เงื่อนไขการลงทะเบียน:
    1 ครีเอเตอร์ต้องมีผู้ติดตามมากกว่า # คนขึ้นไป
    2 ยอดคนดูคลิปโดยเฉลี่ย # วิว
    3 ยอดขายย้อนหลัง 1 เดือน มากกว่า # บาทขึ้นไป`,
    Select_the_creator: `เลือกบัญชีครีเอเตอร์`,
    no_qualified_creator_account: `ยังไม่มีบัญชีครีเอเตอร์ที่ตรงตามเงื่อนไข`,
    Please_select_creator: `เลือกบัญชีครีเอเตอร์`,
    You_can_request_up_to_x_samples: `คุณสามารถขอสินค้าตัวอย่างได้สูงสุด x รายการ`,
    If_the_sample_order_of_the_application_is___x__one_application_quota_will_be_restored_: `ถ้าคำสั่งซื้อสินค้าตัวอย่างมากกว่า x คำสั่งซื้อ คุณมีสิทธิได้รับการพิจารณาเพื่อรับสินค้าตัวอย่างเพิ่มเติม`,
    got_it: `เข้าใจแล้ว`,
    After_receiving_the_free_sample_I_agree_to_post_a_video_or_LIVE_video_that_contains_the_product_link_I_ll_Keep_the_video_public_for_at_least_3_days_or_keep_the_product_link_in_my_LIVE_video_for_at_least_10_minutes_: `หลังจากได้รับสินค้าตัวอย่าง ฉันยินยอมทีจะโพสต์วิดีโอหรือวิดีโอไลฟ์ที่มีลิงก์สินค้า โดยตั้งค่าวิดีโอเป็นสาธารณะไว้อย่างน้อย 3 วัน หรือแสดงลิงก์สินค้าไว้ในวีดิโอไลฟ์เป็นเวลาอย่างน้อย 10 นาที`,
    Delivery_Information: `ข้อมูลการจัดส่ง`,
    Please_fill_in_your_Line_ID_so_that_we_can_contact_you: `กรุณากรอก Line ID เพื่อการแจ้งสถานะคำขอสินค้าตัวอย่างของคุณ`,
    Enter_Line_ID: `กรอก Line ID`,
    Quit_process: `คุณยืนยันที่จะออกจากหน้านี้หรือไม่`,
    Your_information_will_not_be_saved: `ข้อมูลของคุณจะไม่ถูกบันทึก`,
    Upload_Sales_screenshots_of_Tiktok_showcase_: `ข้อมูลการขายจาก TikTok Showcase`,
    To_facilitate_merchants_to_pass_your_sample_application_as_soon_as_possible__please_upload_a_screenshot_of_TikTOK_showcase_sales_data_: `เพื่อให้ผู้ขายพิจารณาอนุมัติการให้สินค้าตัวอย่างได้ง่ายมากขึ้น กรุณาอัปโหลดภาพหน้าจอข้อมูลการขายจาก TikTok Showcase ของคุณ `,
    Please_ensure_the_authenticity_of_the_pictures__otherwise_your_permission_to_apply_for_samples_may_be_closed_: `โปรดตรวจสอบความถูกต้องของรูปภาพ มิฉะนั้นคำขอของคุณอาจจะไม่ได้รับการพิจารณา`,
    Example: `ตัวอย่าง`,
    Screenshots_Example: `ตัวอย่างภาพหน้าจอ`,
    Please_go_to_the_Tiktok_store_to_capture_the_page_as_below_and_upload_it_: `กรุณาไปที่ TikTok Showcase เพื่อแคปหน้าจอตามตัวอย่างด้านล่างและอัปโหลดรูปภาพ`,
    For_detailed_steps__refer_to_the__instructions_below___: `สามารถดูขั้นตอนการแคปหน้าจอตามด้านล่างนี้ได้เลย`,
    How_to_get: `วิธีแคปหน้าจอข้อมูลการขายจาก TikTok Showcase`,
    Step: `ขั้นตอนที่`,
    Uploaded_time: `เวลาอัปโหลด`,
    Update_sales_of_showcase_screenshot: `อัปเดตภาพหน้าจอข้อมูลการขาย`,
    bdata_notes: `ข้อมูลบางส่วนของคุณ (สถานะบัญชี / ข้อมูลการขาย / สินค้าตัวอย่างที่ได้รับ) จะเปิดเผยแก่ผู้ขาย เมื่อผู้ขายได้รับคำขอสินค้าตัวอย่างจากคุณ`,
    Request_Submitted: `ส่งคำขอเรียบร้อย`,
    Thanks_for_requesting_a_sample_The_seller_will_respond_within_7_days_And_you_can_add_the_product_manually_: `ขอบคุณสำหรับการส่งคำขอสินค้าตัวอย่าง ผู้ขายจะตอบกลับภายใน 7 วัน และคุณสามารถเพิ่มสินค้าได้ด้วยตนเอง `,
    View_details: `ดูรายละเอียด`,
    The_sales_screenshots_of_tiktok_showcase_: `ภาพหน้าจอข้อมูลการขายจาก TikTok Showcase`,
    upload_: `อัปโหลด`,
    update_: `อัปเดต`,
    Request_detail: `รายละเอียด`,
    We_will_finish_the_review_within_7_working_days__please_wait_patiently_: `โปรดรอการพิจารณาคำขอประมาณ 7 วัน`,
    Application_submitted: `ส่งคำขอแล้ว`,
    Application_approved_to_be_shipped_: `คำขอได้รับอนุมัติจากผู้ขาย`,
    Shipped: `กำลังจัดส่งสินค้าตัวอย่าง`,
    Transaction_closed: `คำขอไม่ได้รับการอนุมัติ`,
    Creator_s_account: `บัญชี TikTok`,
    Contact_us: `ติดต่อเรา`,
    Logistics_: `การจัดส่ง`,
    Courier_Company: `จัดส่งโดย`,
    Tracking_number: `หมายเลขติดตามพัสดุ`,
    Go_to_the_logistics_offical_website: `ไปที่เว็บไซต์ของบริษัทขนส่ง`,
    Fans: `ผู้ติดตาม`,
    order: 'เลขที่คำสั่งซื้อ ',
    edit: 'แก้ไข',
    add_address: 'เพิ่มที่อยู่',
    upload_image: 'อัปโหลดรูปภาพ',
    sample_order_management: 'ขอสินค้าตัวอย่างฟรี',
    'x笔订单待上传履约视频/直播，履约后达到出单要求获得':
      'เรารอคุณอัปโหลดวิดีโอหรือไลฟ์โปรโมทสินค้า {{count}} เพื่อรับรางวัลไปเลย',
    '฿投流奖励（先到先得）':
      ' ฿{{reward}} อย่าช้า! ใครอัปก่อนมีสิทธิ์ได้รางวัลก่อน',
    'x笔订单待上传履约视频/直播，履约后达到出单要求可获得额外免费样品数量。':
      'จะต้องอัปโหลดคำสั่งซื้อ {{count}} ไปยังวิดีโอหรือไลฟ์ หากวิดีโอหรือไลฟ์ของคุณตรงตามเงื่อนไข คุณจะได้รับตัวอย่างฟรีเพิ่มเติม',
    '去查看>': 'ตรวจสอบตอนนี้ >',
    投流奖励说明: 'รางวัลยิงโฆษณาฟรี',
    样品奖励说明: 'รางวัลขอรับสินค้าตัวอย่าง',
    发货待履约: `กำลังดำเนินการจัดส่ง`,
    履约审核中: `อยู่ในระหว่างตรวจสอบ`,
    履约失败: `ไม่สำเร็จ`,
    已完成: `เสร็จสิ้น`,
    直播: `LIVE`,
    视频: `Video`,
    投流奖励状态: `สถานะรางวัลของคุณ`,
    未达标: `- ไม่ตรงตามเกณฑ์`,
    直播无投流奖励: `- ไม่มีรางวัลสำหรับการไลฟ์`,
    未上传视频ID: `- รหัสวิดีโอยังไม่อัปโหลด`,
    待发放: `- รอดำเนินการ`,
    已发放: `- เสร็จสิ้น`,
    无投流奖励: `- ไม่มีรางวัล`,
    上传视频链接或直播截图: `อัปโหลดลิงก์วิดีโอหรือแคปหน้าจอไลฟ์สดของคุณ`,
    上传视频ID: 'กรุณาอัปโหลดรหัสวิดีโอ',
    '已发货，进入履约流程': `อยู่ในระหว่างการจัดส่งสินค้า`,
    '我们会在三个工作日内完成审核，请耐心等待': `กรุณารอการตรวจสอบภายใน 3 วันทำการ`,
    履约待审核: `รอการตรวจสอบ`,
    '提交成功 待审核': `ยื่นคำขอเรียบร้อย รอการตรวจสอบ`,
    审核拒绝: `ปฏิเสธคำขอ`,
    '审核被拒绝，请重新您的履约信息': `คำขอถูกปฏิเสธ กรุณากรอกข้อมูลอีกครั้ง`,
    履约成功后可以开放一个样品资格: `คุณจะสามารถยื่นคำขอสินค้าตัวอย่าง หลังจากการตรวจสอบสำเร็จ`,
    请点击下方按钮填写履约信息: `กรุณาคลิกด้านล่างเพื่อกรอกข้อมูล`,
    审核通过: `อนุมัติคำขอ`,
    履约流程: `การดำเนินการ`,
    发货流程: `การจัดส่งสินค้า`,
    填写履约信息: `ข้อมูลการขาย`,
    请上传视频ID: `กรุณาอัปโหลดรหัสวิดีโอ`,
    '选填，请复制视频ID，用于视频投放奖励': `ไม่บังคับ: กรุณาคัดลอกรหัสวิดีโอเพื่อทำการโปรโมท`,
    '如何获得视频ID？': `รับรหัสวิดีโออย่างไร?`,
    确认上传: `ยืนยันการอัปโหลด`,
    '上传后不可更改，确认要继续吗？': `หลังจากอัปโหลดแล้วจะไม่สามารถแก้ไขได้ ยืนยันที่จะดำเนินการต่อหรือไม่?`,
    上一步: `ก่อนหน้า`,
    下一步: `ถัดไป`,
    视频和直播: `วิดีโอและไลฟ์สตรีมมิ่ง`,
    履约方式: `วิธีดำเนินการ`,
    请复制视频链接到此处: `กรุณาคัดลอกลิงก์วิดีโอที่นี่`,
    必填: `ต้องระบุ`,
    选填: `ไม่บังคับ`,
    履约申请: `การขอสินค้าตัวอย่าง`,
    '如何履约？': `วิธีการกรอกข้อมูล`,
    '1、履约完成后，只要您的销售件数超过x件，可以恢复一个申请样品的名额。': `1. หลังจากตรวจสอบสำเร็จ หากยอดขายของคุณเกิน {{count}} ชิ้น คุณสามารถขอรับโควต้าสำหรับการขอสินค้าตัวอย่างอีก 1 ครั้ง`,
    '1、履约完成并审核通过后，可以恢复一个申请样品的名额。':
      '1. หลังจากปฏิบัติตามกฎเสร็จสิ้นแล้ว โควต้าการขอสินค้าตัวอย่างหนึ่งรายการจะเปิดให้กับคุณ',
    '2、您可以选择视频/直播的履约形式。': `2. อัปโหลดวิดีโอหรือไลฟ์`,
    '3、视频履约需要填写视频链接': `3. จำเป็นต้องใส่ลิงก์วิดีโอทุกครั้ง`,
    '4、直播履约需要填写直播截图，我们会为您提供示例图': `4. เพื่อการตรวจสอบที่เร็วยิ่งขึ้น กรุณาอัปโหลดภาพหน้าจอยอดขายจากการไลฟ์ คุณสามารถดูตัวอย่างภาพหน้าจอก่อนอัปโหลดได้`,
    '5、请填写真实的履约信息，我们会进行核实。': `5. โปรดกรอกข้อมูลที่ถูกต้อง `,
    我已知晓: `เข้าใจแล้ว`,
    '1. Go to creator tools in the TikTok profile page': `1. ไปที่เครื่องมือครีเอเตอร์ในหน้าโปรไฟล์ TikTok`,
    _2__Turn_on__Ad_settings__: `2. เปิด "การตั้งค่าโฆษณา"`,
    '3. Select the TikTok post you want to create the ad with, and go to the ad settings page.': `3. เลือกโพสต์ TikTok ที่คุณต้องการใช้สร้างโฆษณา และไปที่หน้าการตั้งค่าโฆษณา`,
    _4__Turn_on_the__Ad_authorization__and_generate_a_video_code__Please_select_the_longest_period_of_validity_for_the_code__: `4. เปิด "การอนุญาตโฆษณา" และสร้างรหัสวิดีโอ ({{replaceme}})`,
    'Please select the longest period of validity for the code.': `โปรดเลือกระยะเวลาที่โค้ดมีผลใช้งานได้นานที่สุด`,
    '您未达到投流奖励要求，请继续加油哦！': `คุณยังไม่ตรงเงื่อนไขที่จะได้{{replaceme}} สู้อีกนิดนะ!`,
    'video promote reward': '{{link}} ฿{{amount}}',
    'video promote reward 文字部分': 'รับรางวัล',
    '您已达到投流奖励要求，投流视频ID：，请耐心等待运营人员联系商家为您投流': `วิดีโอโปรโมทของคุณตรงตามเงื่อนไข ได้รับไปเลย ฿{{amount}} รหัสวิดีโอ: {{videoId}} โปรดรอทีมงานติดต่อเพื่อดำเนินการขั้นต่อไป`,
    '恭喜达到出单要求！投流奖励已发放给视频ID：': `ยินดีด้วย! ยอดคำสั่งซื้อตรงตามเงื่อนไข ได้รับรางวัลไปเลย ฿{{amount}} นี่คือรหัสวิดีโอของคุณ: {{videoId}}`,
    '已完成，检测到您已销售超过件，本样品名额已释放': `เราได้ตรวจสอบว่ายอดขายของคุณมากกว่า {{count}} รายการ คุณได้รับโควต้าสำหรับการขอสินค้าตัวอย่างอีก 1 ครั้ง`,
    '达人收到样品并发布履约视频/直播后，一个月内，达成以下要求，即可获得฿视频投流奖励。': `หลังจากที่รับสินค้าตัวอย่างและโพสต์วิดีโอหรือไลฟ์แล้ว หากคุณตรงตามเงื่อนไขที่กำหนดภายในหนึ่งเดือน <b>คุณจะได้รับรางวัล {{amount}}</b>`,
    '฿视频投流奖励。': `คุณจะได้รับรางวัล ฿{{amount}}`,
    商品出单量: `คำสั่งซื้อของสินค้า`,
    GMV: `GMV ของสินค้า`,
    视频播放量: `จำนวนรับชม`,
    数量: `จำนวน`,
    最多申请件: `ไม่เกิน {{count}}`,
    '收到样品且上传发布的视频/直播后，样品商品的订单销量＞单时，您的账号将恢复1个免费样品额度。': `หลังจากได้รับสินค้าตัวอย่างแล้ว และได้มีการโพสต์วิดีโอหรือไลฟ์ หากสินค้าตัวอย่างนี้มีคำสั่งซื้อมากกว่า {{count}} ชิ้น ได้รับโควต้าขอสินตัวอย่างฟรีเพิ่มไปเลย！`,
    '商品出单数≥': `- คำสั่งซื้อ ≥ {{count}}`,
    '商品GMV≥฿': `- GMV ≥ ฿{{amount}}`,
    '视频播放量≥': `- จำนวนรับชมวิดีโอ ≥ {{count}}`,
    '已完成，销售本商品达到件，就可以释放本样品名额': `เราได้ตรวจสอบว่ายอดขายของสินค้ามากกว่า {{count}} ชิ้น คุณได้รับโควต้าสำหรับการขอสินค้าตัวอย่างอีก 1 ครั้ง`,
    '尽快提交视频/直播履约信息，达到出单要求获得中投流奖励（先到先得）': `เพียงแค่คุณส่งวิดีโอหรือไลฟ์ หากตรงตามเงื่อนไข รับไปเลย ฿{{amount}} รางวัลมีจำนวนจำกัด ใครส่งก่อนได้ก่อนเลย!`,
    '尽快提交视频/直播履约信息，达到出单要求可获得额外免费样品数量': `เพียงแค่คุณส่งวิดีโอหรือไลฟ์  รับไปเลยตัวอย่างฟรีเพิ่มหากตรงตามเงื่อนไข`,
    '达到出单要求，获得฿投流奖励，先到先得': `หากตรงตามเงื่อนไข! รับไปเลย ฿{{amount}} สำหรับการโปรโมท มาก่อน มีสิทธิ์ก่อน`,
    该订单已上传视频ID: 'คำสั่งซื้ออยู่ในรหัสวิดีโอ',
    'Promote Video ID': 'รหัสโปรโมทวิดีโอ',
    提示: 'เคล็ดลับ',
    '该tiktok账号仅能同时申请x个样品，请及时将历史申请履约，否则无法申请新的样品查看《样品申请规则》': `บัญชี TikTok นี้สามารถขอสินค้าตัวอย่างได้จำนวน {{count}} ชิ้นต่อหนึ่งครั้ง โปรด{{履约}} มิเช่นนั้นจะไม่สามารถขอสินค้าตัวอย่างเพิ่มได้ ดู {{样品申请规则}}`,
    履约: `ปฏิบัติตามกฎ`,
    样品申请规则: 'กฎการขอสินค้าตัวอย่าง',
    '申请条件: 该商品要求达人粉丝数≥；点击查看《样品申请规则》': `เงื่อนไข: ในการขอสินค้าชิ้นนี้ ครีเอเตอร์ต้องมีผู้ติดตาม ≥ {{count}}；คลิกเพื่อดู{{样品申请规则}}`,
    待平台审核: `รอการอนุมัติจากแพลตฟอร์ม`,
    待商家审核: `รอการอนุมัติจากร้านค้า`,
    '平台审核通过，待商家审核': `ได้รับการอนุมัติจากแพลตฟร์มแล้ว กำลังรอการอนุมัติจากร้านค้า`,
    '商家审核通过，待发货': `ได้รับการอนุมัติจากร้านค้าแล้ว กำลังรอการจัดส่ง`,
    '注意：直播履约需要在直播时进行截圈，点击查看案例': `หมายเหตุ: เพื่อการดำเนินการ กรุณาแคปหน้าจอขณะไลฟ์สด {{点击查看案例}}`,
    点击查看案例: `คลิกเพื่อดูตัวอย่าง`,
    可履约方式: `วิธีการปฏิบัติ`,
    达人免费领养次数说明: `คำอธิบายที่ครีเอเตอร์ได้อบรมฟรี`,
    序号: `หมายเลข`,
    最近一个月橱窗销售额要求: `ยอดขายในเดือนล่าสุดจากแท็บการแสดงสินค้า`,
    可同时免费领取样品数量: `จำนวนสินค้าตัวอย่างที่สามารถรับได้ฟรีได้ในเวลาเดียวกัน`,
    '注意:': `หมายเหตุ:`,
    '1.达人领取样品后，完成履约并审核通过，可以恢复一次达人免费领样次数。': `1.หลังจากที่ครีเอเตอร์ได้รับสินค้าตัวอย่างแล้วและปฏิบัติตามกฎเสร็จสิ้นแล้วจะได้รับโควต้าในการรับสินค้าตัวอย่างฟรี `,
    '2.部分商品领取样品要求以商家实际要求为准。': `จำนวนสินค้าตัวอย่างที่ร้านค้าขอมาต้องยึดตามความเป็นจริง`,
    销售额范围: `ช่วงยอดขาย`,
    '请上传TikTOK店铺橱窗销售数据截图，以方便商家尽快通过您的样品申请': `กรุณาแนบไฟล์ยอดขายย้อนหลัง1เดือน`,
    '您上传的橱窗销售截图已过期，请重新上传': `รูปยอดขายเกิน30วันแล้ว กรุณาอัปเดตรูปยอดขายย้อนหลัง1เดือนอีกครั้ง`,
    请选择您最近一个月的橱窗销量额范围: `โปรดเลือกช่วงเวลายอดขายในเดือนที่แล้ว`,
    及以上: ' ขึ้นไป',
    总销售额: `ยอดขายทั้งหมด`,
    'Please upload/update showcase sales screenshots for sample approval.':
      'โปรดอัปโหลด/อัปเดตภาพหน้าจอหน้าต่างการขายสินค้าเพื่อการอนุมัติการขอสินค้าตัวอย่าง',
    提交时间: 'เวลาจัดส่ง',
    商家补发: `ร้านค้าส่งสินค้าเพิ่มเติม`,
    upload: 'นำเข้ารูปภาพ',
    新人免费样品X1: 'สินค้าตัวอย่างฟรีสำหรับผู้ใช้ใหม่ X1',
    即将失效: 'หมดอายุเร็วๆนี้',
    'Free samples': 'สินค้าตัวอย่างฟรี',
    免费样品额度: 'โควต้าสินค้าตัวอย่าง',
    升级免费样品额度: 'อัปเกรดโควตาสินค้าตัวอย่าง',
    及时履约: 'ดำเนินการอย่างรวดเร็ว',
    '需要您在拿到样品后及时履约，否则免费样品额度将不再被返还。':
      'คุณต้องเริ่มดำเนินการทันทีหลังจากได้รับสินค้าตัวอย่าง มิฉะนั้นจะไม่ได้รับโควตาสินค้าตัวอย่างกลับคืน',
    提升GMV: 'เพิ่ม GMV',
    '通过提升自身的内容销售能力，可以根据Youpik uChoice的样品规则，获得更多免费样品资格！':
      'คุณจะได้สิทธิ์ในการรับสินค้าตัวอย่างมากขึ้นตามเกณฑ์ของ Youpik uChoice! เพียงแค่เพิ่มประสิทธิภาพให้กับคอนเทนต์การขายของคุณ',
    'Report level problem upload proof': 'Report level problem & upload proof',
    当前等级: 'ระดับปัจจุบัน',
    '30天成交件数': 'จำนวนการซื้อขาย 30 วันที่ผ่านมา',
    提升成交件数: 'พัฒนา จำนวนการซื้อขาย',
    '❗️出单送申样机会+出单教程❗️': 'รับสินค้าตัวอย่าง + คอสสอนขาย',
    首次购样流程: 'ขั้นตอนการซื้อสินค้าตัวอย่างครั้งแรก',
    '点击可查看购样教程，如不需要，点击【跳过】按钮即可。':
      'คลิกเพื่อดูวิธีการซื้อตัวอย่าง หรือคลิก "สั่งซื้อต่อ" หากไม่ต้องการ',
    继续购样: 'สั่งซื้อต่อ',
    '查看购样流程>': 'วิธีสั่งซื้อ >',
    '挂橱窗，解锁出单教程': 'เพิ่ม showcase, ปลดล็อคดูบทเรียนฟรี',
    '平台赠送您该商品的出单教程，助您内容销量的增长！请先将商品添加橱窗~':
      '<main>บนแอปมี<button>วิดีโอตัวอย่าง</button>สินค้านี้เพื่อช่วยเพิ่มยอดขาย โปรด<red>เพิ่มสินค้าลงใน showcase ก่อน</red></main>',
    '添加橱窗>': 'เพิ่ม showcase >',
    已解锁回本教程: 'สามารถดูวิดีโอตัวอย่างได้แล้ว',
    '平台赠送您该商品的出单教程，助您内容销量的增长！可前往“我的”页面查看~':
      'บนแอปมี<span style="color: #FF0032;">วิดีโอตัวอย่าง</span>สินค้านี้เพื่อช่วยเพิ่มยอดขาย! สามารถดูได้ในหน้า "ของฉัน"',
    '回本教程>': 'วิดีโอตัวอย่าง >',
    '请认准【YOUPIK】高佣链接':
      'โปรดตรวจสอบว่าลิงก์มีคำว่า "YOUPIK" ค่าคอมมิชชันสูง',
    'DOs弹窗处点击add showcase': 'DOs: คลิกเพิ่ม showcase ในหน้าป๊อปอัพ',
    'DONTs弹窗处点击view more后add showcase':
      'DONTs: คลิกดูเพิ่มเติมหลังจากนั้นแล้วคลิกเพิ่ม showcase',
    成本购样: 'ซื้อสินค้าตัวอย่าง',
    添加橱窗: 'เพิ่ม showcase',
    解锁教程: 'ดูคลิปตัวอย่าง',
    模仿带货: 'ทำคอนเทนต์ตาม',
    轻松出单: 'ยอดขายเพิ่มขึ้น',
    免费样品: `รับสินค้าตัวอย่างฟรี！`,
    流程详情: 'รายละเอียดขั้นตอน',
    购样流程: 'วิธีการซื้อตัวอย่าง'
  },
  vi: {
    submitTips:
      'Một số dữ liệu kinh doanh của bạn (hoạt động / doanh số / hoàn thành mẫu) sẽ được hiển thị cho người bán khi họ nhận được yêu cầu mẫu từ bạn.',
    RequestSubmitted: 'Yêu cầu đã được gửi',
    requestSubTips:
      'Cảm ơn bạn đã yêu cầu mẫu. Người bán sẽ phản hồi trong vòng 7 ngày. Và bạn có thể thêm sản phẩm vào danh sách của mình.',
    ViewDetails: 'Xem chi tiết',
    Sample_Request: 'Yêu cầu mẫu',
    Pending_Review: 'Đang chờ xem xét',
    To_be_shipped: 'Chờ giao hàng',
    To_be_received: 'Chờ nhận',
    Closed: 'Đã đóng',
    You_Currently_have_no_Orders: 'Không có đơn hàng ở đây.',
    Request_Sample: 'Yêu cầu mẫu',
    Request_Time: 'Thời gian yêu cầu',
    Track_delivery: 'Theo dõi giao hàng',
    Sample: 'Mẫu',
    Please_authorize_tiktok_account: 'Vui lòng ủy quyền tài khoản TikTok',
    conditions: `Điều kiện：
    1. Sản phẩm này yêu cầu số lượng người hâm mộ của người sáng tạo ≥ #;
    2. Lượt xem video trung bình ≥ #;
    3. Doanh thu GMV trong tháng qua ≥ #;`,
    Select_the_creator: 'Chọn người sáng tạo',
    no_qualified_creator_account: 'Không có tài khoản đủ điều kiện',
    Please_select_creator: 'Vui lòng chọn người sáng tạo',
    You_can_request_up_to_x_samples: 'Bạn có thể yêu cầu tối đa x mẫu',
    If_the_sample_order_of_the_application_is___x__one_application_quota_will_be_restored_:
      'Nếu đơn hàng mẫu của ứng dụng ≥ x, một chỉ tiêu ứng dụng sẽ được phục hồi;',
    got_it: 'Đã hiểu',
    After_receiving_the_free_sample_I_agree_to_post_a_video_or_LIVE_video_that_contains_the_product_link_I_ll_Keep_the_video_public_for_at_least_3_days_or_keep_the_product_link_in_my_LIVE_video_for_at_least_10_minutes_:
      'Sau khi nhận được mẫu miễn phí, tôi đồng ý đăng một video hoặc video LIVE có liên kết sản phẩm. Tôi sẽ giữ video công khai ít nhất 3 ngày, hoặc giữ liên kết sản phẩm trong video LIVE của tôi ít nhất 10 phút.',
    Delivery_Information: 'Thông tin giao hàng',
    Please_fill_in_your_Line_ID_so_that_we_can_contact_you:
      'Vui lòng điền ID Line của bạn để chúng tôi có thể liên lạc với bạn',
    Enter_Line_ID: 'Nhập ID Line',
    Quit_process: 'Thoát quá trình',
    Your_information_will_not_be_saved: 'Thông tin của bạn sẽ không được lưu',
    Upload_Sales_screenshots_of_Tiktok_showcase_:
      'Tải lên ảnh chụp màn hình doanh số của TikTok showcase',
    To_facilitate_merchants_to_pass_your_sample_application_as_soon_as_possible__please_upload_a_screenshot_of_TikTOK_showcase_sales_data_:
      'Để giúp các nhà bán hàng thông qua đơn xin mẫu của bạn càng sớm càng tốt, vui lòng tải lên ảnh chụp màn hình dữ liệu doanh số TikTok showcase;',
    Please_ensure_the_authenticity_of_the_pictures__otherwise_your_permission_to_apply_for_samples_may_be_closed_:
      'Vui lòng đảm bảo tính xác thực của các bức ảnh, nếu không quyền xin mẫu của bạn có thể bị đóng;',
    Example: 'Ví dụ',
    Screenshots_Example: 'Ví dụ về ảnh chụp màn hình',
    Please_go_to_the_Tiktok_store_to_capture_the_page_as_below_and_upload_it_:
      'Vui lòng truy cập cửa hàng TikTok để chụp ảnh trang như bên dưới và tải lên;',
    For_detailed_steps__refer_to_the__instructions_below___:
      'Để biết các bước chi tiết, tham khảo hướng dẫn bên dưới ↓;',
    How_to_get: 'Cách lấy',
    Step: 'Bước',
    Uploaded_time: 'Thời gian tải lên',
    Update_sales_of_showcase_screenshot:
      'Cập nhật ảnh chụp màn hình doanh số của showcase',
    bdata_notes:
      'Một số dữ liệu kinh doanh của bạn (hoạt động / doanh số / hoàn thành mẫu) sẽ được hiển thị cho người bán khi họ nhận được yêu cầu mẫu từ bạn.',
    Request_Submitted: 'Yêu cầu đã được gửi',
    Thanks_for_requesting_a_sample_The_seller_will_respond_within_7_days_And_you_can_add_the_product_manually_:
      'Cảm ơn bạn đã yêu cầu mẫu. Người bán sẽ phản hồi trong vòng 7 ngày. Và bạn có thể thêm sản phẩm vào danh sách của mình.',
    View_details: 'Xem chi tiết',
    The_sales_screenshots_of_tiktok_showcase_:
      'Ảnh chụp màn hình doanh số của TikTok showcase',
    upload_: 'tải lên',
    update_: 'cập nhật',
    Request_detail: 'Chi tiết yêu cầu',
    We_will_finish_the_review_within_7_working_days__please_wait_patiently_:
      'Chúng tôi sẽ hoàn tất việc xem xét trong vòng 7 ngày làm việc, vui lòng chờ đợi.',
    Application_submitted: 'Đơn đã được gửi',
    Application_approved_to_be_shipped_: 'Đơn đã được phê duyệt, chờ giao hàng',
    Shipped: 'Đã giao hàng',
    Transaction_closed: 'Giao dịch đã đóng',
    Creator_s_account: 'Tài khoản người sáng tạo',
    Contact_us: 'Liên hệ với chúng tôi',
    Logistics_: 'Giao hàng',
    Courier_Company: 'Công ty vận chuyển',
    Tracking_number: 'Số theo dõi',
    Go_to_the_logistics_offical_website:
      'Truy cập trang web chính thức của dịch vụ giao hàng',
    Fans: 'Người hâm mộ',
    order: 'Đơn hàng:',
    edit: 'Chỉnh sửa',
    add_address: 'Thêm địa chỉ',
    upload_image: 'Tải lên hình ảnh',
    sample_order_management: 'Quản lý đơn hàng mẫu',
    'x笔订单待上传履约视频/直播，履约后达到出单要求获得':
      '{{count}} đơn hàng chờ tải lên video/LIVE để hoàn thành, sau khi hoàn thành yêu cầu đơn hàng',
    '฿投流奖励（先到先得）':
      ' đ{{reward}} phần thưởng quảng bá video (hoàn thành trước nhận trước)',
    'x笔订单待上传履约视频/直播，履约后达到出单要求可获得额外免费样品数量。':
      '{{count}} đơn hàng chờ tải lên video/LIVE để hoàn thành. Sau khi hoàn thành, bạn có thể nhận thêm số lượng mẫu miễn phí.',
    '去查看>': 'Xem ngay >',
    投流奖励说明: 'Giải thưởng quảng bá video',
    样品奖励说明: 'Giải thưởng mẫu thử',
    发货待履约: 'Chờ hoàn thành',
    履约审核中: 'Đang xem xét hoàn thành',
    履约失败: 'Hoàn thành thất bại',
    已完成: 'Đã hoàn thành',
    直播: 'LIVE',
    视频: 'Video',
    投流奖励状态: 'Trạng thái giải thưởng',
    未达标: 'Chưa đạt yêu cầu',
    直播无投流奖励: 'Không có giải thưởng cho LIVE',
    未上传视频ID: 'Video ID chưa tải lên',
    待发放: 'Chờ phát hành',
    已发放: 'Đã phát hành',
    无投流奖励: 'Không có giải thưởng',
    上传视频链接或直播截图:
      'Tải lên liên kết video hoặc ảnh chụp màn hình LIVE',
    上传视频ID: 'Tải lên VIDEO Code',
    '已发货，进入履约流程': 'Đã giao hàng, vào quy trình hoàn thành',
    '我们会在三个工作日内完成审核，请耐心等待':
      'Chúng tôi sẽ hoàn tất việc kiểm tra trong vòng 3 ngày làm việc, vui lòng kiên nhẫn chờ đợi.',
    履约待审核: 'Hoàn thành chờ xác nhận',
    '提交成功 待审核': 'Gửi thành công, chờ kiểm tra',
    审核拒绝: 'Xét duyệt bị từ chối',
    '审核被拒绝，请重新您的履约信息':
      'Việc xét duyệt bị từ chối, vui lòng nhập lại thông tin hoàn thành của bạn.',
    履约成功后可以开放一个样品资格:
      'Sau khi hoàn thành, bạn có thể mở một chỉ tiêu mẫu.',
    请点击下方按钮填写履约信息:
      'Vui lòng nhấp vào nút bên dưới để điền thông tin hoàn thành',
    审核通过: 'Kiểm tra được phê duyệt',
    履约流程: 'Quá trình hoàn thành',
    发货流程: 'Quá trình giao hàng',
    填写履约信息: 'Điền thông tin hoàn thành',
    请上传视频ID: 'Vui lòng tải lên VIDEO Code',
    '选填，请复制视频ID，用于视频投放奖励':
      'Tùy chọn, copy ads code để nhận thưởng quảng cáo video',
    '如何获得视频ID？': 'Làm thế nào để lấy ID video?',
    确认上传: 'Xác nhận tải lên',
    '上传后不可更改，确认要继续吗？':
      'Sau khi tải lên, không thể thay đổi. Bạn có chắc chắn muốn tiếp tục không?',
    上一步: 'Quay lại',
    下一步: 'Tiếp theo',
    视频和直播: 'Video & Livestream',
    履约方式: 'Cách hoàn thành',
    请复制视频链接到此处: 'Vui lòng sao chép liên kết video vào đây',
    必填: 'Bắt buộc',
    选填: 'Tùy chọn',
    履约申请: 'Đăng ký hoàn thành',
    '如何履约？': 'Làm thế nào để hoàn thành?',
    '1、履约完成后，只要您的销售件数超过x件，可以恢复一个申请样品的名额。':
      '1. Sau khi hoàn thành, nếu số lượng bán hàng của bạn vượt quá {{count}} sản phẩm, bạn có thể khôi phục một chỉ tiêu mẫu.',
    '1、履约完成并审核通过后，可以恢复一个申请样品的名额。':
      '1. Sau khi hoàn thành và được phê duyệt, bạn có thể mở lại một chỉ tiêu mẫu.',
    '2、您可以选择视频/直播的履约形式。':
      '2. Bạn có thể chọn hình thức hoàn thành bằng video/ livestream.',
    '3、视频履约需要填写视频链接':
      '3. Hoàn thành bằng video yêu cầu phải điền liên kết video.',
    '4、直播履约需要填写直播截图，我们会为您提供示例图':
      '4. Hoàn thành bằng livestream yêu cầu phải tải lên ảnh chụp màn hình livestream, và chúng tôi sẽ cung cấp hình mẫu cho bạn.',
    '5、请填写真实的履约信息，我们会进行核实。':
      '5. Vui lòng điền thông tin hoàn thành chính xác và chúng tôi sẽ kiểm tra.',
    我已知晓: 'Tôi đã hiểu',
    '1. Go to creator tools in the TikTok profile page':
      '1. Vào công cụ tạo nội dung trên trang hồ sơ TikTok',
    '2. Turn on "Ad settings."': '2. Bật "Cài đặt quảng cáo."',
    '3. Select the TikTok post you want to create the ad with, and go to the ad settings page.':
      '3. Chọn bài đăng TikTok mà bạn muốn tạo quảng cáo và vào trang cài đặt quảng cáo.',
    '4. Turn on the "Ad authorization" and generate a video code.（{{replaceme}}）':
      '4. Bật "Ủy quyền quảng cáo" và tạo mã video.（{{replaceme}}）',
    'Please select the longest period of validity for the code.':
      'Vui lòng chọn thời gian hiệu lực dài nhất cho mã.',
    '您未达到投流奖励要求，请继续加油哦！':
      'Bạn chưa đạt yêu cầu để nhận thưởng quảng cáo video. Cố gắng hơn nhé!',
    'video promote reward': 'đ{{amount}} {{link}}',
    'video promote reward 文字部分': 'thưởng quảng cáo video',
    '您已达到投流奖励要求，投流视频ID：，请耐心等待运营人员联系商家为您投流':
      'Bạn đã đạt yêu cầu nhận thưởng quảng cáo video. Mã video quảng cáo: {{videoId}}. Vui lòng chờ đội ngũ vận hành liên hệ với nhà cung cấp để thực hiện quảng cáo cho bạn.',
    '恭喜达到出单要求！投流奖励已发放给视频ID：':
      'Chúc mừng bạn đã đạt yêu cầu đặt hàng! Thưởng quảng cáo video đ{{amount}} đã được cấp cho Video ID: {{videoId}}.',
    '已完成，检测到您已销售超过件，本样品名额已释放':
      'Hoàn tất. Chúng tôi đã biết bạn đã bán được hơn {{count}} sản phẩm. Chỉ tiêu mẫu này đã được giải phóng.',
    '达人收到样品并发布履约视频/直播后，一个月内，达成以下要求，即可获得đ视频投流奖励。':
      'Sau khi bạn nhận được mẫu và đăng video/livestream hoàn thành, đạt yêu cầu trong vòng một tháng sẽ nhận được phần thưởng quảng cáo video {{amount}}.',
    'đ视频投流奖励。': 'đ{{amount}} phần thưởng quảng cáo video',
    商品出单量: 'Số lượng đơn hàng sản phẩm',
    GMV: 'GMV của sản phẩm',
    视频播放量: 'Lượt xem video',
    数量: 'Số lượng',
    最多申请件: 'Tối đa {{count}} sản phẩm',
    '收到样品且上传发布的视频/直播后，样品商品的订单销量＞单时，您的账号将恢复1个免费样品额度。':
      'Sau khi nhận mẫu và tải lên Video/LIVESTREAM, khi doanh số đơn hàng sản phẩm mẫu >{{count}}, tài khoản của bạn sẽ được cấp lại 1 chỉ tiêu mẫu miễn phí.',
    '商品出单数≥': 'Số đơn hàng sản phẩm ≥ {{count}}',
    '商品GMV≥đ': 'GMV của sản phẩm ≥ đ{{amount}}',
    '视频播放量≥': 'Lượt xem video ≥ {{count}}',
    '已完成，销售本商品达到件，就可以释放本样品名额':
      'Hoàn tất, nếu doanh số bán sản phẩm này đạt {{count}} sản phẩm, chỉ tiêu mẫu này có thể được giải phóng.',
    '尽快提交视频/直播履约信息，达到出单要求获得中投流奖励（先到先得）':
      'Vui lòng gửi thông tin hoàn thành Video/LIVESTREAM càng sớm càng tốt để đạt yêu cầu đặt hàng và nhận phần thưởng quảng cáo video đ{{amount}}.',
    '尽快提交视频/直播履约信息，达到出单要求可获得额外免费样品数量':
      'Vui lòng gửi thông tin hoàn thành Video/LIVESTREAM càng sớm càng tốt để nhận thêm số lượng mẫu miễn phí.',
    '达到出单要求，获得đ投流奖励，先到先得':
      'Đạt yêu cầu đặt hàng, nhận thưởng quảng cáo video đ{{amount}}.',
    该订单已上传视频ID: 'Đơn hàng này đã tải lên mã video quảng cáo',
    'Promote Video ID': 'Mã video quảng cáo',
    提示: 'nhắc nhở',
    '该tiktok账号仅能同时申请x个样品，请及时将历史申请履约，否则无法申请新的样品查看《样品申请规则》':
      'Tài khoản TikTok này chỉ có thể cùng lúc yêu cầu {{count}} mẫu. Vui lòng hoàn thành các yêu cầu mẫu trước đó kịp thời, nếu không bạn sẽ không thể yêu cầu mẫu mới. Xem {{样品申请规则}}.',
    履约: 'Hoàn thành',
    样品申请规则: 'Quy tắc xin mẫu',
    '申请条件: 该商品要求达人粉丝数≥；点击查看《样品申请规则》':
      'Điều kiện đăng ký: Sản phẩm này yêu cầu số lượng người theo dõi của người tạo nội dung ≥ {{count}}; nhấp để xem {{样品申请规则}}.',
    待平台审核: 'Chờ nền tảng xem xét',
    待商家审核: 'Chờ người bán xem xét',
    '平台审核通过，待商家审核': 'Nền tảng đã phê duyệt, chờ người bán xem xét',
    '商家审核通过，待发货': 'Người bán đã phê duyệt, chờ giao hàng',
    '注意：直播履约需要在直播时进行截圈，点击查看案例':
      'Lưu ý: Hoàn thành livestream yêu cầu chụp ảnh màn hình trong khi livestream. {{点击查看案例}}.',
    点击查看案例: 'Nhấp để xem trường hợp',
    可履约方式: 'Cách hoàn thành',
    达人免费领养次数说明:
      'Giải thích số lần người tạo nội dung có thể nhận mẫu miễn phí',
    序号: 'Số thứ tự',
    最近一个月橱窗销售额要求: 'Yêu cầu doanh thu cửa sổ trong tháng qua',
    可同时免费领取样品数量: 'Số lượng mẫu miễn phí có thể nhận cùng lúc',
    '注意:': 'Lưu ý:',
    '1.达人领取样品后，完成履约并审核通过，可以恢复一次达人免费领样次数。':
      '1. Sau khi người tạo nội dung nhận mẫu và hoàn thành, nếu được phê duyệt, có thể khôi phục một lần nhận mẫu miễn phí.',
    '2.部分商品领取样品要求以商家实际要求为准。':
      '2. Một số sản phẩm yêu cầu nhận mẫu theo yêu cầu thực tế của người bán.',
    销售额范围: 'Phạm vi doanh thu',
    '请上传TikTOK店铺橱窗销售数据截图，以方便商家尽快通过您的样品申请':
      'Vui lòng tải lên ảnh chụp màn hình doanh thu cửa sổ TikTok trong 1 tháng để nhà cung cấp nhanh chóng phê duyệt đơn xin mẫu của bạn.',
    '您上传的橱窗销售截图已过期，请重新上传':
      'Ảnh chụp màn hình doanh thu cửa sổ bạn tải lên đã quá hạn 30 ngày, vui lòng tải lại.',
    请选择您最近一个月的橱窗销量额范围:
      'Chọn phạm vi doanh thu cửa sổ của bạn trong tháng qua.',
    及以上: ' và trên',
    总销售额: 'Tổng doanh thu',
    'Please upload/update showcase sales screenshots for sample approval.':
      'Vui lòng tải lên/cập nhật ảnh chụp màn hình doanh thu cửa sổ để phê duyệt mẫu.',
    提交时间: 'Thời gian gửi',
    商家补发: 'Người bán gửi lại',
    upload: 'tải lên',
    新人免费样品X1: 'Mẫu miễn phí cho người mới X1',
    即将失效: 'Sắp hết hạn',
    'Free samples': 'Mẫu miễn phí',
    免费样品额度: 'Hạn yêu cầu mẫu miễn phí',
    升级免费样品额度: 'Nâng cấp hạn mẫu miễn phí',
    及时履约: 'Hoàn thành kịp thời',
    '需要您在拿到样品后及时履约，否则免费样品额度将不再被返还。':
      'Bạn cần hoàn thành mẫu kịp thời sau khi nhận, nếu không hạn ngạch mẫu miễn phí sẽ không được hoàn lạ.',
    提升GMV: 'Tăng GMV',
    '通过提升自身的内容销售能力，可以根据Youpik uChoice的样品规则，获得更多免费样品资格！':
      'Bằng cách cải thiện khả năng bán hàng của bạn, bạn có thể nhận được nhiều mẫu miễn phí hơn theo quy tắc mẫu của Youpik uChoice!',
    'Report level problem upload proof': 'Report level problem & upload proof',
    当前等级: 'Cấp độ hiện tại',
    '30天成交件数': 'Số giao dịch trong 30 ngày',
    提升成交件数: 'Cải thiện Số giao dịch',
    '❗️出单送申样机会+出单教程❗️': 'nhận mẫu miễn phí + hướng dẫn bán hàng',
    首次购样流程: 'Quy trình mua mẫu lần đầu tiên',
    '点击可查看购样教程，如不需要，点击【跳过】按钮即可。':
      'Nhấp vào để xem hướng dẫn mua mẫu, hoặc nhấp "Tiếp tục" nếu bạn không cần.',
    继续购样: 'mua mẫu >',
    '查看购样流程>': 'Xem quy trình mua mẫu >',
    '挂橱窗，解锁出单教程': 'Thêm showcase, mở khóa hướng dẫn bán hàng',
    '平台赠送您该商品的出单教程，助您内容销量的增长！请先将商品添加橱窗~':
      '<main>Nền tảng cung cấp <button>hướng dẫn bán hàng</button> cho sản phẩm này để giúp bạn tăng doanh thu. Vui lòng <red>thêm sản phẩm vào showcase trước</red></main>.',
    '添加橱窗>': 'Thêm >',
    已解锁回本教程: 'Đã mở khóa hướng dẫn bán hàng',
    '平台赠送您该商品的出单教程，助您内容销量的增长！可前往“我的”页面查看~':
      'Nền tảng cung cấp <span style="color: #FF0032;">hướng dẫn bán hàng</span> cho sản phẩm này để giúp bạn tăng doanh thu! Có thể xem tại trang "Của tôi".',
    '回本教程>': 'Hướng dẫn bán hàng >',
    '请认准【YOUPIK】高佣链接':
      'Vui lòng đảm bảo liên kết có chứa từ "YOUPIK" chứa hoa hồng cao.',
    'DOs弹窗处点击add showcase':
      'DOs: Nhấp vào thêm showcase trong cửa sổ bật lên',
    'DONTs弹窗处点击view more后add showcase':
      'DONTs: Sau khi nhấp vào xem thêm, nhấp vào thêm showcase',
    成本购样: 'Mua mẫu',
    添加橱窗: 'Thêm showcase',
    解锁教程: 'Mở khóa hướng dẫn',
    模仿带货: 'Bán hàng mẫu',
    轻松出单: 'Dễ dàng ra đơn',
    免费样品: `Mẫu miễn phí`,
    流程详情: 'Chi tiết quy trình',
    购样流程: 'Quy trình mua mẫu'
  }
}
