import { metadataTemplate, inApp } from '@/lib/server/utils'
import { Inner } from './inner'
import { i18nS } from '@/lib/server/i18n'

export async function generateMetadata() {
  const title = i18nS.t('Activity.BYD汽车活动商品')

  if (inApp()) {
    return metadataTemplate({
      title,
      showNativeNavBar: true,
      showNativeNavBarContactButton: true
    })
  }

  return metadataTemplate({ title })
}

export default async function Index() {
  return <Inner />
}
