import { Image } from '@/components/Image'
import { YoupikHighCom } from '@/components/YoupikHighCom'
import { resizeImageUrl } from '@/lib/client/utils'
import { formatPrice, formatSales } from '@/lib/format'
import React, { useEffect, useState } from 'react'
import { Trans } from 'react-i18next'
import rank_item_cell from '@/../public/images/ranking/rank_item_cell.png'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { addToShowCase, checkAuth } from '@/lib/actions'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { isLanguageEN, isProd } from '@/lib/utils'
import { info } from '@/app/api/api-uchoice/tt/item/info/request'
import { Skeleton } from 'antd-mobile'

export const ItemCell = () => {
  const router = useRouter()
  const [item, setItem] = useState<ItemInfoDto>()

  useEffect(() => {
    fetchItemInfo()
  }, [])

  const fetchItemInfo = async () => {
    const { code, result } = await info({
      id: '276596387414046'
    })
    if (code === 200) {
      setItem(result)
    }
  }

  const toPdp = () => {
    router.push(`/product/${item!.id}`)
  }

  const onAddToShowCase = async () => {
    statistic({
      eventName: EventName.ranking_add_to_showcase,
      param: {
        id: item?.id
      }
    })
    checkAuth(() => addToShowCase(item!, 'goojodoq'))
  }

  const onRequestSample = e => {
    e.stopPropagation()

    statistic({
      eventName: EventName.ranking_sample_apply,
      param: {
        id: item!.id
      }
    })

    router.push(`/product/${item!.id}`)
  }

  return item ? (
    <div className="mt-[24px] rounded-[16px] bg-white p-[20px]" onClick={toPdp}>
      <div className="mb-[24px] flex">
        <img
          src={resizeImageUrl(item.homeImgUrl, 212)}
          className="size-[212px] rounded-[8px]"
        ></img>
        <div className="flex flex-1 flex-col overflow-hidden pl-[20px]">
          <div className="">
            <div className="line-clamp-2 overflow-hidden text-[28px] text-black">
              {isLanguageEN() ? item.nameEn : item.name}
            </div>

            <div className="mt-[8px]">
              <YoupikHighCom
                commission={`${item.commissionRate}`}
              ></YoupikHighCom>
            </div>
          </div>

          <span className="mt-[18px] text-[24px] font-bold text-black">
            {item.minPrice === item.maxPrice
              ? `${formatPrice(item.minPrice, true)}`
              : `${formatPrice(item.minPrice, true)} ~ ${formatPrice(
                  item.maxPrice,
                  true
                )}`}
          </span>
        </div>
      </div>

      <div className="flex items-center justify-end">
        <div
          className="flex h-[56px] items-center justify-center rounded-[4px] border-2 border-primary px-[20px] touch-opacity"
          onClick={e => {
            e.stopPropagation()

            checkAuth(onAddToShowCase)
          }}
        >
          <span className="text text-center text-[24px] font-bold text-primary">
            {i18n.t('Product.添加橱窗')}
          </span>
        </div>
        <div className="w-[20px]"></div>
        <div
          className="flex h-[56px] items-center justify-center rounded-[4px] border-2 border-primary bg-primary px-[20px] touch-opacity"
          onClick={onRequestSample}
        >
          <span className="text text-center text-[24px] font-bold text-white">
            {i18n.t('Product.requestSample')}
          </span>
        </div>
      </div>
    </div>
  ) : (
    <Skeleton.Paragraph lineCount={5} animated />
  )
}
