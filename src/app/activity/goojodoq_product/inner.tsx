'use client'
import { i18n } from '@/lib/client/i18n'
import React from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { Image } from '@/components/Image'
import { useGrayBody } from '@/lib/hooks/useGrayBody'
import car from '@/../public/images/activity/car.png'
import turns_line_bg from '@/../public/images/activity/turns_line_bg.png'
import checkbox_on_green from '@/../public/images/common/checkbox_on_green.png'
import { ItemCell } from './components/ItemCell'
import { Trans } from 'react-i18next'
import { useRouter } from '@/lib/hooks/useRouter'
import { copyText } from '@/lib/client/utils'

export const Inner = () => {
  const mounted = useMounted()
  useGrayBody()

  const router = useRouter()

  return mounted ? (
    <div className="px-[24px] pt-[28px]">
      <div className="rounded-[12px] bg-white px-[24px] pb-[20px] pt-[24px]">
        <div className="relative">
          <Image className="h-[40px] w-[132px]" src={turns_line_bg}></Image>
          <span className="absolute left-[10px] top-0 text-[28px] text-black02">
            {i18n.t('Activity.活动流程')}
          </span>
        </div>
        <div className="mt-[20px] text-[24px] text-black">
          {i18n.t(
            'Activity.活动商品挂橱窗>>在线申请样品>>按要求发布推广视频>>积累更多活动积分>>有机会把BYD汽车开回家！'
          )}
        </div>
        <div className="mt-[16px] flex justify-end">
          <Image className="h-[42px] w-[72px]" src={car}></Image>
        </div>
      </div>

      <ItemCell />

      <div className="mt-[24px] rounded-[12px] bg-white p-[24px]">
        <div className="flex items-center justify-between">
          <span className="text-[30px] font-bold text-black">
            {i18n.t('Activity.达人要求')}
          </span>
          <span
            className="text-[24px] text-link touch-opacity"
            onClick={() => router.push('/activity/goojodoq_rule')}
          >
            {i18n.t('Activity.详细规则>')}
          </span>
        </div>
        <div className="mt-[20px] rounded-[8px] bg-background p-[24px] pb-[8px]">
          <div className="mb-[16px] flex">
            <Image
              className="mt-[2px] size-[28px]"
              src={checkbox_on_green}
            ></Image>
            <div className="flex-1">
              <span className="ml-[8px] text-[24px] leading-[34px] text-gray6E">
                {i18n.t('Activity.粉丝量')} ≥ 2000
              </span>
            </div>
          </div>
          <div className="mb-[16px] flex">
            <Image
              className="mt-[2px] size-[28px]"
              src={checkbox_on_green}
            ></Image>
            <div className="flex-1">
              <span className="ml-[8px] text-[24px] leading-[34px] text-gray6E">
                {i18n.t('Activity.已开通电商橱窗权限')}
              </span>
            </div>
          </div>
          <div className="mb-[16px] flex">
            <Image
              className="mt-[2px] size-[28px]"
              src={checkbox_on_green}
            ></Image>
            <div className="flex-1">
              <span className="ml-[8px] text-[24px] leading-[34px] text-gray6E">
                {i18n.t('Activity.日常视频质量能达标')}
              </span>
            </div>
          </div>
        </div>
        {/*  */}
        <div className="mt-[24px] flex items-center justify-between">
          <span className="text-[30px] font-bold text-black">
            {i18n.t('Activity.视频要求')}
          </span>
        </div>
        <div className="mt-[20px] rounded-[8px] bg-background p-[24px] pb-[8px]">
          <div className="mb-[16px] flex">
            <Image
              className="mt-[2px] size-[28px]"
              src={checkbox_on_green}
            ></Image>
            <div className="flex-1">
              <span className="ml-[8px] text-[24px] leading-[34px] text-gray6E">
                {i18n.t('Activity.必须在活动期间发布视频')}
              </span>
            </div>
          </div>
          <div className="mb-[16px] flex">
            <Image
              className="mt-[2px] size-[28px]"
              src={checkbox_on_green}
            ></Image>
            <div className="flex-1">
              <span className="ml-[8px] text-[24px] leading-[34px] text-gray6E">
                <Trans
                  i18nKey="Activity.必须添加要求的Hashtag：#GOOJODOQDanceChallenge和#GFS006"
                  components={{
                    red: (
                      <span
                        className="cursor-pointer text-primary"
                        onClick={e => {
                          // @ts-ignore
                          copyText(e.target?.innerText + ' ')
                        }}
                      ></span>
                    )
                  }}
                ></Trans>
              </span>
            </div>
          </div>
          <div className="mb-[16px] flex">
            <Image
              className="mt-[2px] size-[28px]"
              src={checkbox_on_green}
            ></Image>
            <div className="flex-1">
              <span className="ml-[8px] text-[24px] leading-[34px] text-gray6E">
                {i18n.t('Activity.必须绑定YOUPIK TAP链接（履约）')}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  ) : null
}
