import React from 'react'
import Image from 'next/image'
import bg_coupon from '@/../public/images/coupon1212/bg_coupon.png'
import icon_calendar from '@/../public/images/coupon1212/icon_calendar.png'
import styles from './index.module.scss'
import { i18n } from '@/lib/client/i18n'
import { isRegionVN } from '@/lib/utils'

const CouponCard = () => {
  return <div className={styles.coupon_card_container}>
    <Image src={bg_coupon} alt="" className={styles.bg_coupon}></Image>
    <div className={styles.content_container}>
      <div className={styles.title}>{i18n.t('Coupon1212.Total value of Tiktok')}</div>
      <div className={styles.price} dangerouslySetInnerHTML={{ __html: isRegionVN() ? i18n.t('Coupon1212.CouponValueVN') : i18n.t('Coupon1212.CouponValueTH') }}></div>
      <div className={styles.date_container}>
        <Image src={icon_calendar} alt="" className={styles.icon_calendar}></Image>
        <div className={styles.date_label}>{isRegionVN() ? i18n.t('Coupon1212.Dec.10 - Dec.13') : i18n.t('Coupon1212.Dec.10 - Dec.12')}</div>
      </div>
    </div>
  </div>
}

export default CouponCard
