import React from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import icon_save_money from '@/../public/images/coupon1212/icon_save_money.png'
import icon_ready_to_use from '@/../public/images/coupon1212/icon_ready_to_use.png'
import icon_offical_security from '@/../public/images/coupon1212/icon_offical_security.png'
import icon_buy_discount from '@/../public/images/coupon1212/icon_buy_discount.png'
import styles from './index.module.scss'

const CouponDesc = () => {
  const descList = [{
    icon: icon_save_money,
    title1: i18n.t('Coupon1212.Save Money1'),
    title2: i18n.t('Coupon1212.Save Money2'),
  }, {
    icon: icon_ready_to_use,
    title1: i18n.t('Coupon1212.Ready to use1'),
    title2: i18n.t('Coupon1212.Ready to use2'),
  }, {
    icon: icon_offical_security,
    title1: i18n.t('Coupon1212.Offical Security1'),
    title2: i18n.t('Coupon1212.Offical Security2'),
  }, {
    icon: icon_buy_discount,
    title1: i18n.t('Coupon1212.Buy Discount1'),
    title2: i18n.t('Coupon1212.Buy Discount2'),
  },]

  return <div className={styles.coupon_desc_container}>
    <div className={styles.desc_list_container}>
      {
        descList.map((item: any, index) => (
          <div className={styles.desc_item_container} key={index}>
            <Image src={item.icon} alt="" className={styles.desc_icon}></Image>
            <div className={styles.desc_title_container}>
              {item.title1 && <div className={styles.desc_title1}>{item.title1}</div>}
              {item.title2 && <div className={styles.desc_title2}>{item.title2}</div>}
            </div>
          </div>
        ))
      }
    </div>
  </div>
}

export default CouponDesc
