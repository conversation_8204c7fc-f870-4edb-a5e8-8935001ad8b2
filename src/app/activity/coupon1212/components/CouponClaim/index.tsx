import React from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import icon_point from '@/../public/images/coupon1212/icon_point.png'
import icon_hand from '@/../public/images/coupon1212/icon_hand.png'
import styles from './index.module.scss'

const CouponClaim = () => {
  return <div className={styles.coupon_claim_container}>
    <div className={styles.btn_container}>
      <div className={styles.btn_title}> {i18n.t('Coupon1212.Claim Now')}</div>
      <Image src={icon_point} alt="" className={styles.icon_point}></Image>
      <Image src={icon_hand} alt="" className={styles.icon_hand}></Image>
    </div>
  </div>
}

export default CouponClaim
