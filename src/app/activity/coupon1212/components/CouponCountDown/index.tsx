'use client'
import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import bg_count_down from '@/../public/images/coupon1212/bg_count_down.png'
import styles from './index.module.scss'
import { isRegionVN } from '@/lib/utils'

function calculateTimeRemaining(remainingTime) {
  // 计算剩余时间中的天数、小时数、分钟数和秒数
  const days = Math.floor(remainingTime / (1000 * 60 * 60 * 24))
  const hours = Math.floor(
    (remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
  )
  const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000)

  // 添加补零逻辑
  const formattedDays = days < 10 ? `0${days}` : `${days}`
  const formattedHours = hours < 10 ? `0${hours}` : `${hours}`
  const formattedMinutes = minutes < 10 ? `0${minutes}` : `${minutes}`
  const formattedSeconds = seconds < 10 ? `0${seconds}` : `${seconds}`

  return {
    restDays: formattedDays,
    restHours: formattedHours,
    restMinutes: formattedMinutes,
    restSeconds: formattedSeconds
  }
}

const startTime = 1733673600000  // 2023-10-9 00:00:00
const endTime12 = 1734019199000 // 2023-10-12 23:59:59
const endTime13 = 1734105599000 // 2023-10-13 23:59:59

const CouponCountDown = () => {
  const [days, setDays] = useState('0')
  const [hours, setHours] = useState('0')
  const [minutes, setMinutes] = useState('0')
  const [seconds, setSeconds] = useState('0')

  // status 0-已关闭 1-活动未开始 2-活动中 3-活动结束
  const [status, setStatus] = useState(1)

  useEffect(() => {
    if (Date.now() < startTime) {
      setStatus(1)
    } else if (Date.now() > startTime && Date.now() < (isRegionVN() ? endTime13 : endTime12)) {
      setStatus(2)
    } else {
      setStatus(3)
    }
  }, [])

  useEffect(() => {
    let interval: any = null
    if (status === 1) {
      interval = setInterval(() => {
        const remainingTime = startTime - Date.now()
        if (remainingTime > 0) {
          const { restDays, restHours, restMinutes, restSeconds } =
            calculateTimeRemaining(remainingTime)
          setDays(restDays)
          setHours(restHours)
          setMinutes(restMinutes)
          setSeconds(restSeconds)
        } else {
          // 活动已经开始
          clearInterval(interval)
          interval = null
          setStatus(2)
        }
      }, 1000)
    } else if (status === 2) {
      interval = setInterval(() => {
        const remainingTime = (isRegionVN() ? endTime13 : endTime12) - Date.now()
        if (remainingTime > 0) {
          const { restDays, restHours, restMinutes, restSeconds } =
            calculateTimeRemaining(remainingTime)
          setDays(restDays)
          setHours(restHours)
          setMinutes(restMinutes)
          setSeconds(restSeconds)
        } else {
          // 活动已经结束
          clearInterval(interval)
          interval = null
          setStatus(3)
        }
      }, 1000)
    }
    return () => {
      if (interval) {
        clearInterval(interval)
        interval = null
      }
    }
  }, [status])


  return <div className={styles.coupon_count_down_container}>
    <Image src={bg_count_down} alt="" className={styles.bg_count_down}></Image>
    <div className={styles.content_container}>
      {(status === 0 || status === 3) && (
        <div className={styles.end_container}>
          {i18n.t('Coupon1212.本次开奖活动已结束！')}
        </div>
      )}
      {(status === 1 || status === 2) && (
        <div className={styles.progress_container}>
          <div className={styles.desc_first}>
            {status === 1
              ? i18n.t('Coupon1212.Start in')
              : i18n.t('Coupon1212.Ending in')}
          </div>
          <div className={styles.num_card}>{days}</div>
          <div className={styles.desc}>{i18n.t('Coupon1212.xx day')}</div>
          <div className={styles.num_card}>{hours}</div>
          <div className={styles.seperator}>:</div>
          <div className={styles.num_card}>{minutes}</div>
          <div className={styles.seperator}>:</div>
          <div className={styles.num_card}>{seconds}</div>
        </div>
      )}
    </div>
  </div>
}

export default CouponCountDown
