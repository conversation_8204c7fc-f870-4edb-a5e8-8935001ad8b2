import React from 'react'
import Inner from './inner'
import { metadataTemplate, inApp } from '@/lib/server/utils'
import { i18nS } from '@/lib/server/i18n'
import { isLanguageTH, isLanguageVI } from '@/lib/utils'

interface Props {
  params: {
    id: string
  }
}

export default async function Index(props: Props) {
  return <Inner />
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: i18nS.t('Coupon1212.Creator Super Coupons'), })
  }

  // const bg_coupon1212_vi = `${process.env.NEXT_PUBLIC_URL}/images/newer/bg_arrival_top_vi.png`
  // const bg_coupon1212_th = `${process.env.NEXT_PUBLIC_URL}/images/newer/bg_arrival_top_th.png`
  // const bg_coupon1212_en = `${process.env.NEXT_PUBLIC_URL}/images/newer/bg_arrival_top_en.png`
  // let bgcoupon1212 = bg_coupon1212_en
  // if (isLanguageVI()) {
  //   bgcoupon1212 = bg_coupon1212_vi
  // }
  // if (isLanguageTH()) {
  //   bgcoupon1212 = bg_coupon1212_th
  // }

  const description = ''
  const title = i18nS.t('Coupon1212.Creator Super Coupons')
  const icon = `${process.env.NEXT_PUBLIC_URL}/images/coupon1212/icon_coupon1212.png`

  return metadataTemplate({ title, description, icon })
}
