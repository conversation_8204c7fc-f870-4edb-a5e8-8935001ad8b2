'use client'
import React, { useMemo } from 'react'
import Image from 'next/image'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import { isIOS } from '@/lib/client/utils'
import { isLanguageTH, isLanguageVI } from '@/lib/utils'
import { launchUrl } from '@/lib/client/utils'
import { isRegionVN } from '@/lib/utils'
import { ypkClient, Events, ypkH5 } from '@/lib/ypk'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import bg_coupon1212_en from '@/../public/images/coupon1212/bg_coupon1212_en.png'
import bg_coupon1212_th from '@/../public/images/coupon1212/bg_coupon1212_th.png'
import bg_coupon1212_vn from '@/../public/images/coupon1212/bg_coupon1212_vn.png'
import CouponCard from '../components/CouponCard'
import CouponCountDown from '../components/CouponCountDown'
import CouponDesc from '../components/CouponDesc'
import CouponClaim from '../components/CouponClaim'
import CouponFooter from '../components/CouponFooter'
import CouponAlarm from '../components/CouponAlarm'
import styles from './index.module.scss'

const url_th = 'https://vt.tiktok.com/ZSjKcFyxo/';
const url_vn = 'https://vt.tiktok.com/ZSjEdwYJS/';

const Coupon1212 = () => {
  const bg_coupon1212 = useMemo(() => {
    let bg_coupon1212_pic = bg_coupon1212_en
    if (isLanguageVI()) {
      bg_coupon1212_pic = bg_coupon1212_vn
    }
    if (isLanguageTH()) {
      bg_coupon1212_pic = bg_coupon1212_th
    }
    return bg_coupon1212_pic
  }, [])

  const handleCliamNow = () => {
    if (ypkClient) {
      ypkClient.postMessage(Events.openBrowser, {
        url: url_th,
      })
      statistic({
        eventName: EventName.click_coupon1212_cliam_yk_th,
      })
      return
    }
    if (ypkH5) {
      window.location.href = url_th
      statistic({
        eventName: EventName.click_coupon1212_cliam_yk_th,
      })
      return
    }
    if (isRegionVN()) {
      launchUrl(url_vn)
      statistic({
        eventName: EventName.click_coupon1212_cliam_uce_vn,
      })
    } else {
      launchUrl(url_th)
      statistic({
        eventName: EventName.click_coupon1212_cliam_uce_th,
      })
    }
  }

  return <TransparentNavPage
    title={i18n.t('Coupon1212.Creator Super Coupons')}
    transparent={false}
    hide={!webview}
    showShareButton
  >
    <div className={styles.coupon1212_container} id='coupon1212-claim-button' onClick={handleCliamNow}>
      <Image src={bg_coupon1212} alt="" className={styles.bg_coupon1212}></Image>
      <div className={styles.page_content_container}>
        <div className={styles.coupon_card_container}>
          <CouponCard></CouponCard>
        </div>
        <div className={styles.coupon_count_down_container}>
          <CouponCountDown></CouponCountDown>
        </div>
        <div className={styles.coupon_desc_container}>
          <CouponDesc></CouponDesc>
        </div>
        <div className={styles.coupon_alarm_container}>
          <CouponAlarm></CouponAlarm>
        </div>
        <div className={styles.coupon_claim_container}>
          <CouponClaim></CouponClaim>
        </div>
        {isIOS() && <div className={styles.coupon_footer_container}>
          <CouponFooter></CouponFooter>
        </div>}
      </div>
    </div>
  </TransparentNavPage>
}

export default Coupon1212
