'use client'
import { i18n } from '@/lib/client/i18n'
import React, { useEffect } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { Image } from '@/components/Image'
import { useGrayBody } from '@/lib/hooks/useGrayBody'
import { copyText, px2rem, rpxToPx } from '@/lib/client/utils'
import CreatorsBar from '@/components/CreatorsBar'
import copy from '@/../public/images/common/copy.png'
import { webview } from '@/lib/client/webview'
import {
  TtInviteCodeAccountVo,
  accountList
} from '@/app/api/api-uchoice/invite/code/accountList'
import { InvalidCells } from './components/InvalidCells'
import ValidCells from './components/ValidCells'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import useIsCampaignValid, { CampaignStatus } from './hooks/useIsCamplainValid'
import { WebviewEvents } from '@/lib/client/webview/events'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import invite_banner_activiy_valid_th from '@/../public/images/activity/invite_banner_activiy_valid.png'
import invite_banner_activiy_invalid_th from '@/../public/images/activity/invite_banner_activiy_invalid.png'
import invite_progress_activiy_valid_th from '@/../public/images/activity/invite_progress_activiy_valid.png'
import invite_progress_activiy_invalid_th from '@/../public/images/activity/invite_progress_activiy_invalid.png'
import invite_banner_activiy_valid_en from '@/../public/images/activity/invite_banner_activiy_valid_en.png'
import invite_banner_activiy_invalid_en from '@/../public/images/activity/invite_banner_activiy_invalid_en.png'
import invite_progress_activiy_valid_en from '@/../public/images/activity/invite_progress_activiy_valid_en.png'
import invite_progress_activiy_invalid_en from '@/../public/images/activity/invite_progress_activiy_invalid_en.png'
import invite_banner_activiy_valid_vn from '@/../public/images/activity/invite_banner_activiy_valid.png'
import invite_banner_activiy_invalid_vn from '@/../public/images/activity/invite_banner_activiy_invalid_vn.png'
import invite_progress_activiy_valid_vn from '@/../public/images/activity/invite_progress_activiy_valid.png'
import invite_progress_activiy_invalid_vn from '@/../public/images/activity/invite_progress_activiy_invalid_vn.png'
import { matchLanguage } from '@/lib/utils'
import styles from './index.module.scss'

export const Inner = () => {
  const mounted = useMounted()
  useGrayBody()

  const { campaignId, unionId } = useRouteParams<{
    campaignId?: string
    unionId?: string
  }>()

  const [creators, setCreators] = React.useState<TtInviteCodeAccountVo[]>([])
  const [selectCreator, setSelectCreator] =
    React.useState<TtInviteCodeAccountVo>()
  const { loading: validLoading, campaignValid } = useIsCampaignValid(campaignId || '')

  useEffect(() => {
    fetchAccountList()
  }, [])

  const fetchAccountList = async () => {
    const { code, result } = await accountList()
    if (code == 200) {
      setCreators(result)

      if (result.length > 0) {
        if (unionId) {
          setSelectCreator(result.find(item => item.unionId == unionId))

          // 放到数组第一个
          const foundCreator = result.find(item => item.unionId == unionId)
          if (foundCreator) {
            setCreators([
              foundCreator,
              ...result.filter(item => item.unionId != unionId)
            ])
          }
        } else {
          setSelectCreator(result[0])
        }
      }
    }
  }

  const onShare = () => {
    if (!selectCreator?.inviteCode) {
      return
    }

    const url = `${window.location.origin}/activity/goojodoq_join?campaignId=${campaignId || '1001'
      }&sharer=${selectCreator?.displayName}&inviteCode=${selectCreator?.inviteCode}`
    console.log(url)

    webview?.send(WebviewEvents.shareUrl, {
      url,
      desc: campaignValid === CampaignStatus.Valid ? '「💥สะสมแต้มลุ้นรับรถ BYD มูลค่า 569,900 บาท✨เข้า uChoice Pro และรับสิทธิ์ด่วน!」' : '',
    })
  }

  return mounted && !validLoading ? (
    <div className="px-[24px] pt-[28px]">
      <CreatorsBar
        creators={creators}
        selectedUnionId={selectCreator?.unionId}
        onSelect={unionId => {
          setSelectCreator(creators.find(item => item.unionId == unionId))
        }}
      ></CreatorsBar>

      {campaignValid === CampaignStatus.Valid && selectCreator ? (
        <div className="mt-[32px]">
          <Image
            className="h-[280px] w-[702px]"
            src={matchLanguage({
              en: invite_banner_activiy_valid_en,
              th: invite_banner_activiy_valid_th,
              vi: invite_banner_activiy_valid_vn
            })}
          ></Image>
        </div>
      ) : (
        <div className="mt-[32px]">
          <Image
            className="h-[280px] w-[702px]"
            src={matchLanguage({
              en: invite_banner_activiy_invalid_en,
              th: invite_banner_activiy_invalid_th,
              vi: invite_banner_activiy_invalid_vn
            })}
          ></Image>
        </div>
      )}

      {campaignValid === CampaignStatus.Valid && (
        <div className="mt-[32px]" onClick={() => copyText(selectCreator?.inviteCode || '')}>
          <div className="text-[32px] font-bold text-black02">
            {i18n.t('Activity.分享你的邀请码给朋友！')}
          </div>
          <div className="mt-[24px] flex h-[80px] w-[702px] items-center justify-between rounded-[8px] border-[2px] border-primary bg-white">
            <span className="ml-[20px] text-[28px] text-gray8A">
              {i18n.t('Activity.分享邀请码')}
            </span>
            <div className="flex items-center">
              <span className="text-[28px] font-bold text-black30">
                {selectCreator?.inviteCode}
              </span>
              <div
                className="p-[20px]"
              >
                <Image src={copy} className="size-[32px]"></Image>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-[32px]">
        {campaignValid === CampaignStatus.Valid ? (
          <Image
            className="h-[940px] w-[702px]"
            src={matchLanguage({
              en: invite_progress_activiy_valid_en,
              th: invite_progress_activiy_valid_th,
              vi: invite_progress_activiy_valid_vn
            })}
          ></Image>
        ) : (
          <Image
            className="h-[560px] w-[702px]"
            src={matchLanguage({
              en: invite_progress_activiy_invalid_en,
              th: invite_progress_activiy_invalid_th,
              vi: invite_progress_activiy_invalid_vn
            })}
          ></Image>
        )}
      </div>

      {campaignValid === CampaignStatus.Valid && selectCreator?.unionId && (
        <ValidCells
          unionId={selectCreator?.unionId}
          bottomComponent={
            <div>
              {selectCreator?.unionId && (
                <InvalidCells unionId={selectCreator?.unionId}></InvalidCells>
              )}
            </div>
          }
        ></ValidCells>
      )}

      <div
        style={{
          width: '100%',
          height: webview
            ? `${px2rem(webview?.getData().bottomSafeArea + 48 + 80 + 24 + 48)}`
            : (px2rem(48 + 80 + 24 + 48)),
        }}
      ></div>

      <div
        className={styles.btn_footer_container}
        style={{
          paddingTop: rpxToPx(24),
          paddingBottom: webview
            ? `${px2rem(webview?.getData().bottomSafeArea + 48)}`
            : px2rem(48)
        }}
      >
        <div
          className="flex h-[80px] w-[702px] items-center justify-center rounded-[4px] bg-primary touch-opacity"
          onClick={() => {
            onShare()

            if (campaignValid === CampaignStatus.Valid) {
              statistic({
                eventName: EventName.goojodoq_share_when_valid
              })
            } else {
              statistic({
                eventName: EventName.goojodoq_share_when_invalid
              })
            }
          }}
        >
          <span className="text-[32px] font-bold text-white">
            {i18n.t('Activity.SHARE NOW!')}
          </span>
        </div>
      </div>
    </div>
  ) : null
}
