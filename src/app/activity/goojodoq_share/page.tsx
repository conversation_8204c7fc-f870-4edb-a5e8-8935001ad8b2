import { metadataTemplate, inApp } from '@/lib/server/utils'
import { Inner } from './inner'
import { i18nS } from '@/lib/server/i18n'

export async function generateMetadata() {
  const title = i18nS.t('Activity.邀请好友')

  if (inApp()) {
    return metadataTemplate({
      title,
      showNativeNavBar: true,
    })
  }

  return metadataTemplate({ title, icon: `${process.env.NEXT_PUBLIC_URL}/images/fmcg/activity_byd.png`, })
}

export default async function Index() {
  return <Inner />
}
