import { useState, useEffect } from 'react'
import { campaignIsValid } from '@/app/api/api-uchoice/seller/campaigns/isValid'

export enum CampaignStatus {
  Unknown = 'unknown',
  Valid = 'valid',
  Invalid = 'invalid'
}

const useIsCampaignValid = (campaignId: string) => {
  const [loading, setLoading] = useState(false)
  const [campaignValid, setCampaignValid] = useState<CampaignStatus>(
    CampaignStatus.Unknown
  )

  const fetchCampaignIsValid = async () => {
    try {
      setLoading(true)
      const { code, result } = await campaignIsValid(campaignId)
      setLoading(false)
      if (code === 200) {
        setCampaignValid(result ? CampaignStatus.Valid : CampaignStatus.Invalid)
      }
    } catch (error) {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCampaignIsValid()
  }, [campaignId])

  return {
    loading,
    campaignValid,
    reFetch: fetchCampaignIsValid
  }
}

export default useIsCampaignValid
