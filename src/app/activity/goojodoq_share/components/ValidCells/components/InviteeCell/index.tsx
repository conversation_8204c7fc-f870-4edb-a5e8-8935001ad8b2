import React from 'react'
import { Image } from '@/components/Image'
import pendg_icon from '@/../public/images/activity/pending.png'
import done_icon from '@/../public/images/activity/done.png'
import avatar_icon from '@/../public/images/activity/avatar.png'
import { TtInviteCodeFollowVo } from '@/app/api/api-uchoice/invite/code/followList'
import { formatSales } from '@/lib/format'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'

interface InviteeCellProps {
  creator: TtInviteCodeFollowVo
}

export const InviteeCell: React.FC<InviteeCellProps> = ({ creator }) => {
  return (
    <div className="flex w-full items-center justify-between py-[30px]">
      <div className="flex items-center">
        <div className="relative">
          <div className="pl-[12px]">
            <Image
              src={creator.avatar || avatar_icon}
              className="size-[104px] rounded-[52px]"
              withPlaceholder
            ></Image>
          </div>
          <div className="absolute bottom-0 flex h-[40px] w-[128px] items-center justify-center rounded-[20px] bg-[#342524]">
            <span className="text-[24px] text-[#FED7B5]">
              Level {creator.tier}
            </span>
          </div>
        </div>
        <div className="ml-[20px] flex flex-col justify-center">
          <div className="flex items-center">
            <span className={styles.title}>
              {creator.displayName}
            </span>
          </div>

          <span className="mt-[28px] text-[24px] text-gray8A">
            {i18n.t('TiktokData.粉丝数')} {formatSales(creator.followerCount)}
          </span>
        </div>
      </div>
      {creator.status !== 2 && (
        <div className="flex flex-col items-end">
          {creator.status === 1 && (
            <span className="mb-[28px] text-[28px] text-gray8A">
              {i18n.t('Activity.+5,000积分')}
            </span>
          )}

          <div className="flex items-center">
            <Image
              src={creator.status === 1 ? done_icon : pendg_icon}
              className="size-[24px]"
            ></Image>
            <span
              className="ml-[12px] text-[28px] text-black30"
              style={{
                color: creator.status === 1 ? '#22B35E' : '#FE6D45'
              }}
            >
              {creator.status === 1
                ? i18n.t('Activity.完成5,000 views')
                : i18n.t('Activity.待完成5,000 views')}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
