'use client'
import {
  TtInviteCodeFollowVo,
  followList
} from '@/app/api/api-uchoice/invite/code/followList'
import { i18n } from '@/lib/client/i18n'
import { loading } from '@/lib/client/loading'
import { useIntersectionObserver } from '@/lib/hooks/useIntersectionObserver'
import React, { useEffect, useRef, useState } from 'react'
import { InviteeCell } from './components/InviteeCell'
import { DotLoading } from 'antd-mobile'
import StatusView, { StatusViewType } from '@/app/components/StatusView'

const pageSize = 10

interface Props {
  unionId: string
  bottomComponent: React.ReactNode
}

const ValidCells = ({ unionId, bottomComponent }: Props) => {
  const { ref, isLeave } = useIntersectionObserver()

  const pageNoRef = useRef(1)
  const loadingRef = useRef(false)
  const [fetchLoading, setFetchLoading] = useState(false)

  const [noMore, setNoMore] = useState(false)
  const [data, setData] = useState<TtInviteCodeFollowVo[]>([])

  useEffect(() => {
    if (!isLeave) {
      setTimeout(() => {
        fetch()
      }, 100)
    }
  }, [isLeave])

  useEffect(() => {
    console.log('unionId changed', unionId)
    loading.show({ userInteractive: false })
    fetch(true)
  }, [unionId])

  const fetch = async (firstPage = false) => {
    if (firstPage) {
      pageNoRef.current = 1
      loadingRef.current = false
      setFetchLoading(false)
    } else {
      if (loadingRef.current || noMore) return
    }

    loadingRef.current = true
    setFetchLoading(true)

    const { code, result } = await followList({
      isValid: true,
      pageNo: pageNoRef.current,
      pageSize,
      unionId
    })

    loading.hide()

    loadingRef.current = false
    setFetchLoading(false)

    if (code === 200) {
      if (firstPage) {
        setData(result.list)
      } else {
        setData(data.concat(result.list))
      }

      if (result.list.length < pageSize) {
        setNoMore(true)
      }

      pageNoRef.current++
    }
  }

  return true ? (
    <div className="mt-[32px]">
      <div className="rounded-[8px] bg-white px-[24px] pt-[34px]">
        <div className="text-[32px] font-bold text-black02">
          {i18n.t('Activity.全部邀请')}
        </div>
        <div>
          {data.map((it, index) => (
            <div key={index} className="flex flex-col items-center">
              <InviteeCell creator={it}></InviteeCell>
              {index !== data.length - 1 && (
                <div className="h-[2px] w-[654px] bg-background"></div>
              )}
            </div>
          ))}
        </div>

        {!fetchLoading && data.length === 0 && (
          <StatusView status={StatusViewType.empty} style={{ padding: '24px 0' }}></StatusView>
        )}

        {fetchLoading && !noMore && (
          <div className="flex justify-center pb-[24px]">
            <DotLoading></DotLoading>
          </div>
        )}

        {data.length > 0 && noMore && (
          <div className="pb-[24px] text-center font-[24px] text-grayCC">
            {i18n.t('Common.No data')}
          </div>
        )}
      </div>

      {bottomComponent}

      <div ref={ref} className="h-[1px]"></div>
    </div>
  ) : null
}

export default ValidCells
