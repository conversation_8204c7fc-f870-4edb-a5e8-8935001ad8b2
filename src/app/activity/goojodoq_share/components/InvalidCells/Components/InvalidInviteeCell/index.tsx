import React from 'react'
import { Image } from '@/components/Image'
import avatar_icon from '@/../public/images/activity/avatar.png'
import { TtInviteCodeFollowVo } from '@/app/api/api-uchoice/invite/code/followList'
import { i18n } from '@/lib/client/i18n'

interface Props {
  creator: TtInviteCodeFollowVo
}

export const InvalidInviteeCell = ({ creator }: Props) => {
  return (
    <div className="flex w-full items-center justify-between bg-white px-[24px] py-[30px]">
      <div className="flex items-center">
        <Image
          src={avatar_icon}
          className="size-[104px] rounded-[52px]"
          withPlaceholder
        ></Image>

        <span className="ml-[20px] text-[28px] text-black30">
          {creator.displayName}
        </span>

        <div className="ml-[20px] flex h-[40px] items-center justify-center rounded-[20px] bg-[#D8D8D830] px-[20px]">
          <span className="text-[24px] text-gray8A">
            {i18n.t('Activity.已注销')}
          </span>
        </div>
      </div>
      <span className="text-[24px] text-gray8A">
        {i18n.t('Activity.任务已失效')}
      </span>
    </div>
  )
}
