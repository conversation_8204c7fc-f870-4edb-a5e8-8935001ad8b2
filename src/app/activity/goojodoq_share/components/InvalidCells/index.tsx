import React, { useEffect } from 'react'
import { InvalidInviteeCell } from './Components/InvalidInviteeCell'
import gray_arrow_down_icon from '@/../public/images/common/gray_arrow_down.png'
import gray_arrow_up_icon from '@/../public/images/common/gray_arrow_up.png'
import { Image } from '@/components/Image'
import {
  followList,
  TtInviteCodeFollowVo
} from '@/app/api/api-uchoice/invite/code/followList'
import { i18n } from '@/lib/client/i18n'

export const InvalidCells = ({ unionId }: { unionId: string }) => {
  const [creators, setCreators] = React.useState<TtInviteCodeFollowVo[]>([])
  const [expanded, setExpanded] = React.useState(false)

  useEffect(() => {
    fetch()
  }, [unionId])

  const fetch = async () => {
    const { code, result } = await followList({
      isValid: false,
      pageNo: 1,
      pageSize: 999,
      unionId
    })

    if (code == 200) {
      setCreators(result?.list || [])
    }
  }

  return creators.length > 0 ? (
    <div className="mt-[24px]">
      <div
        className="mb-[18px] flex items-center justify-center"
        onClick={() => {
          setExpanded(!expanded)
        }}
      >
        <span className="mr-[8px] text-[24px] text-gray8A">
          {expanded
            ? i18n.t('Activity.收起已注销用户')
            : i18n.t('Activity.查看已注销用户')}
        </span>
        <Image
          src={expanded ? gray_arrow_down_icon : gray_arrow_up_icon}
          className="h-[24px] w-[24px]"
        ></Image>
      </div>
      <div className="rounded-[8px] bg-white">
        {expanded &&
          creators.map((item, index) => {
            return (
              <div key={index} className="flex flex-col items-center">
                <InvalidInviteeCell creator={item}></InvalidInviteeCell>
                {index !== creators.length - 1 && (
                  <div className="h-[2px] w-[654px] bg-background"></div>
                )}
              </div>
            )
          })}
      </div>
    </div>
  ) : null
}
