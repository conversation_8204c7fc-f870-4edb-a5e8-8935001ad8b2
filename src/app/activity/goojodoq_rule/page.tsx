import { metadataTemplate, inApp } from '@/lib/server/utils'
import goojodoq_activity_rule from '@/../public/images/activity/goojodoq_activity_rule.png'
import { Image } from '@/components/Image'
import { i18nS } from '@/lib/server/i18n'

export async function generateMetadata() {
  const title = i18nS.t('Activity.BYD汽车活动商品')

  if (inApp()) {
    return metadataTemplate({
      title,
      showNativeNavBar: true,
      showNativeNavBarContactButton: true
    })
  }

  return metadataTemplate({ title })
}

export default async function Index() {
  return (
    <Image
      className="h-[4810px] w-[750px]"
      src={goojodoq_activity_rule}
    ></Image>
  )
}
