'use client'
import { i18n } from '@/lib/client/i18n'
import React, { useEffect, useState } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { Image } from '@/components/Image'
import { useGrayBody } from '@/lib/hooks/useGrayBody'
import { copyText, makeSureInApp, px2rem, rpxToPx } from '@/lib/client/utils'
import copy from '@/../public/images/common/copy.png'
import { webview } from '@/lib/client/webview'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { useRouter } from '@/lib/hooks/useRouter'
import { JoinUs } from '@/components/JoinUs'
import useIsCampaignValid, {
  CampaignStatus
} from '../goojodoq_share/hooks/useIsCamplainValid'
import { getSellerCampaignDetail } from '@/app/api/api-uchoice/seller/campaigns/detail'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import join_progress_activiy_valid_th from '@/../public/images/activity/join_progress_activiy_valid.png'
import join_progress_activiy_valid_en from '@/../public/images/activity/join_progress_activiy_valid_en.png'
import join_progress_activiy_valid_vn from '@/../public/images/activity/join_progress_activiy_valid.png'
import { isRegionTH, isRegionVN, matchLanguage } from '@/lib/utils'
import styles from './index.module.scss'

const CampaignValid = ({ onJoin }: { onJoin?: () => void }) => {
  const mounted = useMounted()
  const router = useRouter()
  useGrayBody()

  const { campaignId, sharer, inviteCode } = useRouteParams<{
    campaignId?: string
    sharer?: string
    inviteCode?: string
  }>()

  const [campaignDetail, setCampaignDetail] = useState<any>({})

  const fetchCampaignDetail = async () => {
    const { code, result } = await getSellerCampaignDetail(campaignId || '1001')
    if (code === 200) {
      setCampaignDetail(result)
    }
  }

  useEffect(() => {
    fetchCampaignDetail()
  }, [])

  return mounted ? (
    <div className="">
      <div className="relative">
        <img src={campaignDetail.bannerUrl}></img>
        <div className="absolute bottom-[396px] right-0">
          <div
            className="flex h-[48px] items-center justify-center rounded-l-[24px] bg-white px-[24px] touch-opacity"
            onClick={() => {
              router.push(`/activity/goojodoq_rule`)
            }}
          >
            <span className="text-[24px] text-[#1C7FE0]">
              {i18n.t('Activity.活动规则')}
            </span>
          </div>
        </div>
      </div>

      <div className="mt-[32px] px-[24px]" onClick={() => {
        copyText(inviteCode || '')
      }}>
        <div className="text-[32px] font-bold text-black02">
          {i18n
            .t('Activity.绑定name的邀请码，BYD汽车等你来拿')
            .replace('{{name}}', sharer)}
        </div>
        <div className="mt-[24px] flex h-[80px] w-[702px] items-center justify-between rounded-[8px] border-[2px] border-primary bg-white">
          <span className="ml-[20px] text-[28px] text-gray8A">
            {i18n.t('Activity.分享邀请码')}
          </span>
          <div className="flex items-center">
            <span className="text-[28px] font-bold text-black30">
              {inviteCode}
            </span>
            <div
              className="p-[20px]"
            >
              <Image src={copy} className="size-[32px]"></Image>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-[32px] px-[24px]">
        <Image
          className="h-[808px] w-[702px]"
          src={matchLanguage({
            en: join_progress_activiy_valid_en,
            th: join_progress_activiy_valid_th,
            vi: join_progress_activiy_valid_vn
          })}
        ></Image>
      </div>

      <div
        style={{
          height: webview
            ? `${px2rem(webview?.getData().bottomSafeArea + 48 + 80 + 24 + 48)}`
            : (px2rem(48 + 80 + 24 + 48))
        }}
      ></div>

      <div
        className={styles.btn_footer_container}
        style={{
          paddingTop: rpxToPx(24),
          paddingBottom: webview
            ? `${px2rem(webview?.getData().bottomSafeArea + 48)}`
            : px2rem(48)
        }}
      >
        <div
          className="flex h-[80px] w-[702px] items-center justify-center rounded-[4px] bg-primary"
          onClick={onJoin}
        >
          <span className="text-[32px] font-bold text-white">
            {i18n.t('Activity.JOIN NOW!')}
          </span>
        </div>
      </div>
    </div>
  ) : null
}

export const Inner = () => {
  const { campaignId, sharer } = useRouteParams<{
    campaignId?: string
    sharer?: string
    inviteCode?: string
  }>()

  const { loading: validLoading, campaignValid } = useIsCampaignValid(campaignId || '')

  return (
    !validLoading && <TransparentNavPage
      hide={!webview}
      title={i18n.t('Activity.name邀请您参加活动').replace('{{name}}', sharer)}
      transparent={false}
    >
      <div>
        {campaignValid === CampaignStatus.Valid && (
          <CampaignValid
            onJoin={async () => {
              makeSureInApp({
                route: 'Web',
                params: {
                  initialURL: `${window.location.origin}/rankinghit/${campaignId || ''
                    }?region=${isRegionVN() ? 'VN' : 'TH'}`,
                },
                desc: '「💥สะสมแต้มลุ้นรับรถ BYD มูลค่า 569,900 บาท✨เข้า uChoice Pro และรับสิทธิ์ด่วน!」',
              })

              statistic({
                eventName: EventName.goojodoq_join_when_valid
              })
            }}
          ></CampaignValid>
        )}
      </div>

      {campaignValid === CampaignStatus.Invalid && (
        <JoinUs
          afterJoin={() => {
            statistic({
              eventName: EventName.goojodoq_join_when_invalid
            })
          }}
        ></JoinUs>
      )}
    </TransparentNavPage>
  )
}
