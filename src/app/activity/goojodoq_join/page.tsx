import { metadataTemplate, inApp } from '@/lib/server/utils'
import { Inner } from './inner'
import { i18nS } from '@/lib/server/i18n'

interface Props {
  searchParams: {
    sharer?: string
  }
}

export async function generateMetadata({ searchParams: { sharer } }: Props) {
  const title = i18nS
    .t('Activity.name邀请您参加活动')
    .replace('{{name}}', sharer || '')

  return metadataTemplate({
    title,
    icon: `${process.env.NEXT_PUBLIC_URL}/images/fmcg/activity_byd.png`,
  })
}

export default async function Index() {
  return <Inner />
}
