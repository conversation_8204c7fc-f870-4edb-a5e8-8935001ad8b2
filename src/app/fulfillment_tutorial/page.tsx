'use client'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import React, { useEffect, useRef, useState } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import { useRouter } from '@/lib/hooks/useRouter'
import styles from './index.module.scss'
import InsertColoredText from '@/components/InsertColoredText'
import ImageWithPreload from '../components/ImageWithPreload'
import { rpxToPx } from '@/lib/client/utils'
import { webview } from '@/lib/client/webview'
import LangsImg from './langs_img'
const FulfillmentTutorial = () => {
  const mounted = useMounted()
  const router = useRouter()

  const imgPathImg = '/images/showcase/img_1.png'
  const icon_right = '/images/promotion/icon_right.png'
  const icon_error = '/images/promotion/icon_error.png'
  return (
    mounted && (
      <TransparentNavPage
        title={i18n.t('Promotion.fulfillmentTutorial')}
        transparent={false}
        hide={!webview}
      >
        <div className={styles.container}>
          <div className={styles.tutorial_tips}>
            <span className={styles.vertical_line}></span>
            <span> {i18n.t('Promotion.fulfillmentDoubt')}</span>
          </div>
          <div className={styles.tutorial_title1}>
            {i18n.t('Promotion.fulfillmentTitle1')}
          </div>
          <InsertColoredText
            text={i18n.t('Promotion.fulfillmentTip1')}
            className={styles.fullfillment_words}
          >
            <span className={styles.color_words}>
              {i18n.t('Promotion.fulfillmentRedTip1')}
            </span>
          </InsertColoredText>
          <div className={styles.img_box}>
            <LangsImg imgIndex={1} />
          </div>
          <div className={styles.fulfillment_tips1}>
            <ImageWithPreload
              src={icon_right}
              preview
              imageStyle={{
                width: rpxToPx(36),
                height: rpxToPx(36)
              }}
            />
            <span className={styles.fulfillment_tip2}>
              {i18n.t('Promotion.fulfillmentTip2')}
            </span>
          </div>
          <div className={styles.img_box}>
            <LangsImg imgIndex={2} />
            <div style={{ marginRight: '48px' }}></div>
            <LangsImg imgIndex={3} />
          </div>
          <div className={styles.tutorial_title1} style={{ marginTop: '20px' }}>
            {i18n.t('Promotion.fulfillmentTitle2')}
          </div>
          <InsertColoredText
            text={i18n.t('Promotion.fulfillmentTip4')}
            className={styles.fullfillment_words}
          >
            <span className={styles.color_words}>
              {i18n.t('Promotion.fulfillmentRedTip4')}
            </span>
          </InsertColoredText>
          <div className={styles.all_made_end}>
            <span className={styles.horizontal_line}></span>
            &nbsp;&nbsp;
            <span>{i18n.t('Promotion.allmadeforcreator')}</span>
            &nbsp;&nbsp;
            <span className={styles.horizontal_line}></span>
          </div>
        </div>
      </TransparentNavPage>
    )
  )
}

export default FulfillmentTutorial
