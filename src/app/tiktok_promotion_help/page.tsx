'use client'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import React, { useEffect, useRef, useState } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { webview } from '@/lib/client/webview'
import LangsImg from './langs_img'
import Button from '../components/Button'
import { WebviewEvents } from '@/lib/client/webview/events'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import InsertColoredText from '@/components/InsertColoredText'
const TiktokPromotionHelp = () => {
  const mounted = useMounted()

  return (
    mounted && (
      <TransparentNavPage
        title={i18n.t('Promotion.promotionTutorialPage')}
        transparent={false}
        hide={!webview}
      >
        <div className={styles.container}>
          <div className={styles.tutorial_tips}>
            <span> {i18n.t('Promotion.path1')}</span>
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip1')}
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip2')}
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip3')}
          </div>

          <div className={styles.img_box}>
            <LangsImg imgIndex={1} />
            <LangsImg imgIndex={2} />
            <LangsImg imgIndex={3} />
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip4')}
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip5')}
          </div>
          <div className={styles.img_box}>
            <LangsImg imgIndex={4} />
            <LangsImg imgIndex={5} />
            <LangsImg imgIndex={6} />
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip6')}
          </div>
          <InsertColoredText
            text={i18n.t('Promotion.tip7')}
            className={styles.tutorial_title}
          >
            <span className={styles.color_words}>
              {i18n.t('Promotion.tip7_1')}
            </span>
            <span className={styles.color_words}>
              {i18n.t('Promotion.tip7_2')}
            </span>
            <span className={styles.color_words}>
              {i18n.t('Promotion.tip7_3')}
            </span>
          </InsertColoredText>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip8')}
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip9')}
          </div>
          <div className={styles.img_box}>
            <LangsImg imgIndex={7} />
            <LangsImg imgIndex={8} />
            <LangsImg imgIndex={9} />
          </div>

          <div className={styles.tutorial_tips2}>
            <span> {i18n.t('Promotion.path2')}</span>
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip10')}
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip11')}
          </div>
          <div className={styles.img_box}>
            <LangsImg imgIndex={10} />
            <LangsImg imgIndex={11} />
            <LangsImg imgIndex={12} />
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip5')}
          </div>
          <InsertColoredText
            text={i18n.t('Promotion.tip7')}
            className={styles.tutorial_title}
          >
            <span className={styles.color_words}>
              {i18n.t('Promotion.tip7_1')}
            </span>
            <span className={styles.color_words}>
              {i18n.t('Promotion.tip7_2')}
            </span>
            <span className={styles.color_words}>
              {i18n.t('Promotion.tip7_3')}
            </span>
          </InsertColoredText>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip12')}
          </div>
          <div className={styles.img_box_center}>
            <LangsImg imgIndex={6} />
            <div style={{ marginRight: '48px' }}></div>
            <LangsImg imgIndex={14} />
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip13')}
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip14')}
          </div>
          <div className={styles.tutorial_title}>
            {i18n.t('Promotion.tip15')}
          </div>
          <div className={styles.img_box_center}>
            <LangsImg imgIndex={16} />
            <div style={{ marginRight: '48px' }}></div>
            <LangsImg imgIndex={15} />
          </div>
          <Button
            onClick={async () => {
              statistic({ eventName: EventName.open_tiktok_to_promote })
              await webview?.send(WebviewEvents.launch, {
                url: 'tiktok://'
              })
            }}
            title={i18n.t('Promotion.openTikTokToPromote')}
            className={styles.add_btn}
          ></Button>
        </div>
      </TransparentNavPage>
    )
  )
}

export default TiktokPromotionHelp
