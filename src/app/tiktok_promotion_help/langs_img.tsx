import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import Image from 'next/image'
import { rpxToPx } from '@/lib/client/utils'
import { isLanguageTH, isLanguageVI, isRegionTH } from '@/lib/utils'
import { ImagePreview } from 'react-vant'
import ImageWithPreload from '../components/ImageWithPreload'
const LangsImg = ({ imgIndex, width = 95, height = 202 }) => {
  const lang: any = localStorage.getItem('language')
  const imgPathViImg = `/images/promotion/pro_img_${imgIndex}.png`
  const imgPathThImg = `/images/promotion/pro_img_th_${imgIndex}.png`
  const imgPathEnImg = `/images/promotion/pro_img_en_${imgIndex}.png`
  if (isRegionTH()) {
    if (isLanguageTH()) {
      return (
        <ImageWithPreload
          src={imgPathThImg}
          className={styles.step_img}
          preview
          imageStyle={{
            width: rpxToPx(190),
            height: rpxToPx(404)
          }}
          width={width}
          height={height}
        />
      )
    } else {
      return (
        <ImageWithPreload
          alt={imgPathEnImg}
          src={imgPathEnImg}
          className={styles.step_img}
          preview
          imageStyle={{
            width: rpxToPx(190),
            height: rpxToPx(404)
          }}
          width={width}
          height={height}
        />
      )
    }
  } else {
    if (isLanguageVI()) {
      return (
        <ImageWithPreload
          alt={imgPathViImg}
          src={imgPathViImg}
          className={styles.step_img}
          preview
          imageStyle={{
            width: rpxToPx(190),
            height: rpxToPx(404)
          }}
          width={width}
          height={height}
        />
      )
    } else {
      return (
        <ImageWithPreload
          alt={imgPathEnImg}
          src={imgPathEnImg}
          className={styles.step_img}
          preview
          imageStyle={{
            width: rpxToPx(190),
            height: rpxToPx(404)
          }}
          width={width}
          height={height}
        />
      )
    }
  }
}

export default LangsImg
