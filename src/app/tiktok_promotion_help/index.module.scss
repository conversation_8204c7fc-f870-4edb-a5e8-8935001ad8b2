.container {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-top: 2px solid #eee;
    overflow-y: scroll;
    padding: 28px 24px 260px 24px;
    position: relative;
}

.tutorial_tips {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
    font-weight: bold;
    font-size: 30px;
    color: #FE6D45;
    background-image: url("../../../public/images/promotion/path_bg.png");
    box-sizing: border-box;
    background-size: 100% 100%;
    max-width: 702px;
    height: 76px
}

.tutorial_tips2 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
    font-weight: bold;
    font-size: 30px;
    color: #FE6D45;
    background-image: url("../../../public/images/promotion/path_bg.png");
    box-sizing: border-box;
    background-size: 100% 100%;
    max-width: 702px;
    height: 76px;
    margin-top: 60px;
}

.add_btn {
    width: 702px;
    height: 80px;
    background: #FE6D45;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 32px;
    color: #FFFFFF;
    position: fixed;
    bottom: 64px;
    padding: 24px;
    box-sizing: border-box;

}

.vertical_line {
    display: inline-block;
    width: 6px;
    height: 28px;
    background: #FE6D45;
    margin-right: 6px;
}

.tutorial_title {

    font-size: 28px;
    color: #303030;
    margin: 24px 0 0 0
}



.fullfillment_words {
    font-weight: normal;
    font-size: 26px;
    color: #303030;
    margin-top: 16px;
}

.color_words {
    font-weight: normal;
    font-size: 28px;
    color: #FE2C55;
}

.img_box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 28px;
}

.img_box_center {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 28px;
}

.fulfillment_tips1 {
    display: flex;
    margin: 42px 0 0 0;
}

.fulfillment_tip2 {
    font-weight: normal;
    font-size: 26px;
    color: #303030;
    margin-left: 12px;
}

.all_made_end {
    width: 702px;
    position: absolute;
    bottom: 60px;
    font-weight: normal;
    font-size: 24px;
    color: #9A9A9A;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.horizontal_line {
    width: 48px;
    height: 2px;
    background-color: #9a9a9a;
}

.step_img {
    width: 190px !important;
    height: 404px !important;
}