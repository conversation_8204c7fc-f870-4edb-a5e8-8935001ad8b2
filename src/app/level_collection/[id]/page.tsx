import React from 'react'
import Inner from './inner'
import { metadataTemplate, inApp } from '@/lib/server/utils'
import { i18nS } from '@/lib/server/i18n'
import { isLanguageTH, isLanguageVI } from '@/lib/utils'
import styles from './index.module.scss'

export default async function Index() {
  return <Inner />
}

export async function generateMetadata() {
  if (inApp()) {
    return metadataTemplate({ title: '' })
  }

  const description = ''
  const title = i18nS.t('Activity.For Lx Only', { level: 0 })
  let icon = `${process.env.NEXT_PUBLIC_URL}/images/newer/level_thumbnail_en.png`
  if (isLanguageTH()) {
    icon = `${process.env.NEXT_PUBLIC_URL}/images/newer/level_thumbnail_th.png`
  }
  if (isLanguageVI()) {
    icon = `${process.env.NEXT_PUBLIC_URL}/images/newer/level_thumbnail_vn.png`
  }

  return metadataTemplate({ title, description, icon })
}
