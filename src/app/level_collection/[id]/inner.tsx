'use client'
import React, { useEffect, useRef, useState } from 'react'
import Image from 'next/image'
import { Tabs } from 'antd-mobile'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { webview } from '@/lib/client/webview'
import { getToken, makeSureInApp, px2rem, scaledPx } from '@/lib/client/utils'
import { isLanguageEN, isLanguageTH } from '@/lib/utils'
import { WebviewEvents } from '@/lib/client/webview/events'
import { i18n } from '@/lib/client/i18n'
import bg_login from '@/../public/images/newer/bg_level_login.png'
import bg_login_share from '@/../public/images/newer/bg_level_login_share.png'
import ProductItem from '../components/ProductItem'
import { getUserInfo } from '@/app/api/api-uchoice/uChoice/account/current/request'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { getFreeSampleCategories } from '@/app/api/api-uchoice/home/<USER>'
import { getFreeSampleItems } from '@/app/api/api-uchoice/home/<USER>'
import { addToShowCase, checkAuth } from '@/lib/actions'
import { useRouter } from '@/lib/hooks/useRouter'
import styles from './inner.module.scss'

const Inner: React.FC = () => {
  const router = useRouter()
  const [maxTier, setMaxTier] = useState<number>(-1)
  const [activeKey, setActiveKey] = useState<string>('')
  const activeKeyRef = useRef<string>('')
  const [fetchLoading, setFetchLoading] = useState<boolean>(true)
  const [categoryList, setCategoryList] = useState<any>([])
  const [productList, setProductList] = useState([])
  const [isQualityUser, setQualityUser] = useState(true)

  const handleUserLevel = async () => {
    if (webview) {
      const token = await getToken()
      if (token) {
        const res: any = await getUserInfo()
        if (res.code == 200 && res.result) {
          const { isQualityUser, maxTier: level } = res.result
          setQualityUser(!!isQualityUser)
          setMaxTier(level || 0)
          requestProductList(level || 0)
        }
      }
    }
  }

  const requestProductList = (level: number) => {
    setFetchLoading(true)
    setProductList([])
    getFreeSampleItems({
      customCategoryId: activeKeyRef.current,
      level: level,
      pageSize: activeKeyRef.current === '' ? 999 : 50
    })
      .then((res: any) => {
        setFetchLoading(false)
        if (res.code == 200 && res.result) {
          setProductList(res.result || [])
        }
      })
      .catch((err: any) => {
        setFetchLoading(false)
      })
  }

  const requestCategoryList = async () => {
    const res = await getFreeSampleCategories()
    if (res.code === 200 && res.result) {
      const tempCategoryList = res.result.map((item: any) => {
        const { name, nameEn, id } = item
        return {
          label: isLanguageEN() ? nameEn : name,
          value: `${id}`
        }
      })
      setCategoryList([
        { label: i18n.t('HotSpot.全部'), value: '' },
        ...tempCategoryList
      ])
    }
  }

  useEffect(() => {
    toLogin()
    initState()
  }, [])

  const initState = async () => {
    if (webview) {
      const token = await getToken()
      if (token) {
        handleUserLevel()
      } else {
        setMaxTier(0)
        requestProductList(0)
      }
    } else {
      setMaxTier(0)
      requestProductList(0)
    }
    requestCategoryList()
  }

  const toLogin = async () => {
    if (webview) {
      if (!(await getToken())) {
        await webview?.send(WebviewEvents.makeSureLogined)
        window.location.reload()
      }
    } else {
      makeSureInApp()
    }
  }

  const handleOnTabChange = value => {
    setActiveKey(value)
    activeKeyRef.current = value
    requestProductList(maxTier)
  }

  const toPdp = async item => {
    if (webview) {
      if (!(await getToken())) {
        await webview?.send(WebviewEvents.makeSureLogined)
        window.location.reload()
        return
      }
      router.push(`${window.location.origin}/product/${item.id}`)
    } else {
      makeSureInApp()
    }
  }

  const onProductItemPressed = async item => {
    if (webview) {
      if (!(await getToken())) {
        await webview?.send(WebviewEvents.makeSureLogined)
        window.location.reload()
        return
      }
      toGet(item)
    } else {
      makeSureInApp()
    }
  }

  const toGet = item => {
    if (isQualityUser) {
      // free sample
      router.push(`${window.location.origin}/product/${item.id}?popup=true`)
    } else {
      // add showcase
      checkAuth(() => addToShowCase(item, 'level_collection'))
    }
  }

  return (
    <TransparentNavPage
      title={
        maxTier === -1 ? '' : i18n.t('Activity.For Lx Only', { level: maxTier })
      }
      transparent
      showBackButton
      showShareButton
      onShared={() =>
        statistic({ eventName: EventName.newer_collections_share })
      }
    >
      <div className={styles.inner_container}>
        <div className={styles.top_container}>
          <div style={{ overflow: 'hidden' }}>
            <Image
              src={webview ? bg_login : bg_login_share}
              className={styles.bg_login}
              alt=""
            ></Image>
          </div>
          <div className={styles.top_content_container}>
            {maxTier === 0 && (
              <div className={styles.logined_container}>
                <div className={styles.level_desc}>
                  ✅&nbsp;{i18n.t('Activity.L0晋升L1出单橱窗必备商品')}
                </div>
                <div className={styles.level_desc}>
                  ✅&nbsp;{i18n.t('Activity.近期热销，流量爆款')}
                </div>
                <div className={styles.level_desc}>
                  ✅&nbsp;{i18n.t('Activity.同等级达人热门带货商品')}
                </div>
              </div>
            )}
            {maxTier !== -1 && maxTier !== 0 && (
              <div className={styles.logined_container}>
                <div className={styles.level_desc}>
                  ✅&nbsp;{i18n.t('Activity.同等级达人热门带货商品')}
                </div>
                <div className={styles.level_desc}>
                  ✅&nbsp;{i18n.t('Activity.近期热销，流量爆款')}
                </div>
                <div className={styles.level_desc}>
                  ✅&nbsp;{i18n.t('Activity.高申样通过率')}
                </div>
              </div>
            )}
            <div className={styles.tab_top_border}></div>
          </div>
        </div>
        {categoryList.length !== 0 && (
          <div
            className={styles.tab_container}
            style={{
              top: webview
                ? webview?.getData().topSafeArea + scaledPx(44)
                : scaledPx(84)
            }}
          >
            <Tabs
              activeKey={activeKey}
              style={{
                '--active-line-color': '#FE6D45',
                '--active-line-height': '3px',
                '--active-title-color': '#FE6D45',
                '--title-font-size': '14px',
                '--active-line-border-radius': '0'
              }}
              onChange={handleOnTabChange}
            >
              {categoryList.map((item: any, index: number) => (
                <Tabs.Tab title={item.label} key={item.value}></Tabs.Tab>
              ))}
            </Tabs>
          </div>
        )}
        {fetchLoading && (
          <StatusView
            status={StatusViewType.loading}
            style={{ padding: '48px 0' }}
          ></StatusView>
        )}
        {!fetchLoading && productList.length === 0 && (
          <StatusView
            status={StatusViewType.empty}
            style={{ padding: '48px 0' }}
          ></StatusView>
        )}
        {productList.map((item, index) => (
          <ProductItem
            key={index}
            item={item}
            isQualityUser={isQualityUser}
            onPressed={onProductItemPressed}
            toPdp={toPdp}
          ></ProductItem>
        ))}
      </div>
    </TransparentNavPage>
  )
}

export default Inner
