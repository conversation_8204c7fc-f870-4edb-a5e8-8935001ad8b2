'use client'
import React, { useEffect, useState } from 'react'
import styles from './index.module.scss'
import HighHead from './components/high_head/high_head'
import HighTab from '@/app/components/TabCard'
import { useMounted } from '@/lib/hooks/useMounted'
import { getWebItemListByPage } from '@/app/api/api-uchoice/tt/item/highItem'
import HighItem from './components/high_item'
import { i18n } from '@/lib/client/i18n'
import { StickyHeader } from '@/components/StickyHeader'
import { useIntersectionObserver } from '@/lib/hooks/useIntersectionObserver'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { webview } from '@/lib/client/webview'

const HighSell = () => {
  const mounted = useMounted()
  const [itemInfo, setItemInfo] = useState<any>([])
  const [hasMore, setHasMore] = useState(true)
  const [pageNo, setPageNO] = useState(1)
  const [total, setTotal] = useState(0)
  const [categoryId, setCategoryId] = useState(0)
  const { ref, isLeave } = useIntersectionObserver()

  useEffect(() => {
    webItemListByPage()
  }, [])

  const handleTabClick = async item => {
    setItemInfo([])
    setPageNO(1)
    setCategoryId(item.id)
    webItemListByPage(item.id, 1)
  }
  const webItemListByPage = (id?, page?) => {
    setHasMore(true)
    getWebItemListByPage({
      pageNo: page ? page : pageNo,
      pageSize: '20',
      highCommission: true,
      categoryId: id || id === 0 ? id : categoryId
    }).then(res => {
      if (res.code == 200) {
        const list: any = res?.result?.list || []
        if (list.length == 0) {
          setHasMore(false) // 如果没有更多数据了，设置 hasMore 为 false
        }
        if (list.length > 0) {
          setTotal(res.result?.total)

          id || id === 0
            ? setItemInfo([...list])
            : setItemInfo(itemInfo.concat(list))
          setHasMore(true)
        }
      }
    })
  }
  const fetchData = () => {
    if (total == itemInfo.length) {
      setHasMore(false)
      return
    }

    let page = pageNo + 1
    setPageNO(page)
    // 模拟异步加载数据
    setTimeout(() => {
      webItemListByPage(null, page)
    }, 1000)
  }
  return (
    mounted && (
      <TransparentNavPage
        title={isLeave ? i18n.t('LowPrice.高佣榜') : ''}
        showShareButton={isLeave ? true : false}
        showBackButton={isLeave ? true : false}
        hide={!webview}
      >
        <div className={styles.high_container}>
          <div ref={ref}></div>
          <div style={{ zIndex: isLeave ? '9' : '999' }}>
            <HighHead
              titleName={i18n.t('LowPrice.highComTitle')}
              title={i18n.t('LowPrice.高佣榜')}
            ></HighHead>
          </div>
          <StickyHeader zIndex="999">
            <div className={styles.high_tab}>
              {mounted && <HighTab handleTabClick={handleTabClick}></HighTab>}
            </div>
          </StickyHeader>
          <div className={styles.high_content}>
            <HighItem
              hasMore={hasMore}
              itemInfo={itemInfo}
              fetchData={fetchData}
            ></HighItem>
          </div>
        </div>
      </TransparentNavPage>
    )
  )
}

export default HighSell
