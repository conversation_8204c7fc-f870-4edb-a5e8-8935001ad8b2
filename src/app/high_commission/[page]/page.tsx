import { metadataTemplate, inApp } from '@/lib/server/utils'
import Inner from './inner'
import React from 'react'
import { i18nS } from '@/lib/server/i18n'

export async function generateMetadata() {
  if (inApp()) {
    return metadataTemplate({ title: i18nS.t('LowPrice.高佣榜') })
  }
  const title = i18nS.t('LowPrice.高佣榜')
  const icon =
    'https://file.uchoice.pro/public/img/231219/single.png?x-oss-process=style/jpg'

  return metadataTemplate({ title, icon })
}

export default async function Index() {
  return <Inner />
}
