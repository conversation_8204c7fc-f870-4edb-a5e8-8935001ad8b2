import React, { useEffect } from 'react'
import styles from './index.module.scss'
import InfiniteScroll from 'react-infinite-scroll-component'
import Image from 'next/image'
import SerialItem from '../../../../components/Serial_item'
import { useRouter } from '@/lib/hooks/useRouter'
import { useMounted } from '@/lib/hooks/useMounted'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { i18n } from '@/lib/client/i18n'
import HandlerOnceTap from '@/lib/handlerOnceTap'
import { formatPrice } from '@/lib/format'

const HighItem = ({ itemInfo, fetchData, hasMore }) => {
  const router = useRouter()
  const mounted = useMounted()

  return (
    <div className={styles.item_box}>
      {hasMore && itemInfo.length == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.loading}></StatusView>
        </div>
      )}
      {!hasMore && itemInfo.length == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.empty}></StatusView>
        </div>
      )}
      {mounted && itemInfo && itemInfo.length > 0 && (
        <InfiniteScroll
          dataLength={itemInfo.length}
          next={fetchData}
          hasMore={hasMore}
          loader={
            <div className={styles.noMore}>{i18n.t('Common.Loading')}</div>
          }
          endMessage={
            itemInfo.length > 3 ? (
              <div className={styles.noMore}>{i18n.t('Common.Empty')}</div>
            ) : null
          }
        >
          {itemInfo.map((item, index) => {
            return (
              <div
                className={styles.item_container}
                key={index}
                onClick={() => {
                  HandlerOnceTap(() => {
                    router.push(`${window.location.origin}/product/${item.id}`)
                  })
                }}
              >
                <div className={styles.item}>
                  <SerialItem item={item} index={index}></SerialItem>
                  <div className={styles.item_content}>
                    <Image
                      unoptimized={true}
                      src={item.image}
                      alt="title"
                      width={192}
                      height={192}
                      className={styles.item_img}
                    />
                    <div className={styles.item_desc}>
                      <div className={styles.item_pruductName}>
                        {item.productName}
                      </div>
                      <div className={styles.item_prices}>
                        <div className={styles.item_comm}>
                          {i18n.t('LowPrice.佣金')}{' '}
                          <span className={styles.item_comm_num}>
                            {item.commissionRate}
                          </span>
                        </div>
                        <div className={styles.item_earn}>
                          {i18n.t('LowPrice.Earn')}{' '}
                          <span className={styles.item_earn_num}>{`${
                            item.minEarn === item.maxEarn
                              ? formatPrice(item.maxEarn, true)
                              : `${formatPrice(
                                  item.minEarn,
                                  true
                                )}~${formatPrice(item.maxEarn, true)}`
                          }`}</span>
                        </div>
                      </div>
                      {/* 公开佣金 */}
                      <div className={styles.public_commission}>
                        <div className={styles.commission_img}>
                          <span style={{ marginTop: '3px' }}>
                            {item.commissionDiff}
                          </span>
                        </div>
                        <span className={styles.plan_commission}>
                          {i18n.t('LowPrice.公开佣金')}
                          {item.planCommissionPercent
                            ? item.planCommissionPercent
                            : 0}{' '}
                        </span>
                      </div>
                      <div className={styles.item_sold_btn}>
                        <div className={styles.item_price}>
                          <span className={styles.item_price_num}>
                            {`${
                              item.maxPrice === item.minPrice
                                ? formatPrice(item.maxPrice, true)
                                : `${formatPrice(
                                    item.minPrice,
                                    true
                                  )}~${formatPrice(item.maxPrice, true)}`
                            }`}
                          </span>{' '}
                        </div>

                        <div
                          className={styles.item_btn}
                          // onClick={() => {
                          //   router.push(
                          //     `${window.location.origin}/product/${item.id}`
                          //   )
                          // }}
                        >
                          {i18n.t('LowPrice.GetFreeSample')}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </InfiniteScroll>
      )}
      {itemInfo.length > 0 && itemInfo.length < 4 && (
        <div className={styles.noMore}>{i18n.t('Common.Empty')}</div>
      )}
    </div>
  )
}

export default HighItem
