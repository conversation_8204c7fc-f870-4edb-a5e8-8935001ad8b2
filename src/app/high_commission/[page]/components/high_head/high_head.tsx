import React from 'react'
import Image from 'next/image'
import sign_left from '@/../public/images/high_commission/icon_left.png'
import sign_right from '@/../public/images/high_commission/icon_right.png'
import icon_back from '@/../public/images/high_commission/icon_back.png'
import icon_share from '@/../public/images/high_commission/icon_share.png'
import styles from './high_head.module.scss'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useRouter } from '@/lib/hooks/useRouter'
import { scaledPx } from '@/lib/client/utils'

const HighHead = ({ titleName, title }) => {
  const router = useRouter()
  return (
    <div
      className={styles.head_bg}
      style={{
        paddingTop: webview ? `${webview?.getData().topSafeArea}px` : '24px'
      }}
    >
      {webview && (
        <div className={styles.title_box}>
          <div
            onClick={() => {
              router.back()
            }}
          >
            {' '}
            <Image src={icon_back} alt="icon" className={styles.icon_back} />
          </div>

          <span className={styles.title}>{title}</span>
          <div
            onClick={() =>
              webview?.send(WebviewEvents.shareUrl, {
                url: window.location.href
              })
            }
          >
            <Image src={icon_share} alt="icon" className={styles.icon_share} />
          </div>
        </div>
      )}
      <div className={styles.icons_imgs}>
        <Image src={sign_left} alt="title" className={styles.sign_img} />
        <span className={styles.title_name}>{titleName}</span>
        <Image src={sign_right} alt="title" className={styles.sign_img} />
      </div>
    </div>
  )
}

export default HighHead
