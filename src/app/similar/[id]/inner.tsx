'use client'
import React, { useEffect, useRef, useState } from 'react'
import Image from 'next/image'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import ProductItem from './components/ProductItem'
import ProductFilter from './components/ProductFilter'
import SimilarProductList from './components/SimilarProductList'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { getSimilarProductDetail } from '@/app/api/api-uchoice/showcase/getSimilarProductDetail'
import { getSimilarProductList } from '@/app/api/api-uchoice/showcase/getSimilarProductList'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { scaledPx } from '@/lib/client/utils'
import icon_divider_left from '@/../public/images/similar/icon_divider_left.png'
import icon_divider_right from '@/../public/images/similar/icon_divider_right.png'
import list_loading from '@/../public/images/similar/list_loading.png'
import styles from './inner.module.scss'

interface Props {
  id: string
}

const Inner: React.FC<Props> = ({ id }) => {
  const [firstListFetch, setFirstListFetch] = useState<boolean>(true)
  const [currentSimilarList, setCurrentSimilarList] = useState([])
  const allSimilarListRef = useRef([])
  const [fetchDetailLoading, setFetchDetailLoading] = useState<boolean>(false)
  const [productDetail, setProductDetail] = useState({})
  const [currentActive, setCurrentActive] = useState('all')
  const currentActiveRef = useRef('all')
  const [currentOrder, setCurrentOrder] = useState('normal')
  const currentOrderRef = useRef('normal')
  const pageSize = 10

  useEffect(() => {
    getProductDetail()
    getProductList(true)
  }, [])

  const getProductDetail = async () => {
    setFetchDetailLoading(true)
    try {
      const res = await getSimilarProductDetail({ productId: id })
      setFetchDetailLoading(false)
      const { code, result } = res
      if (code == 200 && result) {
        setProductDetail(result)
      }
    } catch (error) {
      setFetchDetailLoading(false)
      console.log(error)
    }
  }

  const getProductList = async (refresh: boolean = false) => {
    if (refresh) {
      const params: any = {
        pageNo: 1,
        pageSize,
        productId: id
      }
      try {
        const res = await getSimilarProductList(params)
        setFirstListFetch(false)
        const { code, result } = res
        if (code == 200 && result) {
          const { list } = result
          allSimilarListRef.current = list || []
          setCurrentSimilarList(allSimilarListRef.current)
        }
      } catch (error) {
        setFirstListFetch(false)
      }
    } else {
      let sortDirection = currentOrderRef.current // asc升序 desc降序
      const allSimilarListTemp = JSON.parse(
        JSON.stringify(allSimilarListRef.current)
      )
      if (currentActiveRef.current === 'all') {
        setCurrentSimilarList(allSimilarListTemp)
      } else if (currentActiveRef.current === 'week') {
        if (sortDirection === 'asc') {
          setCurrentSimilarList(
            allSimilarListTemp.sort((a: any, b: any) => a.sales7d - b.sales7d)
          )
        } else {
          setCurrentSimilarList(
            allSimilarListTemp.sort((a: any, b: any) => b.sales7d - a.sales7d)
          )
        }
      } else if (currentActiveRef.current === 'comm') {
        if (sortDirection === 'asc') {
          setCurrentSimilarList(
            allSimilarListTemp.sort(
              (a: any, b: any) => a.commissionRate - b.commissionRate
            )
          )
        } else {
          setCurrentSimilarList(
            allSimilarListTemp.sort(
              (a: any, b: any) => b.commissionRate - a.commissionRate
            )
          )
        }
      } else if (currentActiveRef.current === 'price') {
        if (sortDirection === 'asc') {
          setCurrentSimilarList(
            allSimilarListTemp.sort((a: any, b: any) => a.price - b.price)
          )
        } else {
          setCurrentSimilarList(
            allSimilarListTemp.sort((a: any, b: any) => b.price - a.price)
          )
        }
      }
    }
  }

  const handleOnFilterChange = (active: string, order: string) => {
    statistic({
      eventName: EventName.similar_filter_click,
      param: {
        active: active,
        order: order
      }
    })
    setCurrentActive(active)
    setCurrentOrder(order)
    currentActiveRef.current = active
    currentOrderRef.current = order
    getProductList(false)
    const targetScrollElement = document.getElementById('targetScrollElement')
    if (targetScrollElement) {
      window.scrollTo({
        top:
          targetScrollElement.offsetTop -
          (webview
            ? webview?.getData().topSafeArea + scaledPx(44)
            : scaledPx(40)),
        behavior: 'smooth'
      })
    }
  }

  return (
    <TransparentNavPage
      title={i18n.t('Product.相似高佣商品')}
      transparent={false}
      hide={!webview}
    >
      <div className={styles.inner_container}>
        <ProductItem
          ductItem
          productDetail={productDetail}
          loading={fetchDetailLoading}
        ></ProductItem>
        <div className={styles.similar_container}>
          <div className={styles.similar_title_divider}></div>
          <div className={styles.similar_title_container}>
            <Image
              src={icon_divider_left}
              alt="icon_divider_left"
              className={styles.icon_divider_left}
            />
            <div className={styles.similar_title}>
              {i18n.t('Product.以下为相似高佣商品')}
            </div>
            <Image
              src={icon_divider_right}
              alt="icon_divider_right"
              className={styles.icon_divider_right}
            />
          </div>
          <div id="targetScrollElement"></div>
          <ProductFilter
            currentActive={currentActive}
            currentOrder={currentOrder}
            onChange={handleOnFilterChange}
            loading={firstListFetch}
          ></ProductFilter>
          {firstListFetch && (
            <Image
              src={list_loading}
              alt="list_loading"
              className={styles.list_loading}
            />
          )}
          {currentSimilarList.length > 0 && (
            <SimilarProductList
              similarList={currentSimilarList}
            ></SimilarProductList>
          )}
          {!firstListFetch && currentSimilarList.length === 0 && (
            <StatusView
              style={{ padding: '48px 0' }}
              status={StatusViewType.empty}
            ></StatusView>
          )}
        </div>
      </div>
    </TransparentNavPage>
  )
}

export default Inner
