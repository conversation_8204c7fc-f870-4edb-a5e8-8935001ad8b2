import React from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import icon_fire from '@/../public/images/similar/icon_fire.png'
import icon_divider from '@/../public/images/similar/icon_divider.png'
import detail_loading from '@/../public/images/similar/detail_loading.png'
import { formatPrice, formatSales } from '@/lib/format'
import styles from './index.module.scss'

const ProductItem = props => {
  const { productDetail, loading } = props

  return (
    <div className={styles.product_item_container}>
      {loading && (
        <Image
          src={detail_loading}
          alt="detail_loading"
          className={styles.detail_loading}
        ></Image>
      )}
      {!loading && (
        <img className={styles.thumbnail} src={productDetail.image}></img>
      )}
      {!loading && (
        <div className={styles.content_container}>
          <div className={styles.title}>{productDetail.productName || ''}</div>
          <div className={styles.sold_last_week_container}>
            <Image
              src={icon_fire}
              alt="icon_fire"
              className={styles.icon_fire}
            ></Image>
            <div className={styles.sold_last_week}>
              {i18n.t('Promotion.solddays')}{' '}
              {formatSales(productDetail.sales7d)}
            </div>
          </div>
          <div className={styles.comm_container}>
            <div className={styles.comm_label} style={{ marginRight: 4 }}>
              {i18n.t('Product.公开佣金')}
            </div>
            <div className={styles.comm_value}>{`${Math.ceil(
              productDetail.commissionRate * 100
            )}%`}</div>
          </div>
          <div className={styles.price_container}>
            <div className={styles.price}>
              {formatPrice(productDetail.price, true)}
            </div>
            <div className={styles.earn_container}>
              <div className={styles.earn_label}>{i18n.t('Product.Earn')}</div>
              <Image
                src={icon_divider}
                alt="earn_divider"
                className={styles.earn_divider}
              ></Image>
              <div className={styles.earn_value_container}>
                <div className={styles.earn_value}>
                  {formatPrice(productDetail.earn, true)}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ProductItem
