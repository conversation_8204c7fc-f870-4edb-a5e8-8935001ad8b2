'use client'
import React, { useMemo } from 'react'
import Image from 'next/image'
import classNames from 'classnames'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import { scaledPx } from '@/lib/client/utils'
import icon_order_asc from '@/../public/images/similar/icon_order_asc.png'
import icon_order_desc from '@/../public/images/similar/icon_order_desc.png'
import icon_order_normal from '@/../public/images/similar/icon_order_normal.png'
import styles from './index.module.scss'

const ProductFilter = props => {
  // currentActive: 'all' | 'week' | 'comm' | 'price'
  // currentOrder: 'asc' | 'desc'
  const { currentActive, currentOrder, onChange, loading } = props

  const onFilterChange = (active: string) => {
    if (loading) {
      return
    }
    if (active === 'all' && currentActive !== 'all') {
      onChange(active, 'normal')
      return
    }
    if (currentActive === active) {
      let order = 'normal'
      if (active === 'week' || active === 'comm') {
        if (currentOrder == 'normal') {
          order = 'desc'
        } else if (currentOrder == 'desc') {
          order = 'asc'
        } else if (currentOrder == 'asc') {
          order = 'normal'
          onChange('all', order)
          return
        }
      } else {
        if (currentOrder == 'normal') {
          order = 'asc'
        } else if (currentOrder == 'asc') {
          order = 'desc'
        } else if (currentOrder == 'desc') {
          order = 'normal'
          onChange('all', order)
          return
        }
      }
      onChange(active, order)
    } else {
      if (active === 'week' || active === 'comm') {
        onChange(active, 'desc')
      } else {
        onChange(active, 'asc')
      }
    }
  }

  const weekIcon = useMemo(() => {
    if (currentActive === 'week' && currentOrder === 'asc') {
      return icon_order_asc
    }
    if (currentActive === 'week' && currentOrder === 'desc') {
      return icon_order_desc
    }
    return icon_order_normal
  }, [currentActive, currentOrder])

  const commonIcon = useMemo(() => {
    if (currentActive === 'comm' && currentOrder === 'asc') {
      return icon_order_asc
    }
    if (currentActive === 'comm' && currentOrder === 'desc') {
      return icon_order_desc
    }
    return icon_order_normal
  }, [currentActive, currentOrder])

  const priceIcon = useMemo(() => {
    if (currentActive === 'price' && currentOrder === 'asc') {
      return icon_order_asc
    }
    if (currentActive === 'price' && currentOrder === 'desc') {
      return icon_order_desc
    }
    return icon_order_normal
  }, [currentActive, currentOrder])

  return (
    <div
      className={styles.product_filter_container}
      style={{
        top: webview
          ? webview?.getData().topSafeArea + scaledPx(44)
          : scaledPx(40)
      }}
    >
      <div
        className={styles.filter_item_container}
        onClick={() => onFilterChange('all')}
      >
        <div
          className={classNames({
            [styles.filter_item_label]: true,
            [styles.filter_item_label_active]: currentActive === 'all'
          })}
        >
          {i18n.t('Product.综合排序')}
        </div>
      </div>
      <div
        className={styles.filter_item_container}
        onClick={() => onFilterChange('week')}
      >
        <div
          className={classNames({
            [styles.filter_item_label]: true,
            [styles.filter_item_label_active]: currentActive === 'week'
          })}
        >
          {i18n.t('Product.7天销量')}
        </div>
        <Image
          className={styles.icon_order}
          alt="icon_order"
          src={weekIcon}
        ></Image>
      </div>
      <div
        className={styles.filter_item_container}
        onClick={() => onFilterChange('comm')}
      >
        <div
          className={classNames({
            [styles.filter_item_label]: true,
            [styles.filter_item_label_active]: currentActive === 'comm'
          })}
        >
          {i18n.t('Product.佣金率')}
        </div>
        <Image
          className={styles.icon_order}
          alt="icon_order"
          src={commonIcon}
        ></Image>
      </div>
      <div
        className={styles.filter_item_container}
        onClick={() => onFilterChange('price')}
      >
        <div
          className={classNames({
            [styles.filter_item_label]: true,
            [styles.filter_item_label_active]: currentActive === 'price'
          })}
        >
          {i18n.t('Product.价格')}
        </div>
        <Image
          className={styles.icon_order}
          alt="icon_order"
          src={priceIcon}
        ></Image>
      </div>
    </div>
  )
}

export default ProductFilter
