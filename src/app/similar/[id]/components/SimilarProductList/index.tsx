'use client'
import React, { useRef, useState } from 'react'
import { info } from '@/app/api/api-uchoice/tt/item/info/request'
import { loading } from '@/lib/client/loading'
import { webview } from '@/lib/client/webview'
import { WebViewToPages, WebviewEvents } from '@/lib/client/webview/events'
import { CopiedModal } from '@/app/product/components/copiedModal'
import { SkuModal } from '@/app/product/components/skuModal'
import { TikTokItemDetailVo } from '@/app/api/api-uchoice/tiktok/item/detail/dtos'
import SimilarProductItem from '../SimilarProductItem'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { checkAuth } from '@/lib/actions'
import styles from './index.module.scss'

const SimilarProductList = props => {
  const { similarList } = props
  const [itemInfo, setItemInfo] = useState<any>({})
  const itemInfoRef = useRef<any>({})

  const [skuModalVisible, setSkuModalVisible] = useState(false)
  const [copiedModalVisible, setCopiedModalVisible] = useState(false)

  const toggleSkuModal = () => {
    if (skuModalVisible) {
      setItemInfo({})
    }
    setSkuModalVisible(visible => !visible)
  }

  const toggleCopiedModal = () => {
    setCopiedModalVisible(visible => !visible)
  }

  const getProductDetail = async (id: string, callback) => {
    try {
      loading.show()
      const { code, result } = await info({ id })
      loading.hide()
      if (code === 200 && result) {
        setItemInfo(result)
        itemInfoRef.current = result
        callback()
      }
    } catch (error) {
      loading.hide()
    }
  }

  const copyLink = item => {
    if (!item.link && !item.sourceLink) {
      return
    }
    const info = {
      link: item.link || '',
      sourceLink: item.sourceLink || ''
    }
    setItemInfo(info)
    itemInfoRef.current = info
    toggleCopiedModal()
    statistic({
      eventName: EventName.similar_list_item_copy_click,
      param: {
        productId: item.id || ''
      }
    })
  }

  const requestSample = id => {
    getProductDetail(id, () => {
      checkAuth(toggleSkuModal)
    })
    statistic({
      eventName: EventName.similar_list_item_request_sample_click,
      param: {
        productId: id
      }
    })
  }

  return (
    <div className={styles.similar_product_list_container}>
      {similarList.map((item, index) => {
        return (
          <div key={index} className={styles.similar_product_item_container}>
            <SimilarProductItem
              item={item}
              copyLink={() => copyLink(item)}
              requestSample={requestSample}
            ></SimilarProductItem>
            <div className={styles.divider}></div>
          </div>
        )
      })}
      <CopiedModal
        itemInfo={itemInfo}
        visible={copiedModalVisible}
        toggle={toggleCopiedModal}
      ></CopiedModal>
      {itemInfo?.skuList?.length > 0 && (
        <SkuModal
          visible={skuModalVisible}
          toggle={toggleSkuModal}
          itemInfo={itemInfo}
          onSelectedSku={async (sku, count) => {
            // 因为数据详情和老的详情的接口字段不一样，做下兼容
            const item = itemInfo as TikTokItemDetailVo

            let convertedItemInfo = {}

            if ('isTap' in itemInfo) {
              convertedItemInfo = {
                id: `${item.ttaItemId}`,
                productId: item.productId,
                name: item.productName,
                nameEn: item.productName,
                itemType: 1,
                campaignId: '',
                homeImgUrl: item.homeImgUrl,
                skuResDtoList: [],
                itemExtensionDto: {
                  id: '',
                  content: ''
                },
                skuList: item.skuList,
                visible: true,
                totalStock: 1,
                promotionEndTime: 1,
                salesStr: '',
                commissionRate: item.creatorCommissionPercentStr,
                planCommission: '',
                maxPrice: 1.0,
                minPrice: 1.0,
                minEarn: 1.0,
                maxEarn: 1.0,
                isCollected: false,
                link: '',
                sourceLink: '',
                isShowImg: false,
                shopCode: '',
                shopName: '',
                itemLabels: []
              }
            } else {
              convertedItemInfo = {
                ...itemInfo,
                sellerId: itemInfo.sellerId ? `${itemInfo.sellerId}` : undefined
              }
            }

            await webview?.send(WebviewEvents.toPage, {
              name: WebViewToPages.sampleApply,
              params: {
                itemInfo: convertedItemInfo,
                sku,
                count: `${count}`
              }
            })
          }}
        ></SkuModal>
      )}
    </div>
  )
}

export default SimilarProductList
