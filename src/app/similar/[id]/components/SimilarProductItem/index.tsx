import React from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import { useRouter } from '@/lib/hooks/useRouter'
import { formatPrice, formatSales } from '@/lib/format'
import icon_fire from '@/../public/images/similar/icon_fire.png'
import icon_divider from '@/../public/images/similar/icon_divider.png'
import icon_logo from '@/../public/images/similar/icon_logo.png'
import icon_copy from '@/../public/images/product/detail/link.png'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import HandlerOnceTap from '@/lib/handlerOnceTap'
import styles from './index.module.scss'
import { addToShowCase, checkAuth } from '@/lib/actions'

const SimilarProductItem = props => {
  const { item, copyLink, requestSample } = props
  const router = useRouter()

  const toCopyLink = e => {
    e.stopPropagation()
    copyLink(item.id)
  }

  const toAddShowcase = e => {
    e.stopPropagation()
    checkAuth(() => addToShowCase(item, 'similar_products'))
  }

  const toRequestSample = e => {
    e.stopPropagation()
    requestSample(item.id)
  }

  return (
    <div
      className={styles.product_item_container}
      onClick={() =>
        HandlerOnceTap(() => {
          router.push(`${window.location.origin}/product/${item.id}`)
          statistic({
            eventName: EventName.similar_list_item_detail_click,
            param: {
              productId: item.id
            }
          })
        })
      }
    >
      <div className={styles.content_container}>
        <img className={styles.thumbnail} src={item.image}></img>
        <div className={styles.content_container}>
          <div className={styles.title}>{item.productName || ''}</div>
          <div className={styles.sold_last_week_container}>
            <Image
              src={icon_fire}
              alt="icon_fire"
              className={styles.icon_fire}
            ></Image>
            <div className={styles.sold_last_week}>
              {i18n.t('Product.最近一周销量')}: {formatSales(item.sales7d)}
            </div>
          </div>
          <div className={styles.comm_container}>
            <Image
              src={icon_logo}
              alt="icon_logo"
              className={styles.icon_logo}
            ></Image>
            <div className={styles.comm_label}>
              {i18n.t('Product.Youpik High Com.')}
            </div>
            <div className={styles.comm_value}>{`${Math.floor(
              item.commissionRate * 100
            )}%`}</div>
          </div>
          <div className={styles.price_container}>
            <div className={styles.price}>{formatPrice(item.price, true)}</div>
            <div className={styles.earn_container}>
              <div className={styles.earn_label}>{i18n.t('Product.Earn')}</div>
              <Image
                src={icon_divider}
                alt="earn_divider"
                className={styles.earn_divider}
              ></Image>
              <div className={styles.earn_value_container}>
                <div className={styles.earn_value}>
                  {formatPrice(item.earn, true)}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className={styles.footer_container}>
        <div className={styles.copy_container} onClick={toCopyLink}>
          <Image
            src={icon_copy}
            className={styles.icon_copy}
            alt="icon_copy"
          ></Image>
          <div className={styles.copy_label}>{i18n.t('Product.复制链接')}</div>
        </div>
        <div className={styles.right_container}>
          <div className={styles.btn_add_showcase} onClick={toAddShowcase}>
            {i18n.t('Product.添加橱窗')}
          </div>
          <div className={styles.btn_request_sample} onClick={toRequestSample}>
            {i18n.t('Product.requestSample')}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SimilarProductItem
