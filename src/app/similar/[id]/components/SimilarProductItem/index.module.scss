.product_item_container {
  display: flex;
  flex-direction: column;
  padding: 28px 24px;
  background-color: #ffffff;

  .content_container {
    display: flex;
    margin-bottom: 28px;

    .detail_loading {
      width: 702px;
      height: 300px;
    }

    .thumbnail {
      width: 200px;
      height: 200px;
      border-radius: 4px;
      margin-right: 20px;
    }

    .content_container {
      display: flex;
      flex-direction: column;
      flex: 1;

      .title {
        max-width: 482px;
        font-size: 28px;
        color: #333333;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 16px;
      }

      .sold_last_week_container {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .icon_fire {
          width: 20px;
          height: 24px;
          margin-right: 8px;
        }

        .sold_last_week {
          font-size: 24px;
          color: #8A8A8A;
          line-height: 28px;
        }
      }

      .comm_container {
        display: flex;
        align-items: center;
        height: 32px;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        margin-bottom: 24px;
        align-self: flex-start;
        background-color: #F4393B;
        padding-right: 2px;

        .icon_logo {
          width: 32px;
          height: 32px;
        }

        .comm_label {
          font-size: 22px;
          color: #FFFFFF;
          line-height: 24px;
          padding: 0 8px;
        }

        .comm_value {
          height: 28px;
          font-size: 24px;
          color: #F4393B;
          line-height: 28px;
          font-weight: bold;
          background-color: #FFFFFF;
          border-radius: 2px;
          padding: 0 8px;
        }
      }

      .price_container {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .price {
          font-size: 28px;
          color: #333333;
          line-height: 42px;
          font-weight: bold;
        }

        .earn_container {
          display: flex;
          align-items: center;
          background-color: #FE6D45;
          padding-left: 12px;
          border-radius: 2px;

          .earn_label {
            font-size: 24px;
            color: #FFFFFF;
            line-height: 34px;
          }

          .earn_divider {
            width: 28px;
            height: 44px;
          }

          .earn_value_container {
            height: 44px;
            background-color: #FFF0EC;
            display: flex;
            align-items: center;
            padding-right: 16px;

            .earn_value {
              font-size: 26px;
              color: #FE6D45;
              line-height: 36px;
              font-weight: bold;
            }
          }
        }
      }
    }
  }

  .footer_container {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .copy_container {
      display: flex;
      flex-direction: column;

      .icon_copy {
        width: 48px;
        height: 48px;
        margin-bottom: 2px;
      }

      .copy_label {
        font-size: 20px;
        color: #6E6E6E;
        line-height: 22px;
      }
    }

    .right_container {
      display: flex;
      border-radius: 4px;
      border: 2px solid #FE6D45;

      .btn_add_showcase {
        height: 76px;
        background-color: #ffffff;
        font-weight: bold;
        font-size: 24px;
        color: #FE6D45;
        line-height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 296px;
      }

      .btn_request_sample {
        height: 76px;
        background-color: #FE6D45;
        font-weight: bold;
        font-size: 24px;
        color: #FFFFFF;
        line-height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 296px;
      }
    }
  }
}