import React from 'react'
import Inner from './inner'
import { metadataTemplate, inApp } from '@/lib/server/utils'
import { getSimilarProductDetailServer } from '@/app/api/api-uchoice/showcase/getSimilarProductDetailServer'
import { i18nS } from '@/lib/server/i18n'
import styles from './index.module.scss'

interface Props {
  params: {
    id: string
  }
}

export default async function Index(props: Props) {
  return <Inner id={props.params.id} />
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: i18nS.t('Product.相似高佣商品') })
  }

  const itemInfo: any = await getSimilarProductDetailServer(params)

  const description = itemInfo.productName || ''
  const title = i18nS.t('Product.相似高佣商品')
  const icon = itemInfo.image

  return metadataTemplate({ title, description, icon })
}
