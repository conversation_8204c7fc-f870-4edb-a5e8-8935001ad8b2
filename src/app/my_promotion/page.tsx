import React, { Suspense } from 'react'
import Inner from './inner'
import StatusViewI18ns from '../components/StatusViewI18ns'
import styles from './index.module.scss'
import { metadataTemplate } from '@/lib/server/utils'
import { i18nS } from '@/lib/server/i18n'
export async function generateMetadata() {
  return metadataTemplate({
    showNativeNavBar: true,
    nativeNavBarTitle: i18nS.t('Promotion.myPromotionRecord')
  })
}

export default async function Index() {
  return (
    <Suspense
      fallback={
        <div className={styles.status_bg}>
          <StatusViewI18ns></StatusViewI18ns>
        </div>
      }
    >
      <Inner></Inner>
    </Suspense>
  )
}
