'use client'

import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { useLayoutEffect } from 'react'
import tiktok_authing from '@/../public/images/tiktok/tiktok_authing.png'
import { Image } from '@/components/Image'
import { i18n } from '@/lib/client/i18n'
import { useMounted } from '@/lib/hooks/useMounted'

export const Inner = () => {
  const { code } = useRouteParams<{ code: string }>()
  const mounted = useMounted()

  useLayoutEffect(() => {
    if (webview) {
      webview.send(WebviewEvents.tiktokAuthComplete, {
        code
      })
    }
  }, [])

  return mounted ? (
    <div className="flex w-full flex-col items-center justify-center px-[40px] pt-[400px]">
      <Image className="h-[414px] w-[654px]" src={tiktok_authing}></Image>
      <span className="mt-[14px] text-center text-[32px] text-gray">
        {i18n.t('Login.Authorizing')}
      </span>
    </div>
  ) : null
}
