import { request } from '@/lib/client/request'
import { getBaseUrl } from '@/lib/utils'

export interface ReportAnIncidentParams {
  traceId?: string
  eventName?: string
  param?: object
  path?: string
  eventType?: number
}

// 上报事件
export const reportAnIncident = (data: ReportAnIncidentParams) => {
  const truthyData = {
    ...data,
    param: data.param ? JSON.stringify(data.param) : undefined
  }

  request<unknown>({
    url: 'api-member/toonMember/reportAnIncident',
    method: 'post',
    data: truthyData
  }).catch(() => {})
}

export const reportAnIncidentYk = (data: ReportAnIncidentParams) => {
  const truthyData = {
    ...data,
    param: data.param ? JSON.stringify(data.param) : undefined
  }

  request<unknown>({
    url: 'https://api.ypkshop.com/api-member/toonMember/reportAnIncident',
    method: 'post',
    data: truthyData
  }).catch(() => {})
}

export const sendBeaconForReportAnIncident = (body: any) => {
  const url = `${getBaseUrl()}/api-member/toonMember/reportAnIncident`

  const headers = {
    type: 'application/json'
  }
  const blob = new Blob([JSON.stringify(body)], headers)
  navigator.sendBeacon(url, blob)
}

export const sendBeaconForReportAnIncidentYk = (body: any) => {
  const url = `https://api.ypkshop.com/api-member/toonMember/reportAnIncident`

  const headers = {
    type: 'application/json'
  }
  const blob = new Blob([JSON.stringify(body)], headers)
  navigator.sendBeacon(url, blob)
}
