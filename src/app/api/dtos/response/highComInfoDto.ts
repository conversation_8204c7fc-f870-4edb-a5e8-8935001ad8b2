export interface HighComInfoDto {
  isProductCategory: any
  categoryItems: any
  ttaItems: any
  total: number
  list: ResDto[]
}

interface SkuResDto {}
interface ResDto {
  total: number
  anchorCount: string
  campaignId: string
  collectionTime: null | string
  commissionRate: string
  creatorCommissionPercent: number
  customCategoryId: number
  esSkus: SkuResDto[]
  id: string
  image: string
  isApplied: null | boolean
  isCollected: boolean
  isExpired: null | boolean
  isNewCampaign: boolean
  isShowcaseDisabled: boolean
  itemLabels: any[] // 根据实际数据结构补充
  itemSort: null | any // 根据实际数据结构补充
  itemSubsidyDTO: null | any // 根据实际数据结构补充
  labelType: number[] // 根据实际数据结构补充
  link: string
  maxEarn: number
  maxPrice: number
  minEarn: number
  minPrice: number
  productId: string
  productName: string
  productStatus: number
  promotionEndTime: number
  promotionStartTime: number
  sales: number
  salesForLast30Days: number
  salesForLast30DaysStr: string
  salesForTwoWeeksRate: string
  salesStr: string
  shopCode: string
  shopName: string
  shopStatus: number
  sourceLink: string
  stock: number
  updateTime: null | any // 根据实际数据结构补充
  youpikStatus: number
  isProductCategory: boolean
}
