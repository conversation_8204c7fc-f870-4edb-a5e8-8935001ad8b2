import {
  ShareLinkItemCategory,
  TtCustomCategory
} from '@/app/api/api-uchoice/rankings/getTapRankingList/dtos'
import { request } from '@/lib/client/request'

// 获取分类tab信息
export const getTtaCategoryInfo = async () => {
  return await request<TtCustomCategory[]>({
    url: 'api-uchoice/tt/item/getTtaCategoryInfo',
    method: 'get'
  })
}
//获取TikTok今日热卖榜类目列表
export const getShareLinkItemCategory = async () => {
  return await request<ShareLinkItemCategory[]>({
    url: 'api-uchoice/tt/category/getShareLinkItemCategory',
    method: 'get'
  })
}
export const showcaseInfo = async (query: any) => {
  return await request<any>({
    url: 'api-uchoice/tt/item/showcaseInfo',
    method: 'get',
    params: query
  })
}
