import { request } from '@/lib/client/request'
import { ApiResponse } from '@/lib/types/http'

/**
 * 用户地址信息
 */
export interface UChoiceMemberAddressDTO {
  /**
   * 详细地址
   */
  addressDetail?: string
  /**
   * 市Code
   */
  cityCode?: string
  /**
   * 市Id
   */
  cityId?: number
  /**
   * 市名称
   */
  cityName?: string
  /**
   * 市英文名称
   */
  cityNameEn?: string
  /**
   * 街道code
   */
  districtCode?: string
  /**
   * 街道Id
   */
  districtId?: number
  /**
   * 街道
   */
  districtName?: string
  /**
   * 街道英文名称
   */
  districtNameEn?: string
  /**
   * 姓名
   */
  fullName?: string
  /**
   * id
   */
  id?: number
  /**
   * 是否默认地址
   */
  isDefault?: boolean
  /**
   * LineId
   */
  lineId?: string
  /**
   * 手机号
   */
  mobile?: string
  /**
   * 邮编
   */
  postCode?: string
  /**
   * 省code
   */
  provinceCode?: string
  /**
   * 省Id
   */
  provinceId?: number
  /**
   * 省名称
   */
  provinceName?: string
  /**
   * 省英文名称
   */
  provinceNameEn?: string
}

/**
 * 样品订单提交参数
 */
export interface SampleOrderDto {
  commercialOrder?: boolean
  /**
   * 商单id(商单时必传)
   */
  commercialOrderId?: number
  /**
   * 申请数量
   */
  count?: number
  /**
   * 达人id(商单时必传)
   */
  expertInviteId?: number
  /**
   * id (非商品编号,为商品主键Id)
   */
  id?: number
  /**
   * 用户地址信息
   */
  memberAddress?: UChoiceMemberAddressDTO
  /**
   * 可履约方式: 1-视频 2-直播 3-直播&视频
   */
  performanceWay?: number
  /**
   * 销售数据凭证,app申样时前端没传这个值
   */
  salesDataVoucher?: string
  /**
   * skuId
   */
  skuId?: number
  /**
   * 来源: 4-商单
   */
  source?: number
  /**
   * gmv如果由系统爬取的,需要上传改字段
   */
  tapSearchRecordId?: number
  /**
   * tiktok用户唯一id
   */
  unionId?: string
}

// 提交订单V3
export const submitOrderV3 = async (params: SampleOrderDto) => {
  return await request<number>({
    url: '/api-uchoice/order/user/submitOrderV3',
    method: 'post',
    data: params
  })
}
