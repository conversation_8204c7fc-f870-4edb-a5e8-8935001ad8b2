import { request } from '@/lib/client/request'
import { ApiResponse } from '@/lib/types/http'

/**
 * 商单打款收款人详情信息
 */
export interface CommercialOrderPaymentRecipientDetailVo {
  /**
   * 审核状态 1-待审核 2-待打款 3-已打款 4-审核拒绝
   */
  auditStatus?: number
  /**
   * 收款卡号
   */
  bankAccountNumber?: string
  /**
   * 银行卡照片,隔开
   */
  bankCardImg?: string
  /**
   * 收款银行名称
   */
  bankName?: string
  /**
   * 公司营业执照,隔开
   */
  businessLicenseImg?: string
  /**
   * 收款人性别 0-先生 1-女士 3-小姐
   */
  gender?: number
  /**
   * 主键ID
   */
  id?: number
  /**
   * 身份证居住地址
   */
  idCardAddress?: string
  /**
   * 身份证照片,隔开
   */
  idCardImg?: string
  /**
   * 身份证姓名
   */
  idCardName?: string
  /**
   * 身份证号码
   */
  idCardNumber?: string
  /**
   * 联系电话
   */
  phone?: string
  /**
   * 审核原因
   */
  reason?: string
  /**
   * 打款类型: 0-个人, 1-公司
   */
  recipientType?: number
}

/**
 * 履约审核信息
 */
export interface CommercialOrderPerformanceReviewInfoVo {
  /**
   * 时长
   */
  duration?: boolean
  /**
   * Hashtag达标
   */
  hashTag?: boolean
  /**
   * 挂链
   */
  showcase?: boolean
}

/**
 * 履约详情信息
 */
export interface CommercialOrderPerformanceListVo {
  /**
   * 投流码
   */
  adsCode?: string
  /**
   * 达人唯一名
   */
  displayName?: string
  /**
   * 主键id
   */
  id?: number
  /**
   * 审核操作人id
   */
  operatorId?: number
  /**
   * 审核操作人名称
   */
  operatorName?: string
  /**
   * 销量
   */
  paidOrderNum?: number
  /**
   * 履约内容编号
   */
  performanceNo?: number
  /**
   * pic_id
   */
  picId?: number
  /**
   * pic名称
   */
  picName?: string
  /**
   * 拒绝code码
   */
  rejectCodeList?: string[]
  /**
   * 其他拒绝理由内容
   */
  rejectRemarks?: string
  /**
   * 审核信息
   */
  reviewInfoVo?: CommercialOrderPerformanceReviewInfoVo
  /**
   * 审核状态: 0-待审核 1-合格 2-不合格
   */
  status?: number
  /**
   * 视频id
   */
  videoId?: number
  /**
   * 视频标题
   */
  videoTitle?: string
  /**
   * 视频url
   */
  videoUrl?: string
  /**
   * 播放数
   */
  viewCount?: number
}

/**
 * 商单样品订单信息
 */
export interface CommercialOrderSampleOrderVo {
  /**
   * Ads code是否必须 0:否 1:是
   */
  adsCodeRequired?: number
  /**
   * 商单ID
   */
  commercialOrderId?: number
  /**
   * 商单内容数量要求
   */
  contentCount?: number
  /**
   * 达人邀约ID
   */
  expertInviteId?: number
  /**
   * 合作报价
   */
  inviteQuotation?: number
  /**
   * 商单名称
   */
  name?: string
  /**
   * 打款信息
   */
  paymentInfo?: CommercialOrderPaymentRecipientDetailVo
  /**
   * 履约列表
   */
  performanceList?: CommercialOrderPerformanceListVo[]
  /**
   * 商单履约流程值: 0-没有 1-寄样 2-发布履约视频 3-平台审核履约内容 4-达人上传收款信息 5-Youpik打款
   */
  performanceStatus?: number
}

/**
 * OrderInfoVo
 */
export interface OrderInfoVo {
  /**
   * 头像
   */
  avatar?: string
  /**
   * 中文拒绝原因
   */
  cnReason?: string
  /**
   * 商单信息
   */
  commercialOrderInfo?: CommercialOrderSampleOrderVo
  /**
   * 佣金
   */
  commissionFee: number
  /**
   * 佣金率 eg:15%
   */
  commissionRate: string
  /**
   * 快递公司
   */
  courierCompany?: string
  /**
   * 申请时间
   */
  createTime: number
  /**
   * 发货时间
   */
  deliverTime?: number
  /**
   * 投流状态
   */
  deliveryTrafficStatus?: number
  /**
   * 英文拒绝原因
   */
  enReason?: string
  expressDeliveryVo?: ExpressDeliveryVo
  /**
   * 近一个月GMV
   */
  gmvRecentMonthStr?: string
  /**
   * 主键
   */
  id: number
  /**
   * 商品模块
   */
  itemInfos: OrderItemInfoVo[]
  /**
   * 橱窗链接
   */
  link?: string
  /**
   * 最高GMV(销售额范围)
   */
  maxAmount?: number
  /**
   * member account表主键
   */
  memberAccountId?: number
  memberAddress: UChoiceMemberAddressDTO
  /**
   * 最低GMV(销售额范围)
   */
  minAmount?: number
  /**
   * 达人昵称
   */
  nickname?: string
  /**
   * 粉丝数量
   */
  numberOfFans: string
  orderDetailFlowInfo?: OrderDetailFlowInfo
  /**
   * 订单号
   */
  orderNo: number
  /**
   * 可履约方式: 0:未选择 1-视频 2-直播 3-直播&视频
   */
  performanceWay: number
  promiseInfoVo?: PromiseInfoVo
  /**
   * 待履约状态: 0-正常 1-未挂橱窗 2-等待内容发布
   */
  promiseStatus?: number
  /**
   * 拒绝原因code
   */
  reasonCode?: string
  /**
   * 平台审核时间
   */
  reviewTime?: number
  /**
   * 销售数据凭证
   */
  salesDataVoucher: string
  /**
   * 更新凭证时间
   */
  salesDataVoucherTime?: number
  /**
   * 样品销售类型：1-视频 2-直播 3-直播&视频
   */
  sampleSaleType: number
  /**
   * 来源：1-用户申请 2-OPS添加 3-TAP申样订单 4-商单
   */
  source: number
  /**
   * 商品状态：-1000->已拒绝 -5000->履约失败 1000->待平台审核 2000->待商家审核 2001->已通过(待发货) 3000->已发货(待履约) 3500->履约审核中 4000->已完成
   */
  status: number
  /**
   * 供应商审核时间
   */
  supplierReviewTime?: number
  /**
   * 系统gmv
   */
  systemCrawlerGmv?: number
  /**
   * 泰语拒绝原因
   */
  thReason?: string
  /**
   * 层级
   */
  tier?: number
  /**
   * 快递单号
   */
  trackingNumber?: string
  /**
   * 达人ID
   */
  unionId: string
  /**
   * 更新时间
   */
  updateTime: number
  /**
   * 投流视频id
   */
  videoId?: string
  /**
   * 越南语拒绝原因
   */
  vnReason?: string
  [property: string]: any
}

/**
 * 商单信息
 */
export interface CommercialOrderInfo {
  /**
   * 商单ID
   */
  commercialOrderId?: number
  /**
   * 商单标题
   */
  title?: string
  /**
   * 商单状态
   */
  status?: number
  /**
   * 商单类型
   */
  type?: number
  /**
   * 商单金额
   */
  amount?: number
  /**
   * 货币类型
   */
  currency?: string
  /**
   * 品牌信息
   */
  brandInfo?: {
    brandName?: string
    brandLogo?: string
    [property: string]: any
  }
  /**
   * 履约要求
   */
  requirements?: {
    contentRequirements?: string
    platforms?: string[]
    deadline?: number
    [property: string]: any
  }
  /**
   * 创建时间
   */
  createTime?: number
  [property: string]: any
}

/**
 * ExpressDeliveryVo
 */
export interface ExpressDeliveryVo {
  /**
   * 快递公司
   */
  courierCompany?: string
  /**
   * 增补快递公司
   */
  courierCompanyAdd?: string
  /**
   * 提交时间
   */
  createTime?: number
  /**
   * 增补提交时间
   */
  createTimeAdd?: number
  /**
   * 是否已增补物流
   */
  isLogisticsAdded?: boolean
  tapDeliverInfo?: TapExpressDeliverInfoVo
  /**
   * 快递单号
   */
  trackingNumber?: string
  /**
   * 增补快递单号
   */
  trackingNumberAdd?: string
  [property: string]: any
}

/**
 * TapExpressDeliverInfoVo
 */
export interface TapExpressDeliverInfoVo {
  /**
   * 预计送达时间(开始)
   */
  etaTimeMax?: number
  /**
   * 预计送达时间(结束)
   */
  etaTimeMin?: number
  /**
   * 物流信息
   */
  logisticsDetails?: Logistics[]
  /**
   * 物流承运商
   */
  providerName?: string
  /**
   * 申样数量
   */
  quantity?: number
  [property: string]: any
}

/**
 * Logistics
 */
export interface Logistics {
  /**
   * 详细描述
   */
  standard?: string
  /**
   * 时间
   */
  time?: number
  /**
   * 标题
   */
  title?: string
  [property: string]: any
}

/**
 * OrderItemInfoVo
 */
export interface OrderItemInfoVo {
  /**
   * 佣金
   */
  commissionFee?: number
  /**
   * 数量
   */
  count?: number
  /**
   * 商品Id
   */
  itemId?: number
  /**
   * 商品名称
   */
  itemTitle?: string
  /**
   * 商品图片
   */
  itemUrl?: string
  /**
   * 规格
   */
  specifications?: string
  /**
   * 单价
   */
  unitFee?: number
  [property: string]: any
}

/**
 * UChoiceMemberAddressDTO
 */
export interface UChoiceMemberAddressDTO {
  /**
   * 详细地址
   */
  addressDetail?: string
  /**
   * 市Code
   */
  cityCode?: string
  /**
   * 市Id
   */
  cityId?: number
  /**
   * 市名称
   */
  cityName?: string
  /**
   * 市英文名称
   */
  cityNameEn?: string
  /**
   * 街道code
   */
  districtCode?: string
  /**
   * 街道Id
   */
  districtId?: number
  /**
   * 街道
   */
  districtName?: string
  /**
   * 街道英文名称
   */
  districtNameEn?: string
  /**
   * 姓名
   */
  fullName?: string
  /**
   * id
   */
  id?: number
  /**
   * 是否默认地址
   */
  isDefault?: boolean
  /**
   * LineId
   */
  lineId?: string
  /**
   * 手机号
   */
  mobile?: string
  /**
   * 邮编
   */
  postCode?: string
  /**
   * 省code
   */
  provinceCode?: string
  /**
   * 省Id
   */
  provinceId?: number
  /**
   * 省名称
   */
  provinceName?: string
  /**
   * 省英文名称
   */
  provinceNameEn?: string
  [property: string]: any
}

/**
 * OrderDetailFlowInfo
 */
export interface OrderDetailFlowInfo {
  flowManagementVo?: FlowManagementAppVo
  /**
   * 投流状态: 1-投流未达标 2-已达标,未投流 3-投流奖励已发放 4-名额未释放 5-名额已释放
   */
  type?: number
  /**
   * 投流视频id
   */
  videoId?: string
  [property: string]: any
}

/**
 * FlowManagementAppVo
 */
export interface FlowManagementAppVo {
  /**
   * 投流金额
   */
  flowAmount?: string
  /**
   * GMV
   */
  gmv?: string
  /**
   * 是否开放投流奖励
   */
  isRewardEnabled?: boolean
  /**
   * 商品出单数
   */
  ordersCount?: string
  /**
   * 订单销量阈值
   */
  sampleThreshold?: string
  /**
   * 视频播放数
   */
  videoPlayCount?: string
  [property: string]: any
}

/**
 * PromiseInfoVo
 */
export interface PromiseInfoVo {
  /**
   * 是否被拒绝过
   */
  isRejected?: boolean
  /**
   * 直播ID
   */
  liveId?: string
  /**
   * 直播链接
   */
  liveLink?: string
  /**
   * 直播截图
   */
  liveScreenshotUrl?: string
  /**
   * 未拒绝数量
   */
  notRejectedCount?: number
  /**
   * 待履约订单数
   */
  pendingFulfillmentOrderCount?: number
  /**
   * 履约率
   */
  performanceRate?: string
  /**
   * 履约率比例
   */
  performanceRatio?: number
  /**
   * 履约类型：1-app上传 2-订单回溯 3-PIC
   */
  performanceType?: number
  /**
   * 可履约方式: 1-视频 2-直播 3-直播&视频
   */
  performanceWay?: number
  /**
   * 已履约数量
   */
  performedCount?: number
  /**
   * 已发货订单数
   */
  shippedOrderCount?: number
  /**
   * 直播时间
   */
  timeList?: LiveTime[]
  /**
   * 总订单数
   */
  totalOrderCount?: number
  /**
   * 视频Id
   */
  videoId?: string
  /**
   * 视频链接
   */
  videoUrl?: string
  [property: string]: any
}

/**
 * LiveTime
 */
export interface LiveTime {
  /**
   * 结束时间
   */
  endTime?: number
  /**
   * 开始时间
   */
  startTime?: number
  [property: string]: any
}

// 获取申样条件
export const getOrderInfoByOrderNo = async (orderNo: string) => {
  return await request<OrderInfoVo>({
    url: '/api-uchoice/order/user/getOrderInfoByOrderNo',
    method: 'get',
    params: {
      orderNo
    }
  })
}
