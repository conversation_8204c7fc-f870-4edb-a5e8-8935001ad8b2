/**
 * TikTokApiShowcaseAccountVo
 */
export interface TikTokApiShowcaseAccountVo {
  /**
   * 授权账号列表
   */
  accountList?: TikTokShopAddShowcaseAccountVo[]
  /**
   * 状态
   */
  status?: TikTokApiShowcaseAccountStatus
}

/**
 * TikTokShopAddShowcaseAccountVo
 */
export interface TikTokShopAddShowcaseAccountVo {
  /**
   * 用户头像
   */
  avatar?: string
  /**
   * 用户名称
   */
  displayName?: string
  /**
   * 粉丝数
   */
  followerCount?: number
  /**
   * 是否默认选中
   */
  isChoose?: boolean
  /**
   * 用户关联表id
   */
  memberAccountId?: number
  /**
   * 账号状态
   */
  status?: TikTokShopAccountStatus
}

export enum TikTokApiShowcaseAccountStatus {
  Normal = 1,
  NoTTAccount = 2,
  AuthExpired = 3
}

export enum TikTokShopAccountStatus {
  Normal = 1,
  AuthExpired = 2,
  NoShowcasePermission = 3
}
