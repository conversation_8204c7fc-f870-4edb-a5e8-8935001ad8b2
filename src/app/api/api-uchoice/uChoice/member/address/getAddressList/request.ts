import { request } from '@/lib/client/request'

/**
 * UChoiceMemberAddressVo
 */
export interface UChoiceMemberAddressVo {
  /**
   * 详细地址
   */
  addressDetail?: string
  /**
   * 市Code
   */
  cityCode?: string
  /**
   * 市Id
   */
  cityId?: number
  /**
   * 市名称
   */
  cityName?: string
  /**
   * 市英文名称
   */
  cityNameEn?: string
  /**
   * 街道code
   */
  districtCode?: string
  /**
   * 街道Id
   */
  districtId?: number
  /**
   * 街道
   */
  districtName?: string
  /**
   * 街道英文名称
   */
  districtNameEn?: string
  /**
   * 收件人名称
   */
  fullName?: string
  /**
   * 地址id
   */
  id?: number
  /**
   * 是否默认地址
   */
  isDefault?: boolean
  /**
   * LineId
   */
  lineId?: string
  /**
   * 用户id
   */
  memberId?: number
  /**
   * 手机号
   */
  mobile?: string
  /**
   * 邮编
   */
  postCode?: string
  /**
   * 省code
   */
  provinceCode?: string
  /**
   * 省Id
   */
  provinceId?: number
  /**
   * 省名称
   */
  provinceName?: string
  /**
   * 省英文名称
   */
  provinceNameEn?: string
  [property: string]: any
}

/**
 * 获取地址列表
 */
export const getAddressList = () => {
  return request<UChoiceMemberAddressVo[]>({
    url: '/api-uchoice/uChoice/member/address/getAddressList',
    method: 'get'
  })
}
