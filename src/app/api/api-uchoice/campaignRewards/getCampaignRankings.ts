import { request } from '@/lib/client/request'

// 获取活动详情信息
export const getCampaignRankings = async (params: any) => {
  return await request<any>({
    url: '/api-uchoice/campaignRewards/getCampaignRankings',
    method: 'get',
    params
  })
}

export const getCouponList = async () => {
  return await request<any>({
    url: '/api-mall/lazada/coupon/getCouponList',
    method: 'get'
  })
}

export const receiveCoupon = async (params: any) => {
  return await request<any>({
    url: '/api-mall/lazada/coupon/receiveCoupon',
    method: 'get',
    params
  })
}

export const lazadaUrl = async (params: any) => {
  return await request<any>({
    url: '/api-mall/lazada/affiliate/item/lazadaUrl',
    method: 'get',
    params
  })
}
