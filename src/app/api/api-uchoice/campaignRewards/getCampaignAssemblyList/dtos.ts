export interface CampaignAssemblyListResult {
  /**
   * 直播活动集合列表
   */
  liveList: CampaignDto[]
  /**
   * 视频活动集合列表
   */
  videoList: CampaignDto[]
}

export interface CampaignDto {
  /**
   * 活动id
   */
  campaignId: number
  /**
   * 结束时间
   */
  endTime: number
  /**
   * 是否主会场
   */
  isMainVenue: boolean
  /**
   * logo
   */
  logo: string
  /**
   * 开始时间
   */
  startTime: number
  /**
   * 状态：0-已关闭 1-活动未开始 2-活动中 3-活动结束
   */
  status: number
  /**
   * 活动标题
   */
  title: string
  [property: string]: any
}
