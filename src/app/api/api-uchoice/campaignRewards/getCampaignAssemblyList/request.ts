import { request } from '@/lib/client/request'
import { CampaignAssemblyListResult } from './dtos'
import { fetcher } from '@/lib/server/fetcher'

// 获取活动集合
export const getCampaignAssemblyList = async () => {
  return await request<CampaignAssemblyListResult>({
    url: '/api-uchoice/campaignRewards/getCampaignAssemblyList',
    method: 'get'
  })
}

export const getCampaignAssemblyListServer = async () => {
  return await fetcher(
    `api-uchoice/campaignRewards/getCampaignAssemblyList`
  )
}
