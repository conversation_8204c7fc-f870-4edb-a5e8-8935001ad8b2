import { request } from '@/lib/client/request'
import { ApiResponse } from '@/lib/types/http'

/**
 * 商单履约内容URL信息
 */
export interface CommercialOrderPerformanceUrlDTO {
  /**
   * 投流码
   */
  adsCode?: string
  /**
   * 主键id(接口又返回的话, 需要传上来)
   */
  id?: number
  /**
   * 履约内容编号
   */
  performanceNo: number
  /**
   * 视频url
   */
  videoUrl: string
}

/**
 * app上传商单履约信息参数
 */
export interface CommercialOrderPerformanceInfoAppDTO {
  /**
   * 订单号
   */
  orderNo: number
  /**
   * 履约内容信息
   */
  performance: CommercialOrderPerformanceUrlDTO
}

/**
 * 上传商单履约信息响应项
 */
export interface OpsUploadCommercialOrderVo {
  /**
   * 错误码: 0-链接无效 1-视频链接重复 2-adsCode码重复
   */
  errorCode?: number
  /**
   * 履约内容编号
   */
  performanceNo?: number
}

// app上传商单履约信息
export const submitCommercialPerformanceInfo = async (
  params: CommercialOrderPerformanceInfoAppDTO
) => {
  return await request<OpsUploadCommercialOrderVo[]>({
    url: '/api-uchoice/manager/commercial/performance/submit/info',
    method: 'post',
    data: params
  })
}
