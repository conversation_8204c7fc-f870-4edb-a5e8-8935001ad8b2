import { request } from '@/lib/client/request'
import { ApiResponse } from '@/lib/types/http'

/**
 * 履约详情查询参数
 */
export interface CommercialPerformanceDetailParams {
  /**
   * id
   */
  id: number
}

/**
 * 履约审核信息
 */
export interface CommercialOrderPerformanceReviewInfoVo {
  /**
   * 时长
   */
  duration?: boolean
  /**
   * Hashtag达标
   */
  hashTag?: boolean
  /**
   * 挂链
   */
  showcase?: boolean
}

/**
 * 履约详情信息
 */
export interface CommercialOrderPerformanceListVo {
  /**
   * 投流码
   */
  adsCode?: string
  /**
   * 达人唯一名
   */
  displayName?: string
  /**
   * 主键id
   */
  id?: number
  /**
   * 审核操作人id
   */
  operatorId?: number
  /**
   * 审核操作人名称
   */
  operatorName?: string
  /**
   * 销量
   */
  paidOrderNum?: number
  /**
   * 履约内容编号
   */
  performanceNo?: number
  /**
   * pic_id
   */
  picId?: number
  /**
   * pic名称
   */
  picName?: string
  /**
   * 拒绝code码
   */
  rejectCodeList?: string[]
  /**
   * 其他拒绝理由内容
   */
  rejectRemarks?: string
  /**
   * 审核信息
   */
  reviewInfoVo?: CommercialOrderPerformanceReviewInfoVo
  /**
   * 审核状态: 0-待审核 1-合格 2-不合格
   */
  status?: number
  /**
   * 视频id
   */
  videoId?: number
  /**
   * 视频标题
   */
  videoTitle?: string
  /**
   * 视频url
   */
  videoUrl?: string
  /**
   * 播放数
   */
  viewCount?: number
}

// 查看履约详情信息
export const getCommercialPerformanceDetail = async (
  params: CommercialPerformanceDetailParams
) => {
  return await request<CommercialOrderPerformanceListVo>({
    url: '/api-uchoice/manager/commercial/performance/detail',
    method: 'get',
    params
  })
}
