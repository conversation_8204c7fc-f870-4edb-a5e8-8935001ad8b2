import {
  AnchorIdParams,
  GetTiktokAnchorSimilarParams,
  TikTokSimilarAnchorVo,
  TiktokAnchorDetailVo
} from './dtos'
import { request } from '@/lib/client/request'

// 获取TikTok达人详情
export const getTiktokAnchorDetail = async (query: AnchorIdParams) => {
  return await request<TiktokAnchorDetailVo>({
    url: 'api-uchoice/tiktok/anchor/getTiktokAnchorDetail',
    method: 'get',
    params: query
  })
}

export const getTiktokAnchorSimilar = async (
  query: GetTiktokAnchorSimilarParams
) => {
  return await request<TikTokSimilarAnchorVo[]>({
    url: 'api-uchoice/tiktok/anchor/getTiktokAnchorSimilar',
    method: 'get',
    params: query
  })
}
