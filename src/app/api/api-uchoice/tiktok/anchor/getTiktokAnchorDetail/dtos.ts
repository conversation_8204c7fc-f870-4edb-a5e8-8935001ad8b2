export interface AnchorIdParams {
  anchorId: string
}

/**
 * TiktokAnchorDetailVo
 */
export interface TiktokAnchorDetailVo {
  /**
   * 达人id
   */
  anchorId: string
  /**
   * 达人账号页面
   */
  anchorUrl?: string
  /**
   * 达人头像
   */
  avatar: string
  /**
   * 达人分类
   */
  categoryName?: string
  /**
   * 带货类目
   */
  categoryProductName?: string
  /**
   * id
   */
  displayName: string
  /**
   * 昵称
   */
  nickname: string
  /**
   * 总粉丝数
   */
  followerCount?: number
  /**
   * 粉丝画像数据json
   */
  followerDataJson: string
  /**
   * 近30天直播平均评论数
   */
  liveAvgCommentCount?: number
  /**
   * 近30天直播平均点赞数
   */
  liveAvgLikeCount?: number
  /**
   * 近30天电商直播平均观众数
   */
  liveAvgUv?: number
  /**
   * 近30天电商直播次数
   */
  liveCount?: number
  /**
   * 预估直播销售额
   */
  liveGmv?: number
  /**
   * 直播gpm最大值
   */
  maxLiveGpm?: number
  /**
   * 视频gpm最大值
   */
  maxVideoGpm?: number
  /**
   * 直播gpm最小值
   */
  minLiveGpm?: number
  /**
   * 视频gpm最小值
   */
  minVideoGpm?: number
  /**
   * 总销售额
   */
  totalGmv?: number
  trendOfFollowerGrowth: TiktokFollowerVo
  trendOfTotalFanBase: TiktokFollowerVo
  /**
   * 近30天视频平均评论数
   */
  videoAvgCommentCount?: number
  /**
   * 近30天视频平均点赞数
   */
  videoAvgLikeCount?: number
  /**
   * 近30天视频平均播放次数
   */
  videoAvgViewCount?: number
  /**
   * 近30天发布视频数
   */
  videoCount?: number
  /**
   * 预估视频销售额
   */
  videoGmv?: number
  productCategoryNameList: string[]
}

/**
 * TiktokFollowerVo
 */
export interface TiktokFollowerVo {
  /**
   * 粉丝变化数
   */
  followerCount: number
  /**
   * 粉丝变化趋势
   */
  followerData: TikTokFollowerDataVo[]
  /**
   * 粉丝变化率
   */
  followerRate: number
  [property: string]: any
}

/**
 * TikTokFollowerDataVo
 */
export interface TikTokFollowerDataVo {
  /**
   * 日期
   */
  date: number
  /**
   * 粉丝数
   */
  followerCount: number
  [property: string]: any
}

export interface GetTiktokAnchorSimilarParams {
  anchorId: string
  minAnchorId: string
}

/**
 * TikTokSimilarAnchorVo
 */
export interface TikTokSimilarAnchorVo {
  /**
   * 达人id
   */
  anchorId: number
  /**
   * 头像
   */
  avatar: string
  /**
   * 昵称
   */
  displayName: string
  /**
   * 总粉丝数
   */
  followerCount: number
  /**
   * 直播观众数
   */
  liveUv: number
  /**
   * 视频点赞数
   */
  videoLikeCount: number
  [property: string]: any
}
