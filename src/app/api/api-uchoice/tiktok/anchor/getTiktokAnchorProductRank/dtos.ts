export interface GetTiktokAnchorProductParams {
  /**
   * 类目
   */
  customCategoryId?: number
  /**
   * 日期类型：1-日 2-周 3-月
   */
  dateType?: number
  /**
   * 结束时间
   */
  endTime?: number
  /**
   * 最高 GMV
   */
  maxGmv?: number
  /**
   * 最低 GMV
   */
  minGmv?: number
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 排行榜类型：1-带货达人榜 2-涨粉达人榜
   */
  rankType: number
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * 开始时间
   */
  startTime?: number
  [property: string]: any
}

export interface TikTokAnchorProductRankVo {
  /**
   * 达人id
   */
  anchorId: number
  /**
   * 达人头像
   */
  avatar: string
  /**
   * 类目名称
   */
  categoryName?: string
  /**
   * 昵称
   */
  displayName: string
  /**
   * 总粉丝数
   */
  followerCount: number
  /**
   * 总销售额
   */
  gmv?: number
  /**
   * 排名
   */
  rankNo?: number
  /**
   * 粉丝增量
   */
  risingPowder?: number
  [property: string]: any
}
