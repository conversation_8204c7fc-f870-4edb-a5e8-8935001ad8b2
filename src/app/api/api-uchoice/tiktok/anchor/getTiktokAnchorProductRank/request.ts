import { ListResult } from '@/lib/types/http'

import { GetTiktokAnchorProductParams, TikTokAnchorProductRankVo } from './dtos'
import { request } from '@/lib/client/request'

export const getTiktokAnchorRank = async (
  query: GetTiktokAnchorProductParams
) => {
  return (await request)<ListResult<TikTokAnchorProductRankVo>>({
    url: 'api-uchoice/tiktok/anchor/getTiktokAnchorRank',
    method: 'get',
    params: query
  })
}
