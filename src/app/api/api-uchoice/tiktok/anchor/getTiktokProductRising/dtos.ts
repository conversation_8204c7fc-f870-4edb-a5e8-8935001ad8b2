export interface GetTiktokProductRisingParams {
  /**
   * 主播ID
   */
  anchorId: string
  /**
   * 日期结束时间
   */
  endTime: string
  /**
   * 日期开始时间
   */
  startTime: string
}
/**
 * TikTokAnchorProductTrendVo
 */
export interface TikTokAnchorProductTrendVo {
  /**
   * 直播销售额占比
   */
  liveSalesRate: number
  /**
   * 直播带货趋势
   */
  liveTrend: TikTokAnchorProductTrendDataVo[]
  /**
   * 总销售额
   */
  totalGmv: number
  /**
   * 短视频带货趋势
   */
  videoTrend: TikTokAnchorProductTrendDataVo[]
  [property: string]: any
}

/**
 * TikTokAnchorProductTrendDataVo
 */
export interface TikTokAnchorProductTrendDataVo {
  /**
   * 日期
   */
  date: number
  /**
   * 销售额
   */
  gmv: number
  [property: string]: any
}
