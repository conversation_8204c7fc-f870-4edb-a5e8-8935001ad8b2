import { request } from '@/lib/client/request'
import { GetTiktokProductRisingParams } from '../getTiktokProductRising/dtos'
import { TikTokTrendOfFollowerVo } from './dtos'

// 获取粉丝趋势
export const getTiktokFansRising = async (
  query: GetTiktokProductRisingParams
) => {
  return await request<TikTokTrendOfFollowerVo>({
    url: 'api-uchoice/tiktok/anchor/getTiktokFansRising',
    method: 'get',
    params: query
  })
}
