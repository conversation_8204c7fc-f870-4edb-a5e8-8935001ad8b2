import { request } from '@/lib/client/request'

export interface ItemRankQuery {
  /**
   * 类目id
   */
  customCategoryId?: number
  /**
   * 日期时间戳
   */
  dateStartTimestamp?: number
  /**
   * 日期类型: 1-日榜 2-周榜 3-月榜
   */
  dateType?: number
  /**
   * 是否是高佣
   */
  isHighCommission?: boolean
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * 榜单类型: 1-热销 2-潜力 3-持续好货
   */
  type?: number
}
export const itemRankList = async (query: ItemRankQuery) => {
  return await request<any>({
    url: '/api-uchoice/tiktok/item/itemRankList',
    method: 'get',
    params: query
  })
}
