import { SkuList } from '../../../tt/item/info/dtos'

export interface IdParams {
  id: string
}

/**
 * TikTokItemDetailVo
 */
export interface TikTokItemDetailVo {
  /**
   * 精选高佣佣金率(数值) eg: 1000
   */
  creatorCommissionPercent: number
  /**
   * 精选高佣佣金率(百分比) eg:15%
   */
  creatorCommissionPercentStr: string
  /**
   * 商品主图
   */
  homeImgUrl: string
  /**
   * 主键
   */
  id: string
  /**
   * 是否收藏
   */
  isCollected: boolean
  /**
   * 是否是tap商品
   */
  isTap: boolean
  itemSubsidyVo?: ItemSubsidyVo
  /**
   * 商品链接
   */
  link: string
  /**
   * 最高赚取
   */
  maxEarn: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最低赚取
   */
  minEarn: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 公开佣金率(数值) eg:300
   */
  planCommissionPercent: number
  /**
   * 公开佣金率(百分比)  eg:15%
   */
  planCommissionPercentStr: string
  /**
   * 商品id
   */
  productId: string
  /**
   * 商品名称
   */
  productName: string
  salesCountVo: TikTokSalesCountVo
  /**
   * 店铺图片
   */
  sellerAvatar?: string
  /**
   * 店铺id
   */
  sellerId: number
  /**
   * 店铺名称
   */
  sellerName: string
  /**
   * 商品sku信息
   */
  skuList: SkuList[]
  /**
   * sku属性图
   */
  skuResDtoList: Sku主属性图片[]
  /**
   * 原始详情页链接（不一定是tap链接）
   */
  sourceLink: string
  /**
   * 销量趋势
   */
  trendList: TikTokTrendVo[]
  /**
   * tap商品id
   */
  ttaItemId?: any

  itemExtensionDto: {
    content?: string
    contentEn?: string
  }
  // 其实不存在
  sampleTotal?: number
  // 其实不存在
  isShowBuyTip?: boolean
  // 其实不存在
  itemSubsidyDescEn: null | string
  // 其实不存在
  itemSubsidyDescTh: null | string
  affiliateShareLink?: string
  // 是否新申样流程
  isNewSample?: boolean
}

/**
 * ItemSubsidyVo
 */
export interface ItemSubsidyVo {
  /**
   * pdp详情(英文)
   */
  descriptionEn: string
  /**
   * pdp详情(泰文)
   */
  descriptionTh: string
  /**
   * 标题(英文)
   */
  titleEn: string
  /**
   * 标题(泰文)
   */
  titleTh: string
  /**
   * 图片链接
   */
  url: string
  [property: string]: any
}

/**
 * TikTokSalesCountVo
 */
export interface TikTokSalesCountVo {
  /**
   * 销售额
   */
  gmv: number
  /**
   * 销量
   */
  sales: number
  [property: string]: any
}

/**
 * SkuDto
 */
export interface SkuDto {
  /**
   * 实际价格 泰铢
   */
  actualFee?: number
  /**
   * 真实库存(web用)
   */
  actualStock?: number
  /**
   * 运费
   */
  fare?: number
  /**
   * skuId
   */
  id?: number
  /**
   * 商品id
   */
  itemId?: number
  /**
   * 每人限购数量
   */
  limitBuyNum?: number
  /**
   * 限制库存
   */
  limitStock?: number
  /**
   * 商品
   */
  listDetailId?: number
  /**
   * 价格（泰铢）
   */
  price?: number
  /**
   * returnIntegral
   */
  returnIntegral?: number
  /**
   * 商家sku
   */
  sellerSku?: string
  /**
   * sku属性
   */
  skuAttrList?: Sku属性[]
  /**
   * sku图片
   */
  skuImage?: string
  /**
   * 商品sku编号
   */
  skuNo?: string
  /**
   * 失效时间
   */
  specialEndTime?: number
  /**
   * 折扣价 泰铢
   */
  specialPrice?: number
  /**
   * 生效时间
   */
  specialStartTime?: number
  /**
   * sku状态
   */
  status?: string
  /**
   * 库存|数量
   */
  stock?: number
  [property: string]: any
}

/**
 * sku属性
 */
export interface Sku属性 {
  /**
   * attributes_value
   */
  attributesValue?: string
  /**
   * sku属性Id
   */
  id?: number
  /**
   * 是否是主属性
   */
  primaryAttr?: boolean
  /**
   * 商品skuid
   */
  skuId?: number
  /**
   * spec_name
   */
  specName?: string
  [property: string]: any
}

/**
 * sku主属性图片
 */
export interface Sku主属性图片 {
  /**
   * 图片位置
   */
  imagePosition?: number
  /**
   * sku主属性图片
   */
  imgList?: string[]
  /**
   * 主属性名称
   */
  specAttr?: string
  [property: string]: any
}

/**
 * TikTokTrendVo
 */
export interface TikTokTrendVo {
  axisX: number
  axisY: number
  axisY2: number
}
