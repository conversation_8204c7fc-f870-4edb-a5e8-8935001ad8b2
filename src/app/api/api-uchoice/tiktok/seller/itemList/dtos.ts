export interface SellerItemListParams {
  /**
   * 类目id
   */
  customCategoryId?: number
  /**
   * 日期时间戳
   */
  dateStartTimestamp?: number
  /**
   * 日期类型: 1-日榜 2-周榜 3-月榜
   */
  dateType?: number
  /**
   * 卖家id(主键)
   */
  id: string
  /**
   * 页码
   */
  pageNo: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
}

/**
 * TikTokItemRankListVo
 */
export interface TikTokItemRankListVo {
  /**
   * 关联人数
   */
  anchorCount: number
  /**
   * 精选高佣佣金率(数值) eg: 1000
   */
  creatorCommission: number
  /**
   * 精选高佣佣金率(百分比) eg:15%
   */
  creatorCommissionPercent: string
  /**
   * 主键
   */
  id: number
  /**
   * 图片链接
   */
  image: string
  /**
   * 是否是tap商品
   */
  isTap: boolean
  /**
   * 最近一周销量
   */
  lastWeekSales: number
  /**
   * 最高赚取
   */
  maxEarn: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最低赚取
   */
  minEarn: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 公开佣金率(数值) eg:300
   */
  planCommission: number
  /**
   * 公开佣金率(百分比)  eg:15%
   */
  planCommissionPercent: string
  /**
   * 商品id
   */
  productId: number
  /**
   * 商品名称
   */
  productName: string
  /**
   * 昨日销量
   */
  yesterdaySales: number
  [property: string]: any
}

export interface SellerRankListParams {
  /**
   * 类目id
   */
  customCategoryId?: number
  /**
   * 日期时间戳
   */
  dateStartTimestamp?: number
  /**
   * 日期类型: 1-日榜 2-周榜 3-月榜
   */
  dateType?: number
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  [property: string]: any
}

export interface TikTokSellerRankListVo {
  /**
   * 关联达人总数
   */
  anchorCount: number
  /**
   * id
   */
  id?: number
  /**
   * 店铺头像
   */
  image: string
  /**
   * 最近一周销量
   */
  lastWeekSales: number
  /**
   * 卖家id
   */
  sellerId?: number
  /**
   * 店铺名称
   */
  sellerName: string
  /**
   * 昨日销量
   */
  yesterdaySales: number
  [property: string]: any
}
