import { ListResult } from '@/lib/types/http'
import {
  SellerItemListParams,
  TikTokItemRankListVo,
  SellerRankListParams,
  TikTokSellerRankListVo
} from './dtos'
import { request } from '@/lib/client/request'

// 卖家商品
export const sellerItemList = async (params: SellerItemListParams) => {
  return (await request)<ListResult<TikTokItemRankListVo>>({
    url: 'api-uchoice/tiktok/seller/itemList',
    method: 'get',
    params: params
  })
}
export const sellerRankList = async (params: SellerRankListParams) => {
  return (await request)<ListResult<TikTokSellerRankListVo>>({
    url: 'api-uchoice/tiktok/seller/sellerRankList',
    method: 'get',
    params: params
  })
}
