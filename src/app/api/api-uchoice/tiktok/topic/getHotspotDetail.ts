import { request } from '@/lib/client/request'

// 查询热点详情
export const requestHotspotDetail = async (
  hotspotId: string = '',
  dateType = 1,
  customCategoryId = ''
) => {
  const params: any = {
    hotspotId,
    dateType
  }
  if (customCategoryId) {
    params.customCategoryId = customCategoryId
  }

  return await request<any>({
    url: 'api-uchoice/tiktok/topic/getHotspotDetail',
    method: 'get',
    params
  })
}
