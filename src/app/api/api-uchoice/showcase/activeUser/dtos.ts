/**
 * ShowcaseTiktokUserVo
 */
export interface ShowcaseTiktokUserVo {
  /**
   * 头像
   */
  avatar?: string
  /**
   * 是否有当前国家权限
   */
  countryPermissions?: boolean
  /**
   * 昵称
   */
  displayName?: string
  /**
   * 是否有橱窗权限
   */
  showcasePermissions?: boolean

  /**
   * 状态值 0：无效，1：有效 2:未登录
   */
  status: ShowcaseUserStatus
  /**
   * tiktok用户唯一标识
   */
  tiktokUserId?: string
}

export enum ShowcaseUserStatus {
  Invalid = 0,
  Valid = 1,
  NotLoggedIn = 2
}
