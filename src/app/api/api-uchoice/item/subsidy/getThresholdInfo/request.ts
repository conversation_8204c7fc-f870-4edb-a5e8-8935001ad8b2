import { SampleThresholdVo } from './dtos'
import { request } from '@/lib/client/request'

// 获取样品申请门槛明细
export const getThresholdInfo = async () => {
  return await request<SampleThresholdVo>({
    url: '/api-uchoice/item/subsidy/getThresholdInfo',
    method: 'get'
  })
}

export const getThresholdInfoV3 = async () => {
  return await request<SampleThresholdVo>({
    url: '/api-uchoice/item/subsidy/getThresholdInfoV3',
    method: 'get'
  })
}
