/**
 * SampleThresholdVo
 */
export interface SampleThresholdVo {
  /**
   * 样本阈值列表
   */
  thresholdList?: SampleThresholdDto[]
  /**
   * 总数
   */
  total?: number
  [property: string]: any
}

/**
 * SampleThresholdDto
 */
export interface SampleThresholdDto {
  /**
   * 最高GMV
   */
  maxAmount?: number
  /**
   * 最低GMV
   */
  minAmount?: number
  /**
   * 数量
   */
  quantity?: number
  /**
   * 层级
   */
  tier?: number
  [property: string]: any
}
