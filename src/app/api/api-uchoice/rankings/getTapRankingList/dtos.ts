import { number } from 'echarts'

export enum GetTapRankingListType {
  SampleApplication = 1, // 申样
  Favorites = 2, // 收藏夹
  AddToShowcase = 3, // 添加橱窗
  BestSellers = 4 // 热销商品
}

/**
 * TapRankListVo
 */
export interface TapRankListVo {
  /**
   * 类目信息
   */
  categoryList?: TtCustomCategory[]
  /**
   * 商品信息
   */
  itemVoList?: TapRankItemVo[]
  [property: string]: any
}

/**
 * ShareLinkItemCategory
 */
export interface ShareLinkItemCategory {
  customCategoryId?: number
  emoji?: string
  id?: number
  name?: string
  nameEn?: string
  sort?: number
}
/**
 * TtCustomCategory
 */
export interface TtCustomCategory {
  /**
   * 类目图标
   */
  emoji?: string
  /**
   * 主键Id
   */
  id?: number
  /**
   * 类目名称(泰文)
   */
  name?: string
  /**
   * 类目名称(泰文)
   */
  nameEn?: string
  /**
   * 类目排序
   */
  sort?: number
  [property: string]: any
}

/**
 * TapRankItemVo
 */
export interface TapRankItemVo {
  /**
   * 带货达人数
   */
  anchorCount: number
  /**
   * 达人卖货信息列表(热销商品)
   */
  anchorSalesInfoVoList?: TapRankItemAnchorVo[]
  /**
   * 活动ID
   */
  campaignId: number
  /**
   * 佣金率 eg:15%
   */
  commissionRate: string
  /**
   * 创造者佣金率
   */
  creatorCommissionPercent: number
  /**
   * 自定义类目id
   */
  customCategoryId: number
  /**
   * sku
   */
  esSkus: EsTtaSku[]
  /**
   * 主键
   */
  id: number
  /**
   * 图片链接
   */
  image: string
  /**
   * 是否收藏
   */
  isCollected: boolean
  /**
   * 商品链接
   */
  link: string
  /**
   * 最高赚取
   */
  maxEarn: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最低赚取
   */
  minEarn: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 对比公开计划佣金率
   */
  planCommissionPercent: string
  /**
   * 商品id
   */
  productId: number
  /**
   * 商品名称
   */
  productName: string
  /**
   * 商品状态 1未通过 2通过 3拒绝 4待终止合作 5已终止合作
   */
  productStatus: number
  /**
   * 活动结束时间
   */
  promotionEndTime?: number
  /**
   * 活动开始时间
   */
  promotionStartTime?: number
  /**
   * 销量
   */
  sales: number
  /**
   * 近30天销量
   */
  salesForLast30Days: number
  /**
   * 近30天销量
   */
  salesForLast30DaysStr: string
  /**
   * 销量 eg: 12K
   */
  salesStr: string
  /**
   * 店铺号
   */
  shopCode: string
  /**
   * 店铺名称
   */
  shopName: string
  /**
   * 店铺评分
   */
  shopScore?: number
  /**
   * 商家商品状态 0 下架 1 上架
   */
  shopStatus: number
  /**
   * 原始详情页链接（不一定是tap链接）
   */
  sourceLink: string
  /**
   * 库存量
   */
  stock: number
  /**
   * 平台商品状态 0 下架 1 上架
   */
  youpikStatus: number
  [property: string]: any
}

/**
 * TapRankItemAnchorVo
 */
export interface TapRankItemAnchorVo {
  /**
   * 达人名称
   */
  displayName?: string
  /**
   * 赚的佣金
   */
  earn?: number
  /**
   * 商品主键
   */
  id?: number
  /**
   * 订单数
   */
  orderCount?: number
  [property: string]: any
}

/**
 * EsTtaSku
 */
export interface EsTtaSku {
  id?: number
  /**
   * SKU图片
   */
  image?: string
  /**
   * 商品id
   */
  itemId?: number
  /**
   * 价格（泰铢）
   */
  price?: number
  /**
   * 商家sku
   */
  sellerSku?: string
  skuAttrs?: EsSkuAttr[]
  /**
   * 商品sku编号
   */
  skuNo?: string
  /**
   * 规格
   */
  skuSaleProps?: string
  /**
   * 库存
   */
  stock?: number
  [property: string]: any
}

/**
 * EsSkuAttr
 */
export interface EsSkuAttr {
  attributesValue?: string
  /**
   * sku属性Id
   */
  id?: number
  /**
   * 是否是主属性
   */
  primaryAttr?: boolean
  specName?: string
  [property: string]: any
}
