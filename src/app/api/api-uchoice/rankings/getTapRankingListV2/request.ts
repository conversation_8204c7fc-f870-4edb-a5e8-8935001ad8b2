import { request } from '@/lib/client/request'
import { TapRankItemInfoVo, GetTapRankingListType } from './dtos'

// 获取Tap商品排行榜--V2
export const getTapRankingListV2 = async (
  type: number,
  customCategoryId: number
) => {
  return await request<TapRankItemInfoVo[]>({
    url: 'api-uchoice/rankings/getTapRankingListV2',
    method: 'get',
    params: {
      type,
      customCategoryId
    }
  })
}
//web-分页获取商品列表

export const getShareLinkItemListByPage = async params => {
  return await request<any>({
    url: 'api-uchoice/tt/item/getShareLinkItemListByPage',
    method: 'get',
    params: params,
    requestKey: 'getShareLinkItemListByPage'
  })
}
