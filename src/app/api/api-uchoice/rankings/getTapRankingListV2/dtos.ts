export enum GetTapRankingListType {
  SampleApplication = 1, // 申样
  Favorites = 2, // 收藏夹
  AddToShowcase = 3, // 添加橱窗
  BestSellers = 4 // 热销商品
}

/**
 * TapRankItemInfoVo
 */
export interface TapRankItemInfoVo {
  /**
   * 达人卖货信息列表(热销商品)
   */
  anchorSalesInfoVoList?: TapRankItemAnchorVo[]
  /**
   * 佣金率 eg:0.15
   */
  commissionRate: number
  /**
   * 自定义类目id
   */
  customCategoryId: number
  /**
   * 商品主键
   */
  id: string
  /**
   * 图片链接
   */
  image: string
  /**
   * 商品链接
   */
  link: string
  /**
   * 最高赚取
   */
  maxEarn: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最低赚取
   */
  minEarn: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 商品id
   */
  productId: string
  /**
   * 商品名称
   */
  productName: string
  /**
   * 相关数量
   */
  relevantQuantity: number
  /**
   * 排行
   */
  rn: number
  /**
   * 近30天销量
   */
  salesForLast30Days: number
  [property: string]: any
}

/**
 * TapRankItemAnchorVo
 */
export interface TapRankItemAnchorVo {
  /**
   * 达人名称
   */
  displayName?: string
  /**
   * 赚的佣金
   */
  earn?: number
  /**
   * 商品主键
   */
  id?: number
  /**
   * 订单数
   */
  orderCount?: number
  [property: string]: any
}
