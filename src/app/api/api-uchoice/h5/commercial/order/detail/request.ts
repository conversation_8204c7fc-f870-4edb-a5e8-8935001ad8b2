import { request } from '@/lib/client/request'

/**
 * 商单详情查询参数
 */
export interface CommercialOrderDetailParams {
  /**
   * 邀请码
   */
  inviteCode: string
}

/**
 * 商单商品描述信息
 */
export interface CommercialOrderItemDescDto {
  /**
   * 参考视频选项是否勾选 0-否 1-是
   */
  referenceVideoOption?: number
  /**
   * 脚本范例链接
   */
  scriptExampleLinkList?: string[]
  /**
   * 脚本范例选项是否勾选 0-否 1-是
   */
  scriptOption?: number
  /**
   * 视频资源列表
   */
  videoResourceList?: string[]
  /**
   * 视频类型 0-本地视频 1-TikTok视频链接
   */
  videoType?: number
}

/**
 * 商单商品信息
 */
export interface CommercialOrderItemVo {
  [property: string]: any
}

/**
 * H5商单详情信息
 */
export interface CommercialOrderH5DetailVo {
  // 被邀请人
  displayName?: string
  /**
   * 接单标志 0:未接单 1:已接单
   */
  acceptOrderFlag?: number
  /**
   * Ads code是否必须 0:否 1:是
   */
  adsCodeRequired?: number
  /**
   * 品牌介绍
   */
  brandDesc?: string
  /**
   * 品牌Logo 资源id
   */
  brandLogo?: string
  /**
   * 品牌名称
   */
  brandName?: string
  /**
   * 每个达人的预算
   */
  budgetPerTalent?: number
  /**
   * 内容数量要求
   */
  contentCount?: number
  /**
   * 内容时长要求秒
   */
  contentDurationSecond?: number
  /**
   * 交付内容类型 0:视频
   */
  deliveryContentType?: number
  /**
   * 预估交付结束时间
   */
  estimatedDeliveryEndTime?: number
  /**
   * 预估交付开始时间
   */
  estimatedDeliveryStartTime?: number
  /**
   * 达人邀约id
   */
  expertInviteId?: string
  /**
   * 粉丝量
   */
  followerCount?: number
  /**
   * 商单编号
   */
  id?: number
  /**
   * 商品描述信息
   */
  itemDesc?: CommercialOrderItemDescDto
  /**
   * 商品信息
   */
  itemVo?: CommercialOrderItemVo
  /**
   * 商单名称
   */
  name?: string
  /**
   * 状态: 0-进行中 1-已交付 2-已取消
   */
  orderStatus?: number
  /**
   * 预估报名结束时间
   */
  registrationEndTime?: number
  /**
   * 预估报名开始时间
   */
  registrationStartTime?: number
  /**
   * 标签，逗号分隔
   */
  tags?: string
  /**
   * TAP挂链是否必须 0:否 1:是
   */
  tapLinkRequired?: number
  /**
   * 目标达人数
   */
  targetTalentCount?: number
  /**
   * 商单总预算
   */
  totalBudget?: number
}

// 商单详情
export const getCommercialOrderDetail = async (
  params: CommercialOrderDetailParams
) => {
  return await request<CommercialOrderH5DetailVo>({
    url: '/api-uchoice/h5/commercial/order/detail',
    method: 'get',
    params
  })
}
