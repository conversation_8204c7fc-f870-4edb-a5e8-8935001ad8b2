import { request } from '@/lib/client/request'
import { ApiResponse } from '@/lib/types/http'

/**
 * 接单参数
 */
export interface CommercialOrderAcceptParams {
  /**
   * 达人邀约id
   */
  expertInviteId?: string
}

// 接单
export const acceptCommercialOrder = async (
  params: CommercialOrderAcceptParams
) => {
  return await request<boolean>({
    url: '/api-uchoice/h5/commercial/order/accept',
    method: 'post',
    params
  })
}
