import { request } from '@/lib/client/request'
import { fetcher } from '@/lib/server/fetcher'

// 获取fmcg活动集合
export const getCampaignList = async (campaignId: string) => {
  return await request<any>({
    url: `/api-uchoice/fmcg/campaigns/campaign/${campaignId}`,
    method: 'get',
  })
}

export const getCampaignListServer = async (campaignId: string) => {
  return await fetcher(`api-uchoice/fmcg/campaigns/campaign/${campaignId}`)
}

