import { request } from '@/lib/client/request'
import { fetcher } from '@/lib/server/fetcher'

// 获取活动详情信息
export const requestCampaignDetail = async (campaignId: string) => {
  return await request<any>({
    url: `/api-uchoice/fmcg/campaigns/${campaignId}/detail`,
    method: 'get',
  })
}

export const requestCampaignDetailServer = async (campaignId: string) => {
  return await fetcher(`api-uchoice/fmcg/campaigns/${campaignId}/detail`);
}