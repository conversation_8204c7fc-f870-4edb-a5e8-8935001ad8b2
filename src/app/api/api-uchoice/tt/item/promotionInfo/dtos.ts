import exp from 'constants'

export interface PromotionInfoParams {
  productId: string
  tiktokUserId: string
}
export interface PromotionContentParams {
  lastDay: string
  type: string
  unionId: string
}

export interface PromotionRelatedParams {
  lastDay: string
  unionId: string
}
export interface PromotionRelatedDto {
  livePromotionCount?: string
  productPromotionEarned?: string
  videoPromotionCount?: string
  promotionGmv?: string
  promotionSales?: string
  promotionCount?: string
}
export interface PromotionInfoDto {
  hasHotVideo?: boolean
  campaignId?: string
  commissionRate?: number // 佣金率
  homeImgUrl?: string // 图片 URL
  id?: string // 产品 ID
  isShowHistory?: boolean // 是否显示历史
  maxEarn?: number // 最大收益
  maxEarnHistory?: number // 历史最大收益
  maxPrice?: number // 最大价格
  minEarn?: number // 最小收益
  minPrice?: number // 最小价格
  name?: string // 产品名称
  nameEn?: string // 产品英文名称
  productId?: string // 产品的 ID
  salesForLast30Days?: number // 最近 30 天的销售
}
export interface PromotionContentListParams {
  lastDay?: string
  pageNo: number
  pageSize: string
  type?: String
  unionId: string
  sortBy?: String
  sortDirection?: 'asc' | 'desc' | '' // 排序值 (可选, asc/desc)
}

export interface PromotionVideoListParams {
  pageNo?: number // 页码 (可选, int32)
  pageSize?: number // 数量 (可选, int32)
  productId?: string // 推广商品ID (必需, int64)
  sortBy?: string // 排序字段 (可选)
  sortDirection?: 'asc' | 'desc' // 排序值 (可选, asc/desc)
  type?: number // 类型 (可选, int32: 0-全部, 1-视频, 2-直播)
  promotionType?: number
  tiktokUserId?: string
}
export interface PromotionContentListDto {
  basicSalesCount: number
  contentType: number
  coverImg: string
  likeCount: number
  productId: string
  promotionType: number
  publishTime: number
  qualitySalesCount: number
  salesCount: number
  sourceUrl: string
  title: string
  viewCount: number
}
export interface PromotionVideoListDto {
  commentCount: number // 评论数 (必需, int32)
  commission: number // 佣金 (必需, number)
  commissionRate: number // 佣金率 (必需, number)
  coverImg: string // 视频封面 (必需, string)
  likeCount: number // 点赞数 (必需, int32)
  sourceUrl?: string // 视频地址 (可选, string)
  type: 1 | 2 // 类型 (必需, 1-视频, 2-直播, int32)
  videoCreateTime: number // 视频创建时间（毫秒）(必需, int64)
  videoDescription: string // 视频描述 (必需, string)
  videoId: string // 视频 ID (必需, string)
  viewCount: number // 访问数（播放数）(必需, int32)
}

export interface ReSaleRecordListParams {
  pageNo?: number
  pageSize?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
  tiktokUserId?: string
}

export interface ReSaleRecordListDto {
  commission: number
  commissionRate: number
  coverImg: string
  gmv: number
  image?: string
  minPrice?: number
  orderCount: number
  productId?: string
  productName?: string
  sourceUrl?: string
  videoCreateTime: number
  videoDescription: string
  videoId: string
  viewCount: number
}

export interface ActiveUserDto {
  avatar: string
  countryPermissions: boolean
  displayName: string
  showcasePermissions: boolean
  status: number
  tiktokUserId: string
}

export interface ShowcaseStatisticsDto {
  joinPlanCount?: number
  promotionCommission?: number
  promotionCommissionIncrease?: number
  promotionCount?: number
  promotionGmv?: number
  totalCommission?: number
  totalCount?: number
}

export interface MySampleLimitV3Params {
  avatar?: string
  canReturnCount?: number
  displayName?: string
  expiredDay?: number
  isAcceptable?: boolean
  isLowPerformance: boolean // 必需
  isValid?: number
  limitType?: number
  lowPerformanceUnionId: string // 必需
  performance: number // 必需
  remainSampleCount?: number
  salesDataVoucherStatus?: number
  salesDataVoucherTime?: number
  tier?: number
  totalSampleCount?: number
  unionId?: string
  unitsSold?: number
}

export interface PromotionContentSuggestion {
  qualityContentCount: number
  waitImproveCount: number
}
