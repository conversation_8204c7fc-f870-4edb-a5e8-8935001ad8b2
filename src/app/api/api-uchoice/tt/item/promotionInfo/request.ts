import { request } from '@/lib/client/request'
import {
  ActiveUserDto,
  MySampleLimitV3Params,
  PromotionContentListDto,
  PromotionContentListParams,
  PromotionContentParams,
  PromotionContentSuggestion,
  PromotionInfoDto,
  PromotionInfoParams,
  PromotionRelatedDto,
  PromotionRelatedParams,
  PromotionVideoListDto,
  PromotionVideoListParams,
  ReSaleRecordListDto,
  ReSaleRecordListParams,
  ShowcaseStatisticsDto
} from './dtos'
import { ListResult } from '@/lib/types/http'

//获取推广内容列表
export const getPromotionContentList = async (
  query: PromotionContentListParams
) => {
  return (await request)<ListResult<PromotionContentListDto>>({
    url: 'api-uchoice/showcase/getPromotionContentList',
    method: 'get',
    params: query
  })
}
//推广商品详情视频列表
export const promotionVideoList = async (query: PromotionVideoListParams) => {
  return (await request)<ListResult<PromotionVideoListDto>>({
    url: 'api-uchoice/tt/item/promotionVideoList',
    method: 'get',
    params: query
  })
}

//获取橱窗重新销售记录
export const getReSaleRecordList = async (query: ReSaleRecordListParams) => {
  return (await request)<ListResult<ReSaleRecordListDto>>({
    url: 'api-uchoice/showcase/getReSaleRecordList',
    method: 'get',
    params: query
  })
}
//获取最近登录的账号信息
export const getActiveUser = async () => {
  return (await request)<ActiveUserDto>({
    url: 'api-uchoice/showcase/getActiveUser',
    method: 'get'
  })
}

export const promotionInfo = async (query: PromotionInfoParams) => {
  return (await request)<PromotionInfoDto>({
    url: 'api-uchoice/tt/item/promotionInfo',
    method: 'get',
    params: query
  })
}

//我的申样额度
export const mySampleLimitV3 = async () => {
  return (await request)<MySampleLimitV3Params>({
    url: 'api-uchoice/uChoice/account/mySampleLimitV3',
    method: 'get'
  })
}

//获取推广内容建议概况
export const getPromotionContentSuggestion = async (
  query: PromotionContentParams
) => {
  return (await request)<PromotionContentSuggestion>({
    url: 'api-uchoice/showcase/getPromotionContentSuggestion',
    method: 'get',
    params: query
  })
}
//获取推广相关数量
export const getPromotionRelatedCount = async (
  query: PromotionRelatedParams
) => {
  return (await request)<PromotionRelatedDto>({
    url: 'api-uchoice/showcase/getPromotionRelatedCount',
    method: 'get',
    params: query
  })
}
//获取橱窗统计信息
export const getShowcaseStatistics = async (query: {
  tiktokUserId: string
}) => {
  return (await request)<ShowcaseStatisticsDto>({
    url: 'api-uchoice/showcase/getShowcaseStatistics',
    method: 'get',
    params: query
  })
}

//获取推广商品列表
export const getSampleRecommendList = async () => {
  return (await request)<ShowcaseStatisticsDto>({
    url: 'api-uchoice/tt/item/getSampleRecommendList',
    method: 'get'
  })
}

//获取推广相关数量-V2
export const getPromotionRelatedCountV2 = async query => {
  return (await request)<ShowcaseStatisticsDto>({
    url: 'api-uchoice/showcase/getPromotionRelatedCountV2',
    method: 'get',
    params: query
  })
}
