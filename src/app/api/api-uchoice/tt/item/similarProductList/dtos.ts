import { PagingParams } from '@/lib/types/http'

export type SimilarProductParams = PagingParams & {
  id: string
}

/**
 * SimilarProductDto
 */
export interface SimilarProductDto {
  /**
   * 带货达人数
   */
  anchorCount: string
  /**
   * 活动ID
   */
  campaignId: number
  /**
   * 收藏时间
   */
  collectionTime?: number
  /**
   * 佣金率 eg:15%
   */
  commissionRate: string
  /**
   * 创造者佣金率
   */
  creatorCommissionPercent: number
  /**
   * 自定义类目id
   */
  customCategoryId: number
  /**
   * sku
   */
  esSkus: EsTtaSku[]
  /**
   * 主键
   */
  id: number
  /**
   * 图片链接
   */
  image: string
  /**
   * 是否已申请样品
   */
  isApplied: boolean
  /**
   * 是否收藏
   */
  isCollected: boolean
  /**
   * 是否有效 1:有效 2:无效
   */
  isExpired: number
  /**
   * 是否为新活动
   */
  isNewCampaign: boolean
  /**
   * 橱窗是否禁用
   */
  isShowcaseDisabled: boolean
  /**
   * 是否为精选商品
   */
  isTap: boolean
  /**
   * 商品标签
   */
  itemLabels: EsItemLabel[]
  /**
   * 商品权重、排序用
   */
  itemSort: number
  itemSubsidyDTO?: ItemSubsidyDTO
  /**
   * 商品标签类型: 1-活动补贴 2-免费样品 3-投流奖励
   */
  labelType: number[]
  /**
   * 商品链接
   */
  link: string
  /**
   * 最高赚取
   */
  maxEarn: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最低赚取
   */
  minEarn: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 对比公开计划佣金率
   */
  planCommissionPercent: string
  /**
   * 商品id
   */
  productId: number
  /**
   * 商品名称
   */
  productName: string
  /**
   * 商品状态 1未通过 2通过 3拒绝 4待终止合作 5已终止合作
   */
  productStatus: number
  /**
   * 活动结束时间
   */
  promotionEndTime?: number
  /**
   * 活动开始时间
   */
  promotionStartTime?: number
  /**
   * 销量
   */
  sales: number
  /**
   * 近30天销量
   */
  salesForLast30Days: number
  /**
   * 近30天销量
   */
  salesForLast30DaysStr: string
  /**
   * 近一周销量
   */
  salesForLastWeekDays: number
  /**
   * 近两周销量增值率
   */
  salesForTwoWeeksRate: string
  /**
   * 昨日销量
   */
  salesForYesterDays: number
  /**
   * 销量 eg: 12K
   */
  salesStr: string
  /**
   * 店铺号
   */
  shopCode: string
  /**
   * 店铺名称
   */
  shopName: string
  /**
   * 商家商品状态 0 下架 1 上架
   */
  shopStatus: number
  /**
   * 原始详情页链接（不一定是tap链接）
   */
  sourceLink: string
  /**
   * 库存量
   */
  stock: number
  /**
   * 更新时间
   */
  updateTime: number
  /**
   * 平台商品状态 0 下架 1 上架
   */
  youpikStatus: number
  [property: string]: any
}

/**
 * EsTtaSku
 */
export interface EsTtaSku {
  id?: number
  /**
   * SKU图片
   */
  image?: string
  /**
   * 商品id
   */
  itemId?: number
  /**
   * 价格（泰铢）
   */
  price?: number
  /**
   * 商家sku
   */
  sellerSku?: string
  skuAttrs?: EsSkuAttr[]
  /**
   * 商品sku编号
   */
  skuNo?: string
  /**
   * 规格
   */
  skuSaleProps?: string
  /**
   * 库存
   */
  stock?: number
  [property: string]: any
}

/**
 * EsSkuAttr
 */
export interface EsSkuAttr {
  attributesValue?: string
  /**
   * sku属性Id
   */
  id?: number
  /**
   * 是否是主属性
   */
  primaryAttr?: boolean
  specName?: string
  [property: string]: any
}

/**
 * EsItemLabel
 */
export interface EsItemLabel {
  /**
   * id
   */
  id?: number
  /**
   * 名称(中)
   */
  name?: string
  /**
   * 名称(英)
   */
  nameEn?: string
  /**
   * 名称(泰语)
   */
  nameTh?: string
  /**
   * 1-潜力新品
   */
  type?: number
  [property: string]: any
}

/**
 * ItemSubsidyDTO
 */
export interface ItemSubsidyDTO {
  /**
   * pdp详情(英文)
   */
  descriptionEn?: string
  /**
   * pdp详情(泰文)
   */
  descriptionTh?: string
  /**
   * 结束时间
   */
  endTime?: number
  /**
   * 顺序
   */
  indexOrder?: number
  /**
   * 商品id
   */
  itemId?: number
  /**
   * 商品id
   */
  productId?: number
  /**
   * 开始时间
   */
  startTime?: number
  /**
   * 标题(英文)
   */
  titleEn?: string
  /**
   * 标题(泰文)
   */
  titleTh?: string
  /**
   * 图片链接
   */
  url?: string
  [property: string]: any
}
