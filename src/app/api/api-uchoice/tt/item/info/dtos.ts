import {
  GetTapRankingListType,
  TapRankItemAnchorVo
} from '../../../rankings/getTapRankingList/dtos'

export interface IdParams {
  id: string
}

export interface ItemInfoDto {
  id: string
  productId: string
  name: string
  nameEn: string
  itemType: number
  campaignId: string
  sellerId: null | string
  categoryId: number
  brandId: null | string
  homeImgUrl: string
  skuResDtoList: SkuResDto[]
  itemExtensionDto: ItemExtensionDto
  skuList: SkuList[]
  visible: boolean
  totalStock: number
  categoryName: string
  categoryNameEn: string
  promotionEndTime: number
  salesStr: string
  commissionRate: string
  planCommission: string
  maxPrice: number
  minPrice: number
  minEarn: number
  maxEarn: number
  isCollected: boolean
  link: string
  sourceLink: string
  isShowImg: boolean
  shopCode: string
  shopName: string
  itemSubsidyDescEn: null | string
  itemSubsidyDescTh: null | string
  itemSubsidyUrl: null | string
  rankingInfo: null | RankingInfo
  salesForLast30Days: number
  salesForLast30DaysStr: string
  salesForTwoWeeksRate: string
  flowManagementInfoVo: FlowManagementInfoVo
  itemLabels: any[]
  isShowSampleTip: boolean
  tikTokSeller: TiktokSeller
  shopScore?: number
  tapRanking?: TapRankingItemDetailDTO
  sampleTotal: number
  isShowBuyTip?: boolean
  sourceMaterial?: TtaItemSourceMaterialDetailVo
  /**
   * 是否配合商家
   */
  isCooperate: boolean
  // 是否为高通过率商品
  isHighPassRateProduct: boolean
  shopReviewCriteria?: ShopReviewCriteriaBo
  affiliateShareLink?: string
  isCanSample?: boolean
  // 是否新申样流程
  isNewSample?: boolean
  /**
   * 等级
   */
  tier: number
  /**
   * 通过率
   */
  rate: string
}

/**
 * ShopReviewCriteriaBo
 */
export interface ShopReviewCriteriaBo {
  /**
   * 粉丝数门槛
   */
  fansThreshold?: number
  /**
   * 销量门槛
   */
  salesThreshold?: number
  /**
   * 店铺号
   */
  shopCode?: string
}

/**
 * TapRankingItemDetailDTO
 */
export interface TapRankingItemDetailDTO {
  /**
   * 带货达人数(type != 4 的时候用这个)
   */
  anchorCount: number
  /**
   * 达人卖货信息列表(type = 4 的时候用这个)
   */
  anchorSalesInfoVoList?: TapRankItemAnchorVo[]
  /**
   * 排名值
   */
  ranking?: number
  /**
   * 类型: 1-申样 2-收藏夹 3-添加橱窗 4-热销商品
   */
  type?: GetTapRankingListType
  customCategoryId?: number
}

interface SkuResDto {
  imagePosition: number
  specAttr: string
  imgList: string[]
}

interface ItemExtensionDto {
  id: string
  content: string
  contentEn: null | string
  shortDesc: null | string
  shortDescEn: null | string
  getItems: null | any
  getItemsEn: null | any
}

export interface SkuList {
  id: string
  skuNo: null | string
  stock: null | any
  actualStock: null | any
  price: number
  specialPrice: number
  actualFee: number
  specialStartTime: null | number
  specialEndTime: null | number
  sellerSku: null | string
  skuAttrList: SkuAttr[]
  skuImage: string
  limitStock: null | any
  limitBuyNum: null | any
  listDetailId: null | string
  returnIntegral: null | any
  status: null | any
  itemId: number
  fare: number
}

interface SkuAttr {
  id: null | string
  skuId: null | string
  primaryAttr: boolean
  specName: string
  attributesValue: string
}

interface RankingInfo {
  // 根据具体情况添加具体的字段和类型
}

interface FlowManagementInfoVo {
  ordersCount: number
  gmv: number
  videoPlayCount: number
  flowAmount: number
  isRewardEnabled: boolean
  sampleThreshold: null | any
  lastModificationTime: number
  operator: string
}

interface TiktokSeller {
  /**
   * 关联达人总数
   */
  anchorCount: number
  /**
   * 联系方式(json)
   */
  contactInfo?: string
  /**
   * 店铺主营商品类目id(tiktok_crawler_category表id)
   */
  crawlerCategoryId: number
  createTime?: number
  /**
   * 自定义商品类目id(tiktok_custom_category表id)
   */
  customCategoryId: number
  /**
   * 总销量额
   */
  gmv: number
  id?: number
  /**
   * 店铺头像
   */
  image: string
  /**
   * 商品总数
   */
  itemCount: number
  /**
   * 总直播数
   */
  liveCount: number
  /**
   * 店铺评分
   */
  rating: number
  /**
   * 国家
   */
  region: string
  /**
   * 总销量
   */
  sales: number
  /**
   * 店铺id
   */
  sellerId: number
  /**
   * 店铺名称
   */
  sellerName: string
  updateTime?: number
  /**
   * 总视频数
   */
  videoCount: number
}

/**
 * TtaItemSourceMaterialDetailVo
 */
export interface TtaItemSourceMaterialDetailVo {
  /**
   * 达人数
   */
  anchorCount?: number
  /**
   * 简绍链接
   */
  brief?: string
  /**
   * 视频脚本信息
   */
  briefAccount?: TtaItemSourceMaterialBriefAccountVo[]
  /**
   * 图片素材信息链接(json)
   */
  imageSourceMaterialList?: {
    id: string
    imageUrl: string
  }[]
  /**
   * 商品id
   */
  productId?: number
  /**
   * 参考视频链接
   */
  referenceVideoList?: string[]
  /**
   * 销量
   */
  sales?: number
  /**
   * 卖点信息
   */
  sellPointList?: TtaItemSourceMateriaSellPointVo[]
  /**
   * 视频素材信息链接(json)
   */
  videoSourceMaterialList?: TtaItemSourceMateriaVideoVo[]
}

/**
 * TtaItemSourceMateriaSellPointVo
 */
export interface TtaItemSourceMateriaSellPointVo {
  /**
   * 卖点简介
   */
  sellPointDesc?: string
  /**
   * 卖点标题
   */
  sellPointTitle?: string
}

/**
 * TtaItemSourceMateriaVideoVo
 */
export interface TtaItemSourceMateriaVideoVo {
  /**
   * 视频封面地址
   */
  coverUrl: string
  /**
   * 视频地址
   */
  videoUrl: string
}

/**
 * TtaItemSourceMaterialBriefAccountVo
 */
export interface TtaItemSourceMaterialBriefAccountVo {
  /**
   * 达人头像
   */
  avatar?: string
  /**
   * 达人名称
   */
  displayName?: string
  /**
   * 0-销量提升 1-单日出单 2-累计出单
   */
  type?: number
  /**
   * 对应值
   */
  value?: string
}
