import { request } from '@/lib/client/request'
import { ListResult } from '@/lib/types/http'

export interface Params {
  /**
   * 是否查询有效数据
   */
  isValid: boolean
  /**
   * 当前页
   */
  pageNo?: number
  /**
   * 页大小
   */
  pageSize: number
  /**
   * tiktok用户id
   */
  unionId: string
  [property: string]: any
}

/**
 * TtInviteCodeFollowVo
 */
export interface TtInviteCodeFollowVo {
  /**
   * 头像
   */
  avatar: string
  /**
   * 用户名称
   */
  displayName: string
  /**
   * 粉丝数
   */
  followerCount: number
  /**
   * 用户别名
   */
  nickname: string
  /**
   * 可获得的积分
   */
  pointsChange: string
  /**
   * 积分状态 0-待完成 1-已完成 2-不展示
   */
  status: number
  /**
   * 等级
   */
  tier: number
  [property: string]: any
}

//邀请人列表接口
export const followList = async (params: Params) => {
  return (await request)<ListResult<TtInviteCodeFollowVo>>({
    url: 'api-uchoice/invite/code/followList',
    method: 'get',
    params
  })
}
