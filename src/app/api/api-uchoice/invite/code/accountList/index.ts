import { request } from '@/lib/client/request'
import { ListResult } from '@/lib/types/http'

/**
 * TtInviteCodeAccountVo
 */
export interface TtInviteCodeAccountVo {
  /**
   * 头像
   */
  avatar: string
  /**
   * 用户名称
   */
  displayName: string
  /**
   * 邀请码
   */
  inviteCode: string
  /**
   * 用户别名
   */
  nickname: string
  /**
   * unionId
   */
  unionId: string
  [property: string]: any
}

//tt账号列表接口
export const accountList = async () => {
  return (await request)<TtInviteCodeAccountVo[]>({
    url: 'api-uchoice/invite/code/accountList',
    method: 'get'
  })
}
