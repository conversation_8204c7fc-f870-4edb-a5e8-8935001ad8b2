import { request } from '@/lib/client/request'
import { ApiResponse } from '@/lib/types/http'

/**
 * 商单打款收款人信息参数
 */
export interface CommercialOrderPaymentRecipientInfoQo {
  /**
   * 收款卡号
   */
  bankAccountNumber?: string
  /**
   * 银行卡照片,隔开
   */
  bankCardImg?: string
  /**
   * 收款银行名称
   */
  bankName?: string
  /**
   * 公司营业执照,隔开
   */
  businessLicenseImg?: string
  /**
   * 商单ID
   */
  commercialOrderId?: number
  /**
   * 达人邀约ID
   */
  expertInviteId?: number
  /**
   * 收款人性别 0-先生 1-女士 3-小姐
   */
  gender?: number
  /**
   * 身份证居住地址
   */
  idCardAddress?: string
  /**
   * 身份证照片,隔开
   */
  idCardImg?: string
  /**
   * 身份证姓名
   */
  idCardName?: string
  /**
   * 身份证号码
   */
  idCardNumber?: string
  /**
   * 联系电话
   */
  phone?: string
  /**
   * 打款类型: 0-个人, 1-公司
   */
  recipientType?: number
}

// 添加打款信息
export const saveCommercialPayment = async (
  params: CommercialOrderPaymentRecipientInfoQo
) => {
  return await request<boolean>({
    url: '/api-uchoice/app/commercial/payment/save',
    method: 'post',
    data: params
  })
}
