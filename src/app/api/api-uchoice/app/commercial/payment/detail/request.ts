import { request } from '@/lib/client/request'
import { ApiResponse } from '@/lib/types/http'

/**
 * 打款详情查询参数
 */
export interface CommercialPaymentDetailParams {
  /**
   * 商单编号
   */
  commercialOrderId?: number
  /**
   * 达人邀约id
   */
  expertInviteId?: number
}

/**
 * 商单打款收款人详情信息
 */
export interface CommercialOrderPaymentRecipientDetailVo {
  /**
   * 审核状态 1-待审核 2-待打款 3-已打款 4-审核拒绝
   */
  auditStatus?: number
  /**
   * 收款卡号
   */
  bankAccountNumber?: string
  /**
   * 银行卡照片,隔开
   */
  bankCardImg?: string
  /**
   * 收款银行名称
   */
  bankName?: string
  /**
   * 公司营业执照,隔开
   */
  businessLicenseImg?: string
  /**
   * 收款人性别 0-先生 1-女士 3-小姐
   */
  gender?: number
  /**
   * 主键ID
   */
  id?: number
  /**
   * 身份证居住地址
   */
  idCardAddress?: string
  /**
   * 身份证照片,隔开
   */
  idCardImg?: string
  /**
   * 身份证姓名
   */
  idCardName?: string
  /**
   * 身份证号码
   */
  idCardNumber?: string
  /**
   * 联系电话
   */
  phone?: string
  /**
   * 审核原因
   */
  reason?: string
  /**
   * 打款类型: 0-个人, 1-公司
   */
  recipientType?: number
}

// 打款详情
export const getCommercialPaymentDetail = async (
  params: CommercialPaymentDetailParams
) => {
  return await request<CommercialOrderPaymentRecipientDetailVo>({
    url: '/api-uchoice/app/commercial/payment/detail',
    method: 'get',
    params
  })
}
