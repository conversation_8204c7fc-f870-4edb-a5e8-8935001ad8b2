import { request } from '@/lib/client/request'

/**
 * Bank
 */
export interface Bank {
  /**
   * 银行code
   */
  code?: string
  createTime?: number
  /**
   * 扩展信息
   */
  ext?: string
  /**
   * Id
   */
  id?: number
  /**
   * 是否支持Payout(0.否 1.是)
   */
  isPayout?: string
  /**
   * 银行名称
   */
  name?: string
  /**
   * swiftCode码
   */
  swiftCode?: string
  /**
   * 1-泰国本地 2-跨境
   */
  type?: number
  [property: string]: any
}

// 获取银行信息列表
export const bankList = async () => {
  return await request<Bank[]>({
    url: 'api-base/bank/list',
    method: 'get'
  })
}
