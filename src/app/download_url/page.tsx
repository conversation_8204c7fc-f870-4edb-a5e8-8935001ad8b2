import { metadataTemplate, inApp } from '@/lib/server/utils'
import React from 'react'
import { i18nS } from '@/lib/server/i18n'
import Inner from './inner'

export async function generateMetadata() {
  if (inApp()) {
    return metadataTemplate({ title: i18nS.t('DownloadUrl.downloadTitle') })
  }
  const title = i18nS.t('DownloadUrl.downloadTitle')

  return metadataTemplate({ title })
}

export default async function Index() {
  return <Inner />
}
