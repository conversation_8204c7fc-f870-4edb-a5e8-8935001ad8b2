.container {
    width: 750px;
    height: 100vh;
    background-color: #000;
    padding: 24px 24px 154px 24px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    z-index: 1;
    position: relative;
    border: 1px solid #000;
}

.container::-webkit-scrollbar {
    display: none;
}

.download_logo {
    width: 320px;
    height: 194px;
    margin-top: 40px;
    margin-bottom: 40px;
    object-fit: contain
}

.app_store {
    width: 530px;
    height: 120px;
    margin-top: 48px;
    margin-bottom: 44px;
    object-fit: contain
}

.google_play {
    width: 530px;
    height: 120px;

    object-fit: contain
}

.nav_bg {
    background-color: #000 !important;
    position: absolute;
    z-index: 100;
}


.nav_title {
    font-weight: bold;
    font-size: 32px;
    color: #FFFFFF;

}

.tiktok_title {
    // display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 36px;
    color: #FFF;
    text-align: center;

}

.creator_boxs {
    width: 100%;

    word-break: break-all;
    word-wrap: break-word;
}

.creator_count {
    font-weight: bold;
    font-size: 32px;
    color: #FFFFFF;
}

.color_words {
    font-weight: bold;
    font-size: 36px;
    color: #FE2C55;
}

.creator_title {

    font-weight: 400;
    font-size: 32px;
    color: #FFFFFF;

}

.creator_color_words {
    font-weight: 400;
    font-size: 32px;
    color: #FE2C55;
}

.uchoice_pro {
    width: 100%;
    padding: 60px 0 0 24px;
    font-weight: bold;
    font-size: 30px;
    color: #FFFFFF;

}

.arr_text {

    font-size: 26px;
    color: #FFFFFF;
    line-height: 60px;
    font-weight: normal;

}

.bottom {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 240px;
    font-weight: normal;
    font-size: 24px;
    color: #FFFFFF;
}

.bottom_arr_color_text {
    color: #2576ff;
}