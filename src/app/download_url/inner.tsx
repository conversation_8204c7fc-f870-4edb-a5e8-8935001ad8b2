'use client'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import InsertColoredText from '@/components/InsertColoredText'
import ImageWithPreload from '../components/ImageWithPreload'
import { resizeImageUrl, rpxToPx } from '@/lib/client/utils'
import { statistic } from '@/lib/statistic'
import { downloadH5Data } from '../api/api-uchoice/tt/h5/downloadH5Data'
import { formatPrice, formatThous } from '@/lib/format'
import { TransComponent } from '../components/Trans'
import { isLanguageTH, isRegionTH, isRegionVN } from '@/lib/utils'
import { Trans } from 'react-i18next'

const app_store = '/images/download/app_store.png'
const download_logo = '/images/download/download_logo.png'
const google_play = '/images/download/google_play.png'
const app_store_vi = '/images/download/app_store_vi.png'
const google_play_th = '/images/download/google_play_th.png'
const app_store_th = '/images/download/app_store_th.png'
const google_play_vi = '/images/download/google_play_vi.png'
const DownloadUrl = () => {
  const [data, setData] = useState<any>()
  const mounted = useMounted()
  const arr = [
    i18n.t('DownloadUrl.freePlay'),
    i18n.t('DownloadUrl.highCom'),
    i18n.t('DownloadUrl.tiktokCheck')
  ]
  const bottomArr = [
    i18n.t('DownloadUrl.contactCustomer'),
    i18n.t('DownloadUrl.customerService'),
    i18n.t('DownloadUrl.publicHolidays')
  ]

  useLayoutEffect(() => {
    setTimeout(() => {
      // @ts-ignore
      window?.ttq?.track('ViewContent', {
        contents: [
          {
            content_id: 'download_view', // string. ID of the product. Example: "1077218".
            content_type: '', // string. Either product or product_group.
            content_name: '' // string. The name of the page or product. Example: "shirt".
          }
        ]
      })
    }, 1000)
  }, [])

  useEffect(() => {
    getDownloadData()
  }, [])
  const getDownloadData = () => {
    downloadH5Data().then(res => {
      if (res.code == 200) {
        console.log(res, 'jopjopk')
        setData(res.result)
      }
    })
  }

  const handleDownload = type => {
    // @ts-ignore
    window?.ttq?.track('ClickButton', {
      contents: [
        {
          content_id: 'download_click', // string. ID of the product. Example: "1077218".
          content_type: type, // string. Either product or product_group.
          content_name: '' // string. The name of the page or product. Example: "shirt".
        }
      ]
    })

    let url = ''
    let eventName = ''
    if (type === 'apple') {
      url = 'https://apps.apple.com/cn/app/uchoice/id1672889647'
      eventName = 'h5_uchoice_download_apple_click'
    } else if (type === 'android') {
      url = 'https://download.youpik.uchoice.pro/public/apk/uchoice-pro.apk'
      eventName = 'h5_uchoice_download_android_click'
    } else if (type === 'google') {
      url = 'https://play.google.com/store/apps/details?id=com.ypk.uceapp'
      eventName = 'h5_uchoice_download_google_click'
    }
    if (url && eventName) {
      window.location.href = url
      statistic({
        eventName: eventName
      })
    }
  }
  return (
    mounted && (
      <TransparentNavPage
        title={i18n.t('DownloadUrl.uChoicePro')}
        transparent={false}
        langBtn
        className={styles.nav_bg}
        titleClass={styles.nav_title}
      >
        <div className={styles.container}>
          <InsertColoredText
            text={i18n.t('DownloadUrl.tiktokHead')}
            className={styles.tiktok_title}
          >
            <span className={styles.color_words}>
              {i18n.t('DownloadUrl.TikTok头部达人')}
            </span>
          </InsertColoredText>
          <img className={styles.download_logo} src={download_logo}></img>

          <div className={styles.creator_boxs}>
            <span className={styles.creator_title}>
              <Trans
                i18nKey="DownloadUrl.count达人使用uchioce"
                components={{
                  b: <span className="font-bold"></span>
                }}
                values={{
                  count: formatThous(data?.anchorCount)
                }}
              ></Trans>
            </span>

            {/* &nbsp;
            {isRegionTH() ? (
              <span className={styles.creator_color_words}>
                {formatPrice(data?.earnMoney, true)}
              </span>
            ) : null} */}
          </div>
          <button onClick={() => handleDownload('apple')}>
            <TransComponent
              th={<img className={styles.app_store} src={app_store_th}></img>}
              vi={<img className={styles.app_store} src={app_store_vi}></img>}
              en={<img className={styles.app_store} src={app_store}></img>}
            ></TransComponent>
          </button>
          <button onClick={() => handleDownload('google')}>
            <TransComponent
              th={
                <img className={styles.google_play} src={google_play_th}></img>
              }
              vi={
                <img className={styles.google_play} src={google_play_vi}></img>
              }
              en={<img className={styles.google_play} src={google_play}></img>}
            ></TransComponent>
          </button>
          <div className={styles.uchoice_pro}>
            <div>{i18n.t('DownloadUrl.uChoiceProWith')}</div>
            {arr.map((item, index) => {
              return (
                <div className={styles.arr_text} key={index}>
                  {item}
                </div>
              )
            })}
          </div>
          {isLanguageTH() && (
            <div className={styles.bottom}>
              {bottomArr.map((item, index) => {
                return (
                  <div
                    onClick={() => {
                      if (index === 0) {
                        window.location.href =
                          'https://line.me/R/ti/p/@932acdjr'
                      }
                      statistic({
                        eventName: 'dist_uchoice_download_chat_click'
                      })
                    }}
                    className={
                      index !== 0
                        ? styles.bottom_arr_text
                        : styles.bottom_arr_color_text
                    }
                    key={index}
                  >
                    {item}
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </TransparentNavPage>
    )
  )
}

export default DownloadUrl
