/* eslint-disable @next/next/no-sync-scripts */
import './globals.css'
import { Toaster } from 'react-hot-toast'
import { Loading } from '@/lib/client/loading'
import { StatisticWatcher } from '@/lib/statistic/Watcher'
import { metadataTemplate } from '@/lib/server/utils'
import { Debug } from '@/components/Debug'
import { i18nSInit } from '@/lib/server/i18n'
import { GoAppBar } from '@/components/GoAppBar'
import type { Viewport } from 'next'
import { DebounceEverywhere } from '@/components/DebounceEverywhere'
import { FirstAddToShowcaseAlert } from '@/components/FirstAddToShowcaseAlert'
import { TikTokSDK } from '@/components/Scripts/tiktokSdk'
import { CounponFloatButton } from '@/components/CounponFloatButton'
import dynamic from 'next/dynamic'
import { MetaSDK } from '@/components/Scripts/metaSdk'

const VideoPreviewer = dynamic(() => import('@/components/VideoPreviewer'), {
  ssr: false
})

const UpdateAlert = dynamic(() => import('@/components/UpdateAlert'), {
  ssr: false
})

export async function generateMetadata() {
  i18nSInit()
  return metadataTemplate()
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false
}

export default function RootLayout({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <html>
      <head>
        <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
        <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.svg" />
      </head>
      <body>
        <GoAppBar />
        <CounponFloatButton />
        <Debug />
        <TikTokSDK />
        <MetaSDK />
        {children}
        <Toaster />
        <Loading />
        <StatisticWatcher />
        <DebounceEverywhere />
        <FirstAddToShowcaseAlert></FirstAddToShowcaseAlert>
        <VideoPreviewer></VideoPreviewer>
        <UpdateAlert></UpdateAlert>
      </body>
    </html>
  )
}
