'use client'

import {
  TopGrandientCard,
  TransparentNavPage
} from '@/components/TransparentNavPage'
import React, { useEffect, useState } from 'react'
import { rankHomePageList } from '@/app/api/api-uchoice/tiktok/item/rankHomePageList/request'
import { TikTokRankHomePageVo } from '@/app/api/api-uchoice/tiktok/item/rankHomePageList/dtos'
import { i18n } from '@/lib/client/i18n'
import { Image } from '@/components/Image'
import { Tabs } from './components/Tabs'
import ProductList from '@/components/product_list'
import AnchorList from '@/components/anchor_list'
import ShopList from '@/components/shop_list'
import { useRouteParams } from '@/lib/hooks/useRouteParams'

export const Inner = () => {
  const routeParams = useRouteParams<{ type: '1' | '2' | '3' }>()
  const [homeData, setHomeData] = useState<TikTokRankHomePageVo>()
  const [activeIndex, setActiveIndex] = useState(0)

  const onChange = (index: number) => {
    setActiveIndex(index)
  }

  useEffect(() => {
    if (routeParams?.type) {
      setActiveIndex(Number(routeParams?.type) - 1)
    }
  }, [])

  useEffect(() => {
    fetch()
  }, [])

  const fetch = async () => {
    const { code, result } = await rankHomePageList()
    if (code === 200) {
      setHomeData(result)
    }
  }

  const onRefresh = () => {
    return new Promise(async resolve => {
      await fetch()
      resolve(true)
    })
  }

  return (
    // <PullToRefresh backgroundColor="#FFEDE4" onRefresh={onRefresh}>
    <TransparentNavPage
      title={i18n.t('TiktokData.Ranking')}
      showBackButton={false}
    >
      <TopGrandientCard>
        <div className="px-[24px]">
          {homeData?.img && (
            <div className="mt-[16px] overflow-hidden rounded-[8px]">
              <Image
                className="h-[200px] w-[702px] object-cover"
                src={homeData?.img || ''}
              ></Image>
            </div>
          )}
        </div>

        <Tabs index={activeIndex} onChange={onChange}></Tabs>
      </TopGrandientCard>

      {activeIndex === 0 && <ProductList></ProductList>}
      {activeIndex === 1 && <AnchorList></AnchorList>}
      {activeIndex === 2 && <ShopList></ShopList>}

      {/* <div className="bg-white px-[24px]">
        <Section
          title={i18n.t('TiktokData.近30天热销商品榜')}
          onMore={() => {
            statistic({
              eventName: EventName.tiktok_data_see_more_hot_sell_products
            })
            router.push('/tiktok_data/product_list/page')
          }}
        >
          {homeData?.productList?.map((item, index) => (
            <div key={`item_${index}`}>
              {index !== 0 && <div className="h-[40px]"></div>}
              <WithRankNum index={index}>
                <ItemCell item={item}></ItemCell>
              </WithRankNum>
              {index !== homeData!.productList!.length - 1 && (
                <div className="mt-[40px] h-[2px] w-full bg-grayEE"></div>
              )}
            </div>
          ))}
        </Section>

        <Section
          title={i18n.t('TiktokData.带货达人榜')}
          desc={
            homeData?.anchorProductTime
              ? formatTime(homeData?.anchorProductTime)
              : ''
          }
          onMore={() => {
            statistic({
              eventName: EventName.tiktok_data_see_more_e_commerce_creator
            })
            router.push('/tiktok_data/anchor_list/page')
          }}
        >
          {homeData?.anchorProductList?.map((item, index) => (
            <div key={`item_${index}`}>
              {index !== 0 && <div className="h-[40px]"></div>}
              <WithRankNum index={index}>
                <KolCell sellGoodsKol={item}></KolCell>
              </WithRankNum>
              {index !== homeData!.anchorProductList!.length - 1 && (
                <div className="mt-[40px] h-[2px] w-full bg-grayEE"></div>
              )}
            </div>
          ))}
        </Section>

        <Section
          title={i18n.t('TiktokData.涨粉达人榜')}
          desc={
            homeData?.anchorFansTime ? formatTime(homeData?.anchorFansTime) : ''
          }
          onMore={() => {
            statistic({
              eventName: EventName.tiktok_data_see_more_fan_growth_rank
            })
            router.push('/tiktok_data/anchor_list/page?tabIndex=6')
          }}
        >
          {homeData?.anchorFansList?.map((item, index) => (
            <div key={`item_${index}`}>
              {index !== 0 && <div className="h-[40px]"></div>}
              <WithRankNum index={index}>
                <KolCell fansKol={item}></KolCell>
              </WithRankNum>
              {index !== homeData!.anchorFansList!.length - 1 && (
                <div className="mt-[40px] h-[2px] w-full bg-grayEE"></div>
              )}
            </div>
          ))}
        </Section>

        <div className="h-[48px]"></div>
      </div> */}
    </TransparentNavPage>
    // </PullToRefresh>
  )
}
