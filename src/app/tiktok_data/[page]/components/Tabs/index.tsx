/* eslint-disable @next/next/no-img-element */
import { i18n } from '@/lib/client/i18n'
import { px2rem } from '@/lib/client/utils'
import { AppColors } from '@/lib/const'

const rank_tab_bg_normal = '/images/tiktok_data/rank_tab_bg_normal.png'
const rank_tab_bg_selected = '/images/tiktok_data/rank_tab_bg_selected.png'
const rank_tab_product_normal =
  '/images/tiktok_data/rank_tab_product_normal.png'
const rank_tab_product_selected =
  '/images/tiktok_data/rank_tab_product_selected.png'
const rank_tab_creator_normal =
  '/images/tiktok_data/rank_tab_creator_normal.png'
const rank_tab_creator_selected =
  '/images/tiktok_data/rank_tab_creator_selected.png'
const rank_tab_shop_normal = '/images/tiktok_data/rank_tab_shop_normal.png'
const rank_tab_shop_selected = '/images/tiktok_data/rank_tab_shop_selected.png'

interface Props {
  index: number
  onChange: (index: number) => void
}

export const Tabs = ({ index = 0, onChange }: Props) => {
  return (
    <div className="flex pt-[28px]">
      <div className="relative h-[100px] w-[250px]" onClick={() => onChange(0)}>
        <img
          src={index === 0 ? rank_tab_bg_selected : rank_tab_bg_normal}
          className="absolute bottom-0 w-[250px]"
        ></img>
        <div
          className="absolute bottom-0 flex w-full items-center justify-center"
          style={{ height: index === 0 ? px2rem(100) : px2rem(88) }}
        >
          <img
            src={
              index === 0 ? rank_tab_product_selected : rank_tab_product_normal
            }
            style={{ width: index === 0 ? px2rem(68) : px2rem(48) }}
          ></img>
          <div className="w-[12px]"></div>
          <span
            className="text-[28px]"
            style={{
              color: index === 0 ? 'white' : AppColors.gray6E,
              fontWeight: index === 0 ? 'bold' : 'normal'
            }}
          >
            {i18n.t('TiktokData.商品')}
          </span>
        </div>
      </div>
      <div className="relative h-[100px] w-[250px]" onClick={() => onChange(1)}>
        <img
          src={index === 1 ? rank_tab_bg_selected : rank_tab_bg_normal}
          className="absolute bottom-0 w-[250px]"
        ></img>
        <div
          className="absolute bottom-0 flex w-full items-center justify-center"
          style={{ height: index === 1 ? px2rem(100) : px2rem(88) }}
        >
          <img
            src={
              index === 1 ? rank_tab_creator_selected : rank_tab_creator_normal
            }
            style={{ width: index === 1 ? px2rem(68) : px2rem(48) }}
          ></img>
          <div className="w-[12px]"></div>
          <span
            className="text-[28px]"
            style={{
              color: index === 1 ? 'white' : AppColors.gray6E,
              fontWeight: index === 1 ? 'bold' : 'normal'
            }}
          >
            {i18n.t('TiktokData.创作者')}
          </span>
        </div>
      </div>
      <div className="relative h-[100px] w-[250px]" onClick={() => onChange(2)}>
        <img
          src={index === 2 ? rank_tab_bg_selected : rank_tab_bg_normal}
          className="absolute bottom-0 w-[250px]"
        ></img>
        <div
          className="absolute bottom-0 flex w-full items-center justify-center"
          style={{ height: index === 2 ? px2rem(100) : px2rem(88) }}
        >
          <img
            src={index === 2 ? rank_tab_shop_selected : rank_tab_shop_normal}
            style={{ width: index === 2 ? px2rem(68) : px2rem(48) }}
          ></img>
          <div className="w-[12px]"></div>
          <span
            className="text-[28px]"
            style={{
              color: index === 2 ? 'white' : AppColors.gray6E,
              fontWeight: index === 2 ? 'bold' : 'normal'
            }}
          >
            {i18n.t('TiktokData.店铺')}
          </span>
        </div>
      </div>
    </div>
  )
}
