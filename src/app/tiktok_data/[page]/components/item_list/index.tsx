import React, { useState, useEffect } from 'react'
import { StoreCell } from '@/app/tiktok_data/components/store/storeCell'
import { WithRankNum } from '@/app/tiktok_data/components/rankNum'
import { WithBottomStoreInfo } from '@/app/tiktok_data/components/product/itemCell'
import styles from './index.module.scss'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useRouter } from '@/lib/hooks/useRouter'
import { useMounted } from '@/lib/hooks/useMounted'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { i18n } from '@/lib/client/i18n'

const ItemList = ({ itemList, fetchData, hasMore, total, isLoading }) => {
  const router = useRouter()
  const mounted = useMounted()
  useEffect(() => {}, [hasMore, isLoading, total])
  return (
    <div className={styles.item_box}>
      {!isLoading && total == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.empty}></StatusView>
        </div>
      )}
      {isLoading && itemList.length == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.loading}></StatusView>
        </div>
      )}
      {mounted && itemList && itemList.length > 0 && (
        <InfiniteScroll
          dataLength={itemList.length}
          next={fetchData}
          hasMore={hasMore}
          loader={
            <div className={styles.noMore}>{i18n.t('Common.Loading')}</div>
          }
          endMessage={
            itemList.length == total ? (
              <div className={styles.noMore}>{i18n.t('Common.Empty')}</div>
            ) : null
          }
        >
          {itemList.map((item, index) => {
            return (
              <div className={styles.store_item_box} key={index}>
                <WithBottomStoreInfo item={item}>
                  <WithRankNum index={index}>
                    <StoreCell item={item}></StoreCell>
                  </WithRankNum>
                </WithBottomStoreInfo>
              </div>
            )
          })}
        </InfiniteScroll>
      )}
    </div>
  )
}

export default ItemList
