'use client'
import { useEffect, useRef, useState } from 'react'
import DetailDesc from '../components/DetailDesc'
import AudienceAnalysisCard from '../components/AudienceAnalysisCard'
import DataAnalysisCard from '../components/DataAnalysisCard'
import HotspotTrending from '../components/HotspotTrending'
import RelatedVideoCard from '../components/RelatedVideoCard'
import DetailOperate from '../components/DetailOperate'
import DetailFooter from '../components/DetailFooter'
import {
  TopGrandientCard,
  TransparentNavPage
} from '@/components/TransparentNavPage'
import { requestHotspotDetail } from '@/app/api/api-uchoice/tiktok/topic/getHotspotDetail'
import { loading } from '@/lib/client/loading'
import { i18n } from '@/lib/client/i18n'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import ComponentCard from '@/app/components/ComponentCard'
import styles from './inner.module.scss'
import StatusView, { StatusViewType } from '@/app/components/StatusView'

interface InnnerProps {
  id: string
}

const Inner: React.FC<InnnerProps> = props => {
  const params: any = useRouteParams()

  const { id } = props
  const [detail, setDetail] = useState<any>({})
  const [dateType, setDateType] = useState<number>(1) // 日期类型 1-近7天 2-近30天 3-近120天 4-近12个月 5-近3年
  const dateTypeRef = useRef<number>(1)

  const getHotspotDetail = async () => {
    try {
      loading.show({ userInteractive: true })
      setDetail({})
      const result = await requestHotspotDetail(
        id,
        dateTypeRef.current,
        params && params.categoryId ? params.categoryId : ''
      )
      if (result.code === 200 && result.result) {
        setDetail(result.result)
      } else {
        setDetail({})
      }
      loading.hide()
    } catch (error) {
      loading.hide()
    }
  }

  useEffect(() => {
    getHotspotDetail()
  }, [])

  const handleChangeDateType = (type: number) => {
    setDateType(type)
    dateTypeRef.current = type
    getHotspotDetail()
  }

  return (
    <TransparentNavPage
      title={i18n.t('HotSpot.TikTok热词详情')}
      showShareButton
    >
      <TopGrandientCard gradientColors={['#ffede4', '#f5f5f5']}>
        <div className={styles.top_gradient_container}>
          <DetailDesc detail={detail}></DetailDesc>
          <div className={styles.data_analysis}>
            <DataAnalysisCard
              detail={detail}
              dateType={dateType}
              setDateType={handleChangeDateType}
            ></DataAnalysisCard>
          </div>
        </div>
      </TopGrandientCard>
      <div className={styles.inner_container}>
        <ComponentCard
          title={i18n.t('HotSpot.热度趋势')}
          className={styles.hotspot_trending}
        >
          {detail.trend ? (
            <HotspotTrending trend={detail.trend}></HotspotTrending>
          ) : (
            <StatusView status={StatusViewType.empty}></StatusView>
          )}
        </ComponentCard>
        {/* <div className={styles.related_video}>
          <RelatedVideoCard></RelatedVideoCard>
        </div> */}
        <div className={styles.audience_analysis}>
          <AudienceAnalysisCard detail={detail}></AudienceAnalysisCard>
        </div>
        <DetailFooter></DetailFooter>
        <DetailOperate id={id} detail={detail}></DetailOperate>
      </div>
    </TransparentNavPage>
  )
}

export default Inner
