import Inner from './inner'
import { metadataTemplate, inApp } from '@/lib/server/utils'
import { isLanguageEN } from '@/lib/utils'
import { requestHotspotDetailServer } from '@/app/api/api-uchoice/tiktok/topic/getHotspotDetailServer'
import { i18nS } from '@/lib/server/i18n'
import { formatNumberStr } from '@/lib/utils'
import styles from './index.module.scss'

interface Props {
  params: {
    id: string
  }
}

export default async function HotspotDetail(props: Props) {
  return (
    <div className={styles.hotspot_detail_container}>
      <Inner id={props.params.id}></Inner>
    </div>
  )
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: '' })
  }

  const iconTh = `${process.env.NEXT_PUBLIC_URL}/images/hotspot/hotspot_share_th.png`
  const iconEn = `${process.env.NEXT_PUBLIC_URL}/images/hotspot/hotspot_share_en.png`
  const icon = isLanguageEN() ? iconEn : iconTh
  let title = ''
  let description = ''
  const res: any = await requestHotspotDetailServer(params.id)
  if (res) {
    const { publishCount, videoViews } = res
    const descList = [
      `${i18nS.t('HotSpot.帖子数')}:${formatNumberStr(publishCount || 0)}`,
      `${i18nS.t('HotSpot.观看数Detail')}:${formatNumberStr(videoViews || 0)}`
    ]
    description = descList.join(' ')
    title = res.hotspotName ? `#${res.hotspotName}` : ''
  }

  return metadataTemplate({ title, description, icon })
}
