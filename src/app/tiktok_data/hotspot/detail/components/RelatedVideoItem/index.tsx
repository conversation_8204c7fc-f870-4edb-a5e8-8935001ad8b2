import React from 'react'
import classNames from 'classnames'
import Image from 'next/image'
import banner from '@/../public/images/fmcg/banner.jpg'
import icon_play from '@/../public/images/hotspot/icon_play.png'
import icon_like from '@/../public/images/hotspot/icon_like.png'
import icon_comment from '@/../public/images/hotspot/icon_comment.png'
import icon_share from '@/../public/images/hotspot/icon_share.png'
import styles from './index.module.scss'

type RelatedVideoItemProps = {} & BaseComponentProps

const RelatedVideoItem: React.FC<RelatedVideoItemProps> = props => {
  const { className, style } = props

  return (
    <div
      className={classNames([styles.related_video_item_container, className])}
      style={style}
    >
      <div className={styles.thumbnail_container}>
        <div className={styles.thumbnail_container}>
          <Image alt="" className={styles.thumbnail} src={banner}></Image>
        </div>
        <div className={styles.icon_play_container}>
          <Image alt="" className={styles.icon_play} src={icon_play}></Image>
        </div>
        <div className={styles.date}>00:01:32</div>
      </div>
      <div className={styles.content_container}>
        <div className={styles.title}>
          tiktok视频文案tiktok视频文案tiktok视频文案tiktok视频文案tiktok视频文案tiktok视频文案tiktok视频
        </div>
        <div className={styles.desc}>28/09/2023 10:59</div>
        <div className={styles.operate_container}>
          <div className={styles.operate_item_container}>
            <Image
              alt=""
              className={styles.operate_item_icon}
              src={icon_like}
            ></Image>
            <div className={styles.label}>190.7k</div>
          </div>
          <div className={styles.operate_item_container}>
            <Image
              alt=""
              className={styles.operate_item_icon}
              src={icon_comment}
            ></Image>
            <div className={styles.label}>190.7k</div>
          </div>
          <div className={styles.operate_item_container}>
            <Image
              alt=""
              className={styles.operate_item_icon}
              src={icon_share}
            ></Image>
            <div className={styles.label}>190.7k</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RelatedVideoItem
