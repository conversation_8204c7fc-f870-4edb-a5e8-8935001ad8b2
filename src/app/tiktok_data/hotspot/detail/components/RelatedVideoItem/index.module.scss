.related_video_item_container {
  position: relative;
  display: flex;
  margin-bottom: 36px;

  .thumbnail_container {
    width: 160px;
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 16px;

    .thumbnail_container {
      width: 100%;
      height: 100%;

      .thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .icon_play_container {
      position: absolute;
      top: 76px;
      left: 56px;

      .icon_play {
        width: 48px;
        height: 48px;
      }
    }

    .date {
      background: #000000;
      opacity: 0.48;
      border-radius: 0px 0px 8px 8px;
      height: 36px;
      font-size: 24px;
      color: #FFFFFF;
      line-height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      bottom: 0;
      left: 0;
      width: 160px;
    }
  }
  
  .content_container {
    display: flex;
    flex-direction: column;
    flex: 1;

    .title {
      font-weight: bold;
      font-size: 28px;
      color: #303030;
      line-height: 40px;
      font-style: normal;
      margin-bottom: 12px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      -webkit-line-clamp: 2;
    }

    .desc {
      font-size: 24px;
      color: #9A9A9A;
      line-height: 28px;
      margin-bottom: 42px;
    }

    .operate_container {
      display: flex;
      justify-content: space-between;

      .operate_item_container {
        display: flex;
        align-items: center;

        .operate_item_icon {
          width: 36px;
          height: 36px;
          margin-right: 8px;
        }
  
        .label {
          font-weight: bold;
          font-size: 24px;
          color: #303030;
          line-height: 28px;
        }
      }
    }
  }
}