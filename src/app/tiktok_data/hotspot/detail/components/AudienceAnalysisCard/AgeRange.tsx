'use client'
import React, { useEffect, useRef } from 'react'
import classNames from 'classnames'
import { nanoid } from 'nanoid'
import * as echarts from 'echarts'
import { rpxToPx } from '@/lib/client/utils'
import styles from './index.module.scss'

type AgeRangeProps = {
  audienceAges: any
} & BaseComponentProps

const colors = ['#FF2751', '#00DBD1', '#0065FF']
const getRichColor = () => {
  const richColor = {}
  colors.forEach((item, idx) => {
    richColor[`percent${idx}`] = {
      color: colors[idx],
      fontSize: rpxToPx(36),
      fontWeight: 'bold'
    }
    richColor[`name${idx}`] = {
      color: colors[idx],
      fontSize: rpxToPx(28)
    }
  })
  return richColor
}

const AgeRange: React.FC<AgeRangeProps> = props => {
  const { className, style, audienceAges } = props
  const idRef = useRef(nanoid())
  const myChartRef = useRef<echarts.ECharts | null>(null)

  const initChart = () => {
    const audienceAgesObj = JSON.parse(audienceAges)
    myChartRef.current = echarts.init(document.getElementById(idRef.current))
    myChartRef.current.setOption({
      tooltip: {
        show: false
      },
      legend: {
        show: false
      },
      silent: true,
      avoidLabelOverlap: false,
      series: [
        {
          name: 'Age Range',
          type: 'pie',
          radius: ['40%', '70%'],
          label: {
            show: true,
            position: 'outside',
            formatter: function (params) {
              const {
                data: { name, value },
                percent,
                dataIndex
              } = params
              return `{percent${dataIndex}|${Math.round(
                value
              )}%}\n{name${dataIndex}|${name}}`
            },
            rich: getRichColor()
          },
          emphasis: {},
          labelLine: {
            show: true
          },
          data: audienceAgesObj.map(item => {
            return {
              value: item.score,
              name: getLevelName(item.age_level)
            }
          }),
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex]
              }
            }
          }
        }
      ]
    })
  }

  const getLevelName = (value: number) => {
    if (value === 3) {
      return '18-24'
    }
    if (value === 4) {
      return '25-34'
    }
    if (value === 5) {
      return '35+'
    }
    return ''
  }

  useEffect(() => {
    initChart()
  }, [])

  return (
    <div
      id={idRef.current}
      className={classNames([styles.age_range_container, className])}
      style={{
        height: rpxToPx(400),
        ...style
      }}
    ></div>
  )
}

export default AgeRange
