import React from 'react'
import ComponentCard from '@/app/components/ComponentCard'
import classNames from 'classnames'
import CardSubTitle from '@/app/components/CardSubTitle'
import AgeRange from './AgeRange'
import RelatedInterests from './RelatedInterests'
import styles from './index.module.scss'
import { i18n } from '@/lib/client/i18n'

type AudienceAnalysisCardProps = {
  detail: any
} & BaseComponentProps

const AudienceAnalysisCard: React.FC<AudienceAnalysisCardProps> = props => {
  const { className, style, detail } = props
  const { audienceAges, audienceInterests } = detail || {}

  return (
    (audienceAges || audienceInterests) && (
      <ComponentCard
        title={i18n.t('HotSpot.受众分析')}
        subTitle={i18n.t('HotSpot.与热点互动过的观众数据')}
      >
        <div
          className={classNames([
            styles.audience_analysis_container,
            className
          ])}
          style={style}
        >
          {audienceAges && (
            <CardSubTitle
              title={i18n.t('HotSpot.年龄段')}
              tips={i18n.t('HotSpot.tips2')}
              className={styles.subtitle}
            ></CardSubTitle>
          )}
          {audienceAges && (
            <AgeRange
              audienceAges={audienceAges}
              style={{ marginBottom: '3.2vw' }}
            ></AgeRange>
          )}
          {audienceInterests && (
            <CardSubTitle
              title={i18n.t('HotSpot.相关兴趣点')}
              tips={i18n.t('HotSpot.tips1')}
              className={styles.subtitle}
            ></CardSubTitle>
          )}
          {audienceInterests && (
            <RelatedInterests
              audienceInterests={audienceInterests}
            ></RelatedInterests>
          )}
        </div>
      </ComponentCard>
    )
  )
}

export default AudienceAnalysisCard
