'use client'
import React, { useMemo } from 'react'
import Image from 'next/image'
import bg_related_interests from '@/../public/images/hotspot/bg_related_interests.png'
import icon_fire_big from '@/../public/images/hotspot/icon_fire_big.png'
import icon_fire_small from '@/../public/images/hotspot/icon_fire_small.png'
import styles from './index.module.scss'

type RelatedInterestsProps = {
  audienceInterests: any
} & BaseComponentProps

const RelatedInterests: React.FC<RelatedInterestsProps> = props => {
  const { className, style, audienceInterests } = props

  const audienceInterestsObj = useMemo(() => {
    if (audienceInterests) {
      return JSON.parse(audienceInterests)
    }
    return undefined
  }, [audienceInterests])

  return (
    <div className={styles.related_interests} style={style}>
      <div className={styles.bg_related_interests_container}>
        <Image
          alt=""
          className={styles.bg_related_interests}
          src={bg_related_interests}
        ></Image>
      </div>
      {audienceInterestsObj &&
        audienceInterestsObj[3] &&
        audienceInterestsObj[3].interestInfo &&
        audienceInterestsObj[3].interestInfo.value && (
          <div className={styles.tag1_container}>
            <div className={styles.label}>
              {audienceInterestsObj[3].interestInfo.value}
            </div>
            <div className={styles.right_container}>
              <Image
                alt=""
                src={icon_fire_small}
                className={styles.icon_fire_small}
              ></Image>
              <div className={styles.hot}>
                {audienceInterestsObj &&
                audienceInterestsObj[3] &&
                audienceInterestsObj[3].score
                  ? audienceInterestsObj[3].score
                  : 0}
              </div>
            </div>
          </div>
        )}
      {audienceInterestsObj &&
        audienceInterestsObj[0] &&
        audienceInterestsObj[0].interestInfo &&
        audienceInterestsObj[0].interestInfo.value && (
          <div className={styles.tag2_container}>
            <div className={styles.label}>
              {audienceInterestsObj[0].interestInfo.value}
            </div>
            <div className={styles.right_container}>
              <Image
                alt=""
                src={icon_fire_big}
                className={styles.icon_fire_big}
              ></Image>
              <div className={styles.hot}>
                {audienceInterestsObj &&
                audienceInterestsObj[0] &&
                audienceInterestsObj[0].score
                  ? audienceInterestsObj[0].score
                  : 0}
              </div>
            </div>
          </div>
        )}
      {audienceInterestsObj &&
        audienceInterestsObj[4] &&
        audienceInterestsObj[4].interestInfo &&
        audienceInterestsObj[4].interestInfo.value && (
          <div className={styles.tag3_container}>
            <div className={styles.label}>
              {audienceInterestsObj[4].interestInfo.value}
            </div>
            <div className={styles.right_container}>
              <Image
                alt=""
                src={icon_fire_small}
                className={styles.icon_fire_small}
              ></Image>
              <div className={styles.hot}>
                {audienceInterestsObj &&
                audienceInterestsObj[4] &&
                audienceInterestsObj[4].score
                  ? audienceInterestsObj[4].score
                  : 0}
              </div>
            </div>
          </div>
        )}
      {audienceInterestsObj &&
        audienceInterestsObj[1] &&
        audienceInterestsObj[1].interestInfo &&
        audienceInterestsObj[1].interestInfo.value && (
          <div className={styles.tag4_container}>
            <div className={styles.label}>
              {audienceInterestsObj[1].interestInfo.value}
            </div>
            <div className={styles.right_container}>
              <Image
                alt=""
                src={icon_fire_small}
                className={styles.icon_fire_small}
              ></Image>
              <div className={styles.hot}>
                {audienceInterestsObj &&
                audienceInterestsObj[1] &&
                audienceInterestsObj[1].score
                  ? audienceInterestsObj[1].score
                  : 0}
              </div>
            </div>
          </div>
        )}
      {audienceInterestsObj &&
        audienceInterestsObj[2] &&
        audienceInterestsObj[2].interestInfo &&
        audienceInterestsObj[2].interestInfo.value && (
          <div className={styles.tag5_container}>
            <div className={styles.label}>
              {audienceInterestsObj[2].interestInfo.value}
            </div>
            <div className={styles.right_container}>
              <Image
                alt=""
                src={icon_fire_small}
                className={styles.icon_fire_small}
              ></Image>
              <div className={styles.hot}>
                {audienceInterestsObj &&
                audienceInterestsObj[2] &&
                audienceInterestsObj[2].score
                  ? audienceInterestsObj[2].score
                  : 0}
              </div>
            </div>
          </div>
        )}
    </div>
  )
}

export default RelatedInterests
