.audience_analysis_container {
  padding-bottom: 24px;

  .subtitle {
    margin-bottom: 40px;
  }
}

.related_interests {
  position: relative;

  .bg_related_interests_container {
    .bg_related_interests {

    }
  }

  .tag1_container {
    position: absolute;
    top: 12px;
    left: 0;
    width: 268px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;

    .label {
      flex: 1;
      font-weight: bold;
      font-size: 18px;
      color: #FFFFFF;
      line-height: 20px;
      text-align: center;
      white-space:nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .hot {
      font-weight: bold;
      font-size: 18px;
      color: #FFFFFF;
      line-height: 20px;
    }
  }

  .tag2_container {
    position: absolute;
    top: 68px;
    left: 152px;
    width: 294px;
    height: 58px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;

    .label {
      flex: 1;
      font-weight: bold;
      font-size: 28px;
      color: #FFFFFF;
      line-height: 32px;
      text-align: center;
      white-space:nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .hot {
      font-weight: bold;
      font-size: 28px;
      color: #FFFFFF;
      line-height: 32px;
    }
  }

  .tag3_container {
    position: absolute;
    top: 22px;
    left: 386px;
    width: 268px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;

    .label {
      flex: 1;
      font-weight: bold;
      font-size: 18px;
      color: #FFFFFF;
      line-height: 20px;
      text-align: center;
      white-space:nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .hot {
      font-weight: bold;
      font-size: 18px;
      color: #FFFFFF;
      line-height: 20px;
    }
  }

  .tag4_container {
    position: absolute;
    top: 144px;
    left: 48px;
    width: 214px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;

    .label {
      flex: 1;
      font-weight: bold;
      font-size: 22px;
      color: #FFFFFF;
      line-height: 20px;
      text-align: center;
      white-space:nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .hot {
      font-weight: bold;
      font-size: 22px;
      color: #FFFFFF;
      line-height: 26px;
    }
  }

  .tag5_container {
    position: absolute;
    top: 144px;
    left: 342px;
    width: 268px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;

    .label {
      flex: 1;
      font-weight: bold;
      font-size: 22px;
      color: #FFFFFF;
      line-height: 20px;
      text-align: center;
      white-space:nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .hot {
      font-weight: bold;
      font-size: 22px;
      color: #FFFFFF;
      line-height: 26px;
    }
  }

  .right_container {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }

  .icon_fire_small {
    width: 16px;
    height: 20px;
    margin-right: 4px;
  }

  .icon_fire_big {
    width: 24px;
    height: 28px;
    margin-right: 4px;
  }
}