'use client'
import React, { useState } from 'react'
import Image from 'next/image'
import classNames from 'classnames'
import { Picker } from 'antd-mobile'
import ComponentCard from '@/app/components/ComponentCard'
import icon_arrow_down from '@/../public/images/hotspot/icon_arrow_down.png'
import { formatNumberStr } from '@/lib/utils'
import styles from './index.module.scss'
import { i18n } from '@/lib/client/i18n'

type DataAnalysisCardProps = {
  detail: any
  dateType: number
  setDateType: (value: number) => void
} & BaseComponentProps

const getDateTimeStr = (type: number) => {
  let dateTimeStr = i18n.t('HotSpot.近7天')
  switch (type) {
    case 1:
      dateTimeStr = i18n.t('HotSpot.近7天')
      break
    case 2:
      dateTimeStr = i18n.t('HotSpot.近30天')
      break
    // case 3:
    //   dateTimeStr = '近120天'
    //   break
    // case 4:
    //   dateTimeStr = '近12个月'
    //   break
    // case 5:
    //   dateTimeStr = '近3年'
    //   break
    default:
      break
  }
  return dateTimeStr
}

const getDateTimeColumns = () => {
  return [
    [
      {
        label: getDateTimeStr(1),
        value: '1'
      },
      {
        label: getDateTimeStr(2),
        value: '2'
      }
      // {
      //   label: getDateTimeStr(3),
      //   value: '3'
      // },
      // {
      //   label: getDateTimeStr(4),
      //   value: '4'
      // },
      // {
      //   label: getDateTimeStr(5),
      //   value: '5'
      // }
    ]
  ]
}

const DataAnalysisCard: React.FC<DataAnalysisCardProps> = props => {
  const { className, style, detail, dateType, setDateType } = props
  const [visible, setVisible] = useState<boolean>(false)

  const handleChangeDateType = () => {
    setVisible(true)
  }

  const headerExtra = () => {
    return (
      <div className={styles.extra_container} onClick={handleChangeDateType}>
        <div className={styles.label}>{getDateTimeStr(dateType)}</div>
        <Image
          alt=""
          src={icon_arrow_down}
          className={styles.icon_arrow}
        ></Image>
      </div>
    )
  }

  return (
    <ComponentCard
      title={i18n.t('HotSpot.数据洞见')}
      headerExtra={headerExtra()}
    >
      <div
        className={classNames([styles.data_analysis_container, className])}
        style={style}
      >
        <div
          className={styles.wrapper_item_container}
          style={{ marginRight: '1.8667vw' }}
        >
          <div className={styles.wrapper_title}>{i18n.t('HotSpot.帖子数')}</div>
          <div className={styles.inner_container}>
            <div
              className={styles.item_container}
              style={{ marginRight: '1.8667vw' }}
            >
              <div className={styles.value}>
                {detail.publishCount
                  ? formatNumberStr(detail.publishCount)
                  : '-'}
              </div>
              <div className={styles.label}>
                <div>{getDateTimeStr(dateType)}</div>
              </div>
            </div>
            <div className={styles.item_container}>
              <div className={styles.value}>
                {detail.publishCountAll
                  ? formatNumberStr(detail.publishCountAll)
                  : '-'}
              </div>
              <div className={styles.label}>{i18n.t('HotSpot.全部')}</div>
            </div>
          </div>
        </div>
        <div className={styles.wrapper_item_container}>
          <div className={styles.wrapper_title}>
            {i18n.t('HotSpot.观看数Detail')}
          </div>
          <div className={styles.inner_container}>
            <div
              className={styles.item_container}
              style={{ marginRight: '1.8667vw' }}
            >
              <div className={styles.value}>
                {detail.videoViews ? formatNumberStr(detail.videoViews) : '-'}
              </div>
              <div className={styles.label}>
                <div>{getDateTimeStr(dateType)}</div>
              </div>
            </div>
            <div className={styles.item_container}>
              <div className={styles.value}>
                {detail.videoViewsAll
                  ? formatNumberStr(detail.videoViewsAll)
                  : '-'}
              </div>
              <div className={styles.label}>{i18n.t('HotSpot.全部')}</div>
            </div>
          </div>
        </div>
        <Picker
          columns={getDateTimeColumns() as any}
          visible={visible}
          onClose={() => {
            setVisible(false)
          }}
          value={`${dateType}` as any}
          onConfirm={(v: any) => {
            setDateType(parseInt(v))
          }}
          cancelText={i18n.t('Common.Cancel')}
          confirmText={i18n.t('Common.Confirm')}
        />
      </div>
    </ComponentCard>
  )
}

export default DataAnalysisCard
