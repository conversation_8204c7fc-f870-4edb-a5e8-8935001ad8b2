'use client'
import React from 'react'
import Image from 'next/image'
import classNames from 'classnames'
import ComponentCard from '@/app/components/ComponentCard'
import RelatedVideoItem from '../RelatedVideoItem'
import icon_arrow_down from '@/../public/images/hotspot/icon_arrow_down.png'
import styles from './index.module.scss'

type RelatedVideoCardProps = {
  onClick?: () => void
} & BaseComponentProps

const RelatedVideoCard: React.FC<RelatedVideoCardProps> = props => {
  const { className, style } = props

  return (
    <ComponentCard title="相关视频" subTitle="近30天“热词”相关的热点视频">
      <div
        className={classNames([styles.realted_video_container, className])}
        style={style}
      >
        {[{}, {}, {}].map((item, index) => {
          return <RelatedVideoItem key={index}></RelatedVideoItem>
        })}
        <div className={styles.btn_more_container}>
          <div className={styles.btn_more}>
            <div className={styles.btn_label}>更多相关视频</div>
            <Image
              alt=""
              className={styles.icon_arrow_down}
              src={icon_arrow_down}
            ></Image>
          </div>
        </div>
      </div>
    </ComponentCard>
  )
}

export default RelatedVideoCard
