'use client'
import React, { useEffect, useMemo, useRef } from 'react'
import classNames from 'classnames'
import { nanoid } from 'nanoid'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import ComponentCard from '@/app/components/ComponentCard'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { rpxToPx } from '@/lib/client/utils'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'

type HotspotTrendingProps = {
  trend: any
} & BaseComponentProps

const colors = ['#FE6D45', '#38A7FF', '#00B8C9']

const formatter = function (value: number, index: number) {
  return value
}

const HotspotTrending: React.FC<HotspotTrendingProps> = props => {
  const { className, style, trend } = props
  const idRef = useRef(nanoid())
  const myChartRef = useRef<echarts.ECharts | null>(null)

  const initChart = () => {
    const trendObj = JSON.parse(trend)
    myChartRef.current = echarts.init(document.getElementById(idRef.current))
    myChartRef.current.setOption({
      color: colors,
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: {
          color: '#ffffff',
          fontSize: rpxToPx(24)
        },
        formatter: function (params: any, index: number) {
          console.log('tooltip params', params)

          let tooltipContent = `${dayjs(params[0].axisValue * 1000).format(
            'DD/MM/YY'
          )}<br/>`
          params.forEach(function (item: any) {
            tooltipContent += `<div>${item.value}</div>`
          })

          return tooltipContent
        },
        extraCssText: 'z-index: 1;'
      },
      legend: {
        show: false
      },
      grid: {
        top: 16,
        left: 0,
        right: 16,
        bottom: 0,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          color: '#9A9A9A',
          fontSize: rpxToPx(22),
          showMinLabel: true,
          showMaxLabel: true,
          formatter: function (value) {
            return dayjs(value * 1000).format('MM-DD')
          }
        },
        axisLine: {
          lineStyle: {
            color: '#EEEEEE'
          }
        },
        data: trendObj.map(item => item.time)
      },
      yAxis: [
        {
          type: 'value',
          alignTicks: true,
          axisLabel: {
            color: '#9A9A9A',
            fontSize: rpxToPx(22),
            formatter
          },
          splitLine: {
            lineStyle: {
              width: 1,
              color: '#EEEEEE'
            }
          }
        }
      ],
      series: [
        {
          name: '总量',
          type: 'line',
          yAxisIndex: 0,
          showSymbol: false,
          lineStyle: {
            width: 1
          },
          data: trendObj.map(item => Math.round(item.value * 100))
        }
      ]
    })
  }

  useEffect(() => {
    initChart()
  }, [])

  return (
    <div
      className={classNames([styles.hotspot_trending_container, className])}
      style={style}
    >
      <div
        id={idRef.current}
        className={styles.line_chart}
        style={{ width: '100%', height: rpxToPx(412) }}
      ></div>
    </div>
  )
}

export default HotspotTrending
