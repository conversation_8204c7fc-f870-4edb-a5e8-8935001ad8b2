import React from 'react'
import classNames from 'classnames'
import dayjs from 'dayjs'
import { copyText } from '@/lib/client/utils'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { toast } from '@/lib/client/toast'

type DetailDescProps = {
  detail: any
} & BaseComponentProps

const DetailDesc: React.FC<DetailDescProps> = props => {
  const { className, style, detail } = props

  const handleCopy = () => {
    if (!detail.hotspotName) {
      return
    }
    copyText(`#${detail.hotspotName}`)
  }

  return (
    <div
      className={classNames([styles.detail_desc_container, className])}
      style={style}
    >
      <div className={styles.top_container}>
        <div className={styles.title}>#{detail.hotspotName || ''}</div>
        <div className={styles.copy} onClick={handleCopy}>
          {i18n.t('HotSpot.复制')}
        </div>
      </div>
      <div className={styles.desc}>
        {i18n
          .t('HotSpot.数据更新至')
          .replace(
            '{{data}}',
            dayjs(detail.updateTime ? parseFloat(detail.updateTime) : 0).format(
              'DD/MM/YYYY hhA'
            )
          )}
      </div>
    </div>
  )
}

export default DetailDesc
