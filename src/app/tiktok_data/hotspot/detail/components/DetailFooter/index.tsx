import React from 'react'
import classNames from 'classnames'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'

type DetailFooterProps = {} & BaseComponentProps

const DetailFooter: React.FC<DetailFooterProps> = props => {
  const { className, style } = props

  return (
    <div
      className={classNames([styles.detail_footer_container, className])}
      style={style}
    >
      {i18n.t('HotSpot.数据由uChoice分析提供仅供参考')}
    </div>
  )
}

export default DetailFooter
