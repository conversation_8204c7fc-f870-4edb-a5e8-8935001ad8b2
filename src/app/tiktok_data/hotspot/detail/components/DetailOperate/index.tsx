import React, { useEffect, useState } from 'react'
import classNames from 'classnames'
import Image from 'next/image'
import icon_star_active from '@/../public/images/hotspot/icon_star_active.png'
import icon_star_normal from '@/../public/images/hotspot/icon_star_normal.png'
import { favoriteOrCancel } from '@/app/api/api-uchoice/tiktok/topic/favoriteOrCancel'
import { loading } from '@/lib/client/loading'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import { getToken, makeSureInApp } from '@/lib/client/utils'
import { WebViewToPages, WebviewEvents } from '@/lib/client/webview/events'
import styles from './index.module.scss'

type DetailOperateProps = {
  id: string
  detail: any
} & BaseComponentProps

const DetailOperate: React.FC<DetailOperateProps> = props => {
  const { className, style, id, detail } = props
  const [collect, setCollect] = useState<boolean>(false)

  useEffect(() => {
    if (detail && detail.hasOwnProperty('isFavorite')) {
      setCollect(detail.isFavorite)
    }
  }, [detail])

  const toggleCollect = async () => {
    if (webview) {
      if (!(await getToken())) {
        await webview?.send(WebviewEvents.makeSureLogined)
        window.location.reload()
        return
      }
    } else {
      await makeSureInApp()
    }
    try {
      loading.show({ userInteractive: true })
      const result = await favoriteOrCancel(id, !collect)
      if (result.code === 200 && result.result) {
        setCollect(!collect)
        webview?.send(WebviewEvents.toggleTopicCollect, {
          id,
          isCollected: `${!collect}`
        })
      }
      loading.hide()
    } catch (error) {
      loading.hide()
    }
  }

  return (
    <div
      className={classNames([styles.detail_operate_container, className])}
      style={style}
    >
      <div className={styles.btn_collect_container}>
        <div className={styles.btn_collect} onClick={toggleCollect}>
          <Image
            alt=""
            className={styles.icon_star}
            src={collect ? icon_star_active : icon_star_normal}
          ></Image>
          <div className={styles.btn_collect_title}>
            {collect ? i18n.t('HotSpot.已收藏') : i18n.t('HotSpot.收藏')}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DetailOperate
