'use client'
import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import classNames from 'classnames'
import ComponentCard from '@/app/components/ComponentCard'
import CloudWordChart from '../CloudWordChart'
import icon_ai from '@/app/images/hotspot/icon_ai.png'
import { i18n } from '@/lib/client/i18n'
import { requestDefaultPage } from '@/app/api/api-uchoice/tiktok/topic/defaultPage'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import styles from './index.module.scss'

type HotspotCloudWordCardProps = {} & BaseComponentProps

const HotspotCloudWordCard: React.FC<HotspotCloudWordCardProps> = props => {
  const { className = '', style = {} } = props

  const [fetchLoading, setFetchingLoading] = useState<boolean>(false)
  const [tempHotspotPush, setTempHotspotPush] = useState<any[]>([])

  const getDefaultPage = async () => {
    try {
      setFetchingLoading(true)
      const result = await requestDefaultPage()
      if (result.code === 200 && result.result) {
        const { hotspotPush } = result.result
        const tempHotspotPush = hotspotPush
          ? hotspotPush.map((item, index) => {
              let level = 30
              let color = 1
              if (index === 0) {
                level = 30
                color = 1
              } else if (index === 1) {
                level = 22
                color = 2
              } else if (index === 2) {
                level = 20
                color = 3
              } else if (index >= 3 && index <= 5) {
                level = 16
                color = 4
              } else {
                level = 12
                color = 5
              }
              const { hotspotId, hotspotName, rankNo } = item
              return {
                id: hotspotId,
                name: `#${hotspotName}`,
                level,
                color
              }
            })
          : []
        setTempHotspotPush(tempHotspotPush)
      }
      setFetchingLoading(false)
    } catch (error) {
      setFetchingLoading(false)
    }
  }

  useEffect(() => {
    getDefaultPage()
  }, [])

  return (
    <div
      className={classNames([styles.hotspot_cloud_word_container, className])}
      style={style}
    >
      <div className={styles.title_container}>
        <Image className={styles.icon_ai} src={icon_ai} alt=""></Image>
        <div className={styles.title}>
          {i18n.t('HotSpot.当前TikTok热点推送')}
        </div>
      </div>
      <ComponentCard showHeader={false} style={{ padding: '2.6vw 0' }}>
        {tempHotspotPush.length === 0 ? (
          <StatusView status={StatusViewType.loading}></StatusView>
        ) : (
          <CloudWordChart hotspotPush={tempHotspotPush}></CloudWordChart>
        )}
      </ComponentCard>
    </div>
  )
}

export default HotspotCloudWordCard
