'use client'
import React from 'react'
import Image from 'next/image'
import classNames from 'classnames'
import { HotSelectListItem } from '../HotSelectList'
import modal_close from '@/../public/images/common/modal_close.png'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'

type HotSelectTagListProps = {
  value: HotSelectListItem[]
  onChange: (value: HotSelectListItem[]) => void
} & BaseComponentProps

const HotSelectTagList: React.FC<HotSelectTagListProps> = props => {
  const { className, style, value, onChange } = props

  return (
    value.length !== 0 && (
      <div
        className={classNames([
          styles.hot_select_tag_list_container,
          className
        ])}
        style={style}
      >
        <div className={styles.hot_select_tag_list}>
          <div className={styles.title_label}>{i18n.t('HotSpot.已选择')}</div>
          {value.map(item => {
            return (
              <div
                className={styles.hot_select_tag_list_item_container}
                key={item.value}
              >
                <div className={styles.label}>{item.label}</div>
                <div
                  onClick={() => {
                    onChange(value.filter(inner => inner.value !== item.value))
                  }}
                >
                  <Image
                    alt=""
                    className={styles.close}
                    src={modal_close}
                  ></Image>
                </div>
              </div>
            )
          })}
        </div>
        <div className={styles.divider}></div>
      </div>
    )
  )
}

export default HotSelectTagList
