'use client'
import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import { Popup } from 'antd-mobile'
import HotSelectList, { HotSelectListItem } from './components/HotSelectList'
import HotSelectTagList from './components/HotSelectTagList'
import modal_close from '@/../public/images/common/modal_close.png'
import { setPersistenceCategotyIds } from '../../data'
import { getCustomCategory } from '@/app/api/api-uchoice/tiktok/category/getCustomCategory'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { loading } from '@/lib/client/loading'
import { isLanguageTH } from '@/lib/utils'
import { toast } from '@/lib/client/toast'
import styles from './index.module.scss'

type HotspotSelectModalProps = {
  visible: boolean
  onClose: () => void
  hotspotSelectedList: HotSelectListItem[]
  onChange: () => void
} & BaseComponentProps

const HotspotSelectModal: React.FC<HotspotSelectModalProps> = props => {
  const { visible, onClose, hotspotSelectedList, onChange } = props
  const [tempHotspotSelectedList, setTempHotspotSelectedList] = useState<
    HotSelectListItem[]
  >([])
  const [list, setList] = useState<HotSelectListItem[]>([])
  const [fetchLoading, setFetchingLoading] = useState(false)

  const onTempChange = (tempValue: HotSelectListItem[]) => {
    if (tempValue.length > 3) {
      toast(i18n.t('HotSpot.最多可监控3个热点类型'))
      return
    }
    setTempHotspotSelectedList(
      list.filter(item => {
        return tempValue.findIndex(inner => inner.value === item.value) !== -1
      })
    )
  }

  const resetState = () => {
    setTempHotspotSelectedList([])
    setList([])
  }

  const getHotspotList = async () => {
    try {
      setFetchingLoading(true)
      const result = await getCustomCategory({ type: '5' })
      if (result.code === 200 && result.result && result.result.length !== 0) {
        setList(
          result.result.map((item: any) => {
            const { id, name, nameEn } = item
            return {
              label: isLanguageTH() ? name : nameEn,
              value: id
            }
          })
        )
      }
      setFetchingLoading(false)
    } catch (error) {
      setFetchingLoading(false)
    }
  }

  useEffect(() => {
    if (visible) {
      setTempHotspotSelectedList(hotspotSelectedList)
      getHotspotList()
    } else {
      resetState()
    }
  }, [visible, hotspotSelectedList])

  const handleSave = async () => {
    if (tempHotspotSelectedList.length > 3) {
      toast(i18n.t('HotSpot.最多可监控3个热点类型'))
      return
    }
    setPersistenceCategotyIds(
      tempHotspotSelectedList.map(item => item.value).join(',')
    )
    onChange()
    onClose()
  }

  return (
    <Popup
      visible={visible}
      onMaskClick={() => {
        onClose && onClose()
      }}
      onClose={() => {
        onClose && onClose()
      }}
      bodyStyle={{ height: '80vh' }}
    >
      <div className={styles.hotspot_select_modal_content_container}>
        <div className={styles.header_container}>
          <div></div>
          <div className={styles.title}>{i18n.t('HotSpot.选择热点类型')}</div>
          <div onClick={onClose}>
            <Image alt="" className={styles.close} src={modal_close}></Image>
          </div>
        </div>
        <div className={styles.tip_container}>
          <div className={styles.tip}>
            {i18n.t('HotSpot.您可以选择自己创作兴趣的话题领域')}
          </div>
        </div>
        <div className={styles.select_list_container}>
          <HotSelectTagList
            value={tempHotspotSelectedList}
            onChange={onTempChange}
          ></HotSelectTagList>
          {fetchLoading ? (
            <StatusView status={StatusViewType.loading}></StatusView>
          ) : (
            <HotSelectList
              list={list}
              value={tempHotspotSelectedList}
              onChange={onTempChange}
            ></HotSelectList>
          )}
        </div>
        <div className={styles.btn_container} onClick={handleSave}>
          <div className={styles.title}>{i18n.t('HotSpot.保存')}</div>
        </div>
      </div>
    </Popup>
  )
}

export default HotspotSelectModal
