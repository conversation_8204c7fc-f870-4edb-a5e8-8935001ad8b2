import React from 'react'
import Image from 'next/image'
import classNames from 'classnames'
import icon_check_active from '@/../public/images/common/icon_check_active.png'
import icon_check_normal from '@/../public/images/common/icon_check_normal.png'
import styles from './index.module.scss'

export type HotSelectListItem = {
  label: string
  value: string
}

type HotSelectListProps = {
  value: HotSelectListItem[]
  onChange: (value: HotSelectListItem[]) => void
  list: HotSelectListItem[]
} & BaseComponentProps

const HotSelectList: React.FC<HotSelectListProps> = props => {
  const { style, className, value, onChange, list } = props

  const handleItemClick = item => {
    if (value.findIndex(inner => inner.value === item.value) !== -1) {
      onChange(value.filter(inner => inner.value != item.value))
    } else {
      onChange(value.concat(item))
    }
  }

  return (
    <div
      className={classNames([styles.hot_select_list_container, className])}
      style={style}
    >
      {list.map(item => {
        return (
          <div
            className={styles.hot_select_list_item_container}
            key={item.value}
            onClick={() => handleItemClick(item)}
          >
            <div className={styles.label}>{item.label}</div>
            <Image
              alt=""
              className={styles.icon_check}
              src={
                value.findIndex(inner => inner.value === item.value) != -1
                  ? icon_check_active
                  : icon_check_normal
              }
            ></Image>
          </div>
        )
      })}
    </div>
  )
}

export default HotSelectList
