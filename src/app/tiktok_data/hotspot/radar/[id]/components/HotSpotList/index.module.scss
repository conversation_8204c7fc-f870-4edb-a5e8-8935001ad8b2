.hotspot_list_container {
  .hotspot_item_container {
    display: flex;
    flex-direction: column;

    .hotspot_item_top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 36px;

      .hotspot_title_container {
        display: flex;
        align-items: center;

        .hotspot_title {
          font-size: 32px;
          font-weight: bold;
          color: #303030;
          line-height: 44px;
        }

        .new_tag {
          background: #FE2C55;
          border-radius: 2px;
          height: 28px;
          line-height: 28px;
          padding: 0 6px;
          color: #fff;
          font-size: 24px;
          margin-left: 8px;
        }
      }

      .bg_rank_container {
        position: relative;
        width: 44px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;

        .bg_rank {
          position: absolute;
          top: 0;
          left: 0;
        }

        .rank_label {
          position: relative;
          font-size: 32px;
          font-weight: bold;
          color: #ffffff;
          line-height: 32px;
        }
      }
    }

    .hotspot_content_container {
      display: flex;

      .hotspot_content_left {
        display: flex;

        .hotspot_content_left_item_container {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-right: 24px;
          width: 168px;

          .label {
            font-size: 24px;
            color: #8a8a8a;
            line-height: 34px;
            text-align: center;
            word-break: break-all;
          }

          .value {
            font-size: 30px;
            font-weight: bold;
            color: #303030;
            line-height: 36px;
            text-align: center;
            word-break: break-all;
          }
        }
      }
    }
  }
}
