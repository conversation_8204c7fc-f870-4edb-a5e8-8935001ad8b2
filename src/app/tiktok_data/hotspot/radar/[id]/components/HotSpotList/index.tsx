'use client'
import React from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import ComponentCard from '@/app/components/ComponentCard'
import bg_cu from '@/app/images/common/bg_cu.png'
import bg_silver from '@/app/images/common/bg_silver.png'
import bg_gold from '@/app/images/common/bg_gold.png'
import Image from 'next/image'
import SimpleLineChart from '@/app/components/SimpleLineChart'
import { formatNumberStr } from '@/lib/utils'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'
import { rpxToPx } from '@/lib/client/utils'
import styles from './index.module.scss'

const HotspotList = props => {
  const { hotpointList, categoryId } = props
  const mounted = useMounted()
  const router = useRouter()

  const getBgRank = (index: number): any => {
    if (index === 0) {
      return bg_gold
    }
    if (index === 1) {
      return bg_silver
    }
    if (index === 2) {
      return bg_cu
    }
    return null
  }

  const handleItemClick = (item: any) => {
    if (!categoryId) {
      router.push(`/tiktok_data/hotspot/detail/${item.hotspotId}`)
    } else {
      router.push(
        `/tiktok_data/hotspot/detail/${item.hotspotId}?categoryId=${categoryId}`
      )
    }
  }

  return (
    <div className={styles.hotspot_list_container}>
      {hotpointList.map((item: any, index: number) => {
        return (
          <ComponentCard
            key={item.hotspotId}
            style={{ marginBottom: '3.2vw' }}
            onClick={() => handleItemClick(item)}
          >
            <div className={styles.hotspot_item_container}>
              <div className={styles.hotspot_item_top}>
                <div className={styles.hotspot_title_container}>
                  <div className={styles.hotspot_title}>
                    {item.hotspotName ? `#${item.hotspotName}` : ''}
                  </div>
                  {item.isFirstTop100 && (
                    <div className={styles.new_tag}>New</div>
                  )}
                </div>
                <div className={styles.bg_rank_container}>
                  {index <= 2 && (
                    <Image
                      src={getBgRank(index)}
                      className={styles.bg_rank}
                      alt=""
                    ></Image>
                  )}
                  <div
                    className={styles.rank_label}
                    style={{ color: index <= 2 ? '#ffffff' : '#83726A' }}
                  >
                    {index + 1}
                  </div>
                </div>
              </div>
              <div className={styles.hotspot_content_container}>
                <div className={styles.hotspot_content_left}>
                  <div
                    className={styles.hotspot_content_left_item_container}
                    style={{ width: rpxToPx(186) }}
                  >
                    <div className={styles.value}>
                      {formatNumberStr(item.publishCount || 0)}
                    </div>
                    <div className={styles.label}>
                      {i18n.t('HotSpot.讨论数')}
                    </div>
                  </div>
                  <div
                    className={styles.hotspot_content_left_item_container}
                    style={{ width: rpxToPx(152) }}
                  >
                    <div className={styles.value}>
                      {formatNumberStr(item.videoViews || 0)}
                    </div>
                    <div className={styles.label}>
                      {i18n.t('HotSpot.观看数')}
                    </div>
                  </div>
                </div>
                {mounted ? (
                  <SimpleLineChart data={item.trendList}></SimpleLineChart>
                ) : (
                  <div></div>
                )}
              </div>
            </div>
          </ComponentCard>
        )
      })}
    </div>
  )
}

export default HotspotList
