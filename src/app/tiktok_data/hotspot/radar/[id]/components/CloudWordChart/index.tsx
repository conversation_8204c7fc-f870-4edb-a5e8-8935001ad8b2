'use client'
import React, { useEffect, useRef } from 'react'
import { useRouter } from '@/lib/hooks/useRouter'
import { nanoid } from 'nanoid'
import { rpxToPx } from '@/lib/client/utils'
import { WordCloud } from '@antv/g2plot'
import styles from './index.module.scss'

type CloudWordChartProps = {
  hotspotPush: any[]
} & BaseComponentProps

const CloudWordChart: React.FC<CloudWordChartProps> = props => {
  const { hotspotPush, className, style } = props
  const idRef = useRef(nanoid())
  const router = useRouter()

  const initChart = () => {
    const wordCloud = new WordCloud(idRef.current, {
      data: hotspotPush,
      wordField: 'name',
      weightField: 'level',
      colorField: 'color',
      color: ['#FE6D45', '#FE6D45', '#FE6D45', '#FE8A6A', '#FEA78F'],
      autoFit: true,
      spiral: 'rectangular',
      interactions: [{ type: 'tooltip', enable: false }],
      wordStyle: {
        fontFamily: 'Verdana',
        fontSize: [12, 30],
        rotation: 0
      },
      random: () => 0.5
    })

    wordCloud.on('element:click', (args: any) => {
      router.push(`/tiktok_data/hotspot/detail/${args.data.data.datum?.id}`)
    })

    wordCloud.render()
  }

  useEffect(() => {
    initChart()
  }, [])

  return (
    <div
      id={idRef.current}
      className={styles.cloud_word_chart}
      style={{
        width: '100%',
        height: rpxToPx(356),
        ...style
      }}
    ></div>
  )
}

export default CloudWordChart
