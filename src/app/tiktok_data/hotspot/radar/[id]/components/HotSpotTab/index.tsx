import React from 'react'
import { CapsuleTabs } from 'antd-mobile'
import { HotSelectListItem } from '../HotspotSelectModal/components/HotSelectList'
import classNames from 'classnames'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'

type HotSpotTabProps = {
  list: HotSelectListItem[]
  active: string
  onChange: (value: string) => void
  onHotspotAdd: () => void
} & BaseComponentProps

const HotspotTab: React.FC<HotSpotTabProps> = props => {
  const { list, active, onChange, style, className, onHotspotAdd } = props

  return (
    <div
      className={classNames([styles.hotspot_tab_container, className])}
      style={style}
    >
      <CapsuleTabs activeKey={active} onChange={onChange}>
        {list.map(item => {
          return (
            <CapsuleTabs.Tab
              title={item.label}
              key={item.value}
            ></CapsuleTabs.Tab>
          )
        })}
      </CapsuleTabs>
      <div onClick={onHotspotAdd} className={styles.btn_hotspot_add}>
        +
      </div>
    </div>
  )
}

export default HotspotTab
