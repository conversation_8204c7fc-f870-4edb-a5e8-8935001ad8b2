import React from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import ComponentCard from '@/app/components/ComponentCard'
import icon_add from '@/app/images/hotspot/icon_add.png'
import styles from './index.module.scss'

type HotspotAddCardProps = {
  onClick?: () => void
} & BaseComponentProps

const HotspotAddCard: React.FC<HotspotAddCardProps> = props => {
  const { className = '', style = {}, onClick } = props

  return (
    <ComponentCard
      showHeader={false}
      style={{ padding: '2.667vw 3.2vw', ...style }}
      className={className}
      onClick={onClick}
    >
      <div className={styles.hotspot_add_container}>
        <Image className={styles.icon_add} src={icon_add} alt=""></Image>
        <div>{i18n.t('HotSpot.添加热点类型')}</div>
      </div>
    </ComponentCard>
  )
}

export default HotspotAddCard
