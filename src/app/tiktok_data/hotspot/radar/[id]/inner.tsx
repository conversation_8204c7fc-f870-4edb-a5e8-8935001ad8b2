'use client'
import { useEffect, useState } from 'react'
import HotspotList from './components/HotSpotList'
import HotspotTab from './components/HotSpotTab'
import HotspotAddCard from './components/HotspotAddCard'
import HotspotCloudWordCard from './components/HotspotCloudWordCard'
import HotspotSelectModal from './components/HotspotSelectModal'
import { HotSelectListItem } from './components/HotspotSelectModal/components/HotSelectList'
import { requestHotspotList } from '@/app/api/api-uchoice/tiktok/topic/getHotspotList'
import { getCustomCategoryByIds } from '@/app/api/api-uchoice/tiktok/category/getCustomCategoryByIds'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import {
  TopGrandientCard,
  TransparentNavPage
} from '@/components/TransparentNavPage'
import { webview } from '@/lib/client/webview'
import { getPersistenceCategotyIds } from './data'
import { isLanguageTH } from '@/lib/utils'
import styles from './inner.module.scss'

const Inner = () => {
  const [visible, setVisible] = useState<boolean>(false)
  const [fetchLoading, setFetchingLoading] = useState<boolean>(false)
  const [hotpointList, setHotpointList] = useState<any[]>([])
  const [hotspotSelectedList, setHotspotSelectedList] = useState<
    HotSelectListItem[]
  >([])

  const [tabActive, setTabActive] = useState<string>('')
  const [tabFetchLoading, setTabFetchLoading] = useState<boolean>(false)

  const mounted = useMounted()

  const requestCustomCategoryByIds = async (ids: string) => {
    try {
      setTabFetchLoading(true)
      const res = await getCustomCategoryByIds({ ids })
      if (res.code === 200 && res.result) {
        const list = res.result
        if (list.length !== 0) {
          setTabActive(`${list[0].id}`)
          setHotspotSelectedList(
            list.map(item => {
              const { id, name, nameEn } = item
              return {
                label: isLanguageTH() ? name : nameEn,
                value: id
              }
            })
          )
          getHotspotList(list[0].id)
        } else {
          setTabActive('')
          getHotspotList('')
        }
      } else {
        setTabActive('')
        getHotspotList('')
      }
      setTabFetchLoading(false)
    } catch (error) {
      setTabFetchLoading(false)
    }
  }

  const getHotspotList = async (hotspotId: any = '') => {
    try {
      setHotpointList([])
      setFetchingLoading(true)
      const query: any = {
        pageNo: 1,
        pageSize: 100
      }
      if (hotspotId) {
        query.customCategoryId = hotspotId
      }
      const result = await requestHotspotList(query)
      if (result.code === 200 && result.result) {
        const { list } = result.result
        if (list && list.length !== 0) {
          list.forEach(item => {
            item.trendList = item.trend ? JSON.parse(item.trend) : ''
          })
          setHotpointList(list)
        }
      }
      setFetchingLoading(false)
    } catch (error) {
      setFetchingLoading(false)
    }
  }

  const getDefaultPage = async () => {
    setFetchingLoading(true)
    const ids = getPersistenceCategotyIds()
    if (ids) {
      requestCustomCategoryByIds(ids)
    } else {
      getHotspotList()
    }
  }

  useEffect(() => {
    getDefaultPage()
  }, [])

  const handleHotspotSelectChange = () => {
    getDefaultPage()
  }

  const handleAddHotspot = () => {
    if (tabFetchLoading) {
      return
    }
    setVisible(true)
  }

  const handleHotspotTabChange = value => {
    if (fetchLoading) {
      return
    }
    setTabActive(value)
    getHotspotList(value)
  }

  const onHotspotAdd = () => {
    setVisible(true)
  }

  return (
    <TransparentNavPage title={i18n.t('HotSpot.热点AI雷达')}>
      <TopGrandientCard gradientColors={['#ffede4', '#f5f5f5']}>
        <div style={{ height: '3.2w' }}></div>
      </TopGrandientCard>
      <div className={styles.inner_container}>
        <HotspotCloudWordCard></HotspotCloudWordCard>
        <div className={styles.title}>{i18n.t('HotSpot.我监控的AI热点')}</div>
        {hotspotSelectedList.length === 0 && (
          <HotspotAddCard
            onClick={handleAddHotspot}
            style={{ marginBottom: '3.2vw' }}
          ></HotspotAddCard>
        )}
        {hotspotSelectedList.length !== 0 && (
          <HotspotTab
            list={hotspotSelectedList}
            active={tabActive}
            onChange={handleHotspotTabChange}
            onHotspotAdd={onHotspotAdd}
            style={{ marginBottom: '3.2vw' }}
          ></HotspotTab>
        )}
        {!mounted || fetchLoading ? (
          <StatusView
            status={StatusViewType.loading}
            style={{ padding: '48px 0' }}
          ></StatusView>
        ) : (
          <HotspotList
            categoryId={tabActive}
            hotpointList={hotpointList}
          ></HotspotList>
        )}
        <HotspotSelectModal
          visible={visible}
          onClose={() => {
            setVisible(false)
          }}
          hotspotSelectedList={hotspotSelectedList}
          onChange={handleHotspotSelectChange}
        ></HotspotSelectModal>
      </div>
    </TransparentNavPage>
  )
}

export default Inner
