import { i18n } from '@/lib/client/i18n'
import React from 'react'

interface Props {
  text: string
}

export const HighCommTag = ({ text }: Props) => {
  return (
    <div className="flex">
      <div className="flex items-center rounded-[2px] bg-[#F4393B] py-[2px] pr-[2px]">
        <span className="mx-[12px] text-[24px] leading-[24px] text-white">
          {i18n.t('TiktokData.精选高佣')}
        </span>
        <div className="flex h-[28px] items-center justify-center rounded-[2px] bg-white px-[10px]">
          <span className="text-[26px] font-bold leading-[26px] text-[#F4393B]">
            {text}
          </span>
        </div>
      </div>
    </div>
  )
}
