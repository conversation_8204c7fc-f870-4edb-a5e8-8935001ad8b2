import React from 'react'
import Image from 'next/image'
import { HighCommTag } from '@/app/tiktok_data/components/highCommTag'
import { formatMinMax, formatPrice } from '@/lib/format'
import { useRouter } from '@/lib/hooks/useRouter'
import { TikTokItemRankListVo } from '@/app/api/api-uchoice/tiktok/seller/itemList/dtos'
import { i18n } from '@/lib/client/i18n'

interface Props {
  item: TikTokItemRankListVo
}

export const ProductCell = ({ item }: Props) => {
  const router = useRouter()

  const onClick = () => {
    router.push(`/tiktok_data/product/${item.id}`)
  }

  return (
    <div
      className="flex flex-1 overflow-hidden pb-[24px] touch-opacity"
      onClick={onClick}
    >
      <Image
        unoptimized={true}
        src={item.image}
        width={180}
        height={180}
        className="size-[180px] rounded-[4px]"
        alt=""
      ></Image>
      <div className="flex-1 overflow-hidden pl-[20px]">
        <div className="loading-[42px] line-clamp-2 h-[81px] text-[28px] font-bold text-black33">
          {item.productName}
        </div>

        <div className="pt-[2px] text-[28px] font-bold text-black33">
          {formatMinMax(
            formatPrice(item.minPrice, true),
            formatPrice(item.maxPrice, true)
          )}
        </div>

        <div className="flex flex-wrap items-center pt-[10px]">
          {item.creatorCommission ? (
            <div className="pr-[18px]">
              <HighCommTag text={item.creatorCommissionPercent}></HighCommTag>
            </div>
          ) : null}
          {item.creatorCommissionPercent &&
          item.creatorCommissionPercent !== item.planCommissionPercent &&
          item.planCommissionPercent ? (
            <span
              className={`break-words text-[24px] text-gray8A `}
              style={{
                wordBreak: 'break-all',
                textDecorationLine: item.creatorCommission
                  ? 'line-through'
                  : 'none'
              }}
            >
              {item.creatorCommission
                ? i18n.t('Rank.公开佣金') + ' ' + item.planCommissionPercent
                : i18n.t('Rank.佣金') + ' ' + item.planCommissionPercent}
            </span>
          ) : null}
        </div>
      </div>
    </div>
  )
}
