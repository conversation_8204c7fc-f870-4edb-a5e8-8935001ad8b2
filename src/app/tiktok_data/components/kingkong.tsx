import React from 'react'
import kingkong_product from '@/../public/images/tiktok_data/kingkong_product.png'
import kingkong_kol from '@/../public/images/tiktok_data/kingkong_kol.png'
import kingkong_shop from '@/../public/images/tiktok_data/kingkong_shop.png'
import kingkong_ai from '@/../public/images/tiktok_data/kingkong_ai.png'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'
import { Image } from '@/components/Image'

export const KingKong = () => {
  const router = useRouter()

  return (
    <div className="flex justify-between">
      <div
        className="flex w-[144px] flex-col items-center pt-[48px] touch-opacity"
        onClick={() => router.push('/tiktok_data/product_list/page')}
      >
        <Image src={kingkong_product} className="size-[88px]" alt=""></Image>
        <span className="mt-[12px] text-center text-[24px] leading-[30px] text-black">
          {i18n.t('TiktokData.商品榜')}
        </span>
      </div>
      <div
        className="flex w-[144px] flex-col items-center pt-[48px] touch-opacity"
        onClick={() => router.push('/tiktok_data/anchor_list/page')}
      >
        <Image src={kingkong_kol} className="size-[88px]" alt=""></Image>
        <span className="mt-[12px] text-center text-[24px] leading-[30px] text-black">
          {i18n.t('TiktokData.达人榜')}
        </span>
      </div>
      <div
        className="flex w-[144px] flex-col items-center pt-[48px] touch-opacity"
        onClick={() => router.push('/tiktok_data/shop_list/page')}
      >
        <Image src={kingkong_shop} className="size-[88px]" alt=""></Image>
        <span className="mt-[12px] text-center text-[24px] leading-[30px] text-black">
          {i18n.t('TiktokData.店铺榜')}
        </span>
      </div>
      <div
        className="flex w-[144px] flex-col items-center pt-[48px] touch-opacity"
        onClick={() => {
          router.push('/tiktok_data/hotspot/radar/page')
        }}
      >
        <Image src={kingkong_ai} className="size-[88px]" alt=""></Image>
        <span className="mt-[12px] text-center text-[24px] leading-[30px] text-black">
          {i18n.t('TiktokData.AI话题')}
        </span>
      </div>
    </div>
  )
}
