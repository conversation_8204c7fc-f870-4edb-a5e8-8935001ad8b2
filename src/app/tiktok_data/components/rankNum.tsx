import React from 'react'
import rank_1 from '@/../public/images/tiktok_data/rank_1.png'
import rank_2 from '@/../public/images/tiktok_data/rank_2.png'
import rank_3 from '@/../public/images/tiktok_data/rank_3.png'
import Image from 'next/image'
import { ChildrenProps } from '@/lib/types/common'

type Props = {
  index: number
}

export const RankNum = ({ index }: Props) => {
  const rank_icon = () => {
    let icon = rank_1
    switch (index) {
      case 0:
        icon = rank_1
        break
      case 1:
        icon = rank_2
        break
      case 2:
        icon = rank_3
        break
    }
    return icon
  }
  return (
    <div className="flex w-[72px] items-center">
      {index < 3 ? (
        <Image
          // src={[rank_1, rank_2, rank_3][index]}
          src={rank_icon()}
          className="h-[68px] w-[46px]"
          alt=""
        ></Image>
      ) : (
        <span className="w-[46px] text-center text-[30px] font-bold text-[#83726A]">
          {index + 1}
        </span>
      )}
    </div>
  )
}

export const WithRankNum = ({ index, children }: Props & ChildrenProps) => {
  return (
    <div className="flex w-full">
      <RankNum index={index}></RankNum>
      {children}
    </div>
  )
}
