import { i18n } from '@/lib/client/i18n'
import React from 'react'
import primary_arrow_right from '@/../public/images/common/primary_arrow_right.png'
import { Image } from '@/components/Image'

interface Props {
  title: string
  desc?: string
  children: React.ReactNode
  onMore: () => void
}
export const Section = ({ title, desc, children, onMore }: Props) => {
  return (
    <>
      <div className="flex items-center justify-between pb-[36px] pt-[48px]">
        <div className="flex flex-col">
          <span className="text-[36px] font-bold text-black">{title}</span>
          {desc && <span className="text-[24px] text-gray9A">{desc}</span>}
        </div>
        {children && (
          <div
            className="flex h-[48px] items-center rounded-[2px] bg-background px-[24px] touch-opacity"
            onClick={onMore}
          >
            <span className="text-[26px] font-bold text-primary">
              {i18n.t('TiktokData.查看更多')}
            </span>
          </div>
        )}
      </div>
      {children}
      {children && (
        <div className="flex items-center pt-[48px]">
          <div className="h-[2px] flex-1 bg-grayEE"></div>
          <div
            className="flex items-center px-[52px] touch-opacity"
            onClick={onMore}
          >
            <div className="mr-[5px] text-[24px] text-primary">
              {i18n.t('TiktokData.查看更多')}
            </div>
            <Image
              src={primary_arrow_right}
              className="h-[24px] w-[16px]"
            ></Image>
          </div>
          <div className="h-[2px] flex-1 bg-grayEE"></div>
        </div>
      )}
    </>
  )
}
