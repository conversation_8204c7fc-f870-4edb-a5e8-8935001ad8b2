import React from 'react'
import Image from 'next/image'
import { HighCommTag } from '../highCommTag'
import { TikTokItemRankHomePageListVo } from '@/app/api/api-uchoice/tiktok/item/rankHomePageList/dtos'
import {
  formatCount,
  formatMinMax,
  formatPrice,
  formatSales
} from '@/lib/format'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'

interface Props {
  item: TikTokItemRankHomePageListVo
}

export const ItemCell = ({ item }: Props) => {
  const router = useRouter()

  const onClick = () => {
    router.push(`/tiktok_data/product/${item.id}`)
  }

  return (
    <div
      className="flex flex-1 overflow-hidden touch-opacity"
      onClick={onClick}
    >
      <Image
        unoptimized={true}
        src={item.image}
        width={180}
        height={180}
        className="size-[180px] rounded-[4px]"
        alt=""
      ></Image>
      <div className="flex-1 overflow-hidden pl-[20px]">
        <div className="truncate text-[28px] font-bold text-black33">
          {item.productName}
        </div>
        <div className="pt-[10px]">
          {item.creatorCommissionPercent > 0 ? (
            <>
              <div className="pr-[18px]">
                <HighCommTag
                  text={item.creatorCommissionPercentStr}
                ></HighCommTag>
              </div>
              <span className="text-[24px] text-gray8A line-through">
                {i18n.t('TiktokData.公开佣金')} {item.planCommissionPercentStr}
              </span>
            </>
          ) : (
            <span className="text-[24px] text-gray8A">
              {i18n.t('TiktokData.公开佣金')} {item.planCommissionPercentStr}
            </span>
          )}
        </div>
        <div className="pt-[10px] text-[28px] text-primary">
          {i18n.t('Product.Earn')}{' '}
          {formatMinMax(
            formatPrice(item.minEarn, true),
            formatPrice(item.maxEarn, true)
          )}
        </div>
        <div className="pt-[2px] text-[28px] font-bold text-black33">
          {formatMinMax(
            formatPrice(item.minPrice, true),
            formatPrice(item.maxPrice, true)
          )}
        </div>
      </div>
    </div>
  )
}

export const WithBottomItemInfo = ({ item, children, tabIndex }) => {
  return (
    <div>
      {children}
      <div className="mt-[8px] h-[2px] w-full bg-background"></div>
      <div className="flex justify-between">
        {tabIndex == '3' ? (
          <div className="flex w-[50%] flex-col items-center pt-[24px]">
            <div className="text-[32px] text-black">
              {formatSales(item.lastWeekSales, '--')}
            </div>
            <div className="mt-[6px] text-[24px] text-gray6E">
              {i18n.t('Rank.最后一周的销量')}
            </div>
          </div>
        ) : (
          <div className="flex w-[33%] flex-col items-center pt-[24px]">
            <div className="text-[32px] text-black">
              {formatSales(item.lastWeekSales, '--')}
            </div>
            <div className="mt-[6px] text-[24px] text-gray6E">
              {i18n.t('Rank.最后一周的销量')}
            </div>
          </div>
        )}
        {tabIndex == '3' ? (
          <div className="flex w-[50%] flex-col items-center pt-[24px]">
            <div className="text-[32px] text-black">
              {formatSales(item.salesForLast30Days, '--')}
            </div>
            <div className="mt-[6px] text-[24px] text-gray6E">
              {i18n.t('Rank.近30天销量')}
            </div>
          </div>
        ) : (
          <div className="flex w-[33%] flex-col items-center pt-[24px]">
            <div className="text-[32px] text-black">
              {formatSales(item.yesterdaySales, '--')}
            </div>
            <div className="mt-[6px] text-[24px] text-gray6E">
              {i18n.t('TiktokData.日销量')}
            </div>
          </div>
        )}

        {tabIndex == '1' ? (
          <div className="flex w-[33%] flex-col items-center pt-[24px]">
            <div className="text-[32px] text-black">
              {!item.anchorCount ? '--' : formatCount(item.anchorCount)}
            </div>
            <div className="mt-[6px] text-[24px] text-gray6E">
              {i18n.t('TiktokData.关联人数')}
            </div>
          </div>
        ) : tabIndex == '2' ? (
          <div className="flex w-[33%] flex-col items-center pt-[24px]">
            <div className="text-[32px] text-black">
              {item.lastWeekRate || item.lastWeekRate === 0
                ? item.lastWeekRate + '%'
                : '--'}
            </div>
            <div className="mt-[6px] text-[24px] text-gray6E">
              {i18n.t('Rank.增值率')}
            </div>
          </div>
        ) : null}
      </div>
    </div>
  )
}
export const WithBottomStoreInfo = ({ item, children }) => {
  return (
    <div>
      {children}
      <div className="mt-[8px] h-[2px] w-full bg-background"></div>
      <div className="flex justify-between">
        <div className="flex w-[33%] flex-col items-center pt-[24px]">
          <div className="text-[32px] text-black">
            {formatSales(item.lastWeekSales, '--')}
          </div>
          <div className="mt-[6px] text-[24px] text-gray6E">
            {i18n.t('Rank.最后一周的销量')}
          </div>
        </div>
        <div className="flex w-[33%] flex-col items-center pt-[24px]">
          <div className="text-[32px] text-black">
            {formatSales(item.yesterdaySales, '--')}
          </div>
          <div className="mt-[6px] text-[24px] text-gray6E">
            {item.dateType == 1
              ? i18n.t('TiktokData.日销量')
              : item.dateType == 2
              ? i18n.t('Rank.周销量')
              : i18n.t('Rank.月销量')}
          </div>
        </div>
        <div className="flex w-[33%] flex-col items-center pt-[24px]">
          <div className="text-[32px] text-black">
            {!item.anchorCount ? '--' : formatCount(item.anchorCount)}
          </div>
          <div className="mt-[6px] text-[24px] text-gray6E">
            {i18n.t('TiktokData.关联人数')}
          </div>
        </div>
      </div>
    </div>
  )
}

export const WithShopDetailBottomItemInfo = ({
  item,
  children,
  isProduct = false
}) => {
  return (
    <div>
      {children}
      <div className="mt-[8px] h-[2px] w-full bg-background"></div>
      <div className="flex justify-between">
        <div className="flex w-[50%] flex-col items-center pt-[24px]">
          <div className="text-[32px] text-black">
            {formatSales(item.lastWeekSales, '--')}
          </div>
          <div className="mt-[6px] text-[24px] text-gray6E">
            {i18n.t('TiktokData.最近一周销量')}
          </div>
        </div>
        <div className="flex w-[50%] flex-col items-center pt-[24px]">
          <div className="text-[32px] text-black">
            {formatSales(item.sales, '--')}
          </div>
          <div className="mt-[6px] text-[24px] text-gray6E">
            {i18n.t('TiktokData.昨日销量')}
          </div>
        </div>
      </div>
    </div>
  )
}
