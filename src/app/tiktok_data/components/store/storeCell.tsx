import Image from 'next/image'
import React from 'react'
import { ClassTag } from '../classTag'
import { rpxToPx } from '@/lib/client/utils'
import { isLanguageEN } from '@/lib/utils'
import { useRouter } from '@/lib/hooks/useRouter'
import HandlerOnceTap from '@/lib/handlerOnceTap'
export const StoreCell = ({ item }) => {
  const router = useRouter()
  const handleClickDetail = item => {
    router.push(`/tiktok_data/shop/${item.id}`)
  }
  return (
    <div
      className="flex  flex-1 overflow-hidden"
      onClick={() => {
        HandlerOnceTap(() => handleClickDetail(item))
      }}
    >
      <Image
        unoptimized={true}
        src={item.image}
        width={rpxToPx(144)}
        height={rpxToPx(144)}
        className="size-[144px] rounded-[8px]"
        alt=""
      ></Image>
      <div className="flex flex-1 flex-col overflow-hidden pl-[20px]">
        <div className="truncate text-[28px] font-bold text-black33">
          {item.sellerName}
        </div>
        {/* <div className="justify-center pt-[14px]">
          <span className="text-gray8A text-[24px]">评分:</span>
          <span className="text-[24px] text-black">4.8</span>
        </div>  */}
        <div className="pt-[14px]">
          <ClassTag
            text={isLanguageEN() ? item.categoryEn : item.categoryTh}
          ></ClassTag>
        </div>
      </div>
    </div>
  )
}
