import React from 'react'
import {
  TikTokRankAnchorFansVo,
  TikTokRankAnchorProductVo
} from '@/app/api/api-uchoice/tiktok/item/rankHomePageList/dtos'
import { formatSales } from '@/lib/format'
import { useRouter } from '@/lib/hooks/useRouter'
import { Image } from '@/components/Image'
import { i18n } from '@/lib/client/i18n'

interface Props {
  fansKol?: TikTokRankAnchorFansVo
  sellGoodsKol?: TikTokRankAnchorProductVo
}

export const KolCell = ({ fansKol, sellGoodsKol }: Props) => {
  const router = useRouter()

  const onClick = () => {
    router.push(`/tiktok_data/kol/${fansKol?.id || sellGoodsKol?.id}`)
  }

  return (
    <div className="flex w-full flex-1 touch-opacity" onClick={onClick}>
      <div className="flex flex-1 overflow-hidden">
        <Image
          src={fansKol?.image || sellGoodsKol?.image || ''}
          width={116}
          height={116}
          className="size-[116px] rounded-[58px]"
          alt=""
        ></Image>
        <div className="flex-1 overflow-hidden pl-[20px] ">
          <div className="flex justify-between">
            <span className="text-[30px] font-bold text-black">
              {fansKol?.nickname}
              {sellGoodsKol?.nickname}
            </span>
            {fansKol && (
              <span className="text-[32px] font-bold text-black">
                {formatSales(fansKol.followerIncr)}
              </span>
            )}
          </div>
          <div className="mt-[18px] flex justify-between">
            <div className="flex items-center">
              {sellGoodsKol && (
                <span className="text-[26px] text-gray6E">
                  {i18n.t('TiktokData.销售额')}
                  {': '}
                </span>
              )}
              {fansKol && (
                <span className="text-[26px] text-gray6E">
                  {i18n.t('TiktokData.粉丝数')}:{' '}
                  {formatSales(fansKol.followerCount)}
                </span>
              )}
              {sellGoodsKol && (
                <span className="text-[32px] font-bold text-black">
                  {formatSales(sellGoodsKol?.gmv)}
                </span>
              )}
            </div>
            {fansKol && (
              <span className="text-[26px] text-gray6E">
                {i18n.t('TiktokData.粉丝增量')}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
