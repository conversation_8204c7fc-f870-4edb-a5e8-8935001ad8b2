import { metadataTemplate, inApp } from '@/lib/server/utils'
import { Inner } from './inner'
import React from 'react'
import { i18nS } from '@/lib/server/i18n'
import { sellerDetail } from '@/app/api/api-uchoice/tiktok/seller/detail/fetch'

interface Props {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: i18nS.t('TiktokData.店铺详情') })
  }

  const detail = await sellerDetail(params)

  const description = detail.sellerName
  const title = description
  const icon = detail.image

  return metadataTemplate({ title, description, icon })
}

export default async function Index({ params: { id } }: Props) {
  return <Inner id={id} />
}
