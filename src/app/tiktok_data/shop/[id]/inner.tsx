'use client'

import {
  TransparentNavPage,
  TopGrandientCard
} from '@/components/TransparentNavPage'
import { WithShopDetailBottomItemInfo } from '../../components/product/itemCell'
import { useEffect, useRef, useState } from 'react'
import { sellerDetail } from '@/app/api/api-uchoice/tiktok/seller/detail/request'
import { TikTokSellerDetailVo } from '@/app/api/api-uchoice/tiktok/seller/detail/dtos'
import { StickyHeader } from '@/components/StickyHeader'
import { FilterSortBar } from './components/filterSortBar'
import { Header } from './components/header'
import { sellerItemList } from '@/app/api/api-uchoice/tiktok/seller/itemList/request'
import { TikTokItemRankListVo } from '@/app/api/api-uchoice/tiktok/seller/itemList/dtos'
import { ItemCell } from './components/itemCell'
import { loading } from '@/lib/client/loading'
import { i18n } from '@/lib/client/i18n'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { LoadMore } from '@/app/components/LoadMore'
import { useGrayBody } from '@/lib/hooks/useGrayBody'

interface Props {
  id: string
}

export function Inner({ id }: Props) {
  useGrayBody()

  const [shopDetail, setShopDetail] = useState<TikTokSellerDetailVo>()
  const [itemList, setItemList] = useState<TikTokItemRankListVo[]>([])

  const pageNoRef = useRef(1)
  const paramsRef = useRef({})

  const [noMore, setNoMore] = useState(false)
  const [status, setStatus] = useState(StatusViewType.loading)

  useEffect(() => {
    fetchDetail()
  }, [])

  const fetchDetail = async () => {
    const { code, result } = await sellerDetail({ id })
    if (code === 200) {
      setShopDetail(result)
    }
  }

  const loadMore = () => {
    if (noMore) return
    fetchItems()
  }

  const fetchItems = async () => {
    if (pageNoRef.current > 1) loading.show({ userInteractive: true })

    const { code, result } = await sellerItemList({
      id,
      pageSize: 10,
      pageNo: pageNoRef.current,
      ...paramsRef.current
    })
    loading.hide()

    if (code === 200) {
      const list =
        pageNoRef.current === 1 ? result.list : itemList.concat(result.list)

      setItemList(list)
      setNoMore(result.total === list.length || list.length === 0)
      setStatus(
        result.total === 0 && pageNoRef.current === 1
          ? StatusViewType.empty
          : StatusViewType.loading
      )

      pageNoRef.current++
    }
  }

  const onFilterSortChange = params => {
    pageNoRef.current = 1
    paramsRef.current = params

    loading.show({ userInteractive: true })
    fetchItems()
  }

  return (
    <div>
      <TransparentNavPage title={i18n.t('TiktokData.店铺详情')} showShareButton>
        <TopGrandientCard>
          <div className="px-[24px] pb-[24px]">
            <Header shopDetail={shopDetail}></Header>
          </div>
        </TopGrandientCard>

        <StickyHeader>
          <FilterSortBar id={id} onChange={onFilterSortChange}></FilterSortBar>
        </StickyHeader>

        <div className="bg-background px-[24px] pt-[24px]">
          {itemList.length > 0 ? (
            <div>
              {itemList.map((item, i) => (
                <div className="pb-[24px]" key={i}>
                  <div className="rounded-[4px] bg-white p-[24px]">
                    <WithShopDetailBottomItemInfo item={item}>
                      <ItemCell item={item}></ItemCell>
                    </WithShopDetailBottomItemInfo>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="pt-[100px]">
              <StatusView status={status}></StatusView>
            </div>
          )}
        </div>
      </TransparentNavPage>

      <LoadMore
        onLoadMore={loadMore}
        noMore={noMore && itemList.length > 0}
      ></LoadMore>
    </div>
  )
}
