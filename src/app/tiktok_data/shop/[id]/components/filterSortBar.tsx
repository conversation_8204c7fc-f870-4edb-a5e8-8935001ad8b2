import { SortButton } from '@/app/components/SortButton'
import FilterPopUp from '@/components/FilterPopUp'
import CheckedList from '@/components/FilterPopUp/components/CheckedList'
import { isLanguageEN } from '@/lib/utils'
import { i18n } from '@/lib/client/i18n'
import React, { useEffect, useState } from 'react'
import { sellerItemCategory } from '@/app/api/api-uchoice/tiktok/seller/itemCategory/request'
import { TikTokCustomCategoryVo } from '@/app/api/api-uchoice/tiktok/category/dtos'

const sortList = [
  {
    label: i18n.t('TiktokData.按销量排序'),
    key: 'sales'
  },
  { label: i18n.t('TiktokData.按佣金率排序'), key: 'commission' }
]

const firstCategory = {
  id: null,
  name: i18n.t('TiktokData.全部'),
  nameEn: i18n.t('TiktokData.全部')
}

const defaultData = {
  category: firstCategory,
  sortBy: sortList[0]
}

interface Props {
  id: string
  onChange?: (data: { customCategoryId: number; sortBy: string }) => void
}

export const FilterSortBar = ({ id, onChange }: Props) => {
  const [categoryList, setCategoryList] = useState<TikTokCustomCategoryVo[]>([])
  const [selectedTabIndex, setSelectedTabIndex] = useState(-1)
  const [selectedData, setSelectedData] = useState(defaultData)

  useEffect(() => {
    fetch()
  }, [])

  const fetch = async () => {
    const { code, result } = await sellerItemCategory({ id })
    if (code === 200) {
      setCategoryList([firstCategory, ...result])
    }
  }

  const onSelect = ({ key, value }: { key: string; value: any }) => {
    const data = {
      ...selectedData,
      [key]: value
    }

    setSelectedData(data)

    onChange?.({
      customCategoryId: data.category.id!,
      sortBy: data.sortBy.key
    })

    setTimeout(() => {
      setSelectedTabIndex(-1)
    }, 50)
  }

  return categoryList.length > 0 ? (
    <div>
      <div className="flex justify-between bg-white px-[24px] py-[16px]">
        <div className="flex flex-1 justify-center">
          <SortButton
            title={
              isLanguageEN()
                ? selectedData.category.nameEn
                : selectedData.category.name
            }
            selected={selectedTabIndex === 0}
            onSelected={selected => setSelectedTabIndex(selected ? 0 : -1)}
          ></SortButton>
        </div>
        <div className="flex flex-1 justify-center">
          <SortButton
            title={selectedData.sortBy.label}
            selected={selectedTabIndex === 1}
            onSelected={selected => setSelectedTabIndex(selected ? 1 : -1)}
          ></SortButton>
        </div>
      </div>
      <div className="absolute inset-x-0">
        <FilterPopUp visible={selectedTabIndex !== -1}>
          {selectedTabIndex === 0 && (
            <CheckedList
              arrList={categoryList.map(category =>
                isLanguageEN() ? category.nameEn : category.name
              )}
              onCheckedItem={value =>
                onSelect({
                  key: 'category',
                  value: categoryList.filter(
                    category =>
                      (isLanguageEN() ? category.nameEn : category.name) ===
                      value
                  )[0]
                })
              }
            />
          )}
          {selectedTabIndex === 1 && (
            <CheckedList
              arrList={sortList}
              onCheckedItem={value =>
                onSelect({
                  key: 'sortBy',
                  value
                })
              }
            />
          )}
        </FilterPopUp>
      </div>
    </div>
  ) : null
}
