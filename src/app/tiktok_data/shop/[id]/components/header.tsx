import Image from 'next/image'
import React from 'react'
import { ClassTag } from '@/app/tiktok_data/components/classTag'
import { TikTokSellerDetailVo } from '@/app/api/api-uchoice/tiktok/seller/detail/dtos'
import { launchUrl } from '@/lib/client/utils'
import { isLanguageEN } from '@/lib/utils'
import { Skeleton } from 'react-vant'
import tiktok from '@/../public/images/tiktok_data/tiktok.png'

interface Props {
  shopDetail?: TikTokSellerDetailVo
}

export const Header = ({ shopDetail }: Props) => {
  return shopDetail ? (
    <div className="flex">
      <Image
        src={shopDetail.image}
        className="size-[144px] rounded-[8px]"
        width={144}
        height={144}
        alt=""
      ></Image>
      <div className="flex flex-1 flex-col overflow-hidden pl-[20px]">
        <div className="flex items-center justify-between">
          <div className="truncate text-[28px] font-bold text-black33">
            {shopDetail.sellerName}
          </div>
          <div
            className="touch-opacity"
            onClick={() =>
              launchUrl(
                `https://snssdk1180.onelink.me/BAuo?domain_source=tiktok&af_dp=snssdk1180://search?keyword=${shopDetail.sellerName}&params_url=https%3A%2F%2Fwww.tiktok.com%2Fsearch&refer=web&needlaunchlog=1&ug_medium=fe_component&jump_time=1711008785222&page_name=general_search&gd_label=click_wap_search&refer=direct&referer=direct&wid=7212895635434898950&search_keyword=${shopDetail.sellerName}&search_type=general&enter_from=homepage_hot_web&enter_method=search_history&search_sug_type=normal_sug&pre_click_id=&vidab=70508271%2C71916113%2C72072742%2C72080030%2C72099722%2C70860576&seo_vidab=&release=1.0.1.1477&pid=tiktokwebother&jump_time=1711008785222&af_ad=click_wap_search&af_siteid=mobile&c=general_search&af_adset=direct&af_ad_id=no_referrer&vidab=70508271%2C71916113%2C72072742%2C72080030%2C72099722%2C70860576&seo_vidab=&release=1.0.1.1477&canonical=https://www.tiktok.com/search&wid=7212895635434898950`,
                false
              )
            }
          >
            <Image src={tiktok} className="size-[44px]" alt=""></Image>
          </div>
        </div>

        {/* <div className="justify-center pt-[14px]">
          <span className="text-gray8A text-[24px]">评分:</span>
          <span className="text-[24px] text-black">{}</span>
        </div> */}
        <div className="pt-[14px]">
          <ClassTag
            text={
              (isLanguageEN()
                ? shopDetail.categoryEn
                : shopDetail.categoryTh) || ''
            }
          ></ClassTag>
        </div>
      </div>
    </div>
  ) : (
    <Skeleton avatar avatarSize={72}></Skeleton>
  )
}
