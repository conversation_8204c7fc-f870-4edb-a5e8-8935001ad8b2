import { metadataTemplate, inApp } from '@/lib/server/utils'
import { Inner } from './inner'
import React from 'react'
import { itemDetail } from '@/app/api/api-uchoice/tiktok/item/detail/fetch'
import { i18nS } from '@/lib/server/i18n'

interface Props {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: i18nS.t('Product.title') })
  }

  const itemInfo = await itemDetail(params)

  const description = itemInfo.productName
  const title = description
  const icon = itemInfo.homeImgUrl

  return metadataTemplate({ title, description, icon })
}

export default async function Index({ params: { id } }: Props) {
  return <Inner id={id} />
}
