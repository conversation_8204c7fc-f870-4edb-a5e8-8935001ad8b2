'use client'

import { useEffect, useRef, useState } from 'react'
import {
  TransparentNavPage,
  TopGrandientCard
} from '@/components/TransparentNavPage'
import { ItemCard } from './components/itemCard'
import ComponentCard from '@/app/components/ComponentCard'
import { StickyHeader } from '@/components/StickyHeader'
import { itemDetail } from '@/app/api/api-uchoice/tiktok/item/detail/request'
import { TikTokItemDetailVo } from '@/app/api/api-uchoice/tiktok/item/detail/dtos'
import { DataSummaryCell } from './components/dataSummaryCell'
import { BottomBar } from '@/app/product/components/bottomBar'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import HotProductsCell from '@/app/product/components/hotProductsCell'
import { MutilLinesChart } from '@/app/components/MutilLinesChart'
import { AppColors } from '@/lib/const'
import { i18n } from '@/lib/client/i18n'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { Tabs } from '@/app/components/Tabs'
import { BottomBarType } from '@/app/product/components/BottomBar/type'
import { GetSamplePopTypeVoType } from '@/app/api/api-uchoice/order/user/getSamplePopTypeVo/dtos'
import { getUserInfo } from '@/lib/client/utils'
import { MemberVo } from '@/app/api/api-uchoice/uChoice/account/current/dtos'
import { TabSection } from '@/app/components/Tabs/TabSection'

interface Props {
  id: string
}

export function Inner({ id }: Props) {
  const bottomBarRef = useRef()

  // @ts-ignore
  const [itemInfo, setItemInfo] = useState<TikTokItemDetailVo>({})
  const [userInfo, setUserInfo] = useState<MemberVo | null | undefined>()

  useEffect(() => {
    fetchUserInfo()
    onRequstInfo()
  }, [])

  const fetchUserInfo = async () => {
    const res = await getUserInfo()
    setUserInfo(res)
  }

  const onRequstInfo = async () => {
    const { code, result } = await itemDetail({ id })
    if (code === 200) {
      setItemInfo(result)
    }
  }

  const onChangedItemInfo = value => {
    setItemInfo(value)
    const { id, isCollected } = value
    webview?.send(WebviewEvents.toggleCollect, {
      id,
      isCollected: `${isCollected}`
    })
  }
  return (
    <TransparentNavPage
      title={i18n.t('TiktokData.商品详情')}
      showShareButton
      onShared={() => statistic({ eventName: EventName.tiktok_data_pdp_share })}
    >
      <TopGrandientCard>
        <div className="pb-[36px] pl-[24px]">
          <ItemCard
            itemInfo={itemInfo}
            // @ts-ignore
            onShowSkuModal={() => bottomBarRef.current.showSkuModal()}
          ></ItemCard>
        </div>
      </TopGrandientCard>

      <StickyHeader>
        <div className="bg-white pt-[12px]">
          <Tabs
            titles={[
              i18n.t('TiktokData.概览'),
              i18n.t('TiktokData.销售趋势'),
              i18n.t('TiktokData.详情'),
              i18n.t('Product.相似推荐')
            ]}
            lastTabScrollToBottom={false}
          ></Tabs>
        </div>
      </StickyHeader>

      <div className="bg-background px-[24px] pt-[20px]">
        <TabSection></TabSection>
        <DataSummaryCell productId={itemInfo.productId}></DataSummaryCell>

        <TabSection></TabSection>
        <ComponentCard
          className="mt-[24px]"
          title={i18n.t('TiktokData.销售趋势')}
        >
          <MutilLinesChart
            xData={itemInfo?.trendList?.map(it => it.axisX) || []}
            yDatas={[
              itemInfo?.trendList?.map(it => it.axisY) || [],
              itemInfo?.trendList?.map(it => it.axisY2) || []
            ]}
            tabs={[
              {
                label: i18n.t('TiktokData.销量'),
                color: AppColors.primary
              },
              {
                label: i18n.t('TiktokData.浏览量'),
                color: '#38A7FF'
              }
            ]}
          ></MutilLinesChart>
        </ComponentCard>

        <TabSection></TabSection>
        <ComponentCard className="mt-[24px]" title={i18n.t('TiktokData.详情')}>
          <div
            className="break-words"
            dangerouslySetInnerHTML={{
              __html:
                itemInfo?.itemExtensionDto?.content ||
                itemInfo?.itemExtensionDto?.contentEn ||
                ''
            }}
          ></div>
        </ComponentCard>
      </div>

      <TabSection></TabSection>
      {/* @ts-ignore */}
      <HotProductsCell itemInfo={itemInfo}></HotProductsCell>

      {itemInfo?.productId && (
        <BottomBar
          type={
            userInfo && userInfo.isQualityUser === false
              ? BottomBarType.tiktokDataPdpWithOutRequestSample
              : BottomBarType.tiktokDataPdp
          }
          ref={bottomBarRef}
          itemInfo={itemInfo}
          onChangedItemInfo={onChangedItemInfo}
          samplePopTypeVoType={GetSamplePopTypeVoType.none}
        ></BottomBar>
      )}
    </TransparentNavPage>
  )
}
