import { TikTokSalesCountVo } from '@/app/api/api-uchoice/tiktok/item/getProductSalesCount/dtos'
import { getProductSalesCount } from '@/app/api/api-uchoice/tiktok/item/getProductSalesCount/request'
import ComponentCard from '@/app/components/ComponentCard'
import Segmented, { TabItem } from '@/app/components/Segmented'
import { i18n } from '@/lib/client/i18n'
import { loading } from '@/lib/client/loading'
import { formatSales } from '@/lib/format'
import { getTimestampOfDaysAgo } from '@/lib/utils'
import React, { useEffect, useState } from 'react'

const tabList: TabItem[] = [
  {
    label: i18n.t('TiktokData.昨天'),
    value: 'yesterday'
  },
  {
    label: i18n.t('TiktokData.7日'),
    value: 'week'
  },
  {
    label: i18n.t('TiktokData.15日'),
    value: 'half_month'
  },
  {
    label: i18n.t('TiktokData.30日'),
    value: 'month'
  }
]

interface Props {
  productId: string
}

export const DataSummaryCell = ({ productId }: Props) => {
  const [data, setData] = useState<TikTokSalesCountVo>()
  const [active, setActive] = useState<string>('yesterday')

  useEffect(() => {
    const startTime = {
      yesterday: getTimestampOfDaysAgo(1),
      week: getTimestampOfDaysAgo(7),
      half_month: getTimestampOfDaysAgo(15),
      month: getTimestampOfDaysAgo(30)
    }

    if (!productId) {
      return
    }
    fetch(startTime[active])
  }, [active, productId])

  const fetch = async startTime => {
    const { code, result } = await getProductSalesCount({
      productId,
      startTime
    })
    if (code === 200) {
      setData(result)
    }
  }

  const onSegmentChange = (val: string) => {
    setActive(val)
  }

  return (
    <ComponentCard className="" title={i18n.t('TiktokData.数据概览')}>
      <Segmented
        active={active}
        tabList={tabList}
        onChange={onSegmentChange}
      ></Segmented>
      <div className="flex justify-between pt-[24px]">
        <div className="flex h-[136px] flex-1 flex-col items-center justify-center rounded-[4px] bg-background">
          <p className="pb-[4px] text-[32px] font-bold text-black">
            {formatSales(data?.sales, '--')}
          </p>
          <p className="text-[24px] text-gray6E">{i18n.t('TiktokData.销量')}</p>
        </div>
        <div className="w-[24px]"></div>
        <div className="flex h-[136px] flex-1 flex-col items-center justify-center rounded-[4px] bg-background">
          <p className="pb-[4px] text-[32px] font-bold text-black">
            {formatSales(data?.gmv, '--')}
          </p>
          <p className="text-[24px] text-gray6E">
            {i18n.t('TiktokData.销售额')}
          </p>
        </div>
      </div>
    </ComponentCard>
  )
}
