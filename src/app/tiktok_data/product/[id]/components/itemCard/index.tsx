import React, { useState } from 'react'
import { SkuBtn } from './components/skuBtn'
import { PriceInfo } from './components/priceInfo'
import { SelectedHighCom } from './components/selectedHighCom'
import { Shop } from './components/shop'
import { Overlay, Skeleton } from 'react-vant'
import close from '@/../public/images/common/modal_circle_close.png'
import { TikTokItemDetailVo } from '@/app/api/api-uchoice/tiktok/item/detail/dtos'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import { copyText } from '@/lib/client/utils'
import { toast } from '@/lib/client/toast'

interface Props {
  itemInfo: TikTokItemDetailVo
  onShowSkuModal: () => void
}

export const ItemCard = ({ itemInfo, onShowSkuModal }: Props) => {
  const [rewardsModalVisible, setRewardsModalVisible] = useState(false)

  return (
    <div className="relative">
      {itemInfo?.homeImgUrl ? (
        <div className="flex justify-between overflow-hidden">
          <Image
            unoptimized={true}
            alt=""
            width={160}
            height={160}
            className="size-[160px] rounded-[8px]"
            src={itemInfo.homeImgUrl}
          ></Image>
          <div className="flex flex-1 flex-col pl-[20px]">
            <div className="pr-[24px]">
              <span
                className="text-[28px] font-bold leading-[42px] text-black touch-opacity"
                onClick={() => {
                  copyText(itemInfo.productName)
                }}
              >
                {itemInfo.productName}
              </span>
            </div>
            <div className="mt-[12px] flex justify-between">
              {itemInfo.isTap && (
                <div onClick={onShowSkuModal}>
                  <SkuBtn></SkuBtn>
                </div>
              )}
              {/* {itemInfo.itemSubsidyVo && (
                <div
                  className="flex h-[44px] items-center overflow-hidden rounded-l-[24px] bg-primary px-[20px] touch-opacity"
                  onClick={() => setRewardsModalVisible(true)}
                >
                  <span className="text-[24px] text-white">
                    {i18n.t('TiktokData.活动奖励')}
                  </span>
                </div>
              )} */}
            </div>
          </div>
        </div>
      ) : (
        <Skeleton avatar avatarSize={80}></Skeleton>
      )}

      <div className="pr-[24px]">
        <PriceInfo itemInfo={itemInfo}></PriceInfo>

        {itemInfo.isTap && (
          <SelectedHighCom itemInfo={itemInfo}></SelectedHighCom>
        )}

        <Shop itemInfo={itemInfo}></Shop>
      </div>

      <Overlay
        visible={rewardsModalVisible}
        onClick={() => setRewardsModalVisible(false)}
        style={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <div className="flex flex-col items-center justify-center">
          <Image
            alt=""
            className="h-[776px] w-[602px] object-contain"
            width={776}
            height={602}
            // @ts-ignore
            src={itemInfo.itemSubsidyVo?.url}
            unoptimized={true}
          ></Image>
          <div className="pt-[32px]">
            <Image
              src={close}
              className="h-[56px] w-[58px] touch-opacity"
              alt=""
            ></Image>
          </div>
        </div>
      </Overlay>
    </div>
  )
}
