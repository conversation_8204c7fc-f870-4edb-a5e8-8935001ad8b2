'use client'

import { Image } from '@/components/Image'
import { useRouter } from '@/lib/hooks/useRouter'
import React from 'react'
import { TikTokItemDetailVo } from '@/app/api/api-uchoice/tiktok/item/detail/dtos'
import { i18n } from '@/lib/client/i18n'

interface Props {
  itemInfo: TikTokItemDetailVo
}

export const Shop = ({ itemInfo }: Props) => {
  const router = useRouter()

  return itemInfo.sellerName?.length > 0 ? (
    <div
      className="mt-[32px] flex items-center justify-between touch-opacity"
      onClick={() => router.push(`/tiktok_data/shop/${itemInfo.sellerId}`)}
    >
      <div className="flex items-center">
        <span className="mr-[24px] text-[24px] text-gray8A">
          {i18n.t('TiktokData.店铺')}
        </span>
        <Image
          className="mr-[24px] size-[40px]"
          src={itemInfo.sellerAvatar}
        ></Image>
        <span className="mr-[12px] text-[28px] text-black">
          {itemInfo.sellerName}
        </span>
        {/* <span className="mr-[4px] text-[24px] text-[#8A8A8A]">好评率</span>
        <span className="text-[24px] text-black">99%(top100)</span> */}
      </div>
      <Image
        className="h-[20px] w-[12px]"
        src="/images/product/detail/arrow.png"
      ></Image>
    </div>
  ) : null
}
