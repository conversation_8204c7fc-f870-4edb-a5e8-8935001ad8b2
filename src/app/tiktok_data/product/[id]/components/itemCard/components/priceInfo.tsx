import { formatMinMax, formatPrice } from '@/lib/format'
import React from 'react'
import { TikTokItemDetailVo } from '@/app/api/api-uchoice/tiktok/item/detail/dtos'
import { i18n } from '@/lib/client/i18n'

interface Props {
  itemInfo: TikTokItemDetailVo
}

export const PriceInfo = ({ itemInfo }: Props) => {
  return (
    <div className="mt-[32px] flex">
      <div
        className="mr-[24px] flex flex-col items-center"
        style={{
          justifyContent:
            itemInfo.creatorCommissionPercent > 0
              ? 'space-between'
              : 'flex-start'
        }}
      >
        <span className="mb-[12px] text-center text-[26px] leading-[36px] text-gray">
          {i18n.t('TiktokData.最新价格')}
        </span>
        <span
          className="break-all text-[32px] font-bold leading-[34px] text-black33"
          dangerouslySetInnerHTML={{
            __html: formatMinMax(
              formatPrice(itemInfo.minPrice, true),
              formatPrice(itemInfo.maxPrice, true)
            ).replace('~฿', '~<br>฿')
          }}
        ></span>
      </div>

      {itemInfo.creatorCommissionPercent > 0 && (
        <div className="mr-[24px] flex flex-col items-center">
          <span className="mb-[12px] text-center text-[26px] leading-[36px] text-gray">
            {i18n.t('TiktokData.精选高佣')}
          </span>
          <span className="text-[32px] font-bold leading-[48px] text-black33">
            {itemInfo.creatorCommissionPercentStr}
          </span>
        </div>
      )}

      {itemInfo.creatorCommissionPercentStr !==
        itemInfo.planCommissionPercentStr && (
        <div className="flex flex-col items-center">
          <span className="mb-[12px] text-center text-[26px] leading-[36px] text-gray">
            {i18n.t('TiktokData.公开佣金')}
          </span>
          <span className="text-[32px] font-bold leading-[48px] text-black33">
            {itemInfo.planCommissionPercentStr}
          </span>
        </div>
      )}
    </div>
  )
}
