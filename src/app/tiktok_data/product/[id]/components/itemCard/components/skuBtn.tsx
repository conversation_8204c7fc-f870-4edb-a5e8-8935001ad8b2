import React from 'react'
import { Image } from '@/components/Image'
import { i18n } from '@/lib/client/i18n'

export const SkuBtn = () => {
  return (
    <div className="flex justify-between touch-opacity">
      <div className="flex h-[36px] flex-row items-center rounded-[4px] bg-background px-[12px]">
        <span className="text-[24px] text-black">
          {i18n.t('TiktokData.规格')}
        </span>
        <Image
          className="ml-[8px] h-[20px] w-[12px]"
          src={'/images/product/detail/arrow.png'}
        ></Image>
      </div>
    </div>
  )
}
