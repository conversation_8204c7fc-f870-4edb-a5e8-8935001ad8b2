import React from 'react'
import { TikTokItemDetailVo } from '@/app/api/api-uchoice/tiktok/item/detail/dtos'
import { formatMinMax, formatPrice } from '@/lib/format'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'

interface Props {
  itemInfo: TikTokItemDetailVo
}

// 精选高佣
export const SelectedHighCom = ({ itemInfo }: Props) => {
  const router = useRouter()

  return (
    <div className="mt-[28px] flex w-full items-center justify-between rounded-[4px] border border-primary bg-gradient-to-b from-[#FFF2EC] to-[#FFFAF8]  py-[12px] pl-[24px] pr-[14px] text-primary">
      <div className="flex items-center">
        <span className="mr-[16px] text-[32px] font-bold">
          {i18n.t('TiktokData.精选高佣')}
        </span>
        <span className="flex-1 text-[26px]">
          {i18n.t('TiktokData.立享%高佣，赚฿', {
            commission: itemInfo.creatorCommissionPercentStr
          })}
          <span className="font-bold">
            {formatMinMax(
              formatPrice(itemInfo.minEarn, true),
              formatPrice(itemInfo.minEarn, true)
            )}
          </span>
        </span>
      </div>
      <div
        className="flex h-[56px] items-center bg-primary px-[16px] touch-opacity"
        onClick={() => router.push(`/product/${itemInfo.ttaItemId}`)}
      >
        <span className="whitespace-nowrap text-[24px] font-bold text-white">
          {i18n.t('TiktokData.免费领样')}
        </span>
      </div>
    </div>
  )
}
