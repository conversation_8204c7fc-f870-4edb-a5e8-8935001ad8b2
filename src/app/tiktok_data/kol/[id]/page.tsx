import { metadataTemplate, inApp } from '@/lib/server/utils'
import { Inner } from './inner'
import React from 'react'
import { i18nS } from '@/lib/server/i18n'
import { getTiktokAnchorDetail } from '@/app/api/api-uchoice/tiktok/anchor/getTiktokAnchorDetail/fetch'

interface Props {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: i18nS.t('TiktokData.达人详情') })
  }

  const detail = await getTiktokAnchorDetail(params)

  const description = `@${detail.displayName}`
  const title = detail.nickname
  const icon = detail.avatar

  return metadataTemplate({ title, description, icon })
}

export default async function Index({ params: { id } }: Props) {
  return <Inner id={id} />
}
