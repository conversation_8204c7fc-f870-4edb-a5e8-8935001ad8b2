import Segmented, { TabItem } from '@/app/components/Segmented'
import React, { useEffect, useMemo, useState } from 'react'
import { getTiktokProductRising } from '@/app/api/api-uchoice/tiktok/anchor/getTiktokProductRising/request'
import { getTimestampOfDaysAgo } from '@/lib/utils'
import { TikTokAnchorProductTrendVo } from '@/app/api/api-uchoice/tiktok/anchor/getTiktokProductRising/dtos'
import { MutilLinesChart } from '@/app/components/MutilLinesChart'
import { AppColors } from '@/lib/const'
import { formatSales } from '@/lib/format'
import { i18n } from '@/lib/client/i18n'

const tabList: TabItem[] = [
  {
    label: i18n.t('TiktokData.7日'),
    value: 'week'
  },
  {
    label: i18n.t('TiktokData.15日'),
    value: 'half_month'
  },
  {
    label: i18n.t('TiktokData.30日'),
    value: 'month'
  }
]

export const SalesCell = ({ anchorId }: { anchorId: string }) => {
  const [active, setActive] = useState<string>('week')
  const [data, setData] = useState<TikTokAnchorProductTrendVo>()

  const handleSegmentChange = (val: string) => {
    setActive(val)
  }

  useEffect(() => {
    const startTime = {
      week: getTimestampOfDaysAgo(7),
      half_month: getTimestampOfDaysAgo(15),
      month: getTimestampOfDaysAgo(30)
    }

    fetch(startTime[active])
  }, [active])

  const fetch = async startTime => {
    const { code, result } = await getTiktokProductRising({
      anchorId,
      startTime,
      endTime: `${Date.now()}`
    })
    if (code === 200) {
      setData(result)
    }
  }

  const gmvs = useMemo(() => {
    const videoGmv =
      data?.videoTrend?.reduce((result, it) => {
        return result + it.gmv
      }, 0) || 0

    const liveGmv =
      data?.liveTrend?.reduce((result, it) => {
        return result + it.gmv
      }, 0) || 0

    return [videoGmv, liveGmv]
  }, [data])

  return (
    <>
      <div className="rounded-[12px] bg-white p-[24px]">
        <Segmented
          active={active}
          tabList={tabList}
          onChange={handleSegmentChange}
        ></Segmented>
        <div className="mt-[28px] flex items-center">
          <span className="text-[30px] font-bold text-black">
            {i18n.t('TiktokData.带货数据')}
          </span>
        </div>
        <div className="pt-[16px]">
          <div className="flex h-[136px] w-full flex-col items-center rounded-[4px] bg-[#F4F5F7]">
            <div className="mt-[24px] text-[32px] font-bold text-black">
              {formatSales(data?.totalGmv, '--')}{' '}
              {data && data?.liveSalesRate > 0 && (
                <span className="text-[24px] text-gray6E">
                  {i18n.t('TiktokData.直播')}{' '}
                  {(data?.liveSalesRate * 100).toFixed(2)}%
                </span>
              )}
            </div>
            <div className="text-[24px] text-gray6E">
              {i18n.t('TiktokData.总销售额')}
            </div>
          </div>
        </div>
      </div>
      <div className="mt-[24px] rounded-[12px] bg-white p-[24px]">
        <div className="mt-[28px] flex items-center">
          <span className="text-[30px] font-bold text-black">
            {i18n.t('TiktokData.带货趋势')}
          </span>
        </div>
        <MutilLinesChart
          xData={data?.liveTrend.map(it => it.date) || []}
          yDatas={[
            data?.liveTrend.map(it => it.gmv) || [],
            data?.videoTrend.map(it => it.gmv) || []
          ]}
          tabs={[
            {
              label: i18n.t('TiktokData.直播'),
              color: AppColors.primary
            },
            {
              label: i18n.t('TiktokData.视频'),
              color: '#38A7FF'
            }
          ]}
        ></MutilLinesChart>
      </div>
    </>
  )
}
