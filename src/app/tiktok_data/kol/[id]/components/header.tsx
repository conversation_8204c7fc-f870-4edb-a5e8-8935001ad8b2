import React, { useState } from 'react'
import Image from 'next/image'
import tiktok from '@/../public/images/tiktok_data/tiktok.png'
import { TiktokAnchorDetailProps } from '../inner'
import { Skeleton } from 'react-vant'
import { launchUrl } from '@/lib/client/utils'
import { i18n } from '@/lib/client/i18n'

export const Header = ({ anchorDetail }: TiktokAnchorDetailProps) => {
  const [categoryExpanded, setCategoryExpanded] = useState(false)

  return (
    <div className="pt-[24px]">
      {anchorDetail?.avatar ? (
        <>
          <div className="flex flex-1 overflow-hidden">
            <Image
              width={120}
              height={120}
              src={anchorDetail?.avatar || ''}
              className="size-[120px] rounded-[60px]"
              style={{
                opacity: anchorDetail?.avatar ? 1 : 0
              }}
              alt=""
            ></Image>

            <div className="flex-1 overflow-hidden pl-[20px] ">
              <div className="flex items-center justify-between text-[32px] font-bold text-black">
                <span>{anchorDetail?.nickname}</span>
                <div
                  className="touch-opacity"
                  onClick={() => launchUrl(anchorDetail.anchorUrl || '', false)}
                >
                  <Image src={tiktok} className="size-[44px]" alt=""></Image>
                </div>
              </div>
              <div className="mt-[12px] text-[26px] text-gray6E">
                {'@'}
                {anchorDetail?.displayName}
              </div>
              <div className="mt-[12px] text-[26px] text-gray6E">
                {i18n.t('TiktokData.达人分类')}：{anchorDetail?.categoryName}
              </div>
            </div>
          </div>
          <div className="my-[32px] text-[28px] text-gray6E">
            {i18n.t('TiktokData.主营带货类目')}：
            <span className="text-[24px] text-black">
              {anchorDetail?.productCategoryNameList
                .slice(0, categoryExpanded ? 999 : 3)
                .join(', ')}
            </span>
            {!categoryExpanded &&
              anchorDetail?.productCategoryNameList.length > 0 && (
                <span
                  className="ml-[8px] bg-background px-[24px] text-[26px] font-bold leading-[48px] text-primary"
                  onClick={() => setCategoryExpanded(true)}
                >
                  {i18n.t('TiktokData.查看更多')}
                </span>
              )}
          </div>
          {/* 不加这个，这里有个色块，懒得查原因 */}
          <div className="h-px"></div>
        </>
      ) : (
        <div className="pb-[48px]">
          <Skeleton avatar avatarSize={60}></Skeleton>
        </div>
      )}

      {/* <div className="mb-[48px] mt-[24px] text-[28px] text-gray6E">
        {"达人简介：q'q'q'q"}
      </div> */}
      {/* <div className="mb-[48px] mt-[24px] flex items-center">
        <span className="text-[28px] text-gray6E">达人联系方式：</span>
        <div className="flex">
          <Image
            src={''}
            className="mr-[24px] h-[44px] w-[44px]"
            alt=""
          ></Image>
          <Image
            src={''}
            className="mr-[24px] h-[44px] w-[44px]"
            alt=""
          ></Image>
          <Image
            src={''}
            className="mr-[24px] h-[44px] w-[44px]"
            alt=""
          ></Image>
        </div>
      </div> */}
    </div>
  )
}
