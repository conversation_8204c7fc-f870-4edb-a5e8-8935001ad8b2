import React, { useState } from 'react'
import { TiktokAnchorDetailProps } from '../inner'
import { formatMinMax, formatSales } from '@/lib/format'
import { SingleLineChart } from '@/app/components/SingleLineChart'
import Image from 'next/image'
import grow from '@/../public/images/tiktok_data/grow.png'
import Segmented, { TabItem } from '@/app/components/Segmented'
import { i18n } from '@/lib/client/i18n'

const tabList: TabItem[] = [
  {
    label: i18n.t('TiktokData.粉丝总量'),
    value: 'total'
  },
  {
    label: i18n.t('TiktokData.粉丝增量'),
    value: 'grow'
  }
]

export const DataSummaryCell = ({ anchorDetail }: TiktokAnchorDetailProps) => {
  const [fansTrend, setFansTrend] = useState<string>('total')

  return (
    <>
      <div className="rounded-[12px] bg-white p-[24px]">
        <div className="flex items-center">
          <span className="text-[30px] font-bold text-black">
            {i18n.t('TiktokData.数据概览')}
          </span>
          <span className="ml-[12px] text-[24px] text-gray6E">
            {i18n.t('TiktokData.近30日')}
          </span>
        </div>
        <div className="flex justify-between pt-[16px]">
          <div className="flex h-[136px] w-[316px] flex-col items-center rounded-[4px] bg-[#F4F5F7]">
            <div className="mt-[24px] text-[32px] font-bold text-black">
              {formatSales(anchorDetail?.totalGmv, '--')}
            </div>
            <div className="break-words text-center text-[24px] text-gray6E">
              {i18n.t('TiktokData.总销售额')}
            </div>
          </div>
          <div className="flex h-[136px] w-[316px] flex-col items-center rounded-[4px] bg-[#F4F5F7]">
            <div className="mt-[24px] text-[32px] font-bold text-black">
              {formatSales(anchorDetail?.followerCount, '--')}
            </div>
            <div className="break-words text-center text-[24px] text-gray6E">
              {i18n.t('TiktokData.粉丝总量')}
            </div>
          </div>
        </div>

        <div className="mt-[36px] flex items-center">
          <span className="break-words text-center text-[30px] font-bold text-black">
            {i18n.t('TiktokData.直播数据')}
          </span>
          <span className="ml-[12px] break-words text-center text-[24px] text-gray6E">
            {i18n.t('TiktokData.近30日')}
          </span>
        </div>
        <div className="mt-[16px] flex w-full flex-col justify-between rounded-[4px] bg-[#F4F5F7] pb-[24px]">
          <div className="flex justify-around">
            <div className="flex w-[30%]  flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatSales(anchorDetail?.liveCount, '--')}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.直播场次')}
              </div>
            </div>
            <div className="flex w-[30%]  flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatMinMax(
                  formatSales(anchorDetail?.minLiveGpm, '--'),
                  formatSales(anchorDetail?.maxLiveGpm, '--')
                )}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.直播GPM')}
              </div>
            </div>
            <div className="flex w-[30%]  flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatSales(anchorDetail?.liveGmv, '--')}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.预估直播销售额')}
              </div>
            </div>
          </div>
          <div className="flex justify-around">
            <div className="flex w-[30%]  flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatSales(anchorDetail?.liveAvgUv, '--')}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.直播平均观众数')}
              </div>
            </div>
            <div className="flex w-[30%]  flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatSales(anchorDetail?.liveAvgLikeCount, '--')}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.直播平均点赞数')}
              </div>
            </div>
            <div className="flex w-[30%]  flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatSales(anchorDetail?.liveAvgCommentCount, '--')}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.直播平均评论数')}
              </div>
            </div>
          </div>
        </div>

        <div className="mt-[36px] flex items-center">
          <span className="text-[30px] font-bold text-black">
            {i18n.t('TiktokData.视频数据')}
          </span>
          <span className="ml-[12px] text-[24px] text-gray6E">
            {i18n.t('TiktokData.近30日')}
          </span>
        </div>
        <div className="mt-[16px] flex w-full flex-col justify-between rounded-[4px] bg-[#F4F5F7] pb-[24px]">
          <div className="flex justify-around">
            <div className="flex w-[30%]  flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatSales(anchorDetail?.videoCount, '--')}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.视频数量')}
              </div>
            </div>
            <div className="flex w-[30%]  flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatMinMax(
                  formatSales(anchorDetail?.minVideoGpm, '--'),
                  formatSales(anchorDetail?.maxVideoGpm, '--')
                )}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.视频GPM')}
              </div>
            </div>
            <div className="flex w-[30%]  flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatSales(anchorDetail?.videoGmv, '--')}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.预估视频销售额')}
              </div>
            </div>
          </div>
          <div className="flex justify-around">
            <div className="flex w-[30%] flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatSales(anchorDetail?.videoAvgViewCount, '--')}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.视频平均观众数')}
              </div>
            </div>
            <div className="flex w-[30%] flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatSales(anchorDetail?.videoAvgLikeCount, '--')}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.视频平均点赞数')}
              </div>
            </div>
            <div className="flex w-[30%] flex-col items-center">
              <div className="mt-[24px] text-[32px] font-bold text-black">
                {formatSales(anchorDetail?.videoAvgCommentCount, '--')}
              </div>
              <div className="break-words text-center text-[24px] text-gray6E">
                {i18n.t('TiktokData.视频平均评论数')}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-[24px] rounded-[12px] bg-white p-[24px]">
        <div className="flex items-center">
          <span className="text-[30px] font-bold text-black">
            {i18n.t('TiktokData.粉丝趋势')}
          </span>
        </div>

        <div className="pb-[20px] pt-[24px]">
          <Segmented
            active={fansTrend}
            tabList={tabList}
            onChange={setFansTrend}
          ></Segmented>
        </div>

        {fansTrend === 'total' ? (
          <>
            {/* <div className="flex items-center text-[24px] text-gray6E">
              <span>
                粉丝变化数+
                {formatSales(anchorDetail?.trendOfTotalFanBase?.followerCount)}
              </span>
              <Image
                src={grow}
                className="h-[16px] w-[24px]"
                width={24}
                height={16}
                alt=""
              ></Image>
              <span className="ml-[40px]">
                粉丝变化率+{anchorDetail?.trendOfTotalFanBase?.followerRate}%
              </span>
              <Image
                src={grow}
                className="h-[16px] w-[24px]"
                width={24}
                height={16}
                alt=""
              ></Image>
            </div> */}

            <SingleLineChart
              xAxis={
                anchorDetail?.trendOfTotalFanBase?.followerData?.map(
                  item => item.date
                ) || []
              }
              yAxis={
                anchorDetail?.trendOfTotalFanBase?.followerData?.map(
                  item => item.followerCount
                ) || []
              }
              label={i18n.t('TiktokData.粉丝总量')}
            ></SingleLineChart>
          </>
        ) : (
          <>
            {/* <div className="flex items-center text-[24px] text-gray6E">
              <span>
                粉丝变化数+
                {formatSales(
                  anchorDetail?.trendOfFollowerGrowth?.followerCount
                )}
              </span>
              <Image
                src={grow}
                className="h-[16px] w-[24px]"
                width={24}
                height={16}
                alt=""
              ></Image>
              <span className="ml-[40px]">
                粉丝变化率+{anchorDetail?.trendOfFollowerGrowth?.followerRate}%
              </span>
              <Image
                src={grow}
                className="h-[16px] w-[24px]"
                width={24}
                height={16}
                alt=""
              ></Image>
            </div> */}

            <SingleLineChart
              xAxis={
                anchorDetail?.trendOfFollowerGrowth?.followerData?.map(
                  item => item.date
                ) || []
              }
              yAxis={
                anchorDetail?.trendOfFollowerGrowth?.followerData?.map(
                  item => item.followerCount
                ) || []
              }
              label={i18n.t('TiktokData.粉丝增量')}
            ></SingleLineChart>
          </>
        )}
      </div>
    </>
  )
}
