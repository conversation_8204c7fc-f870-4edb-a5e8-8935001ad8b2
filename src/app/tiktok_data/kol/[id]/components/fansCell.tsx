import Segmented, { TabItem } from '@/app/components/Segmented'
import React, { useEffect, useMemo, useState } from 'react'
import { getTimestampOfDaysAgo } from '@/lib/utils'
import { getTiktokFansRising } from '@/app/api/api-uchoice/tiktok/anchor/getTiktokFansRising/request'
import { TikTokTrendOfFollowerVo } from '@/app/api/api-uchoice/tiktok/anchor/getTiktokFansRising/dtos'
import { MutilLinesChart } from '@/app/components/MutilLinesChart'
import { AppColors } from '@/lib/const'
import { SexChart } from '@/app/components/SexChart'
import AgeRange from '@/app/tiktok_data/hotspot/detail/components/AudienceAnalysisCard/AgeRange'
import { TiktokAnchorDetailVo } from '@/app/api/api-uchoice/tiktok/anchor/getTiktokAnchorDetail/dtos'
import { i18n } from '@/lib/client/i18n'

const tabList: TabItem[] = [
  {
    label: i18n.t('TiktokData.7日'),
    value: 'week'
  },
  {
    label: i18n.t('TiktokData.15日'),
    value: 'half_month'
  },
  {
    label: i18n.t('TiktokData.30日'),
    value: 'month'
  }
]

export const FansCell = ({
  anchorId,
  anchorDetail
}: {
  anchorId: string
  anchorDetail?: TiktokAnchorDetailVo
}) => {
  const [active, setActive] = useState<string>('week')
  const [data, setData] = useState<TikTokTrendOfFollowerVo>()

  const handleSegmentChange = (val: string) => {
    setActive(val)
  }

  useEffect(() => {
    const startTime = {
      week: getTimestampOfDaysAgo(7),
      half_month: getTimestampOfDaysAgo(15),
      month: getTimestampOfDaysAgo(30)
    }

    fetch(startTime[active])
  }, [active])

  const fetch = async startTime => {
    const { code, result } = await getTiktokFansRising({
      anchorId,
      startTime,
      endTime: `${Date.now()}`
    })
    if (code === 200) {
      setData(result)
    }
  }

  const audienceAges = useMemo(() => {
    if (anchorDetail?.followerDataJson) {
      const followerAges = JSON.parse(
        anchorDetail.followerDataJson
      ).followerAges

      let converted = []

      for (let index = 0; index < 3; index++) {
        const item = followerAges[index]
        // @ts-ignore
        converted.push({
          // @ts-ignore
          score: item.value * 100,
          age_level: {
            '35+': 5,
            '18-24': 3,
            '25-34': 4
          }[item.key]
        })
      }

      return converted
    }

    return null
  }, [anchorDetail])

  const sexRates = useMemo(() => {
    if (anchorDetail?.followerDataJson) {
      const followerGenders = JSON.parse(
        anchorDetail.followerDataJson
      ).followerGenders

      return [
        Number((followerGenders[0].value * 100).toFixed(1)),
        Number((followerGenders[1].value * 100).toFixed(1))
      ]
    }

    return [0, 0]
  }, [anchorDetail])

  return (
    <>
      <div className="rounded-[12px] bg-white p-[24px]">
        <Segmented
          active={active}
          tabList={tabList}
          onChange={handleSegmentChange}
        ></Segmented>
        <div className="mt-[28px] flex items-center">
          <span className="text-[30px] font-bold text-black">
            {i18n.t('TiktokData.粉丝趋势')}
          </span>
        </div>
        <MutilLinesChart
          xData={data?.trendOfTotalFans.map(it => it.date) || []}
          yDatas={[
            data?.trendOfTotalFans.map(it => it.followerCount) || [],
            data?.trendOfNewFans.map(it => it.followerCount) || []
          ]}
          tabs={[
            {
              label: i18n.t('TiktokData.总量'),
              color: AppColors.primary
            },
            {
              label: i18n.t('TiktokData.增量'),
              color: '#38A7FF'
            }
          ]}
        ></MutilLinesChart>
      </div>

      {audienceAges && (
        <div className="mt-[24px] rounded-[12px] bg-white p-[24px]">
          <div className="mt-[28px] flex items-center">
            <span className="text-[30px] font-bold text-black">
              {i18n.t('TiktokData.粉丝画像')}
            </span>
          </div>
          <div className="pb-[20px] pt-[28px] text-[28px] text-gray6E">
            {i18n.t('TiktokData.性别分布')}
          </div>
          <SexChart maleRate={sexRates[0]} femaleRate={sexRates[1]}></SexChart>
          <div className="pb-[20px] pt-[48px] text-[28px] text-gray6E">
            {i18n.t('TiktokData.年龄分布')}
          </div>
          <AgeRange audienceAges={JSON.stringify(audienceAges)}></AgeRange>
        </div>
      )}
    </>
  )
}
