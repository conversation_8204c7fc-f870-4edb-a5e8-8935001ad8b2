import React, { useEffect, useState } from 'react'
import refresh from '@/../public/images/tiktok_data/refresh.png'
import tips from '@/../public/images/common/tips.png'
import { getTiktokAnchorSimilar } from '@/app/api/api-uchoice/tiktok/anchor/getTiktokAnchorDetail/request'
import {
  TikTokSimilarAnchorVo,
  TiktokAnchorDetailVo
} from '@/app/api/api-uchoice/tiktok/anchor/getTiktokAnchorDetail/dtos'
import { loading } from '@/lib/client/loading'
import { formatSales } from '@/lib/format'
import { useRouter } from '@/lib/hooks/useRouter'
import { Image } from '@/components/Image'
import { i18n } from '@/lib/client/i18n'

interface Props {
  id: string
  anchorDetail: TiktokAnchorDetailVo
}

export const SimilarKolsCell = ({ id, anchorDetail }: Props) => {
  const [anchors, setAnchors] = useState<TikTokSimilarAnchorVo[]>([])
  const [minAnchorId, setMinAnchorId] = useState('0')
  const router = useRouter()

  useEffect(() => {
    fetch()
  }, [])

  const fetch = async () => {
    const { code, result } = await getTiktokAnchorSimilar({
      anchorId: id,
      minAnchorId
    })
    loading.hide()

    if (code === 200) {
      setAnchors(result)
      setMinAnchorId(`${result[result.length - 1].anchorId}`)
    }
  }

  return (
    <div className="rounded-[12px] bg-white p-[24px]">
      <div className="mt-[28px] flex justify-between">
        <span className="text-[30px] font-bold text-black">
          {i18n.t('TiktokData.相似达人')}
        </span>

        <div
          className=" flex h-[56px] items-center justify-center rounded-[28px] bg-background px-[24px] touch-opacity"
          onClick={() => {
            loading.show()
            fetch()
          }}
        >
          <Image alt="" src={refresh} className="h-[28px] w-[32px]"></Image>
          <span className="ml-[8px] text-[26px] text-primary">
            {i18n.t('TiktokData.换一换')}
          </span>
        </div>
      </div>

      <div className="mt-[32px] grid grid-cols-3 gap-[14px]">
        {anchors.map((item, i) => (
          <div
            key={item.anchorId}
            className="flex w-[208px] flex-col items-center  overflow-hidden rounded-[8px] bg-gradient-to-b from-[#FFF7F5] to-[#FFEDE7] px-[16px] py-[28px] touch-opacity"
            onClick={() => router.push(`/tiktok_data/kol/${item.anchorId}`)}
          >
            <Image
              alt=""
              width={96}
              height={96}
              className="size-[96px] rounded-[48px]"
              src={item.avatar}
            ></Image>
            <span className="w-[176px] truncate text-center text-[24px] font-bold text-black">
              {item.displayName}
            </span>
            <div className="mb-[4px] mt-[16px] h-[2px] w-full bg-[#D4E8E9]"></div>
            <div className="mt-[12px] flex w-full items-center justify-between">
              <span className="text-[20px] text-gray8A">
                {i18n.t('TiktokData.粉丝数')}
              </span>
              <span className="text-[22px] text-black">
                {formatSales(item.followerCount, '--')}
              </span>
            </div>
            <div className="mt-[12px] flex w-full items-center justify-between">
              <span className="text-[20px] text-gray8A">
                {i18n.t('TiktokData.直播观看')}
              </span>
              <span className="text-[22px] text-black">
                {formatSales(item.liveUv, '--')}
              </span>
            </div>
            <div className="mt-[12px] flex w-full items-center justify-between">
              <span className="text-[20px] text-gray8A">
                {i18n.t('TiktokData.视频点赞')}
              </span>
              <span className="text-[22px] text-black">
                {formatSales(item.videoLikeCount, '--')}
              </span>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center pt-[28px]">
        <Image alt="" src={tips} className="size-[24px]"></Image>
        <span className="ml-[4px] text-[22px] text-gray9A">
          {i18n.t('TiktokData.直播场观、视频点赞数据均为平均值')}
        </span>
      </div>
    </div>
  )
}
