'use client'

import {
  TransparentNavPage,
  TopGrandientCard
} from '@/components/TransparentNavPage'
import { Header } from './components/header'
import { useEffect, useState } from 'react'
import { StickyHeader } from '@/components/StickyHeader'
import { Section } from './components/section'
import { DataSummaryCell } from './components/dataSummaryCell'
import { SalesCell } from './components/salesCell'
import { FansCell } from './components/fansCell'
import { SimilarKolsCell } from './components/similarKolsCell'
import { getTiktokAnchorDetail } from '@/app/api/api-uchoice/tiktok/anchor/getTiktokAnchorDetail/request'
import { TiktokAnchorDetailVo } from '@/app/api/api-uchoice/tiktok/anchor/getTiktokAnchorDetail/dtos'
import { i18n } from '@/lib/client/i18n'
import { Tabs } from '@/app/components/Tabs'
import { TabSection } from '@/app/components/Tabs/TabSection'

export interface TiktokAnchorDetailProps {
  anchorDetail?: TiktokAnchorDetailVo
}

interface Props {
  id: string
}

export function Inner({ id }: Props) {
  const [anchorDetail, setAnchorDetail] = useState<TiktokAnchorDetailVo>()

  useEffect(() => {
    fetch()
  }, [])

  const fetch = async () => {
    const { code, result } = await getTiktokAnchorDetail({
      anchorId: id
    })
    if (code === 200) {
      setAnchorDetail(result)
    }
  }

  return (
    <TransparentNavPage title={i18n.t('TiktokData.达人详情')} showShareButton>
      <TopGrandientCard>
        <div className="px-[24px]">
          <Header anchorDetail={anchorDetail}></Header>
        </div>
      </TopGrandientCard>

      <StickyHeader>
        <div className="bg-white pt-[12px]">
          <Tabs
            titles={[
              i18n.t('TiktokData.数据概览'),
              i18n.t('TiktokData.带货分析'),
              i18n.t('TiktokData.粉丝分析'),
              i18n.t('TiktokData.相似达人')
            ]}
          ></Tabs>
        </div>
      </StickyHeader>

      <div className="bg-background px-[24px]">
        <TabSection></TabSection>
        <Section title={i18n.t('TiktokData.数据概览')}></Section>
        <DataSummaryCell anchorDetail={anchorDetail}></DataSummaryCell>

        <TabSection></TabSection>
        <Section title={i18n.t('TiktokData.带货分析')}></Section>
        <SalesCell anchorId={id}></SalesCell>

        <TabSection></TabSection>
        <Section title={i18n.t('TiktokData.粉丝分析')}></Section>
        <FansCell anchorId={id} anchorDetail={anchorDetail}></FansCell>

        <TabSection></TabSection>
        <Section title={i18n.t('TiktokData.相似达人')}></Section>
        {anchorDetail && (
          <SimilarKolsCell
            id={id}
            anchorDetail={anchorDetail}
          ></SimilarKolsCell>
        )}
      </div>
    </TransparentNavPage>
  )
}
