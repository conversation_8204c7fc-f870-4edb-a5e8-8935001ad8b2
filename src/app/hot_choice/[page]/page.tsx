import { metadataTemplate, inApp } from '@/lib/server/utils'
import Inner from './inner'
import { info } from '@/app/api/api-uchoice/tt/item/info/fetch'
import React from 'react'
import { i18nS } from '@/lib/server/i18n'
interface Props {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: i18nS.t('LowPrice.爆款榜') })
  }
  const title = i18nS.t('LowPrice.爆款榜')
  const icon =
    'https://file.uchoice.pro/public/img/231219/single2.png?x-oss-process=style/jpg'

  return metadataTemplate({ title, icon })
}

export default async function Index() {
  return <Inner />
}
