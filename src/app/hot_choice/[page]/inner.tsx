'use client'
import React, { useEffect, useState } from 'react'
import styles from './index.module.scss'
import HotTab from '@/app/components/TabCard'
import HotHead from './components/hot_head'
import SalesTab from './components/sales_tab'
import HotItem from './components/hot_item'
import { getWebItemListByPage } from '@/app/api/api-uchoice/tt/item/highItem'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import { StickyHeader } from '@/components/StickyHeader'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { webview } from '@/lib/client/webview'
import { useIntersectionObserver } from '@/lib/hooks/useIntersectionObserver'
import { useRouteParams } from '@/lib/hooks/useRouteParams'

const Inner = () => {
  const mounted = useMounted()
  const [itemInfo, setItemInfo] = useState<any>([])
  const [hasMore, setHasMore] = useState(true)
  const [pageNo, setPageNO] = useState(1)
  const [total, setTotal] = useState(0)
  const [activeSaleTab, setActiveSaleTab] = useState(1)
  const [categoryId, setCategoryId] = useState(0)
  const { ref, isLeave } = useIntersectionObserver()
  const routeParams = useRouteParams<any>()
  useEffect(() => {
    if (routeParams && routeParams?.sales) {
      const { sales } = routeParams
      if (sales == 'total') {
        setActiveSaleTab(2)
      } else {
        setActiveSaleTab(1)
      }
    }
  }, [])
  const [apiParams, serApiParams] = useState<any>({
    sortBy: 'salesForLast30Days',
    sortDirection: 'desc',
    categoryId: 0
  })

  const handleTabClick = async item => {
    setItemInfo([])
    setPageNO(1)
    setCategoryId(item.id)
    webItemListByPage(item.id)
  }
  const handleSaleTabClick = (index, item) => {
    setActiveSaleTab(index)
    setItemInfo([])
    const tabItem = {
      sortBy: item.sortBy,
      sortDirection: item.sortDirection
    }
    serApiParams({ ...apiParams, ...tabItem })
  }

  useEffect(() => {
    webItemListByPage()
  }, [apiParams])

  const webItemListByPage = (id?, page?) => {
    setHasMore(true)
    getWebItemListByPage({
      pageNo: '1',
      pageSize: '100',
      ...apiParams,
      categoryId: id || id === 0 ? id : categoryId
    }).then(res => {
      if (res.code == 200) {
        const list: any = res?.result?.list || []
        if (list.length == 0) {
          setHasMore(false) // 如果没有更多数据了，设置 hasMore 为 false
        }
        if (list.length > 0) {
          setTotal(res.result?.total || 0)
          setItemInfo([...list])
          setHasMore(true)
        }
      }
    })
  }
  const fetchData = () => {
    // if (total == itemInfo.length) {
    //   setHasMore(false)
    //   return
    // }
    // let page = pageNo + 1
    // setPageNO(page)
    // 模拟异步加载数据
    // setTimeout(() => {
    //   webItemListByPage(null)
    // }, 1000)
  }
  const saleArr = [
    {
      sortBy: 'salesForLast30Days',
      value: i18n.t('LowPrice.minSale30'),
      sortDirection: 'desc'
    },
    {
      sortBy: 'sales',
      value: i18n.t('LowPrice.sales'),
      sortDirection: 'desc'
    }
  ]
  return (
    mounted && (
      <TransparentNavPage
        title={isLeave ? i18n.t('LowPrice.爆款榜') : ''}
        showShareButton={isLeave ? true : false}
        showBackButton={isLeave ? true : false}
        // transparent={false}
        hide={!webview}
      >
        <div className={styles.high_container}>
          <div ref={ref}></div>
          <div style={{ zIndex: isLeave ? '9' : '999' }}>
            <HotHead
              titleName={i18n.t('LowPrice.topChoiceTitle')}
              title={i18n.t('LowPrice.爆款榜')}
            ></HotHead>
          </div>
          <StickyHeader zIndex="999">
            <div className={styles.tab_box}>
              {mounted && <HotTab handleTabClick={handleTabClick}></HotTab>}
              <SalesTab
                tabArr={saleArr}
                activeTab={activeSaleTab}
                handleTabClick={handleSaleTabClick}
              ></SalesTab>
            </div>
            <div
              className={styles.tab_content}
              style={{
                transform: `translateX(-${(activeSaleTab - 1) * 100}%)`
              }}
            ></div>
          </StickyHeader>
          <HotItem
            activeSaleTab={activeSaleTab}
            hasMore={hasMore}
            itemInfo={itemInfo}
            fetchData={fetchData}
          ></HotItem>
        </div>
      </TransparentNavPage>
    )
  )
}

export default Inner
