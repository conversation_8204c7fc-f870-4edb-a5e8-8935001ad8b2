import React, { useEffect, useState } from 'react'
import styles from './index.module.scss'
const HotTab = ({ tabArr, handleTabClick, activeTab }) => {
  return (
    <div>
      <div className={styles.tab_container}>
        <div className={styles.tab_buttons}>
          {(tabArr || []).map((item, index) => (
            <div className={styles.buttons_box} key={index}>
              <button
                className={activeTab == index + 1 ? styles.active : ''}
                onClick={() => {
                  handleTabClick(index + 1, item)
                }}
              >
                {item.value}
              </button>
              {activeTab == index + 1 && (
                <div className={styles.underline}></div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default HotTab
