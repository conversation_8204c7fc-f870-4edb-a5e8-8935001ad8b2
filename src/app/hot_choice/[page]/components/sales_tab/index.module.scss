.tab_container {
  width: 100%;
  overflow: auto;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
  background-color: #fff;
  margin-top: -12px;
}

/* Webkit 浏览器（如 Chrome 和 Safari）的滚动条样式 */
.tab_container::-webkit-scrollbar {
  display: none;
}

.tab_buttons {
  display: flex;
  padding: 0 24px 12px 24px;
}

.tab_buttons button {
  padding: 10px 20px;
  border: none;
  outline: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 28px;
  font-weight: 400;
  color: #6e6e6e;
  line-height: 40px;
}

.tab_buttons button.active {
  font-size: 28px;
  font-weight: bold;
  color: #303030;
  line-height: 44px;
}

.tab_content {
  display: flex;
  transition: transform 0.3s ease;
}

.buttons_box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  margin-right: 40px;
}

.underline {
  width: 32px;
  height: 6px;
  background: #fe6d45;
}

.content {
  width: 100%;
}