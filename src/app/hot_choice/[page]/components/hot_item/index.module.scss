.item_container {
  display: flex;
  padding: 24px;
  background-color: #fff;
  margin-bottom: 12px;
}

.item {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.item_box {
  flex: 1;
  background-color: #f5f5f5;
  overflow: scroll;
}

.item_top {
  height: 28px;
  font-size: 24px;
  font-weight: 400;
  color: #6a5322;
}

.item_desc {
  flex: 1;
}

.item_content {
  display: flex;
  flex: 1;
  justify-content: space-between;
}

.item_pruductName {
  height: 80px;
  font-size: 28px;
  font-weight: 400;
  color: #303030;
  line-height: 42px;
  overflow: hidden;
  /* 隐藏溢出部分的文本 */
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item_prices {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 24px 0 12px 0;
}

.item_serial_one {
  width: 72px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 192px;
  background: linear-gradient(180deg, #ffc974 0%, rgba(255, 238, 199, 0) 100%);
  border-radius: 4px 0px 0px 0px;
  padding: 16px 8px;
}

.item_serial_one .item_top {
  font-size: 24px;
  font-weight: 400;
  color: #6a5322;
}

.item_serial_one .serial_num {
  font-size: 28px;
  font-weight: bold;
  color: #6a5322;
  line-height: 32px;
  margin: 0 3px;
}

.item_serial_two {
  width: 72px;
  height: 192px;
  background: linear-gradient(180deg, #cbe2fc 0%, rgba(222, 238, 253, 0) 100%);
  border-radius: 4px 0px 0px 0px;
  padding: 16px 8px;
}

.item_serial_two .item_top {
  font-size: 24px;
  font-weight: 400;
  color: #1c3e56;
  line-height: 28px;
}

.item_serial_two .serial_num {
  font-size: 24px;
  font-weight: bold;
  color: #1c3e56;
  line-height: 28px;
  margin: 0 3px;
}

.item_serial_three {
  width: 72px;
  height: 192px;
  background: linear-gradient(180deg, #ffc585 0%, rgba(254, 232, 210, 0) 100%);
  border-radius: 4px 0px 0px 0px;
  padding: 16px 8px;
}

.item_serial_three .item_top {
  font-size: 24px;
  font-weight: normal;
  color: #792e2f;
  line-height: 28px;
}

.item_serial_three .serial_num {
  font-size: 28px;
  font-weight: bold;
  color: #792e2f;
  line-height: 32px;
  margin: 0 3px;
}

.item_serial_other {
  width: 72px;
  height: 192px;
  border-radius: 4px 0px 0px 0px;
  padding: 16px 8px;
}

.item_serial_other .serial_num {
  font-size: 28px;
  font-weight: bold;
  color: #83726a;
}

.serial_box {
  display: flex;
  align-items: center;
  margin-top: 12px;
}

.serial_img {
  width: 14px;
  height: 22px;
}

.item_img {
  width: 192px;
  height: 192px;
  margin-right: 20px;
}

.serial_num {
  font-size: 28px;
  font-weight: bold;
  color: #6a5322;
  line-height: 32px;
}

.sold_img {
  width: 20px;
  height: 24px;
  margin-right: 8px;
}

.stock {
  font-size: 22px;
  font-weight: normal;
  color: #303030;
  line-height: 22px;
}

.item_price {
  font-size: 24px;
  font-weight: 600;
  color: #303030;
  line-height: 36px;
}

.item_comm {
  height: 32px;
  background: rgba(197, 68, 93, 0.1);
  border-radius: 2px;
  padding: 0 8px;
  font-size: 22px;
  font-weight: normal;
  color: #c5445d;
}

.item_sold_btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.item_earn {
  font-size: 24px;
  font-weight: 400;
  color: #fe6d45;
  line-height: 36px;
}

.item_earn_num {
  font-size: 24px;
  font-weight: 400;
  color: #fe6d45;
  line-height: 36px;
  font-weight: bold;
}

.item_comm_num {
  font-size: 22px;
  font-weight: 400;
  color: #c5445d;
  line-height: 24px;
  font-weight: bold;
}

.item_sold {
  display: flex;
  align-items: center;
}

.stock {
  font-size: 22px;
  font-weight: normal;
  color: #303030;
  line-height: 22px;
}

.sold {
  font-size: 22px;
  font-weight: normal;
  color: #303030;
  line-height: 22px;
}

.item_btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 8px;
  height: 48px;
  background: #fe6d45;
  border-radius: 2px;
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
}

.noMore {
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loadingStatus {
  background-color: #ffffff;
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  padding-top: 150px;
}