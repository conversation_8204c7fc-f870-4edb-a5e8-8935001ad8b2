import React, { useEffect, useState } from 'react'
import styles from './index.module.scss'
import { useMounted } from '@/lib/hooks/useMounted'
import icon_sold from '@/../public/images/high_commission/icon_sold.png'
import InfiniteScroll from 'react-infinite-scroll-component'
import Image from 'next/image'
import SerialItem from '../../../../components/Serial_item'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useRouter } from '@/lib/hooks/useRouter'
import { rpxToPx } from '@/lib/client/utils'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { i18n } from '@/lib/client/i18n'
import HandlerOnceTap from '@/lib/handlerOnceTap'
import { formatPrice } from '@/lib/format'

const HotItem = ({ itemInfo, fetchData, hasMore, activeSaleTab }) => {
  const router = useRouter()
  const mounted = useMounted()
  const onAddToShowCase = async item => {
    if (webview) {
      const canLaunch = await webview?.send(WebviewEvents.canLaunch, {
        url: 'tiktok://'
      })
      if (!canLaunch) {
        webview?.send(WebviewEvents.makeSureTikTokAuthed)
      } else {
        webview?.send(WebviewEvents.launch, {
          url: item.sourceLink
        })
      }
    } else {
      window.location.href = item.sourceLink
    }
  }
  return (
    <div className={styles.item_box}>
      {hasMore && itemInfo.length == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.loading}></StatusView>
        </div>
      )}
      {!hasMore && itemInfo.length == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.empty}></StatusView>
        </div>
      )}
      {mounted && itemInfo && itemInfo.length > 0 && (
        <InfiniteScroll
          dataLength={itemInfo.length}
          next={fetchData}
          hasMore={hasMore}
          loader={<div className={styles.noMore}></div>}
          endMessage={<div className={styles.noMore}></div>}
        >
          {itemInfo.map((item, index) => {
            return (
              <div
                className={styles.item_container}
                key={index}
                onClick={() => {
                  HandlerOnceTap(() => {
                    router.push(`${window.location.origin}/product/${item.id}`)
                  })
                }}
              >
                <div className={styles.item}>
                  <SerialItem item={item} index={index}></SerialItem>
                  <div className={styles.item_content}>
                    <Image
                      unoptimized={true}
                      src={item.image}
                      alt="title"
                      width={rpxToPx(192)}
                      height={rpxToPx(192)}
                      className={styles.item_img}
                    />
                    <div className={styles.item_desc}>
                      <div className={styles.item_pruductName}>
                        {item.productName}
                      </div>
                      <div className={styles.item_prices}>
                        <div className={styles.item_price}>
                          <span className={styles.item_price_num}>
                            {`${
                              item.maxPrice === item.minPrice
                                ? formatPrice(item.minPrice, true)
                                : ` ${formatPrice(
                                    item.minPrice,
                                    true
                                  )}~ ${formatPrice(item.maxPrice, true)}`
                            }`}
                          </span>{' '}
                        </div>
                        <div className={styles.item_comm}>
                          {i18n.t('LowPrice.佣金')}{' '}
                          <span className={styles.item_comm_num}>
                            {item.commissionRate}
                          </span>
                        </div>
                        <div className={styles.item_earn}>
                          {i18n.t('LowPrice.Earn')}{' '}
                          <span className={styles.item_earn_num}>{`${
                            item.minEarn === item.maxEarn
                              ? formatPrice(item.maxEarn, true)
                              : ` ${formatPrice(
                                  item.minEarn,
                                  true
                                )}~ ${formatPrice(item.maxEarn, true)}`
                          }`}</span>
                        </div>
                      </div>
                      <div className={styles.item_sold_btn}>
                        <div className={styles.item_sold}>
                          <Image
                            src={icon_sold}
                            alt="sold"
                            className={styles.sold_img}
                          />
                          <span className={styles.stock}>
                            {activeSaleTab == 1
                              ? item.salesForLast30DaysStr
                              : item.salesStr}
                          </span>
                          &nbsp;&nbsp;
                          <span className={styles.sold}>
                            {' '}
                            {activeSaleTab == 1 ? 'Sold/Month' : 'Sold'}
                          </span>
                        </div>
                        <div
                          className={styles.item_btn}
                          // onClick={() => {
                          // HandlerOnceTap(() => {
                          //   router.push(
                          //     `${window.location.origin}/product/${item.id}`
                          //   )
                          // })
                          // }}
                        >
                          {i18n.t('LowPrice.GetFreeSample')}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </InfiniteScroll>
      )}
      {itemInfo.length > 0 && itemInfo.length < 101 && (
        <div className={styles.noMore}>{i18n.t('Common.Empty')}</div>
      )}
    </div>
  )
}

export default HotItem
