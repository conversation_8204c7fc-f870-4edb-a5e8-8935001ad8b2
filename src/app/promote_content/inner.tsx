'use client'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import {
  MySampleLimitV3Params,
  PromotionContentListDto,
  PromotionContentListParams
} from '../api/api-uchoice/tt/item/promotionInfo/dtos'
import { loading } from '@/lib/client/loading'
import {
  getPromotionContentList,
  getPromotionRelatedCountV2,
  getSampleRecommendList,
  mySampleLimitV3
} from '../api/api-uchoice/tt/item/promotionInfo/request'
import StatusView, { StatusViewType } from '../components/StatusView'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { useEffect, useRef, useState } from 'react'
import Btns from './components/btns'
import Item from './components/item'
import ProductIntro from './components/productIntro'
import SortBtn from './components/SortBtn'
import RecommendItem from './components/RecommendItem'
import { rpxToPx } from '@/lib/client/utils'
import ResaleData from './components/ResaleData'
import OverviewComponment from './components/OverviewComponment'
import DialogAuth from './components/dialog'
import { useMounted } from '@/lib/hooks/useMounted'
import { PullToRefresh, InfiniteScroll } from 'antd-mobile'
import { SkeletonParagraph } from 'antd-mobile/es/components/skeleton/skeleton'
import arrow_up from '@/../public/images/common/arrow_up.png'
import Image from 'next/image'
import { Loading } from 'react-vant'
import { LoadingPage } from '@/components/Loading'

interface Props {
  union_id: string
  isProSaleData?: boolean
}

const sleep = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(null)
    }, 1000)
  })
}

const renderLoading = () => {
  return (
    <div
      className="flex size-full items-center justify-center"
      style={{ backgroundColor: 'rgba(0,0,0,0)' }}
    >
      <Loading type="spinner" size="16px" />
    </div>
  )
}

const InfiniteScrollContent = ({ hasMore }: { hasMore?: boolean }) => {
  return (
    <>
      {hasMore ? (
        <div className={styles.no_more}>Loading...</div>
      ) : (
        <div className={styles.no_more}>{i18n.t('TiktokData.无更多数据')}</div>
      )}
    </>
  )
}

const PromotionContent = () => {
  const phoneScreenHeight =
    typeof window !== 'undefined' ? window.innerHeight : 0
  const mounted = useMounted()
  const [productItem, setProductItem] = useState<MySampleLimitV3Params[]>([]) //头像列表
  const [btnIndex, setBtnIndex] = useState(1) //按钮序号
  const lastDayRef = useRef<string>('30') //选择的日期
  const [selectDate, setSelectDate] = useState<string>(
    i18n.t('Promotion.近30天')
  )
  const reIdRef = useRef(null) //按钮移动的距离
  const [proRelatedItem, setProRelatedItem] = useState<any>({}) //直播数量和推广数据
  const routerParams = useRouteParams<Props>() //路由参数
  const unionIdRef = useRef('') //当前选中的头像
  const [itemList, setItemList] = useState<PromotionContentListDto[]>([])
  const pageNoRef = useRef(1)
  const totalRef = useRef(1)
  const typeRef = useRef('1')
  const [finished, setFinished] = useState<boolean>(false)
  const sortDirectionRef = useRef()
  const [tabIndex, setTabIndex] = useState(0)
  const sortByRef = useRef('')
  const [status, setStatus] = useState(StatusViewType.loading)
  const [authDialog, setAuthDialog] = useState(0)
  const carouselRef = useRef<HTMLDivElement | null>(null)
  const [fetchUserLoading, setFetchUserLoading] = useState(false)
  const fetchVideoLoadingRef = useRef(false)
  const scrollContainerRef = useRef<any>(null)
  const [isVisible, setIsVisible] = useState(false) // 滚动按钮是否可见
  const [recommendData, setRecommendData] = useState<any>([])

  // 在组件挂载时设置事件监听
  useEffect(() => {
    // 检查滚动位置来决定按钮是否可见
    const checkScrollPosition = () => {
      if (scrollContainerRef.current.scrollTop > 300) {
        // 滚动超过300px时显示按钮
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    const interval = setInterval(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.addEventListener(
          'scroll',
          checkScrollPosition
        )
        clearInterval(interval)
      }
    }, 500)
    return () => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.removeEventListener(
          'scroll',
          checkScrollPosition
        )
      }
    }
  }, [])

  // 点击按钮滚动到页面顶部
  const scrollToTop = () => {
    scrollContainerRef.current.scrollTo({
      top: -100,
      behavior: 'smooth' // 平滑滚动
    })
  }

  useEffect(() => {
    fetchAvatarItems()
    if (routerParams?.union_id) {
      unionIdRef.current = routerParams?.union_id
      proTelatedItems()
    }
    // fetchVideoItems()
  }, [])

  //埋点
  const toStatistic = (name, unionId) => {
    statistic({
      eventName: EventName[name],
      param: {
        unionId: unionId
      }
    })
  }

  //获取头像的接口
  const fetchAvatarItems = async () => {
    setFetchUserLoading(true)
    const { code, result } = await mySampleLimitV3()
    setFetchUserLoading(false)

    if (code === 200 && result) {
      const itemsArray = Array.isArray(result) ? result : [result]
      setProductItem(itemsArray)
      if (itemsArray.length > 0) {
        if (unionIdRef.current == '') {
          unionIdRef.current = itemsArray[0].unionId
          proTelatedItems()

          loading.hide()
        }
        setAuthDialog(1)
      } else {
        loading.hide()
        setAuthDialog(2)
        return
      }
      clearData()
    }
  }
  //数据清空页码为1
  const clearData = () => {
    fetchVideoLoadingRef.current = false
    setItemList([])
    pageNoRef.current = 1
    setFinished(false)
    fetchVideoItems()
  }
  //选择头像的触发事件
  const handleItem = currentItem => {
    window.scrollTo(0, 0)
    toStatistic('promotion_center_avatar_user', currentItem.unionId)
    unionIdRef.current = currentItem.unionId
    clearData()
    proTelatedItems()
  }
  //视频或直播按钮的触发事件
  const myRecordsBtneTabs = item => {
    setBtnIndex(item.id)
    typeRef.current = item.id.toString()
    clearData()
    item.id == 1
      ? toStatistic('promotion_center_video_btn', unionIdRef.current)
      : toStatistic('promotion_center_live_btn', unionIdRef.current)
    sortDirectionRef.current = undefined
    sortByRef.current = ''
    proTelatedItems()
  }
  //选择日期的触发事件
  const onDateSelect = item => {
    setSelectDate(item.text)
    switch (item.text) {
      case i18n.t('Promotion.近30天'):
        lastDayRef.current = '30'
        break
      case i18n.t('Promotion.近7天'):
        lastDayRef.current = '7'
        break
      case i18n.t('Promotion.近90天'):
        lastDayRef.current = '90'
        break
      case i18n.t('Promotion.近半年'):
        lastDayRef.current = '180'
        break
    }
    clearData()
    //获取推广数据的接口
    proTelatedItems()
    statistic({
      eventName: EventName.promotion_center_day_btn,
      param: {
        unionId: unionIdRef.current,
        lastDay: lastDayRef.current
      }
    })
  }
  //获取直播数量和推广数据
  const proTelatedItems = async () => {
    const params = {
      lastDay: lastDayRef.current,
      type: typeRef.current,
      unionId: unionIdRef.current
    }
    const { code, result } = await getPromotionRelatedCountV2(params)

    if (code === 200) {
      setProRelatedItem(result)
    }
  }
  //下拉刷新
  const handlePullfresh = () => {
    window.scrollTo(0, 0)
    pageNoRef.current = 1
    fetchVideoLoadingRef.current = false
    fetchVideoItems()
  }
  const ItemInfo = async () => {
    loading.show()
    const res = await getSampleRecommendList()
    if (res.code == 200) {
      if (res.result) {
        setRecommendData(res.result)
      }
      loading.hide()
    }
  }
  //加载列表数据
  const fetchVideoItems = async () => {
    if (fetchVideoLoadingRef.current) return
    fetchVideoLoadingRef.current = true
    setFinished(itemList.length == totalRef.current)
    loading.show()

    const params: PromotionContentListParams = {
      pageNo: pageNoRef.current,
      pageSize: '10',
      type: typeRef.current,
      unionId: unionIdRef?.current,
      lastDay: lastDayRef?.current,
      sortBy: sortByRef.current,
      sortDirection: sortDirectionRef.current
    }
    try {
      const { code, result } = await getPromotionContentList(params)
      loading.hide()
      setFinished(false)
      if (code === 200) {
        if (result.list.length > 0) {
          pageNoRef.current == 1
            ? setItemList([...result.list])
            : setItemList(itemList.concat(result.list))
          totalRef.current = result.total
          setStatus(
            result.total === 0 && pageNoRef.current === 1
              ? StatusViewType.empty
              : StatusViewType.loading
          )
        } else {
          await ItemInfo()
          setStatus(StatusViewType.empty)
          setFinished(true)
        }
        pageNoRef.current++
      }
    } finally {
      fetchVideoLoadingRef.current = false
    }
  }

  const handleSortBtns = (sort, sortIcon) => {
    if (sortDirectionRef.current == 'publish_time' && typeRef.current == '2') {
      sortDirectionRef.current = undefined
      sortByRef.current = ''
    }
    sortDirectionRef.current = sortIcon
    sortByRef.current = sort
    pageNoRef.current = 1

    fetchVideoItems()
    statistic({
      eventName:
        sort == 'publish_time'
          ? EventName.promotion_center_pushish_time
          : EventName.promotion_center_items_sold,
      param: {
        unionId: unionIdRef.current,
        type: typeRef.current,
        lastDay: lastDayRef?.current
      }
    })
  }
  // 点击头像滚动到指定位置
  const scrollToItem = (index: number) => {
    if (!carouselRef.current) return // 非空检查
    const item = carouselRef.current.children[index] as HTMLElement // 将 Element 类型断言为 HTMLElement
    if (item) {
      const offsetLeft = item.offsetLeft // 头像相对于容器的偏移量
      const containerWidth = carouselRef.current.offsetWidth // 容器宽度
      const itemWidth = item.offsetWidth // 头像宽度
      // 滚动到目标位置（使头像居中显示）
      carouselRef.current.scrollTo({
        left: offsetLeft - (containerWidth - itemWidth) / 2,
        behavior: 'smooth' // 平滑滚动
      })
    }
  }

  return mounted ? (
    <div className={styles.item_box} style={{ height: phoneScreenHeight }}>
      {authDialog == 1 ? (
        <div>
          <div className={styles.main_bg}></div>
          {isVisible && (
            <div className={styles.btn_scroll} onClick={scrollToTop}>
              <Image
                alt=""
                src={arrow_up}
                style={{ width: rpxToPx(48), height: rpxToPx(48) }}
              ></Image>
            </div>
          )}
          <PullToRefresh
            onRefresh={async () => {
              await sleep()
              handlePullfresh()
            }}
            renderText={status => {
              return renderLoading()
            }}
          >
            <div
              style={{
                width: '100%',
                height: phoneScreenHeight,
                overflow: 'scroll'
              }}
              ref={scrollContainerRef}
            >
              {fetchUserLoading ? (
                <div
                  style={{
                    height: rpxToPx(248),
                    padding: rpxToPx(24),
                    backgroundColor: '#fff'
                  }}
                >
                  <SkeletonParagraph />
                </div>
              ) : (
                <div className={styles.btn_bg}>
                  <div className={styles.btns_box} ref={carouselRef}>
                    {(productItem || []).map((item, index) => {
                      return (
                        <div
                          key={index}
                          className={styles.btns_item}
                          onClick={() => scrollToItem(index)}
                        >
                          <ProductIntro
                            productItem={item}
                            cardIndex={unionIdRef.current}
                            handleItem={handleItem}
                          />
                        </div>
                      )
                    })}
                  </div>
                  <div className={styles.live_btns_box}>
                    <Btns
                      btnIndex={btnIndex}
                      myRecordsBtneTabs={myRecordsBtneTabs}
                      onSelect={onDateSelect}
                      selectTitle={selectDate}
                      reIdRef={reIdRef}
                      proRelatedCount={proRelatedItem}
                    ></Btns>
                  </div>
                </div>
              )}
              <div className={styles.content}>
                {/*  获取推广数据*/}
                <ResaleData
                  item={proRelatedItem}
                  unionId={unionIdRef.current}
                />

                {itemList.length > 0 ? (
                  <>
                    <div className={styles.sort_btn_box}>
                      <SortBtn
                        handleClickSort={handleSortBtns}
                        type={typeRef.current}
                      />
                    </div>
                    {/* 获取我的推广提示数据 */}
                    {routerParams?.isProSaleData ? (
                      <OverviewComponment
                        lastDay={lastDayRef.current}
                        type={btnIndex}
                        unionId={unionIdRef.current || routerParams?.union_id}
                        proRelatedCount={proRelatedItem}
                      />
                    ) : null}

                    <div>
                      {itemList.map((item, index) => {
                        return (
                          <div
                            key={index}
                            style={{ marginBottom: rpxToPx(24) }}
                          >
                            <Item
                              item={item}
                              tabIndex={tabIndex}
                              type={typeRef.current}
                              lastDay={lastDayRef.current}
                              unionId={unionIdRef.current}
                            ></Item>
                            <div className={styles.bottom_line}></div>
                          </div>
                        )
                      })}
                    </div>
                    <InfiniteScroll
                      loadMore={fetchVideoItems}
                      hasMore={!finished}
                    >
                      <InfiniteScrollContent hasMore={!finished} />
                    </InfiniteScroll>
                  </>
                ) : (
                  <>
                    {finished && recommendData.length > 0 ? (
                      <RecommendItem
                        unionId={unionIdRef.current}
                        data={recommendData}
                      />
                    ) : (
                      <StatusView
                        status={StatusViewType.loading}
                        style={{ marginTop: '100px' }}
                      ></StatusView>
                    )}
                  </>
                )}
              </div>
            </div>
          </PullToRefresh>
        </div>
      ) : authDialog == 2 ? (
        <DialogAuth visible={authDialog == 2} setVisible={setAuthDialog} />
      ) : (
        <LoadingPage visible={true} />
      )}
    </div>
  ) : null
}

export default PromotionContent
