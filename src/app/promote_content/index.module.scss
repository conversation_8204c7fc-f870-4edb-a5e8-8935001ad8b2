.item_box {
    position: relative;
}

.main_bg {
    z-index: -1;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    background-image: url('../../../public/images/profit/profit_bg.png');
    background-size: cover;
    background-size: 750px 100%;
}

.my_promotion_box {
    width: 750px;
    display: flex;
    justify-content: space-between;
}

.noEmypt {
    margin-top: 24px;
}

.btn_bg {
    padding: 24px 0 18px 0;
    width: 750px;
    height: 296px;
    box-sizing: border-box;
    background-color: #fff;
}

.content {
    width: 100%;
    padding: 0 24px 2px 24px;
    box-sizing: border-box;
    overflow-y: auto;

}

.btns_box {
    display: flex;
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow-x: scroll;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    padding: 0 24px;
    padding-bottom: 24px;
    overflow-x: auto;
}

.live_btns_box {
    border-top: 2px solid #eee;
    padding: 0 24px;
}

.btns_box::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Edge */
}

.btns_item {
    margin: 0 15px;
    flex-shrink: 0;
}

.promotion_btns {
    background-color: #f5f5f5f5;
}

.checked_btn {
    padding: 0 24px;
    height: 60px;
    background: #FFF0EC;
    border-radius: 4px;
    border: 2px solid #FE6D45;
    font-size: 28px;
    color: #FE6D45;
    display: flex;
    justify-content: center;
    align-items: center;
}

.unChecked_btn {
    padding: 0 24px;
    height: 60px;
    background: #FFFFFF;
    border-radius: 4px;
    font-size: 28px;
    color: #6E6E6E;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    border: 2px solid #ABABAB;

}

.my_pro_title {
    padding: 0 8px;
    height: 44px;
    border-radius: 4px;
    border: 2px solid #FE6D45;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 22px;
    color: #fe6d45;
}

.status_bg {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.btn_scroll {
    position: fixed;
    bottom: 24px;
    right: 24px;
    width: 86px;
    height: 86px;
    border-radius: 43px;
    background: #fff;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 10px 10px 15px rgba(0, 0, 0, 0.3);
}

.page_bg_box {
    width: 750px;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.page_bg {
    width: 240px;
    height: 240px;
}