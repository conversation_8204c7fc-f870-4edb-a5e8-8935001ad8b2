'use client'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import Image from 'next/image'
import icon_asc from '@/../public/images/profit/icon_asc.png'
import icon_desc from '@/../public/images/profit/icon_desc.png'
import icon_sort from '@/../public/images/profit/icon_sort.png'
import { useState } from 'react'
const SortBtn = ({ handleClickSort, type }) => {
  const [timeIcon, setTimeIcon] = useState(icon_sort)
  const [salesIcon, setSalesIcon] = useState(icon_sort)
  const arr = [
    { title: i18n.t('Profit.发布时间'), value: 'publish_time' },
    { title: i18n.t('Profit.销售件数'), value: 'sales_count' }
  ]
  const handleClickTimeBtn = (sort, icon) => {
    let sortIcon
    setSalesIcon(icon_sort)
    if (icon == icon_sort) {
      setTimeIcon(icon_desc)
      sortIcon = 'desc'
    } else if (icon == icon_asc) {
      setTimeIcon(icon_sort)
      sortIcon = ''
    } else {
      setTimeIcon(icon_asc)
      sortIcon = 'asc'
    }
    handleClickSort(sort, sortIcon)
  }
  const handleClickSaleBtn = (sort, icon) => {
    let sortIcon
    setTimeIcon(icon_sort)
    if (icon == icon_sort) {
      setSalesIcon(icon_desc)
      sortIcon = 'desc'
    } else if (icon == icon_desc) {
      setSalesIcon(icon_asc)
      sortIcon = 'asc'
    } else {
      setSalesIcon(icon_sort)
      sortIcon = ''
    }
    handleClickSort(sort, sortIcon)
  }
  return (
    <div className={styles.sort_boxs}>
      {type == '1' ? (
        <div className={styles.sort_btn_box}>
          <div
            className={styles.sort_btn}
            onClick={() => handleClickTimeBtn('publish_time', timeIcon)}
          >
            {i18n.t('Profit.发布时间')}

            <Image
              src={timeIcon}
              alt="earn_divider"
              unoptimized
              className={styles.icon_img}
            ></Image>
          </div>
        </div>
      ) : null}

      <div className={styles.sort_btn_box}>
        <div
          className={styles.sort_btn}
          onClick={() => handleClickSaleBtn('sales_count', salesIcon)}
        >
          {i18n.t('Profit.销售件数')}

          <Image
            src={salesIcon}
            alt="earn_divider"
            unoptimized
            className={styles.icon_img}
          ></Image>
        </div>
      </div>
    </div>
  )
}

export default SortBtn
