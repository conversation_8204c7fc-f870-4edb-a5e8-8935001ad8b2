.box {

    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 24px;
    padding: 24px;

}

.item_box {
    display: flex;
    justify-content: space-between;
    background-color: #fff;

}

.item_content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item_number {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    // flex-wrap: wrap;
    flex: 1;
}

.progress_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: normal;
    font-size: 22px;
    color: #303030;
}

.progress_content {
    // position: relative;
    width: 100%;
    // display: flex;
    align-items: center;
}

.value_boxs {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
}

.progress_show {
    position: absolute;
    top: 3px;
    left: 50%;
    transform: translateX(-50%);
    color: #000;
    font-weight: bold;
    font-size: 20px;
}

.value_text {
    font-weight: bold;
    font-size: 24px;
    color: #C5445D;
    margin-top: -5px;
    word-break: break-all;

}

.progress_value {
    min-width: 420px
}

.progress_word_1 {
    font-weight: normal;
    font-size: 22px;
    color: #FE2C55;

}

.progress_word_2 {
    width: 200px;
    font-weight: normal;
    font-size: 22px;
    color: #C5445D;
    word-break: break-all;
    word-wrap: break-word;
}

.value_word_2 {
    font-weight: normal;
    font-size: 22px;
    color: #C5445D;
}

.item_sales {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-weight: normal;
    font-size: 22px;
    color: #303030;
    line-height: 32px;

}

.item_sales_value {
    font-weight: bold;

}

.item_img {
    width: 160px !important;
    height: 200px !important;
    margin-right: 16px;
    border-radius: 8px;
    position: relative;
    z-index: 1;

}

.item_live_img {
    width: 200px !important;
    height: 200px !important;
    margin-right: 16px;
    border-radius: 8px;
    position: relative;
    z-index: 1;
}

.item_title_box {
    display: flex;
    flex-direction: column;
}

.icon_play_img {
    width: 64px !important;
    height: 64px !important;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -32px;
    margin-top: -32px;
    z-index: 2;
}

.item_title {
    // flex: 1;
    box-sizing: border-box;
    font-weight: normal;
    font-size: 28px;
    color: #303030;
    margin-top: 8px;
    color: #333;
    height: 38px;
    // line-height: 42px;
    /* 隐藏溢出部分的文本 */
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;

}

.item_time {
    font-weight: normal;
    font-size: 24px;
    color: #8A8A8A;
    margin-top: 8px;

}

.item_tip1 {
    font-weight: normal;
    font-size: 22px;
    color: #FE2C55;
    line-height: 32px;

}

.item_tip2 {
    font-weight: normal;
    font-size: 22px;
    color: #FE2C55;
    line-height: 32px;

}

.item_tip3 {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 22px;
    color: #FE6D45;
    line-height: 32px;
    margin-top: 54px;


}

.video_img {
    width: 32px;
    height: 24px;
    margin-right: 8px;
}

.earn_img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.right_arrow_img {
    width: 14px;
    height: 20px;
    margin-left: 8px;
}

.item_bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 28px;
}

.item_bottom_title1 {
    width: 400px;
    background: #EBFFFF;
    border-radius: 4px;
    border: 2px solid #85E2E1;
    padding: 6px 16px;
    font-size: 22px;
    color: #009E9D;
    margin-bottom: 10px;
    box-sizing: border-box;

}

.item_bottom_word {
    white-space: wrap;
    width: 400px;
    // overflow: hidden;
    // text-overflow: ellipsis;
}

.item_bottom_title2 {

    background: #FEEBFF;
    border-radius: 4px;
    border: 2px solid #E5A4E9;
    padding: 6px 16px;
    font-size: 22px;
    color: #94009E;
    box-sizing: border-box;
}

.item_bottom_title3 {

    background: #FFF9F0;
    border-radius: 4px;
    border: 2px solid #E1C89B;
    padding: 6px 16px;
    font-size: 22px;
    color: #915C00;
    box-sizing: border-box;
}

.item_bottom_title_com {
    display: flex;
    align-items: center;
}


.mypromotion_words {
    // display: flex;
}

.item_bottom_btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 8px;
}

.item_bottom_text {
    font-size: 22px;
    color: #FE6D45;

}