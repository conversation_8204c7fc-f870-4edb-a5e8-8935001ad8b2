import React from 'react'
import rank_1 from '@/../public/images/promotion/rank_1.png'
import rank_2 from '@/../public/images/promotion/rank_2.png'
import rank_3 from '@/../public/images/promotion/rank_3.png'
import rank_4 from '@/../public/images/promotion/rank_3.png'
import Image from 'next/image'
import { ChildrenProps } from '@/lib/types/common'
import styles from './index.module.scss'
type Props = {
  index: number
}

export const RankNum = ({ index }: Props) => {
  const rank_icon = () => {
    let icon = rank_1
    switch (index) {
      case 0:
        icon = rank_1
        break
      case 1:
        icon = rank_2
        break
      case 2:
        icon = rank_3
        break
      case 3:
        icon = rank_4
        break
    }
    return icon
  }
  return (
    <div className={styles.sort_num}>
      {index < 3 ? (
        <Image src={rank_icon()} className="size-[40px]" alt=""></Image>
      ) : (
        <span className="w-[46px] text-center text-[30px] font-bold text-[#83726A]">
          {index + 1}
        </span>
      )}
    </div>
  )
}

export const ItemRank = ({ index, children }: Props & ChildrenProps) => {
  return (
    <div className={styles.item_box}>
      <RankNum index={index}></RankNum>
      {children}
    </div>
  )
}
