'use client'
import React, { useEffect, useState, useRef } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { format } from 'path'
import { formatPrice, formatSales } from '@/lib/format'

const ItemBottom = ({ item, contentType }) => {
  const bottomArr = [
    { label: i18n.t('Profit.GMV'), value: formatSales(item.gmv, '', true) },
    ...(contentType == '3'
      ? [
          {
            label: i18n.t('TiktokData.销量'),
            value: formatSales(item.salesCount, '', true)
          }
        ]
      : []),
    {
      label: i18n.t('Profit.佣金收入'),
      value: formatPrice(item.commission, true)
    },
    {
      label: i18n.t('Profit.播放量'),
      value: formatSales(item.viewCount, '0')
    }
  ]

  return (
    <div className={styles.item_bottom_box}>
      {bottomArr.map(it => {
        return (
          <div key={it.label} className={styles.item_bottom}>
            <div className={styles.item_value}>{it.value}</div>

            <div className={styles.item_label}>{it.label}</div>
          </div>
        )
      })}
    </div>
  )
}
export default ItemBottom
