'use client'
import React, { useEffect, useState, useRef } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import ImageWithPreload from '@/app/components/ImageWithPreload'
import icon_play_btn from '@/../public/images/promotion/icon_play_btn.png'
import { formatPrice, formatSales, formatTime } from '@/lib/format'
import { useRouter } from '@/lib/hooks/useRouter'
import { statistic } from '@/lib/statistic'
import InsertColoredText from '@/components/InsertColoredText'
import { showcaseInfo } from '@/app/api/api-mall/tt/item/categoryInfo'
import { ProgressBar } from 'antd-mobile'
import ItemBottom from './components/itemBottom'
import { EventName } from '@/lib/statistic/const'
import { loading } from '@/lib/client/loading'
const Item = ({ item, tabIndex, type, lastDay, unionId }) => {
  const icon_video = `/images/promotion/icon_video.png`
  const icon_earn_1 = `/images/promotion/icon_earn_1.png`
  const icon_earn_2 = `/images/promotion/icon_earn_2.png`
  const icon_earn_3 = `/images/promotion/icon_earn_3.png`
  const icon_right_arrow = `/images/promotion/icon_right_arrow.png`
  const router = useRouter()
  const tipsArr = [
    item.promotionType == 1
      ? i18n.t('Promotion.优质内容参考')
      : i18n.t('Promotion.查看商品详情'),
    i18n.t('Promotion.查看商品详情'),
    i18n.t('Promotion.再次推广教程')
  ]
  const saleArr1 = [
    {
      title: i18n.t('Promotion.sale_num'),
      value: formatSales(item.salesCount),
      id: '1',
      percent: ((item.salesCount / item.qualitySalesCount) * 100).toFixed(2)
    },
    {
      title: i18n.t('Promotion.high_quantity_sales'),
      value: formatSales(item.qualitySalesCount),
      id: '2'
    }
  ]
  const saleArr2 = [
    {
      title: i18n.t('Promotion.sale_num'),
      value: formatSales(item.salesCount),
      percent: ((item.salesCount / item.basicSalesCount) * 100).toFixed(2),
      id: '1'
    },
    {
      title: i18n.t('Promotion.基础冲刺销量'),
      value: formatSales(item.basicSalesCount),
      id: '2'
    }
  ]
  const saleArr3 = [
    {
      title: i18n.t('Promotion.playVolume'),
      value: formatSales(item.viewCount)
    },

    {
      title: i18n.t('Promotion.numberOfLikes'),
      value: formatSales(item.likeCount),
      id: '1'
    },
    {
      title: i18n.t('Promotion.sale_num'),
      value: formatSales(item.qualitySalesCount),
      id: '2'
    }
  ]
  const saleArr3Live = [
    {
      title: i18n.t('Promotion.sale_num'),
      value: formatSales(item.qualitySalesCount),
      id: '1'
    },
    {
      title: 'gmv',
      value: formatPrice(item.gmv, true),
      id: '2'
    }
  ]
  const handleClickJump = item => {
    statistic({
      eventName: EventName.promotion_center_replace_sales,
      param: {
        unionId: unionId,
        type: type,
        lastDay: lastDay,
        salesCount: item.salesCount,
        clickName:
          item.promotionType == 1
            ? i18n.t('Promotion.建议视频再次推广')
            : i18n.t('Promotion.建议再次直播推广'),
        routeName:
          item.contentType == 1 && item.promotionType == 1
            ? '推广详情页'
            : item.contentType == 3
            ? '推广帮助页'
            : '详情页'
      }
    })
    if (item.contentType == 1 && item.promotionType == 1) {
      router.push(
        `/promotion_details?id=${item.id}&&productId=${item.productId}`
      )
    } else if (item.contentType == 3) {
      router.push(`/tiktok_promotion_help`)
    } else {
      loading.show()
      showcaseInfo({ productId: item.productId }).then(res => {
        console.log('res: ' + JSON.stringify(res))
        if (res.code == 200) {
          router.push(`/product/${res?.result?.id}`)
        }
        loading.hide()
      })
    }
  }

  const progressCompotent = (arr: any) => {
    return (
      <div className={styles.progress_box}>
        <div className={styles.progress_top}>
          <div
            className={styles.progress_content}
            style={{ width: item.promotionType == 1 ? '220px' : '' }}
          >
            <ProgressBar
              className={styles.progress_value}
              percent={Number(arr[0].percent)}
              // text={<span className={styles.value_text}>{arr[1].value}</span>}
              style={{
                '--track-width': '12px',
                '--text-width': '1px',
                '--fill-color':
                  'linear-gradient( 90deg, #FE2C55 0%, #FE6D45 100%)',
                '--track-color': 'rgba(254,109,69,0.2)'
              }}
            />
            <div className={styles.value_boxs}>
              <div className={styles.progress_word_1}>
                {Number(arr[0].value)}
              </div>
              <div className={styles.value_word_2}>{arr[1].value}</div>
            </div>
            {/* <div className={styles.progress_show}>{Number(arr[0].value)}</div> */}
          </div>
        </div>
      </div>
    )
  }
  return (
    <div className={styles.box}>
      <div
        className={styles.item_box}
        onClick={() => {
          if (tabIndex == 0) {
            // statistic({
            //   eventName: EventName.click_promote_play,
            //   param: {
            //     videoId: item.videoId
            //   }
            // })
          } else {
            // statistic({
            //   eventName: EventName.click_my_promote_video,
            //   param: {
            //     videoId: item.videoId
            //   }
            // })
          }
        }}
      >
        <div
          className={
            item.promotionType == 1 ? styles.item_img : styles.item_live_img
          }
          onClick={() => {
            item.sourceUrl &&
              router.push(
                `https://www.tiktok.com/player/v1/${item.videoId}?id=${item.videoId}`
              )
          }}
        >
          {item.promotionType == 1 ? (
            <ImageWithPreload
              width={160}
              height={200}
              src={icon_play_btn}
              className={styles.icon_play_img}
            />
          ) : null}

          <ImageWithPreload
            width={item.promotionType == 1 ? 160 : 200}
            height={200}
            src={item?.coverImg}
            className={
              item.promotionType == 1 ? styles.item_img : styles.item_live_img
            }
          />
        </div>
        <div className={styles.item_content}>
          <div className={styles.item_title_box}>
            <div className={styles.item_title}>
              {item.title ? item.title : i18n.t('Promotion.defaultTitle')}
            </div>
            {item.publishTime && (
              <div className={styles.item_time}>
                <span>{i18n.t('Promotion.releaseTime')}</span>{' '}
                <span>{formatTime(item.publishTime)}</span>
              </div>
            )}
            {/* {item.contentType == 1 && (
              <div className={styles.item_tip1}>
                <span>{i18n.t('Promotion.内容有待提升')}</span>{' '}
              </div>
            )} */}
            {/* {item.contentType == 2 && (
              <div className={styles.item_tip2}>
                <span>{i18n.t('Promotion.销售不佳')}</span>{' '}
              </div>
            )} */}
            {item.contentType == 3 && (
              <div className={styles.item_tip3}>
                <img src={icon_video} className={styles.video_img} />
                <span>
                  {item.promotionType == 1
                    ? i18n.t('Promotion.uChoice认证热销视频')
                    : i18n.t('Promotion.优质直播带货商品')}
                </span>{' '}
              </div>
            )}
          </div>

          <div className={styles.item_number}>
            {
              item.contentType == 1
                ? progressCompotent(saleArr1)
                : item.contentType == 2
                ? progressCompotent(saleArr2)
                : []
              //  item.promotionType == 1
              // ? saleArr3
              // : saleArr3Live
            }
          </div>
          <div className={styles.item_number}>
            {(item.contentType == 1
              ? saleArr1
              : item.contentType == 2
              ? saleArr2
              : []
            ).map(item => {
              return (
                <div key={item.value} className={styles.progress_box}>
                  <div className={styles.progress_bottom}>
                    <div
                      className={
                        item.id == '1'
                          ? styles.progress_word_1
                          : styles.progress_word_2
                      }
                    >
                      <span>{item.title}</span>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
      <ItemBottom item={item} contentType={item.contentType} />
      <div className={styles.item_bottom}>
        {item.contentType == 1 && (
          <div
            className={`${styles.item_bottom_title1} ${styles.item_bottom_title_com}`}
          >
            {' '}
            <img src={icon_earn_1} className={styles.earn_img} />
            <div className={styles.item_bottom_word}>
              {item.promotionType == 1
                ? i18n.t('Promotion.recommended_surpassing')
                : i18n.t('Promotion.建议重复利用样品')}
            </div>
          </div>
        )}
        {item.contentType == 2 && (
          <div
            className={`${styles.item_bottom_title2} ${styles.item_bottom_title_com}`}
          >
            {' '}
            <img src={icon_earn_2} className={styles.earn_img} />
            <span className={styles.item_bottom_word}>
              {' '}
              {item.promotionType == 1
                ? i18n.t('Promotion.recommended_re_create')
                : i18n.t('Promotion.建议重复利用样品')}
            </span>
          </div>
        )}
        {item.contentType == 3 && (
          <div
            className={`${styles.item_bottom_title3} ${styles.item_bottom_title_com}`}
          >
            {' '}
            <img src={icon_earn_3} className={styles.earn_img} />{' '}
            <InsertColoredText
              text={
                item.promotionType == 1
                  ? i18n.t('Promotion.建议视频再次推广')
                  : i18n.t('Promotion.建议再次直播推广')
              }
              className={styles.mypromotion_words}
            >
              <span className={styles.color_words}>
                {formatPrice(item.earn, true)}
              </span>
            </InsertColoredText>
          </div>
        )}
        <div className={styles.item_bottom_btn}>
          <div
            className={styles.item_bottom_text}
            onClick={() => handleClickJump(item)}
          >
            <span>{tipsArr[item.contentType - 1]}</span>
          </div>

          <img src={icon_right_arrow} className={styles.right_arrow_img} />
        </div>
      </div>
    </div>
  )
}

export default Item
