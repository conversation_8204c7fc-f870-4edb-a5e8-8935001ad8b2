import React, { useState } from 'react'
import { Mask } from 'antd-mobile'
import Image from 'next/image'
import styles from './index.module.scss'
import auth_bg from '@/../public/images/profit/auth_bg.png'
import auth_pointer from '@/../public/images/profit/auth_pointer.png'
import { i18n } from '@/lib/client/i18n'
import { WebviewEvents } from '@/lib/client/webview/events'
import { webview } from '@/lib/client/webview'
const DialogAuth = ({ visible = false, setVisible }) => {
  const authTiktok = async () => {
    // setVisible(false)
    await webview?.send(WebviewEvents.makeSureTikTokAuthed)
  }
  return (
    <>
      <Mask visible={visible} className={styles.box}>
        <div className={styles.overlayContent}>
          <Image src={auth_bg} className={styles.auth_bg_img} alt="" />

          <div className={styles.auth_content}>
            <div className={styles.auth_content_title}>
              {i18n.t('Profit.功能待解锁')}
            </div>
            <div className={styles.auth_content_tip}>
              {i18n.t('Profit.授权即可解锁该内容哦')}
            </div>
            <div
              className={styles.auth_content_auth_btn}
              onClick={() => authTiktok()}
            >
              <div className={styles.auth_btn_word}>
                {i18n.t('Profit.授权解锁该功能')}
              </div>
              <div className={styles.auth_btn_img}>
                {' '}
                <Image
                  src={auth_pointer}
                  className={styles.auth_btn_img}
                  alt=""
                />
              </div>
            </div>
          </div>
        </div>
      </Mask>
    </>
  )
}

export default DialogAuth
