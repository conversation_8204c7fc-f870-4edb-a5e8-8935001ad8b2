'use client'
import React, { useEffect, useRef, useState } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { formatPrice } from '@/lib/format'
import BtnTabs from '@/app/components/BtnTabs'
import Button from '@/app/components/Button'
import { Popover } from 'react-vant'
import type { PopoverInstance } from 'react-vant'
import { useMounted } from '@/lib/hooks/useMounted'
const Btns = ({
  btnIndex,
  myRecordsBtneTabs,
  onSelect,
  selectTitle,
  reIdRef,
  proRelatedCount
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [buttonWidths, setButtonWidths] = useState<number[]>([])
  const buttonRefs = useRef<(HTMLDivElement | null)[]>([])
  const icon_down = `/images/profit/icon_down_data.png`
  const popover = useRef<PopoverInstance>(null)
  const mounted = useMounted()
  const salesBtns = [
    {
      text: i18n.t('Promotion.近30天')
    },
    {
      text: i18n.t('Promotion.近7天')
    },
    {
      text: i18n.t('Promotion.近90天')
    },
    {
      text: i18n.t('Promotion.近半年')
    }
  ]
  const recordsBtns = [
    {
      title: `${i18n.t('Sample.视频')}
      (${proRelatedCount?.videoPromotionCount || 0})`,
      // value: 'commission',
      // buryKey: EventName.click_high_income,
      id: 1
    },
    {
      title: `${i18n.t('Sample.直播')}(${
        proRelatedCount?.livePromotionCount || 0
      })`,
      // value: 'video_create_time',
      // buryKey: EventName.click_recently_publish,
      id: 2
    }
  ]

  const scrollLeft = id => {
    if (containerRef.current) {
      if (id == 0) {
        containerRef.current.scrollTo({
          left: 0,
          behavior: 'smooth'
        })
        return
      } else if (id == 2 && id != reIdRef.current) {
        containerRef.current.scrollBy({
          left: buttonWidths[id] / 2,
          behavior: 'smooth'
        })
      }
    }
    reIdRef.current = id
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      const widths = buttonRefs.current.map(ref => ref?.offsetWidth || 0)
      setButtonWidths(widths)
    }, 0)

    return () => clearTimeout(timer)
  }, [recordsBtns])

  return mounted ? (
    <div className={styles.promotion_btns_box}>
      <div className={styles.promotion_btns} ref={containerRef}>
        {recordsBtns.map((item, index) => (
          <div key={index}>
            <BtnTabs
              ref={el => (buttonRefs.current[index] = el)}
              btnIndex={btnIndex}
              item={item}
              onClick={() => {
                scrollLeft(item.id)
                myRecordsBtneTabs(item)
              }}
              btnStyle={styles.btn_normal}
            ></BtnTabs>
          </div>
        ))}
      </div>
      <div className={styles.sale_pop_box}>
        <Popover
          placement="bottom-start"
          ref={popover}
          reference={
            <div className={styles.sale_btn}>
              <Button
                title={selectTitle}
                onClick={() => {}}
                className={styles.sale_btn_title}
              ></Button>
              <img src={icon_down} className={styles.icon_down} />
            </div>
          }
        >
          {salesBtns.map((item, index) => {
            return (
              <div
                className={styles.btn_text}
                key={index}
                onClick={() => {
                  popover.current?.hide(), onSelect(item)
                }}
              >
                {item.text}
              </div>
            )
          })}
        </Popover>
      </div>
    </div>
  ) : null
}

export default Btns
