'use client'
import React, { useEffect, useState, useRef } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import icon_back from '@/../public/images/promotion/icon-back.png'
import ImageWithPreload from '@/app/components/ImageWithPreload'
const Nav = () => {
  return (
    <div className={styles.nav}>
      <ImageWithPreload width={22} height={22} src={icon_back} />
      <div className={styles.title}>{i18n.t('Promotion.promotionShop')}</div>
      <div></div>
    </div>
  )
}

export default Nav
