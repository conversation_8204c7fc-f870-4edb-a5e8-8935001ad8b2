'use client'
import React, { useEffect, useState, useRef } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { ConfigProvider, Popup, Skeleton } from 'react-vant'
import { formatPrice, formatSales } from '@/lib/format'
import InsertColoredText from '@/components/InsertColoredText'
import ImageWithPreload from '@/app/components/ImageWithPreload'
import icon_sign from '@/../public/images/promotion/icon_sign.png'
import icon_close from '@/../public/images/promotion/icon_close_tip.png'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'

const ResaleData = (item, unionId) => {
  const productItem = item.item
  const [modalVisible, setModalVisible] = useState<boolean>(false)

  const modalArr = [
    {
      title: i18n.t('Profit.推广次数'),
      value: i18n.t('Profit.达人通过发布视频')
    },
    {
      title: i18n.t('Profit.推广佣金'),
      value: i18n.t('Profit.达人通过发布视频商品所获的佣金收入')
    },
    {
      title: i18n.t('Profit.推广销量'),
      value: i18n.t('Profit.播销售uChoice商品的销量')
    },
    {
      title: i18n.t('Profit.推广GMV'),
      value: i18n.t('Profit.不区分订单状态')
    }
  ]

  const bottomArr1 = [
    //推广次数
    {
      title: i18n.t('Profit.推广次数'),
      value: formatSales(productItem?.promotionCount, '', true) || '-'
    },
    //推广佣金
    {
      title: i18n.t('Profit.推广佣金'),
      value:
        productItem?.productPromotionEarned ||
        productItem?.productPromotionEarned == 0
          ? formatPrice(productItem?.productPromotionEarned, true)
          : '-'
    }
  ]
  const bottomArr2 = [
    //推广销量
    {
      title: i18n.t('Profit.推广销量'),
      value:
        productItem?.promotionSales || productItem?.promotionSales == 0
          ? formatSales(productItem?.promotionSales, '', true)
          : '-'
    },
    //推广GMV
    {
      title: i18n.t('Profit.推广GMV'),
      value:
        productItem?.promotionGmv || productItem?.promotionGmv == 0
          ? formatPrice(productItem?.promotionGmv, true)
          : '-'
    }
  ]

  useEffect(() => {}, [productItem])
  const handleCancel = () => {
    setModalVisible(false)
  }

  return (
    <div className={styles.boxs}>
      {productItem ? (
        <div className={styles.contents}>
          <div className={styles.title_box}>
            <div className={styles.title}>
              {i18n.t('Profit.promotion_data')}
            </div>
            <div
              onClick={() => {
                setModalVisible(true)
                statistic({
                  eventName: EventName.promotion_center_data_quota,
                  param: {
                    unionId: unionId
                  }
                })
              }}
            >
              <ImageWithPreload
                width={13}
                height={13}
                src={icon_sign}
                className={styles.icon_sign}
              />
            </div>
          </div>
          <div className={styles.data_bottom}>
            {bottomArr1.map((item, index) => {
              return (
                <div key={index} className={styles.item}>
                  <div className={styles.item}>
                    <div className={styles.item_value}>
                      <span>{item.value}</span>
                    </div>
                  </div>
                  <div className={styles.item_title}>
                    <span>{item.title}</span>
                  </div>
                </div>
              )
            })}
          </div>
          <div className={styles.data_bottom}>
            {bottomArr2.map((item, index) => {
              return (
                <div key={index} className={styles.item}>
                  <div className={styles.item}>
                    <div className={styles.item_value}>
                      <span>{item.value}</span>
                    </div>
                  </div>
                  <div className={styles.item_title}>
                    <span>{item.title}</span>
                  </div>
                </div>
              )
            })}
          </div>
          {productItem.promotionCommissionIncrease ? (
            <div className={styles.item_congraduation}>
              <div className={styles.congradulations}>
                {i18n.t('Promotion.Congradulations')}
              </div>
              <div className={styles.cong_text}>
                <InsertColoredText
                  text={i18n.t('Promotion.throughAddingCommission')}
                  className={styles.insert_title}
                >
                  <span className={styles.color_words}>
                    {' '}
                    {formatPrice(
                      productItem.promotionCommissionIncrease,
                      true
                    )}{' '}
                  </span>
                  <span className={styles.congradulations}>
                    {' '}
                    {i18n.t('Promotion.keepTip')}
                  </span>
                </InsertColoredText>
              </div>
            </div>
          ) : null}
        </div>
      ) : null}
      <Popup
        destroyOnClose={true}
        visible={modalVisible}
        style={{
          borderRadius: '8px 8px 0px 0px'
          // overflow: 'scroll'
        }}
        duration={0}
        position="bottom"
        onClose={handleCancel}
      >
        <div className={styles.modal_box}>
          <div className={styles.modal_content}>
            <div className={styles.modal_title_box}>
              <div></div>
              <div className={styles.modal_title}>
                {i18n.t('Promotion.关于指标')}
              </div>
              <div
                onClick={() => {
                  setModalVisible(false)
                }}
              >
                <ImageWithPreload
                  width={32}
                  height={32}
                  src={icon_close}
                  className={styles.icon_close}
                />
              </div>
            </div>

            <div className={styles.modal_item_box}>
              {modalArr.map(item => {
                return (
                  <div key={item.title} className={styles.modal_item}>
                    <div className={styles.modal_item_title}>{item.title}</div>
                    <div className={styles.modal_item_value}>{item.value}</div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </Popup>
    </div>
  )
}

export default ResaleData
