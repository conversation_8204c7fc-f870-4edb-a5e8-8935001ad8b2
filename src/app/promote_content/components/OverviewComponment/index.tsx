import ImageWithPreload from '@/app/components/ImageWithPreload'
import styles from './index.module.scss'
import { i18n } from '@/lib/client/i18n'
import { formatPrice, formatSales } from '@/lib/format'
import { getPromotionContentSuggestion } from '@/app/api/api-uchoice/tt/item/promotionInfo/request'
import { useEffect, useRef, useState } from 'react'
import { PromotionContentSuggestion } from '@/app/api/api-uchoice/tt/item/promotionInfo/dtos'
import InsertColoredText from '@/components/InsertColoredText'
import { isIOS, rpxToPx } from '@/lib/client/utils'

const OverviewComponment = ({ lastDay, type, unionId, proRelatedCount }) => {
  const [data, setData] = useState<PromotionContentSuggestion>()

  useEffect(() => {
    promotionContentSuggestion()
  }, [unionId, lastDay, type])
  const promotionContentSuggestion = () => {
    getPromotionContentSuggestion({
      lastDay: lastDay,
      type: type,
      unionId: unionId
    }).then(res => {
      if ((res.code = 200)) {
        setData(res.result)
      }
    })
  }
  return (
    <div className={styles.avatar_box}>
      <div className={styles.suggest_tip}>
        {data?.qualityContentCount ? (
          <InsertColoredText
            text={i18n.t('Promotion.suggestTips')}
            className={styles.suggest_tip_words}
          >
            <span className={styles.suggest_color_words}>
              {data?.qualityContentCount}
            </span>
            <span className={styles.suggest_color_words}>
              {data?.waitImproveCount}
            </span>
          </InsertColoredText>
        ) : null}
      </div>
    </div>
  )
}

export default OverviewComponment
