'use client'
import React, { useEffect, useState, useRef, use } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { useRouter } from '@/lib/hooks/useRouter'
import { getSampleRecommendList } from '@/app/api/api-uchoice/tt/item/promotionInfo/request'
import Image from 'next/image'
import icon_divider from '@/../public/images/similar/icon_divider.png'
import icon_youpik from '@/../public/images/profit/icon_youpik.png'
import icon_bottom_fire from '@/../public/images/profit/icon_bottom_fire.png'
import {
  formatPrice,
  formatSales,
  formatThous,
  mintoMaxPrice
} from '@/lib/format'
import { useMounted } from '@/lib/hooks/useMounted'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { loading } from '@/lib/client/loading'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
const RecommendItem = ({ unionId, data }) => {
  const mounted = useMounted()
  const router = useRouter()
  const tipsArr = [
    {
      title: i18n.t('Profit.近30天热销商品'),
      img: 'images/profit/icon_fire.png'
    },
    {
      title: i18n.t('Profit.申样通过率高'),
      img: 'images/profit/icon_right.png'
    }
  ]
  const toPdp = id => {
    statistic({
      eventName: EventName.promotion_center_apply_samply,
      param: {
        unionId: unionId,
        id: id
      }
    })
    router.push(`${window.location.origin}/product/${id}`)
  }
  return (
    mounted && (
      <>
        {data.length > 0 ? (
          <div className={styles.recommend_item_box}>
            <div className={styles.title_tips}>
              {i18n.t('Profit.暂无推广记录')}
            </div>
            <div className={styles.tips_introduce}>
              {tipsArr.map(item => {
                return (
                  <div key={item.title} className={styles.tips_item}>
                    <img src={item.img} className={styles.icon_img} />
                    <span>{item.title}</span>
                  </div>
                )
              })}
            </div>
            {data.map(item => {
              return (
                <div className={styles.recommend_item} key={item.id}>
                  <div className={styles.recommend_top}>
                    <img src={item.homeImgUrl} className={styles.home_img} />
                    <div className={styles.recommend_item_content}>
                      <div className={styles.recommend_item_name}>
                        {item.productName}
                      </div>
                      <div className={styles.recommend_item_high}>
                        <div className={styles.item_hight_left}>
                          <Image
                            src={icon_youpik}
                            alt="earn_divider"
                            className={styles.icon_youpik}
                          ></Image>
                          <div className={styles.item_hight_value}>
                            {i18n.t('Profit.youpikHighCom')}
                          </div>
                        </div>
                        <div className={styles.item_hight_right}>{`${Math.floor(
                          item.commissionRate * 100
                        )}%`}</div>
                      </div>
                      <div className={styles.price_container}>
                        <div className={styles.price}>
                          {item.minPrice == item.maxPrice
                            ? formatPrice(item.minPrice, true)
                            : mintoMaxPrice(item.minPrice, item.maxPrice)}
                        </div>
                      </div>
                      <div className={styles.earn_container}>
                        <div className={styles.earn_label}>
                          {i18n.t('Product.Earn')}
                        </div>
                        <Image
                          src={icon_divider}
                          alt="earn_divider"
                          className={styles.earn_divider}
                        ></Image>
                        <div className={styles.earn_value_container}>
                          <div className={styles.earn_value}>
                            {item.minEarn == item.maxEarn
                              ? formatPrice(item.maxEarn, true)
                              : mintoMaxPrice(item.minEarn, item.maxEarn)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.recommend_item_bottom}>
                    <div className={styles.recommend_item_sales}>
                      <Image
                        src={icon_bottom_fire}
                        alt="earn_divider"
                        className={styles.icon_bottom_fire}
                      ></Image>
                      <div className={styles.recommend_item_sold}>
                        {' '}
                        {i18n.t('Showcase.Sold/Month', {
                          sold: formatSales(item?.salesForLast30Days)
                        })}
                      </div>
                    </div>
                    <div
                      className={styles.recommend_item_apply}
                      onClick={() => toPdp(item.id)}
                    >
                      {i18n.t('Product.申请样品')}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        ) : (
          <StatusView
            status={StatusViewType.loading}
            style={{ marginTop: '100px' }}
          ></StatusView>
        )}
      </>
    )
  )
}

export default RecommendItem
