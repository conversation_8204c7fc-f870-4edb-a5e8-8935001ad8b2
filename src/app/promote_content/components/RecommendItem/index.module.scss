.recommend_item_box {
    margin-top: 24px;
}

.title_tips {
    font-weight: bold;
    font-size: 32px;
    color: #303030;

}

.icon_img {
    width: 32px;
    height: 32px;
    margin-right: 12px;
}

.tips_introduce {
    padding: 0 24px 20px 24px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 16px;
    border: 2px solid #FFFFFF;
    margin-top: 24px;
}

.tips_item {
    font-weight: normal;
    font-size: 24px;
    color: #303030;
    line-height: 34px;
    display: flex;
    margin-top: 20px;

}

.price_container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .price {
        font-size: 28px;
        color: #333333;
        line-height: 28px;
        font-weight: bold;
    }


}

.earn_container {
    display: inline-flex;

    align-items: center;
    background-color: #FE6D45;
    padding-left: 12px;
    border-radius: 2px;
    margin-top: 20px;

    .earn_label {
        font-size: 24px;
        color: #FFFFFF;
        line-height: 34px;
    }

    .earn_divider {
        width: 28px;
        height: 44px;
        display: inline;
    }

    .earn_value_container {
        height: 44px;
        background-color: #FFF0EC;
        display: inline-flex;
        align-items: center;
        padding-right: 16px;

        .earn_value {
            font-size: 26px;
            color: #FE6D45;
            line-height: 36px;
            font-weight: bold;
        }
    }
}

.home_img {
    width: 200px;
    height: 200px;
    border-radius: 4px;
    margin-right: 20px;
}

.recommend_item {
    padding: 24px;
    margin: 24px 0;
    background: #FFFFFF;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
}

.recommend_top {
    display: flex;
    justify-content: space-between;
    border-bottom: 2px solid #f5f5f5;
    padding-bottom: 28px;
}

.recommend_item_name {
    height: 40px;
    font-size: 28px;
    font-weight: 400;
    color: #303030;
    line-height: 42px;
    /* 隐藏溢出部分的文本 */
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.icon_youpik {
    width: 36px;
    height: 38px;
    background-color: #8A8A8A;
    // margin-top: 1px;
}

.item_hight_left {
    display: flex;
    align-items: center;
    font-weight: normal;
    font-size: 24px;
    line-height: 34px;
    color: #FFFFFF;
    border-radius: 16px 2px 2px 2px;
    box-sizing: border-box;

}

.item_hight_value {
    background: #F4393B;
    padding: 0 4px;

}

.recommend_item_high {
    display: flex;
    align-items: center;
    margin: 20px 0;

}

.item_hight_right {
    display: inline;
    padding: 4px 10px;
    font-weight: bold;
    font-size: 24px;
    line-height: 24px;
    color: #F4393B;
    box-sizing: border-box;
    border: 1px solid #F4393B;
}

.icon_bottom_fire {
    width: 28px;
    height: 28px;
    margin-right: 8px
}

.recommend_item_sold {
    font-weight: normal;
    font-size: 24px;
    color: #8A8A8A;
    line-height: 28px;

}

.recommend_item_apply {
    height: 76px;
    background: #FE6D45;
    border-radius: 4px;
    font-weight: 600;
    font-size: 24px;
    color: #FFFFFF;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 0 24px;

}

.recommend_item_sales {
    display: flex;
}

.recommend_item_bottom {
    padding-top: 24px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}