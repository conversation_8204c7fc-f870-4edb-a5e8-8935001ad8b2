'use client'
import React, { useEffect, useState, useRef } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import ImageWithPreload from '@/app/components/ImageWithPreload'
import { Skeleton } from 'react-vant'
import { rpxToPx } from '@/lib/client/utils'

const ProductIntro = ({ productItem, handleItem, cardIndex }) => {
  return (
    <div className={styles.boxs}>
      {productItem ? (
        <>
          <div
            className={styles.intro_boxs}
            onClick={() => handleItem(productItem)}
          >
            <div className={styles.intro_content}>
              <ImageWithPreload
                width={rpxToPx(104)}
                height={rpxToPx(104)}
                className={
                  cardIndex == productItem.unionId
                    ? styles.checked_home_img
                    : styles.home_img
                }
                src={productItem?.avatar || ''}
              />
              <div
                className={
                  cardIndex == productItem.unionId
                    ? styles.checked_title
                    : styles.title
                }
              >
                {productItem?.displayName}
              </div>
            </div>
          </div>
        </>
      ) : (
        <Skeleton avatar avatarSize={90}></Skeleton>
      )}
    </div>
  )
}

export default ProductIntro
