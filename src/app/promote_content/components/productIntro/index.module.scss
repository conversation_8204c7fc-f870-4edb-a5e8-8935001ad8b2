.intro_boxs {
    display: flex;
    align-items: flex-start;
    // justify-content: space-around;
    // margin-right: 24px;
}


.intro_content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // margin-top: 28px;
}

.home_img {
    width: 100px !important;
    height: 100px !important;
    border-radius: 50%;
    margin-bottom: 12px;
}

.checked_home_img {
    width: 100px !important;
    height: 100px !important;
    border-radius: 50%;
    border: 2px solid #FE6D45;
    margin-bottom: 12px;
}

.checked_title {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    color: #6e6e6e;
    line-height: 36px;
    font-size: 26px;
    color: #FE6D45;
    white-space: nowrap;
    display: inline;
}

.title {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    white-space: nowrap;
    color: #6e6e6e;
    font-size: 26px;
    display: inline;

}