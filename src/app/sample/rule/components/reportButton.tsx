'use client'
import React from 'react'
import { i18n } from '@/lib/client/i18n'
import { Image } from '@/components/Image'
import { inApp } from '@/lib/client/utils'
import flag from '@/../public/images/sample/rule/flag.png'
import arrow_right from '@/../public/images/sample/rule/arrow_right.png'
import { useMounted } from '@/lib/hooks/useMounted'
import { useRouteParams } from '@/lib/hooks/useRouteParams'

export const ReportButton = () => {
  const routeParams = useRouteParams<{
    tier?: string
    unionId?: string
    nickName?: string
    salesDataVoucher?: string
    minAmount?: string
    maxAmount?: string
    updateTime?: string
  }>()

  const mounted = useMounted()
  return inApp() && mounted ? (
    <div
      className="mt-[32px] flex h-[80px] w-full items-center justify-between rounded-[4px] bg-background px-[20px] touch-opacity"
      onClick={() => {}}
    >
      <div className="flex items-center">
        <Image src={flag} className="mr-[10px] size-[32px]"></Image>
        <div className="text-[26px] text-black">
          {i18n.t('Sample.Report level problem upload proof')}
        </div>
      </div>
      <Image src={arrow_right} className="mr-[10px] h-[24px] w-[16px]"></Image>
    </div>
  ) : null
}
