'use client'
import { SampleThresholdDto } from '@/app/api/api-uchoice/item/subsidy/getThresholdInfo/dtos'
import { getThresholdInfoV3 } from '@/app/api/api-uchoice/item/subsidy/getThresholdInfo/request'
import React, { useEffect, useState } from 'react'
import tier_arrow from '@/../public/images/sample/rule/tier_arrow.png'
import { i18n } from '@/lib/client/i18n'
import { Image } from '@/components/Image'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { formatCount } from '@/lib/format'

export const List = () => {
  const { tier } = useRouteParams<{ tier?: string }>()
  const [thresholds, setThresholds] = useState<SampleThresholdDto[]>([])

  useEffect(() => {
    getThresholdInfoV3().then(res => {
      if (res.code === 200) {
        setThresholds(res.result.thresholdList!)
      }
    })
  }, [])

  return thresholds.length > 0 ? (
    <div>
      {thresholds.map(threshold => (
        <div
          key={threshold.tier!}
          className="flex"
          style={{
            backgroundColor: tier == threshold.tier ? '#FE6D451A' : 'white'
          }}
        >
          <div className="flex h-[100px] w-[204px] items-center justify-center border-l-2 border-t-2 border-background">
            {tier == threshold.tier ? (
              <div className="flex items-center">
                <span className="text-[24px] text-black">
                  {i18n.t('Sample.当前等级')}
                </span>
                <div className="px-[8px]">
                  <Image src={tier_arrow} className="h-[16px] w-[28px]"></Image>
                </div>
                <span className="text-[24px] font-bold text-primary">
                  {threshold.tier}
                </span>
              </div>
            ) : (
              <span className="text-[24px] font-bold text-black">
                {threshold.tier}
              </span>
            )}
          </div>
          <div
            className="flex h-[100px] w-[272px] items-center justify-center border-l-2 border-t-2 border-background text-center font-bold"
            style={{
              color: tier == threshold.tier ? '#FE6D45' : '#303030'
            }}
          >
            {threshold.maxAmount == 0
              ? `${formatCount(threshold.minAmount)}${i18n.t('Sample.及以上')}`
              : `${formatCount(threshold.minAmount)}~${formatCount(threshold.maxAmount)}`}
          </div>
          <div
            className="flex h-[100px] w-[224px] items-center justify-center border-x-2 border-t-2 border-background font-bold"
            style={{
              color: tier == threshold.tier ? '#FE6D45' : '#303030'
            }}
          >
            {threshold.quantity}
          </div>
        </div>
      ))}
    </div>
  ) : null
}
