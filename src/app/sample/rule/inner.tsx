'use client'
import { i18n } from '@/lib/client/i18n'
import React from 'react'
import { List } from './components/list'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { isRegionTH } from '@/lib/utils'
import { webview } from '@/lib/client/webview'

export const Inner = () => {
  return (
    <TransparentNavPage
      title={i18n.t('Sample.样品申请规则')}
      transparent={false}
      hide={!webview}
      showContactUsButton={isRegionTH()}
    >
      <div className="px-[24px] pt-[28px]">
        <div className="mb-[20px] font-[32px] text-black">
          {i18n.t('Sample.免费样品额度')}
        </div>

        <div className="border-b-2 border-background">
          <div className="flex">
            <div className="flex h-[140px] w-[204px] items-center justify-center border-l-2 border-t-2 border-background">
              {i18n.t('Sample.序号')}
            </div>
            <div className="flex h-[140px] w-[272px] items-center justify-center border-l-2 border-t-2 border-background text-center">
              {i18n.t('Sample.30天成交件数')}
            </div>
            <div className="flex h-[140px] w-[224px] items-center justify-center border-x-2 border-t-2 border-background">
              {i18n.t('Sample.Free samples')}
            </div>
          </div>
          <List></List>
        </div>

        {/* <ReportButton></ReportButton> */}

        <div className="mb-[20px] mt-[32px] font-[32px] text-black">
          {i18n.t('Sample.升级免费样品额度')}
        </div>
        <div className="rounded-[4px] border-2 border-background px-[24px] py-[30px]">
          <div className="flex items-center pb-[18px]">
            <div className="size-[36px] rounded-[18px] border-2 border-primary text-center text-[24px] leading-[36px] text-primary">
              1
            </div>
            <span className="ml-[12px] text-[30px] text-black">
              {i18n.t('Sample.及时履约')}
            </span>
          </div>
          <div className="mb-[24px] text-[26px] text-black">
            {i18n.t(
              'Sample.需要您在拿到样品后及时履约，否则免费样品额度将不再被返还。'
            )}
          </div>
          <div className="flex items-center pb-[18px]">
            <div className="size-[36px] rounded-[18px] border-2 border-primary text-center text-[24px] leading-[36px] text-primary">
              2
            </div>
            <span className="ml-[12px] text-[30px] text-black">
              {i18n.t('Sample.提升成交件数')}
            </span>
          </div>
          <div className="mt-[18px] text-[26px] text-black">
            {i18n.t(
              'Sample.通过提升自身的内容销售能力，可以根据Youpik uChoice的样品规则，获得更多免费样品资格！'
            )}
          </div>
        </div>
      </div>
    </TransparentNavPage>
  )
}
