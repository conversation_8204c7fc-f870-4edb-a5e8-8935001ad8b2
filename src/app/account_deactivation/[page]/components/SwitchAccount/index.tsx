/*
 * @Author: <PERSON>
 * @Date: 2024-04-11 09:33:59
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-15 15:55:49
 * @Desc 切换账号
 */
import tipIcon from '@/../public/images/common/tip_icon.png'
import { webview } from '@/lib/client/webview'
import { useRouter } from '@/lib/hooks/useRouter'
import Image from 'next/image'
import './index.scss'
import { i18n } from '@/lib/client/i18n'

const SwitchAccount = ({ userName }: { userName?: string }) => {
  const router = useRouter()

  const onSwitchAccountClick = () => {
    router.push('/login')
  }

  return userName ? (
    <div
      className="switch-account"
      style={{ marginTop: webview ? '0.65rem' : '2rem' }}
    >
      <Image src={tipIcon} alt="" className="icon" />
      <span className="text">{i18n.t('Login.the_current_account')}</span>
      <span className="text account-text">@{userName}</span>
      <button className="switch-button" onClick={onSwitchAccountClick}>
        {i18n.t('Login.switch_acount')}
      </button>
    </div>
  ) : null
}

export default SwitchAccount
