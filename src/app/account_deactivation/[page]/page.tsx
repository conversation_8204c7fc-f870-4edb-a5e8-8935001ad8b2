'use client'
import revert_account_icon from '@/../public/images/common/revert_account_icon.png'
import { unRegisterMember } from '@/app/api/api-uchoice/account/unRegisterMember'
import { getUserInfo } from '@/app/api/api-uchoice/uChoice/account/current/request'
import MaskModal from '@/components/MaskModal'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { i18n } from '@/lib/client/i18n'
import { loading } from '@/lib/client/loading'
import { toast } from '@/lib/client/toast'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useMounted } from '@/lib/hooks/useMounted'
import { useRouter } from '@/lib/hooks/useRouter'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import SwitchAccount from './components/SwitchAccount'
import styles from './index.module.scss'

const AccountDeactivation = () => {
  const [modalVisible, setModalVisible] = useState(false)
  const [userName, setUserName] = useState('')
  const mounted = useMounted()
  const router = useRouter()

  useEffect(() => {
    // play商店进入才需要获取当前用户的信息
    !webview && onGetUserInfo()
  }, [])

  const handleCancel = () => {
    setModalVisible(false)
  }
  const handleConfirm = () => {
    setModalVisible(false)
    if (webview) {
      webview?.send(WebviewEvents.accountDeact)
    } else {
      onUnRegisterMember()
    }
  }
  const generateTips = () => {
    return [
      {
        title: i18n.t('Account.Accountwillnotbe'),
        desc: i18n.t('Account.TheuChoiceproaccountwillnot')
      },
      {
        title: i18n.t('Account.Theaccountassociation'),
        desc: i18n.t('Account.TheuChoiceproaccounthasbeendeauthorized')
      }
    ]
  }

  const handleDeactAccount = () => {
    setModalVisible(true)
  }

  const onGetUserInfo = async () => {
    loading.show()
    try {
      const data = await getUserInfo()
      loading.hide()
      if (data.code !== 200) return
      setUserName(data.result.fullName || '')
    } catch (error) {
      loading.hide()
    }
  }

  const onUnRegisterMember = async () => {
    try {
      loading.show()
      const data = await unRegisterMember()
      loading.hide()
      if (data.code === 200) {
        toast(i18n.t('Common.Logoutsuccessful'))
        setTimeout(() => {
          router.push('/login')
        }, 2000)
      }
    } catch (error) {
      loading.hide()
      console.log(error)
    }
  }

  return (
    mounted && (
      <TransparentNavPage
        title={i18n.t('Account.AccountDeactivation')}
        transparent={false}
        hide={!webview}
      >
        <div className={styles.account_box}>
          {/* play商店进入才需要展示用户信息和切换账号按钮 */}
          {!webview && <SwitchAccount userName={userName} />}
          <div className={styles.deact_content}>
            <Image
              src={revert_account_icon}
              alt="icon"
              className={styles.revert_account_icon}
            />
            <div className={styles.deact_title}>
              {i18n.t('Account.ApplytodeactivateyouruChoiceproaccount')}
            </div>
          </div>
          <div className={styles.deact_desc}>
            <div className={styles.desc_title}>
              {i18n.t('Account.Beforesubmittingtheaccount')}
            </div>
            {generateTips().map((it, index) => (
              <div key={index}>
                <div className={styles.tips_wrap}>
                  <div className={styles.number_wrap}>
                    <span className={styles.number_index}>{index + 1}</span>
                  </div>
                  <div className={styles.tips_desc}>
                    <div className={styles.tips_title}>{it.title}</div>
                    <div className={styles.desc}>{it.desc}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className={styles.deac_btns}>
            <div className={styles.btn_tips}>
              {i18n.t('Account.ByclickingtheDeactivateAccount')}
            </div>
            <div className={styles.btn} onClick={handleDeactAccount}>
              {i18n.t('Account.DeactivateAccount')}
            </div>
          </div>
          <MaskModal
            title={i18n.t('Account.Areyousureyouwanttocanceltheaccount')}
            modalVisible={modalVisible}
            handleCancel={handleCancel}
            handleConfirm={handleConfirm}
          ></MaskModal>
        </div>
      </TransparentNavPage>
    )
  )
}

export default AccountDeactivation
