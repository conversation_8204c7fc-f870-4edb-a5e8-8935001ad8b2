'use client'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import React, { useEffect, useMemo, useState } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import { isIOS, rpxToPx } from '@/lib/client/utils'
import styles from './index.module.scss'
import { getCampaignRankings } from '@/app/api/api-uchoice/campaignRewards/getCampaignRankings'
import { requestCampaignDetail } from '@/app/api/api-uchoice/fmcg/campaigns/detail'
import RichTextComponent from '@/components/ReactHtmlParse'
import { getOldH5Url, isRegionVN } from '@/lib/utils'
import { useRouter } from '@/lib/hooks/useRouter'
import { ImagePreview } from 'react-vant/lib'

const rulesNum = (key, title) => {
  return (
    <div
      className={styles.rules_activity_detail}
      style={{ paddingTop: '24px' }}
    >
      <div className={styles.rules_activity_shape}>
        <span className={styles.rules_activity_num}>{key}</span>
      </div>
      <div className={styles.rules_activity_text}>{title}</div>
    </div>
  )
}
const rulesContent = (arr, it, router) => {
  return (
    <div className={styles.attention}>
      {arr.map((item, index) => {
        let selectionId =
          it && it.itemSetIds && it.itemSetIds.length > 0 ? it.itemSetIds[0] : null
        return (
          <div key={index} className={styles.attention_item}>
            <div className={styles.attention_num}>{item.key}</div>
            <div className={styles.attention_item_box}>
              <span className={styles.attention_title}>{item.title}</span>
              <span
                className={styles.attention_click_word}
                onClick={() => {
                  if (it && item.key == '1') {
                    router.push(
                      `${getOldH5Url()}/pages/tiktok/selection?id=${selectionId}`
                    )
                  } else if (it && item.key == '3') {
                    router.push('/product/copyLinkGuide/page')
                  }
                }}
              >
                {item.clickWord}
              </span>
            </div>
          </div>
        )
      })}
    </div>
  )
}

const attentionArr_normal = [
  {
    key: 1,
    title: i18n.t('Activity.活动GMV'),
    clickWord: i18n.t('Activity.showActivityShop')
  },
  {
    key: 2,
    title: i18n.t('Activity.计算完成状态'),
    clickWord: ''
  },
  {
    key: 3,
    title: i18n.t('Activity.TikTok达人'),
    clickWord: i18n.t('Activity.点击查看教程')
  }
]

const attentionArr_video = [
  {
    key: 1,
    title: i18n.t('Activity.活动GMV_video'),
    clickWord: i18n.t('Activity.showActivityShop')
  },
  {
    key: 2,
    title: i18n.t('Activity.计算完成状态_video'),
    clickWord: ''
  },
  {
    key: 3,
    title: i18n.t('Activity.TikTok达人_video'),
    clickWord: i18n.t('Activity.点击查看教程')
  }
]

const attentionArr_order = [
  {
    key: 1,
    title: i18n.t('Activity.活动GMV_order'),
    clickWord: i18n.t('Activity.showActivityShop')
  },
  {
    key: 2,
    title: i18n.t('Activity.计算完成状态_order'),
    clickWord: ''
  },
  {
    key: 3,
    title: i18n.t('Activity.TikTok达人_order'),
    clickWord: i18n.t('Activity.点击查看教程')
  }
]

const otherArr = isIOS()
  ? [
    {
      key: 1,
      title: isRegionVN() ? i18n.t('Activity.otherTips1_vn') : i18n.t('Activity.otherTips1'),
      clickWord: ''
    },
    {
      key: 2,
      title: i18n.t('Activity.otherTips2'),
      clickWord: ''
    },
    {
      key: 3,
      title: i18n.t('Activity.活动与苹果无关'),
      clickWord: ''
    }
  ]
  : [
    {
      key: 1,
      title: isRegionVN() ? i18n.t('Activity.otherTips1_vn') : i18n.t('Activity.otherTips1'),
      clickWord: ''
    },
    {
      key: 2,
      title: i18n.t('Activity.otherTips2'),
      clickWord: ''
    }
  ]

interface Props {
  params: any
  searchParams: {
    type: number
    campaignId: string
  }
}

const ActivityRules = ({
  params,
  searchParams: { type, campaignId }
}: Props) => {
  const mounted = useMounted()
  const router = useRouter()

  const attentionArr = useMemo(() => {
    if (type == 4) {
      return attentionArr_video
    } else if (type == 5) {
      return attentionArr_order
    } else {
      return attentionArr_normal
    }
  }, [type])

  const [item, setItem] = useState<any>(null)
  useEffect(() => {
    CampaignRankings()
  }, [])
  const CampaignRankings = () => {
    requestCampaignDetail(campaignId).then(res => {
      if (res.code == 200 && res.result) {
        setItem(res.result)
      }
    })
  }

  // 预览图片
  useEffect(() => {
    const listener = e => {
      console.log('click', e.target)
      if (e.target.tagName === 'IMG') {
        ImagePreview.open({
          images: [e.target.src],
          closeable: true
        })
      } else if (
        e.target.childElementCount === 1 &&
        e.target.lastChild.tagName === 'IMG'
      ) {
        ImagePreview.open({
          images: [e.target.lastChild.src],
          closeable: true
        })
      }
    }

    document.addEventListener('click', listener)

    return () => {
      document.removeEventListener('click', listener)
    }
  }, [])

  return (
    mounted && (
      <TransparentNavPage
        title={i18n.t('Activity.赢iPhone活动规则')}
        transparent={false}
        hide={!webview}
      >
        <div
          className={styles.rules_boxs}
          style={{ paddingTop: !webview ? rpxToPx(80) : 0 }}
        >
          <div className={styles.rules_activity_detail}>
            <div className={styles.rules_activity_shape}>
              <span className={styles.rules_activity_num}>01</span>
            </div>
            <div className={styles.rules_activity_text}>
              {i18n.t('Activity.活动详情')}
            </div>
          </div>
          <div className={styles.rules_decri}>
            {item && item.bannerUrl && (
              <img src={item.bannerUrl} className={styles.banner} />
            )}
          </div>

          {rulesNum('02', i18n.t('Activity.注意事项'))}
          {rulesContent(attentionArr, item, router)}
          {/* 需求不要了 */}
          {/* {rulesNum('03', i18n.t('Activity.奖品分发'))}
          <div
            className={styles.rules_distribute_detail}
            style={{ paddingTop: '24px' }}
          >
            <div className={styles.distribute_time}>
              <span>{i18n.t('Activity.分发时间')}: </span>
              <span>xxxx</span>
            </div>
            <div
              className={styles.distribute_tyle}
              onClick={() => {
                copyText('789897')
              }}
            >
              <span>{i18n.t('Activity.分发方式')}:&nbsp; </span>
              <span>{i18n.t('Activity.请添加LINE')}</span>

              <Image src={copy} alt="icon" className={styles.icon_copy} />
            </div>
          </div> */}
          {rulesNum('03', i18n.t('Activity.其他说明'))}
          {rulesContent(otherArr, null, router)}
        </div>
      </TransparentNavPage>
    )
  )
}

export default ActivityRules
