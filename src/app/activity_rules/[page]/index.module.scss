.rules_boxs {
   
    min-height: 100vh;
    background: #F5F5F5;
    overflow: scroll;
    padding-bottom: 48px;
}

.rules_activity_detail {
    padding-top: 16px;
    height: 118px;
    display: flex;
    align-items: center;
}

.rules_activity_shape {
    width: 105px;
    height: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #FE6D45;
    border-radius: 0px 33px 33px 0px;
    transform: rotate(-20deg);
    margin-left: -20px;
}

.rules_activity_num {
    font-size: 40px;
    font-weight: bold;
    color: #FFFFFF;
    line-height: 48px;
}

.rules_activity_text {
    font-size: 36px;
    font-weight: bold;
    color: #303030;
    line-height: 50px;
}

.rules_decri {
    margin: 0 24px;
    background: #FFFFFF;
    border-radius: 4px;
    padding: 28px 24px;

}
.rules_decri_box{
    margin-bottom: 32px;

}
.rules_decri_title {
    font-size: 30px;
    font-weight: normal;
    color: #FF4F19;
    line-height: 42px;
}
.rules_decri_time{
font-size: 26px;
font-weight: normal;
color: #6E6E6E;
line-height: 36px;
}
.attention{
background: #FFFFFF;
border-radius: 4px;
padding: 28px 24px 8px 24px;
margin:24px 24px 0 24px;

}
.attention_item{
    display: flex;
    margin-bottom: 32px;
    
}
.attention_num{
width: 36px;
height: 36px;
background: #FE6D45;
border-radius: 50%;
font-size: 28px;
font-weight: normal;
color: #FFFFFF;
line-height: 40px;
text-align: center;
margin-right:12px ;
}
.attention_item_box{
    flex: 1;
}
.attention_title{
font-size: 30px;
font-weight: normal;
color: #303030;
line-height: 42px;
}
.attention_click_word{
    font-size: 30px;
font-weight: normal;
color:#478BFF;
line-height: 42px;
}
.icon_copy{
    width: 32px;
    height:32px;
    margin-left: 12px;
}
.rules_distribute_detail{
    padding: 28px 24px 34px 24px;
    margin: 0 24px;
    align-items: center;
    background: #fff;
    margin-top: 24px;

}
.distribute_time{
font-size: 30px;
font-weight: normal;
color: #303030;
line-height: 42px;
margin-bottom: 32px;
}
.distribute_tyle{
    display: flex;
font-size: 30px;
font-weight: normal;
color: #303030;
line-height: 42px;

}