'use client'
import guide_sample_process_step1_vn from '@/../public/images/product/detail/guide_sample_process_step1_vn.gif'
import guide_sample_process_step2_vn from '@/../public/images/product/detail/guide_sample_process_step2_vn.gif'
import guide_sample_process_step3_vn from '@/../public/images/product/detail/guide_sample_process_step3_vn.gif'
import guide_sample_process_step4_vn from '@/../public/images/product/detail/guide_sample_process_step4_vn.gif'
import guide_sample_process_step1_th from '@/../public/images/product/detail/guide_sample_process_step1_th.gif'
import guide_sample_process_step2_th from '@/../public/images/product/detail/guide_sample_process_step2_th.gif'
import guide_sample_process_step3_th from '@/../public/images/product/detail/guide_sample_process_step3_th.gif'
import guide_sample_process_step4_th from '@/../public/images/product/detail/guide_sample_process_step4_th.gif'
import { Image } from '@/components/Image'
import { i18n } from '@/lib/client/i18n'
import { useMounted } from '@/lib/hooks/useMounted'
import { useState } from 'react'
import { isRegionVN } from '@/lib/utils'

export const Inner = () => {
  const mounted = useMounted()
  const [currentStep, setCurrentStep] = useState(1)
  const totalSteps = 4

  const gifImages = [
    isRegionVN()
      ? guide_sample_process_step1_vn
      : guide_sample_process_step1_th,
    isRegionVN()
      ? guide_sample_process_step2_vn
      : guide_sample_process_step2_th,
    isRegionVN()
      ? guide_sample_process_step3_vn
      : guide_sample_process_step3_th,
    isRegionVN() ? guide_sample_process_step4_vn : guide_sample_process_step4_th
  ]

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrev = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  // 按钮宽度计算
  const singleButtonWidth = 700 // 单个按钮宽度
  const buttonGap = 24 // 两个按钮之间的间距
  const doubleButtonWidth = (singleButtonWidth - buttonGap) / 2 // 两个按钮并排时每个按钮的宽度

  return mounted ? (
    <div className="mt-[24px] flex flex-col px-[32px]">
      {/* 标题 */}
      <div className="mb-[24px] text-left text-[28px] font-medium text-black30">
        {currentStep}.{currentStep === 1 && i18n.t('Product.View more details')}
        {currentStep === 2 &&
          i18n.t('Product.Scroll up to see more information')}
        {currentStep === 3 && i18n.t('Product.Click "Get free sample"')}
        {currentStep === 4 &&
          `${i18n.t('Product.Aotu add showcase &')} ${i18n.t(
            'Product.Go back to uChoice'
          )}`}
      </div>

      {/* GIF 图片展示 */}
      <div className="mb-[48px] flex justify-center">
        <Image
          src={gifImages[currentStep - 1]}
          alt={`Step ${currentStep}`}
          className="max-w-full"
        />
      </div>

      {/* 按钮区域 */}
      <div className="flex justify-center space-x-[24px]">
        {currentStep > 1 && currentStep < totalSteps ? (
          <>
            <button
              onClick={handlePrev}
              className="h-[80px] rounded-[4px] border-[2px] border-grayCC bg-white text-[24px] font-medium text-black30"
              style={{ width: `${doubleButtonWidth}px` }}
            >
              {i18n.t('Common.Previous')}
            </button>
            <button
              onClick={handleNext}
              className="h-[80px] rounded-[4px] bg-[#FE6D45] text-[24px] font-medium text-white"
              style={{ width: `${doubleButtonWidth}px` }}
            >
              {i18n.t('Common.Next')}
            </button>
          </>
        ) : currentStep === 1 ? (
          <button
            onClick={handleNext}
            className="h-[80px] w-[700px] rounded-[4px] bg-[#FE6D45] text-[24px] font-medium text-white"
          >
            {i18n.t('Common.Next')}
          </button>
        ) : (
          <button
            onClick={handlePrev}
            className="h-[80px] w-[700px] rounded-[4px] border-[2px] border-grayCC bg-white text-[24px] font-medium text-black30"
          >
            {i18n.t('Common.Previous')}
          </button>
        )}
      </div>
    </div>
  ) : null
}
