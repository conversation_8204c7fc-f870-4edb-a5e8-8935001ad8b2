'use client'

import { isRegionTH } from '@/lib/utils'
import { ImageSwiperCell } from '../components/imageSwiperCell'
import { GetSamplePopTypeVoType } from '@/app/api/api-uchoice/order/user/getSamplePopTypeVo/dtos'
import { BottomBarType } from '../components/BottomBar/type'
import { StickyTabs } from '../components/StickyTabs'
import { TabSection } from '@/app/components/Tabs/TabSection'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import Score from '@/app/components/Score'
import VideosCell from '../components/VideosCell'
import GetSampleRequest from '../components/GetSampleRequest'
import UserLevelTips from '../components/UserLevelTips'
import ShopCell from '../components/ShopCell/index'
import RankCell from '../components/RankCell/index'
import NameCell from '../components/NameCell'
import BuySampleCell from '../components/BuySampleCell'
import FreeSampleCell from '../components/FreeSampleCell/index'
import CommissionCell from '../components/commissionCell'
import { useMounted } from '@/lib/hooks/useMounted'
import NewUserCell from '../components/NewUserCell/index'
import Notice from '../components/Notice/index'
import BottomBar from '../components/BottomBar/index'
import RewardsCell from '../components/rewardsCell'
import VariationsCell from '../components/variationsCell'
import DescriptionCell from '../components/descriptionCell'
import HotProductsCell from '../components/hotProductsCell'
import { useLogined } from '@/lib/hooks/useLogined'
import dynamic from 'next/dynamic'
import { useEffect, useState } from 'react'
import { info } from '@/app/api/api-uchoice/tt/item/info/request'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { loading } from '@/lib/client/loading'
import NewFreeSampleCell from '../components/NewFreeSampleCell'

// VideoPreviewer的问题
const MaterialCell = dynamic(() => import('../components/MaterialCell'))

interface Props {
  id: string
}

export function Inner({ id }: Props) {
  const [itemInfo, setItemInfo] = useState<ItemInfoDto>()
  const logined = useLogined()
  const mounted = useMounted()

  useEffect(() => {
    fetch()

    window.pdpRefetch = () => {
      loading.show()
      fetch()
    }
  }, [])

  const fetch = async () => {
    const { code, result } = await info({ id })
    loading.hide()
    if (code === 200) {
      setItemInfo(result)

      if (
        !!webview?.send(WebviewEvents.hasJavaScriptHandler, {
          handlerName: WebviewEvents.pdpLoaded
        })
      ) {
        webview?.send(WebviewEvents.pdpLoaded)
      }
    }
  }

  return itemInfo ? (
    <div className="bg-white">
      <ImageSwiperCell itemInfo={itemInfo}></ImageSwiperCell>
      {mounted && (
        <>
          <div className="h-[24px] w-full"></div>

          <UserLevelTips itemInfo={itemInfo}></UserLevelTips>
          {logined && <NewUserCell itemInfo={itemInfo}></NewUserCell>}
          <CommissionCell itemInfo={itemInfo}></CommissionCell>
          <NameCell itemInfo={itemInfo}></NameCell>

          <div className="flex items-center px-[24px] pt-[24px]">
            {itemInfo.shopScore !== 0 && (
              <Score score={itemInfo.shopScore!}></Score>
            )}
            <div className="flex-1 px-[16px]">
              <ShopCell itemInfo={itemInfo}></ShopCell>
            </div>
          </div>

          {itemInfo.tapRanking && (
            <RankCell
              type={itemInfo.tapRanking?.type!}
              categoryId={itemInfo.tapRanking?.customCategoryId}
              rank={itemInfo.tapRanking?.ranking || 0}
            ></RankCell>
          )}

          <Notice itemInfo={itemInfo}></Notice>

          <StickyTabs
            notShowFreeSample={!!(logined && itemInfo.sampleTotal === 0)}
            itemInfo={itemInfo}
          ></StickyTabs>

          <VideosCell itemInfo={itemInfo}></VideosCell>

          <TabSection></TabSection>

          <GetSampleRequest itemInfo={itemInfo}></GetSampleRequest>

          {logined && !itemInfo.isCanSample ? (
            <BuySampleCell></BuySampleCell>
          ) : itemInfo.isNewSample ? (
            <NewFreeSampleCell></NewFreeSampleCell>
          ) : (
            <FreeSampleCell></FreeSampleCell>
          )}

          {isRegionTH() && <RewardsCell itemInfo={itemInfo}></RewardsCell>}

          <MaterialCell itemInfo={itemInfo}></MaterialCell>

          <VariationsCell></VariationsCell>

          <TabSection></TabSection>
          <DescriptionCell itemInfo={itemInfo}></DescriptionCell>

          <HotProductsCell itemInfo={itemInfo}></HotProductsCell>

          <BottomBar
            item={itemInfo}
            type={
              logined
                ? !itemInfo.isCanSample
                  ? BottomBarType.pdpWithOutRequestSample
                  : BottomBarType.pdp
                : BottomBarType.pdp
            }
            samplePopTypeVoType={GetSamplePopTypeVoType.none}
          ></BottomBar>
        </>
      )}
    </div>
  ) : null
}
