import { metadataTemplate, inApp } from '@/lib/server/utils'
import { isLanguageEN, isRegionTH } from '@/lib/utils'
import { info } from '@/app/api/api-uchoice/tt/item/info/fetch'
import { i18nS } from '@/lib/server/i18n'
import { EventName } from '@/lib/statistic/const'
import { Inner } from './inner'

interface Props {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({
      title: i18nS.t('Product.title'),
      showNativeNavBar: true,
      showNativeNavBarShareButton: true,
      showNativeNavBarContactButton: isRegionTH(),
      nativeNavBarShareEvent: EventName.pdp_share
    })
  }

  const itemInfo = await info(params)
  if (itemInfo) {
    const description = isLanguageEN() ? itemInfo.nameEn : itemInfo.name
    const title = description
    const icon = itemInfo.homeImgUrl

    return metadataTemplate({ title, description, icon })
  } else {
    return metadataTemplate()
  }
}

export default async function Index({ params: { id } }: Props) {
  return <Inner id={id} />
}
