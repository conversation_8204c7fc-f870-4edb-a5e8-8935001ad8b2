'use client'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import React from 'react'

export const Nav = () => {
  return (
    <TransparentNavPage
      title={i18n.t('Product.title')}
      transparent={false}
      hide={!webview}
      showShareButton
      showContactUsButton
      withOutBottomSafeArea
      onShared={() => statistic({ eventName: EventName.pdp_share })}
    ></TransparentNavPage>
  )
}
