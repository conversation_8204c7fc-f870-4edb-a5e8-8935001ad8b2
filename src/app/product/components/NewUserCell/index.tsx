'use client'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { Image } from '@/components/Image'
import React, { useEffect, useState } from 'react'
import gift from '@/../public/images/product/detail/gift.png'
import expire_tag_pre from '@/../public/images/product/detail/expire_tag_pre.png'
import { TransComponent } from '@/app/components/Trans'
import apply_en from '@/../public/images/product/detail/apply_en.png'
import apply_th from '@/../public/images/product/detail/apply_th.png'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'

const NewUserCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  const [expireText, setExpireText] = useState('')

  useEffect(() => {
    handle()
  }, [])

  const handle = async () => {
    if (
      !!(await webview?.send(WebviewEvents.hasJavaScriptHandler, {
        handlerName: WebviewEvents.freeSampleFakeExpireAt
      }))
    ) {
      const text = await webview?.send(WebviewEvents.freeSampleFakeExpireAt)
      setExpireText(text)
    }
  }

  return expireText.length > 0 && itemInfo?.isShowSampleTip ? (
    <div
      className="px-[24px] pt-[24px]"
      onClick={async () => {
        if (webview) {
          await webview?.send(WebviewEvents.makeSureTikTokAuthed)
        }
        // @ts-ignore
        window?.showSkuModal()
      }}
    >
      <div className="flex min-h-[84px] w-full items-center justify-between rounded-[4px] bg-[#FFECE7] pl-[14px]">
        <div className="flex items-center">
          <Image src={gift} className="size-[44px]"></Image>
          <div className="ml-[14px] flex flex-1 flex-col">
            <span className="text-[26px] font-bold text-primary">
              {i18n.t('Product.Free Sample x1')}
            </span>
            <span className="text-[24px] text-primary">
              {i18n.t('Product.for new comers')}
            </span>
          </div>
          <div className="ml-[12px] flex items-center pr-[10px]">
            <Image src={expire_tag_pre} className="h-[36px] w-[12px]"></Image>
            <div className="flex h-[36px] items-center justify-center rounded-r-[4px] bg-primary pl-[2px] pr-[10px]">
              <span className="text-[24px] text-white">{expireText}</span>
            </div>
          </div>
        </div>
        <TransComponent
          en={<Image src={apply_en} className="h-[84px] w-[194px]"></Image>}
          th={<Image src={apply_th} className="h-[84px] w-[194px]"></Image>}
        ></TransComponent>
      </div>
    </div>
  ) : null
}

export default NewUserCell
