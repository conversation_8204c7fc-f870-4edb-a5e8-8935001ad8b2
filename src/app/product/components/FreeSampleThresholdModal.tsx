/* eslint-disable @next/next/no-img-element */
import { px2rem } from '@/lib/client/utils'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import React, { useEffect, useState } from 'react'
import { Popup } from 'antd-mobile'
import { Image } from '@/components/Image'
import tip_icon from '@/../public/images/common/tip_icon.png'
import wrong from '@/../public/images/common/wrong.png'
import right from '@/../public/images/common/right.png'
import { Trans } from 'react-i18next'
import { CheckSampleThresholdVo } from '@/app/api/api-uchoice/order/user/checkSampleItemThreshold/dtos'
import { formatCount } from '@/lib/format'

interface Props {
  visible: boolean
  toggle(): void
  onStillApply(): void
  onKnow(): void
  thresholdData?: CheckSampleThresholdVo
}

export const FreeSampleThresholdModal = ({
  visible,
  toggle,
  onStillApply,
  onKnow,
  thresholdData: initialThresholdData
}: Props) => {
  const [thresholdData, setThresholdData] =
    useState<CheckSampleThresholdVo | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (initialThresholdData) {
      setThresholdData(initialThresholdData)
    }
  }, [initialThresholdData])

  return (
    <Popup
      visible={visible}
      onMaskClick={toggle}
      position="bottom"
      style={{
        borderTopLeftRadius: px2rem(8),
        borderTopRightRadius: px2rem(8)
      }}
    >
      <div className="relative bg-white px-[24px] pt-[32px]">
        {/* 关闭按钮 */}
        <div
          className="absolute right-[24px] top-[24px] z-10 touch-opacity"
          onClick={toggle}
        >
          <img
            className="size-[32px]"
            src="/images/common/modal_close.png"
            alt="close"
          />
        </div>

        {/* 标题 */}
        <div className="mb-[24px] text-center text-[32px] font-medium text-black">
          {i18n.t('Product.商家免费样品门槛')}
        </div>

        {/* Tips提示 */}
        {thresholdData && (
          <div className="mb-[32px] flex items-center rounded-[4px] bg-[rgba(255,141,19,0.14)] px-[16px] py-[14px]">
            <Image src={tip_icon} className="h-[24px] w-[24px]"></Image>
            <span className="ml-[8px] text-[24px]">
              <Trans
                i18nKey="Product.您的销售等级为Llevel，该商品Llevel通过率为percent%"
                components={{
                  main: <span className="text-[#FF8D13]"></span>,
                  red: <span className="text-[#FF3141]"></span>
                }}
                values={{
                  level: formatCount(thresholdData.tier || 0),
                  percent: thresholdData.rate
                }}
              ></Trans>
            </span>
          </div>
        )}

        {/* 申请要求文字 */}
        <div className="mb-[20px] text-[28px] font-bold text-black30">
          {i18n.t('Product.申请的TikTok账号需满足：')}
        </div>

        {/* 要求背景框 */}
        <div className="mt-[20px] rounded-[8px] bg-background px-[26px] py-[16px]">
          {/* 粉丝量要求 */}
          <div className="mb-[16px] flex items-center">
            <Image
              src={thresholdData?.fansReason?.good ? right : wrong}
              className="mr-[8px] h-[28px] w-[28px]"
            ></Image>
            <span className="text-[24px] text-[#6E6E6E]">
              {i18n.t('Product.粉丝量≥count', {
                count: formatCount(thresholdData?.fansReason?.value)
              })}
            </span>
          </div>
          {/* 销量要求 */}
          <div className="mb-[16px] flex items-center">
            <Image
              src={thresholdData?.soldReason?.good ? right : wrong}
              className="mr-[8px] h-[28px] w-[28px]"
            ></Image>
            <span className="text-[24px] text-[#6E6E6E]">
              {i18n.t('Product.近30天销量≥count', {
                count: formatCount(thresholdData?.soldReason?.value)
              })}
            </span>
          </div>
        </div>

        {/* 按钮区域 */}
        <div className="mt-[48px] flex justify-between space-x-[16px]">
          <button
            onClick={onStillApply}
            className="h-[80px] flex-1 rounded-[4px] border-[2px] border-[#FE6D45] bg-white text-[28px] font-medium text-[#FE6D45]"
            disabled={loading}
          >
            {i18n.t('Product.仍要申请')}
          </button>
          <button
            onClick={onKnow}
            className="h-[80px] flex-1 rounded-[4px] bg-[#FE6D45] text-[28px] font-medium text-white"
            disabled={loading}
          >
            {i18n.t('Sample.我已知晓')}
          </button>
        </div>

        <div
          style={{
            height: webview
              ? px2rem(webview?.getData().bottomSafeArea + 46)
              : px2rem(46)
          }}
        ></div>
      </div>
    </Popup>
  )
}
