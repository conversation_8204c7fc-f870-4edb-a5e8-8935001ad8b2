'use client'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { isLanguageEN } from '@/lib/utils'
import { AppColors } from '@/lib/const'
import React from 'react'
import { NoticeBar } from 'react-vant'
import './index.css'

export const SubsidyDescCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  return itemInfo.itemSubsidyDescTh || itemInfo.itemSubsidyDescEn ? (
    <div className="px-[24px]">
      <div className="subsidyDescCell">
        <NoticeBar
          color={AppColors.black}
          background="white"
          text={
            isLanguageEN()
              ? itemInfo.itemSubsidyDescEn
              : itemInfo.itemSubsidyDescTh
          }
        />
      </div>
    </div>
  ) : (
    <div className="h-[28px]"></div>
  )
}
