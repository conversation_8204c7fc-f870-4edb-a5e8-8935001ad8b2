import { useForbiddenBodyScroll } from '@/lib/hooks/useForbiddenBodyScroll'
import alert_top_firework_icon from '@/../public/images/common/alert_top_firework.png'
import alert_process_finger_icon from '@/../public/images/common/alert_process_finger.png'
import alert_circle_left from '@/../public/images/common/alert_circle_left.png'
import alert_circle_right from '@/../public/images/common/alert_circle_right.png'
import alert_sample_process_1_en from '@/../public/images/product/detail/alert_sample_process_1_en.png'
import alert_sample_process_2_en from '@/../public/images/product/detail/alert_sample_process_2_en.png'
import alert_sample_process_3_en from '@/../public/images/product/detail/alert_sample_process_3_en.png'
import alert_sample_process_4_en from '@/../public/images/product/detail/alert_sample_process_4_en.png'
import alert_sample_process_1_th from '@/../public/images/product/detail/alert_sample_process_1_th.png'
import alert_sample_process_2_th from '@/../public/images/product/detail/alert_sample_process_2_th.png'
import alert_sample_process_3_th from '@/../public/images/product/detail/alert_sample_process_3_th.png'
import alert_sample_process_4_th from '@/../public/images/product/detail/alert_sample_process_4_th.png'
import alert_sample_process_1_vn from '@/../public/images/product/detail/alert_sample_process_1_vn.png'
import alert_sample_process_2_vn from '@/../public/images/product/detail/alert_sample_process_2_vn.png'
import alert_sample_process_3_vn from '@/../public/images/product/detail/alert_sample_process_3_vn.png'
import alert_sample_process_4_vn from '@/../public/images/product/detail/alert_sample_process_4_vn.png'
import { Image } from '@/components/Image'
import { useState } from 'react'
import { matchLanguage } from '@/lib/utils'
import { i18n } from '@/lib/client/i18n'
import { Trans } from 'react-i18next'
import { makeBodyScrollable } from '@/lib/client/utils'

export const NewSampleProcessAlert = () => {
  useForbiddenBodyScroll()

  const [began, setBegan] = useState(false)
  const [pageNo, setPageNo] = useState(1)
  const [visible, setVisible] = useState(true)

  return visible ? (
    <div className="fixed inset-0 z-10 flex items-center justify-center bg-black/55 backdrop-blur-[4px]">
      {began ? (
        <div className="flex flex-col items-center">
          <div className="flex items-center justify-between">
            {pageNo > 1 ? (
              <div onClick={() => setPageNo(pageNo - 1)}>
                <Image src={alert_circle_left} className="size-[48px]"></Image>
              </div>
            ) : (
              <div className="w-[48px]"></div>
            )}
            <div className="px-[38px]">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <Image
                className="w-[480px]"
                src={matchLanguage({
                  en: [
                    alert_sample_process_1_en,
                    alert_sample_process_2_en,
                    alert_sample_process_3_en,
                    alert_sample_process_4_en
                  ][pageNo - 1],
                  th: [
                    alert_sample_process_1_th,
                    alert_sample_process_2_th,
                    alert_sample_process_3_th,
                    alert_sample_process_4_th
                  ][pageNo - 1],
                  vi: [
                    alert_sample_process_1_vn,
                    alert_sample_process_2_vn,
                    alert_sample_process_3_vn,
                    alert_sample_process_4_vn
                  ][pageNo - 1]
                })}
              ></Image>
            </div>
            {pageNo < 4 ? (
              <div onClick={() => setPageNo(pageNo + 1)}>
                <Image src={alert_circle_right} className="size-[48px]"></Image>
              </div>
            ) : (
              <div className="w-[48px]"></div>
            )}
          </div>

          <div className="mt-[48px] px-[24px]">
            <span className="text-center text-[28px] text-white">
              {i18n.t('Common.Step')}
              {pageNo}{' '}
              {
                [
                  i18n.t(`Product.View more details`),
                  i18n.t(`Product.Scroll up to see more information`),
                  i18n.t(`Product.Click “Get free sample”`),
                  `${i18n.t(`Product.Aotu add showcase &`)} ${i18n.t(
                    `Product.Sample order syncs to uChoice pro in ~10 mins`
                  )}`
                ][pageNo - 1]
              }
            </span>
          </div>

          {pageNo === 4 && (
            <div
              className="mt-[48px] flex h-[80px] w-[410px] items-center justify-center rounded-[8px] bg-gradient-to-r from-[#FF4F98] via-[#FF2D2A] to-[#FF772F] touch-opacity"
              onClick={() => {
                makeBodyScrollable(true)
                setVisible(false)
                window.showSkuModal()
              }}
            >
              <span className="text-[32px] text-white">
                {i18n.t('Product.立即申样>')}
              </span>
            </div>
          )}
        </div>
      ) : (
        <div className="relative">
          <div className="mt-[48px] flex w-[602px] flex-col items-center rounded-[32px] bg-white px-[72px]">
            <div className="mt-[20px] text-[80px] font-bold text-primary">
              NEW
            </div>
            <div className="mt-[40px] text-[36px] text-black30">
              {i18n.t('Product.新申样路径')}
            </div>
            <div className="mt-[12px] text-[28px] text-gray6E">
              <Trans
                i18nKey="Product.Youpik uChoice新申样流程已上线，发货效率UP↑"
                components={{ red: <span className="text-primary" /> }}
              />
            </div>
            <Image
              className="h-[138px] w-[278px]"
              src={alert_process_finger_icon}
            ></Image>
            <div className="mt-[16px] text-[24px] text-gray6E">
              {i18n.t('Product.通过左右滑动查看每一步')}
            </div>
            <div
              className="mb-[48px] mt-[66px] flex h-[80px] w-[410px] items-center justify-center rounded-[8px] touch-opacity"
              style={{
                background:
                  'linear-gradient( 20deg, #FF4F98 0%, #FF2D2A 59%, #FF2F2A 63%, #FF772F 100%)'
              }}
              onClick={() => {
                setBegan(true)
              }}
            >
              <span className="text-[32px] text-white">
                {i18n.t('Product.开始阅读教程>')}
              </span>
            </div>
          </div>
          <div className="absolute top-0">
            <Image
              src={alert_top_firework_icon}
              className="h-[176px] w-[602px]"
            ></Image>
          </div>
        </div>
      )}
    </div>
  ) : null
}
