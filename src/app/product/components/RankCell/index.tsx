import React from 'react'
import rank_category_selected_1 from '@/../public/images/ranking/rank_category_on_1.png'
import rank_category_selected_2 from '@/../public/images/ranking/rank_category_on_2.png'
import rank_category_selected_3 from '@/../public/images/ranking/rank_category_on_3.png'
import rank_category_selected_4 from '@/../public/images/ranking/rank_category_on_4.png'
import rank_category_arrow_1 from '@/../public/images/ranking/rank_category_arrow_1.png'
import rank_category_arrow_2 from '@/../public/images/ranking/rank_category_arrow_2.png'
import rank_category_arrow_3 from '@/../public/images/ranking/rank_category_arrow_3.png'
import rank_category_arrow_4 from '@/../public/images/ranking/rank_category_arrow_4.png'
import { Image } from '@/components/Image'
import { i18nS } from '@/lib/server/i18n'
import { Cover } from './cover'

interface Props {
  type: number
  categoryId?: number
  rank: number
}

const RankCell = ({ type, categoryId, rank }: Props) => {
  const styles = {
    1: {
      icon: rank_category_selected_1,
      arrow: rank_category_arrow_1,
      title: i18nS.t('Ranking.uChoice样品榜'),
      backgroundColor: '#FBF2E6',
      textColor: '#DD840D'
    },
    2: {
      icon: rank_category_selected_2,
      arrow: rank_category_arrow_2,
      title: i18nS.t('Ranking.uChoice收藏夹榜单'),
      backgroundColor: '#EDECFC',
      textColor: '#5245E5'
    },
    3: {
      icon: rank_category_selected_3,
      arrow: rank_category_arrow_3,
      title: i18nS.t('Ranking.uChoice橱窗榜单'),
      backgroundColor: '#FEF0EC',
      textColor: '#FE6D45'
    },
    4: {
      icon: rank_category_selected_4,
      arrow: rank_category_arrow_4,
      title: i18nS.t('Ranking.uChoice畅销榜单'),
      backgroundColor: '#FEE9EE',
      textColor: '#FE2C55'
    }
  }
  return rank <= 30 ? (
    <div className="relative touch-opacity">
      <div className="px-[24px] pt-[28px]">
        <div
          className="flex h-[64px] w-full items-center justify-between rounded-[4px] pl-[6px] pr-[16px]"
          style={{ backgroundColor: styles[type].backgroundColor }}
        >
          <div className="flex items-center">
            <Image
              src={styles[type].icon}
              className="h-[52px] w-[60px]"
            ></Image>
            <span
              className="ml-[14px] text-[24px]"
              style={{ color: styles[type].textColor }}
            >
              {styles[type].title}
              {' Top '}
              {rank}
            </span>
          </div>
          <Image src={styles[type].arrow} className="h-[24px] w-[16px]"></Image>
        </div>
      </div>
      <Cover type={type} categoryId={categoryId || 0}></Cover>
    </div>
  ) : null
}

export default RankCell
