'use client'
import React from 'react'
import { useRouter } from '@/lib/hooks/useRouter'

export const Cover = ({
  type,
  categoryId
}: {
  type: number
  categoryId: number
}) => {
  const router = useRouter()
  return (
    <div
      className="absolute inset-0"
      onClick={() =>
        router.push(
          `/uchoice_ranking?index=${type - 1}&categoryId=${categoryId}`
        )
      }
    ></div>
  )
}
