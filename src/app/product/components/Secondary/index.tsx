'use client'
import React from 'react'
// import { VariationsCell } from '../../components/variationsCell'
// import { DescriptionCell } from '../../components/descriptionCell'
// import { HotProductsCell } from '../../components/hotProductsCell'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'

const Secondary = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  return (
    <>
      <div className="h-[12px] w-full bg-background"></div>

      <div
        className="touch-opacity"
        // @ts-ignore
        onClick={() => window?.showSkuModal()}
      >
        {/* <VariationsCell></VariationsCell> */}
      </div>

      <div className="h-[12px] w-full bg-background"></div>

      {/* <DescriptionCell itemInfo={itemInfo}></DescriptionCell> */}

      {/* <HotProductsCell itemInfo={itemInfo}></HotProductsCell> */}
    </>
  )
}

export default Secondary
