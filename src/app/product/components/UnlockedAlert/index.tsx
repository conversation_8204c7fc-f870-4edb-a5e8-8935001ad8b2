import React from 'react'
import unlock from '@/../public/images/product/detail/unlock.png'
import { Image } from '@/components/Image'
import { Alert } from '@/components/Alert'
import { i18n } from '@/lib/client/i18n'
import { useRouter } from '@/lib/hooks/useRouter'

interface Props {
  visible: boolean
  onClose: () => void
  productId: string
  id: string
}
export const UnlockedAlert = ({ visible, onClose, productId, id }: Props) => {
  const router = useRouter()
  return (
    <Alert
      onMaskClick={onClose}
      visible={visible}
      title={
        <div className="flex items-center">
          <Image src={unlock} className="size-[44px]"></Image>
          <span className="ml-[12px] text-[36px] font-bold text-black30">
            {i18n.t('Sample.已解锁回本教程')}
          </span>
        </div>
      }
      content={
        <span
          className="mb-[70px] text-[32px] text-black30"
          dangerouslySetInnerHTML={{
            __html: i18n.t(
              'Sample.平台赠送您该商品的出单教程，助您内容销量的增长！可前往“我的”页面查看~'
            )
          }}
        ></span>
      }
      cancelBtn={{ title: i18n.t('Common.Cancel'), onClick: onClose }}
      confirmBtn={{
        title: i18n.t('Sample.回本教程>'),
        onClick: () => {
          router.push(`/promotion_details?id=${id}&productId=${productId}`)
        }
      }}
    ></Alert>
  )
}
