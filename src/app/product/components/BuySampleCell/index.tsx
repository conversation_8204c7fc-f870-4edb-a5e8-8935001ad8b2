'use client'
import { i18n } from '@/lib/client/i18n'
import React from 'react'
import { TransComponent } from '@/app/components/Trans'
import buy_sample_en from '@/../public/images/product/detail/buy_sample_en.png'
import buy_sample_th from '@/../public/images/product/detail/buy_sample_th.png'
import buy_sample_vi from '@/../public/images/product/detail/buy_sample_vi.png'
import buy_sample_progress0 from '@/../public/images/product/detail/buy_sample_progress0.png'
import buy_sample_progress1 from '@/../public/images/product/detail/buy_sample_progress1.png'
import buy_sample_progress2 from '@/../public/images/product/detail/buy_sample_progress2.png'
import buy_sample_progress3 from '@/../public/images/product/detail/buy_sample_progress3.png'
import buy_sample_progress4 from '@/../public/images/product/detail/buy_sample_progress4.png'
import buy_sample_progress5 from '@/../public/images/product/detail/buy_sample_progress5.png'
import reward_bg from '@/../public/images/product/detail/reward_bg.png'
import show_arrow_icon from '@/../public/images/product/detail/show_arrow_icon.png'
import { Image } from '@/components/Image'
import { Cover } from './cover'

const BuySampleCell = () => {
  return (
    <div className="px-[24px] py-[12px]">
      <div className="mb-[24px]  flex items-center justify-between">
        <div className={'pb-[24px] text-[30px] font-bold text-black02'}>
          {i18n.t('Sample.购样流程')}
        </div>

        <div className="relative touch-opacity">
          <div className="flex items-center justify-between py-[12px]">
            <span className="text-[24px] text-black">
              {i18n.t('Sample.流程详情')}
            </span>
            <Image
              className="ml-[8px] h-[24px] w-[16px]"
              src={show_arrow_icon}
            ></Image>
          </div>
          <Cover></Cover>
        </div>
      </div>

      <div className="relative h-[284px] w-full">
        <Image
          src={reward_bg}
          className="absolute left-[36px] top-[8px] h-[164px] w-[650px]"
        ></Image>
        <div className="absolute h-[284px] w-full pr-[0px]">
          <div className="flex h-[142px] justify-between ">
            <div className="flex flex-col items-center">
              <Image src={buy_sample_progress0} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[160px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Sample.成本购样')}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <Image src={buy_sample_progress1} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[160px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Sample.添加橱窗')}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <Image src={buy_sample_progress2} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[160px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Sample.解锁教程')}
              </span>
            </div>
          </div>

          <div className="flex h-[142px] justify-between">
            <div className="flex flex-col items-center">
              <Image src={buy_sample_progress3} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[160px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Sample.免费样品')}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <Image src={buy_sample_progress4} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[160px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Sample.轻松出单')}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <Image src={buy_sample_progress5} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[160px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Sample.模仿带货')}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BuySampleCell
