import React from 'react'
import book from '@/../public/images/product/detail/lock.png'
import { Image } from '@/components/Image'
import { Alert } from '@/components/Alert'
import { i18n } from '@/lib/client/i18n'
import { Trans } from 'react-i18next'
import { useRouter } from '@/lib/hooks/useRouter'

interface Props {
  visible: boolean
  onClose: () => void
  onAddToShowCase: () => void
  productId: string
}
export const UnlockAlert = ({
  visible,
  onClose,
  onAddToShowCase,
  productId
}: Props) => {
  const router = useRouter()
  return (
    <Alert
      onMaskClick={onClose}
      visible={visible}
      title={
        <div className="flex items-center">
          <Image src={book} className="size-[44px]"></Image>
          <span className="ml-[12px] text-[36px] font-bold text-black30">
            {i18n.t('Sample.挂橱窗，解锁出单教程')}
          </span>
        </div>
      }
      content={
        <Trans
          i18nKey="Sample.平台赠送您该商品的出单教程，助您内容销量的增长！请先将商品添加橱窗~"
          components={{
            main: (
              <span
                className="mb-[70px] text-[32px] text-black30"
                onClick={e => {
                  e.stopPropagation()
                }}
              ></span>
            ),
            red: <span className="text-[#FF0032]"></span>,
            button: (
              <span
                className="text-[#FF0032]"
                onClick={e => {
                  e.stopPropagation()
                  // router.push(`/promotion_details?id=${productId}`)
                }}
              ></span>
            )
          }}
        ></Trans>
      }
      cancelBtn={{ title: i18n.t('Common.Cancel'), onClick: onClose }}
      confirmBtn={{
        title: i18n.t('Product.添加橱窗'),
        onClick: onAddToShowCase
      }}
    ></Alert>
  )
}
