'use client'
/* eslint-disable @next/next/no-img-element */
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { SimilarProductDto } from '@/app/api/api-uchoice/tt/item/similarProductList/dtos'
import { similarProductList } from '@/app/api/api-uchoice/tt/item/similarProductList/request'
import { GridProduct } from '@/app/components/Products/gridProduct'
import { i18n } from '@/lib/client/i18n'
import { loading } from '@/lib/client/loading'
import { useIntersectionObserver } from '@/lib/hooks/useIntersectionObserver'
import React, { useEffect, useRef, useState } from 'react'

const HotProductsCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  const { ref, isLeave } = useIntersectionObserver()

  const pageNoRef = useRef(1)
  const loadingRef = useRef(false)

  const [noMore, setNoMore] = useState(false)
  const [data, setData] = useState<SimilarProductDto[]>([])

  useEffect(() => {
    if (!isLeave) {
      fetch()
    }
  }, [isLeave])

  const fetch = async () => {
    if (loadingRef.current || noMore) return

    loadingRef.current = true

    if (pageNoRef.current > 1) loading.show({ userInteractive: true })

    const { code, result } = await similarProductList({
      pageNo: pageNoRef.current,
      pageSize: 10,
      id: itemInfo.id
    })
    loading.hide()

    loadingRef.current = false

    if (code === 200) {
      pageNoRef.current++

      setData(data.concat(result.list))

      if (result.total === 0) {
        setNoMore(true)
      }
    }
  }

  return (
    <div>
      <div className="h-[12px] w-full bg-background"></div>

      <div className="px-[24px]">
        <div className="flex items-center justify-center pb-[24px] pt-[28px]">
          <img
            src="/images/product/detail/hot_left.png"
            className="h-[40px] w-[88px]"
          ></img>
          <span className="mx-[12px] text-[30px] text-black">
            {i18n.t('Product.相似热销商品')}
          </span>
          <img
            src="/images/product/detail/hot_right.png"
            className="h-[40px] w-[88px]"
          ></img>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-[14px] bg-background p-[16px]">
        {data.map((it, i) => (
          <GridProduct key={i} item={it}></GridProduct>
        ))}
      </div>

      {noMore && (
        <div className="bg-background pb-[24px] text-center font-[24px] text-grayCC">
          {i18n.t('Common.No data')}
        </div>
      )}

      <div ref={ref} className="h-[100px]"></div>
    </div>
  )
}

export default HotProductsCell
