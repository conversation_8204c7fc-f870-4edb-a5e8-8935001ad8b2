import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { Image } from '@/components/Image'
import React from 'react'
// import shop from '@/../public/images/product/detail/shop.png'
import show_arrow_icon from '@/../public/images/product/detail/show_arrow_icon.png'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'

export const ShopCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  const router = useRouter()
  return itemInfo.tikTokSeller?.id ? (
    <div
      className="flex items-center touch-opacity"
      onClick={() =>
        router.push(`/tiktok_data/shop/${itemInfo.tikTokSeller.id}`)
      }
    >
      <div className="flex flex-1 items-center">
        <span className="text-[24px] text-gray8A">
          {i18n.t('Product.店铺')}
        </span>
        <div className="mx-[16px] flex size-[48px] items-center justify-center rounded-[2px] bg-background">
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img src={itemInfo.tikTokSeller.image} className="size-[48px]"></img>
        </div>
        <span className="text-[28px] text-black">{itemInfo.shopName}</span>
      </div>
      <Image src={show_arrow_icon} className="h-[24px] w-[16px]"></Image>
    </div>
  ) : null
}
