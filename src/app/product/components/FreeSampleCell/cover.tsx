'use client'
import { useRouter } from '@/lib/hooks/useRouter'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import React from 'react'

export const Cover = () => {
  const router = useRouter()

  return (
    <div
      className="absolute top-0 size-full"
      onClick={() => {
        statistic({
          eventName: EventName.pdp_show_detail_sample
        })
        router.push('/sample_guide/page')
      }}
    ></div>
  )
}
