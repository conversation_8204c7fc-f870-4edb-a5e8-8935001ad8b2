'use client'
import { i18n } from '@/lib/client/i18n'
import React from 'react'
import { TransComponent } from '@/app/components/Trans'
import free_sample_en from '@/../public/images/product/detail/free_sample_en.png'
import free_sample_th from '@/../public/images/product/detail/free_sample_th.png'
import free_sample_vi from '@/../public/images/product/detail/free_sample_vi.png'
import reward_icon0 from '@/../public/images/product/detail/reward_icon0.png'
import reward_icon1 from '@/../public/images/product/detail/reward_icon1.png'
import reward_icon2 from '@/../public/images/product/detail/reward_icon2.png'
import reward_icon3 from '@/../public/images/product/detail/reward_icon3.png'
import reward_icon4 from '@/../public/images/product/detail/reward_icon4.png'
import reward_bg from '@/../public/images/product/detail/reward_bg.png'
import show_arrow_icon from '@/../public/images/product/detail/show_arrow_icon.png'
import { Image } from '@/components/Image'
import { Cover } from './cover'

const FreeSampleCell = () => {
  return (
    <div className="px-[24px] py-[12px]">
      <div className="mb-[24px]  flex items-center justify-between">
        <div className={'text-[30px] font-bold text-black02'}>
          {i18n.t('Product.Tabs领样流程')}
        </div>

        <div className="relative touch-opacity">
          <div className="flex items-center justify-between py-[12px]">
            <span className="text-[24px] text-black">
              {' '}
              {i18n.t('Showcase.pdp_查看详细流程')}
            </span>
            <Image
              className="ml-[8px] h-[24px] w-[16px]"
              src={show_arrow_icon}
            ></Image>
          </div>
          <Cover></Cover>
        </div>
      </div>

      <div className="relative h-[284px] w-full">
        <Image
          src={reward_bg}
          className="absolute left-[36px] top-[8px] h-[164px] w-[650px]"
        ></Image>
        <div className="absolute h-[284px] w-full pr-[0px]">
          <div className="flex h-[142px] justify-between ">
            <div className="flex flex-col items-center">
              <Image src={reward_icon0} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Product.申请样品')}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <Image src={reward_icon1} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Product.审核发货')}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <Image src={reward_icon2} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Product.达人收货')}
              </span>
            </div>
          </div>

          <div className="flex h-[142px] justify-between">
            <div className="flex flex-col items-center">
              <Image src={reward_icon3} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Product.完成带货')}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <Image src={reward_icon4} className="size-[52px]"></Image>
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Product.发视频/直播')}
              </span>
            </div>
            <div className="flex flex-col items-center opacity-0">
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FreeSampleCell
