'use client'
import { GetTapRankingListType } from '@/app/api/api-uchoice/rankings/getTapRankingList/dtos'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { Notice as TheNotice } from '@/app/uchoice_ranking/components/Notice'
import { useMounted } from '@/lib/hooks/useMounted'
import React from 'react'

const Notice = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  const mounted = useMounted()
  return mounted ? (
    <div>
      {itemInfo &&
        itemInfo.tapRanking &&
        ((itemInfo.tapRanking.type! === GetTapRankingListType.BestSellers &&
          itemInfo.tapRanking.anchorSalesInfoVoList &&
          itemInfo.tapRanking.anchorSalesInfoVoList.length > 0) ||
          (itemInfo.tapRanking.type! !== GetTapRankingListType.BestSellers &&
            itemInfo.tapRanking.anchorCount > 0)) && (
          <div className="px-[24px] pt-[20px]">
            <TheNotice
              type={itemInfo.tapRanking.type!}
              anchorCount={itemInfo.tapRanking.anchorCount}
              anchorSalesInfoVoList={itemInfo.tapRanking.anchorSalesInfoVoList}
            ></TheNotice>
          </div>
        )}
    </div>
  ) : null
}

export default Notice
