/* eslint-disable @next/next/no-img-element */
import { TikTokItemDetailVo } from '@/app/api/api-uchoice/tiktok/item/detail/dtos'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { PopupProps } from '@/components/Popup'
import { copyText, px2rem } from '@/lib/client/utils'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useRouter } from '@/lib/hooks/useRouter'
import React, { useEffect } from 'react'
import { Popup } from 'react-vant'

interface Props extends Omit<PopupProps, 'onConfirm' | 'children'> {
  itemInfo: ItemInfoDto | TikTokItemDetailVo
}

export const CopiedModal = ({ visible, toggle, itemInfo }: Props) => {
  const router = useRouter()

  useEffect(() => {
    if (visible) {
      copyText(itemInfo.link || itemInfo.sourceLink)
    }
  }, [visible, itemInfo])

  const onConfirm = async () => {
    if (webview) {
      await webview?.send(WebviewEvents.makeSureTikTokAuthed)
      webview?.send(WebviewEvents.launch, {
        url: 'snssdk1180://lynxview?channel=tea_selection_page&bundle=url-add-product%2Ftemplate.js&surl=https%3A%2F%2Flf16-gecko-source.tiktokcdn.com%2Fobj%2Fbyte-gurd-source-sg%2F8%2Fgecko%2Fresource%2Ftea_selection_page%2Furl-add-product%2Ftemplate.js&use_gecko_first=1&dynamic=1&hide_nav_bar=1&trans_status_bar=1&status_bar_color=00000000&use_bdx=1&source_page_type=external_uchoice&enter_from=external_uchoice&enter_method=external_uchoice'
      })
    } else {
      toggle()
    }
  }

  return (
    <Popup
      visible={visible}
      onClickOverlay={toggle}
      position="bottom"
      style={{
        borderTopLeftRadius: px2rem(8),
        borderTopRightRadius: px2rem(8)
      }}
      overlayStyle={{ backgroundColor: 'rgba(0,0,0,0.5)' }}
    >
      <div className="px-[24px] pt-[82px]">
        <div className="flex justify-center">
          <img
            src="/images/product/detail/success.png"
            className="size-[96px]"
          />
        </div>
        <p className="mt-[30px] text-center text-[30px] text-black">
          {i18n.t('Product.copyTAPshop')}
        </p>
        <div className="text-gray-6E mt-[26px] break-all rounded-[4px] bg-background p-[24px] text-[26px]">
          {itemInfo.link || itemInfo.sourceLink}
        </div>
        <div className="mb-[90px] flex justify-end">
          <span
            className="pt-[4px] text-[26px] text-link underline touch-opacity"
            onClick={() => router.push('/product/copyLinkGuide/page')}
          >
            {i18n.t('Product.addTAPshop')}
          </span>
        </div>
        <div
          className="mb-[24px] flex h-[80px] w-[702px] items-center justify-center rounded-[4px] bg-primary touch-opacity"
          onClick={onConfirm}
        >
          <span className="text-[32px] text-white">
            {i18n.t('Common.Confirm')}
          </span>
        </div>
        <div
          style={{
            height: webview ? `${px2rem(webview?.getData().bottomSafeArea)}` : 0
          }}
        ></div>
      </div>

      <div
        className="absolute right-0 top-0 z-10 p-[24px] touch-opacity"
        onClick={toggle}
      >
        <img
          className="right-0 size-[32px]"
          src="/images/common/modal_close.png"
        ></img>
      </div>
    </Popup>
  )
}
