import React from 'react'
import book from '@/../public/images/product/detail/book.png'
import { Image } from '@/components/Image'
import { Alert } from '@/components/Alert'
import { i18n } from '@/lib/client/i18n'
import { useRouter } from '@/lib/hooks/useRouter'

interface Props {
  visible: boolean
  onClose: () => void
  onBuy: () => void
}
export const FirstBuyAlert = ({ visible, onClose, onBuy }: Props) => {
  const router = useRouter()
  return (
    <Alert
      visible={visible}
      onMaskClick={onClose}
      title={
        <div className="flex items-center">
          <Image src={book} className="size-[44px]"></Image>
          <span className="ml-[12px] text-center text-[36px] font-bold text-black30">
            {i18n.t('Sample.首次购样流程')}
          </span>
        </div>
      }
      content={i18n.t(
        'Sample.点击可查看购样教程，如不需要，点击【跳过】按钮即可。'
      )}
      cancelBtn={{ title: i18n.t('Sample.继续购样'), onClick: onBuy }}
      confirmBtn={{
        title: i18n.t('Sample.查看购样流程>'),
        onClick: () => {
          router.push('/cost_sample_process/page')
        }
      }}
    ></Alert>
  )
}
