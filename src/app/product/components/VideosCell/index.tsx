'use client'
import React, { useEffect, useState } from 'react'
import { Image } from '@/components/Image'
import video_1 from '@/../public/images/product/detail/video_1.png'
import video_2 from '@/../public/images/product/detail/video_2.png'
import video_3 from '@/../public/images/product/detail/video_3.png'
import video_4 from '@/../public/images/product/detail/video_4.png'
import video_play from '@/../public/images/product/detail/video_play.png'
import light_tips from '@/../public/images/common/light_tips.png'
import { i18n } from '@/lib/client/i18n'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { getHotSellingVideos } from '@/app/api/api-uchoice/showcase/getHotSellingVideos/request'
import { HotSellingVideosVo } from '@/app/api/api-uchoice/showcase/getHotSellingVideos/dto'
import { useRouter } from '@/lib/hooks/useRouter'
import { isLanguageEN } from '@/lib/utils'
import { formatPrice } from '@/lib/format'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { webview } from '@/lib/client/webview'
import { WebViewToPages, WebviewEvents } from '@/lib/client/webview/events'
import { TabSection } from '@/app/components/Tabs/TabSection'

const VideosCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  const router = useRouter()
  const [videos, setVideos] = useState<HotSellingVideosVo[]>([])

  useEffect(() => {
    fetch()
  }, [])

  const fetch = async () => {
    const { code, result } = await getHotSellingVideos({
      productId: itemInfo.productId
    })
    if (code === 200) {
      setVideos(result)
    }
  }

  const onPreviewVideo = async (index: number) => {
    const earn =
      itemInfo.minEarn === itemInfo.maxEarn
        ? `${formatPrice(itemInfo.minEarn, true)}`
        : `${formatPrice(itemInfo.minEarn, true)}~${formatPrice(
            itemInfo.maxEarn,
            true
          )}`

    // TODO: earn有问题；添加橱窗按钮事件；

    statistic({
      eventName: EventName.pdp_play_video,
      param: {
        index
      }
    })

    const support = await webview?.send(WebviewEvents.toPage, {
      name: WebViewToPages.video_paging,
      params: {
        videoIds: videos.map(video => video.videoId),
        index: index,
        productImage: itemInfo.homeImgUrl,
        productName: isLanguageEN() ? itemInfo.nameEn : itemInfo.name,
        productEarn: earn,
        productId: itemInfo.id
      }
    })

    if (!support) {
      router.push(
        `https://www.tiktok.com/player/v1/${videos[index].videoId}?id=${
          videos[index].videoId
        }&productImage=${itemInfo.homeImgUrl}&productName=${
          isLanguageEN() ? itemInfo.nameEn : itemInfo.name
        }&productEarn=${earn}&productId=${itemInfo.id}`
      )
    }
  }

  return videos.length > 0 ? (
    <>
      <div className="h-[10px] w-full bg-background"></div>

      <TabSection></TabSection>

      <div className="bg-white px-[24px] pt-[28px]">
        <div className="mb-[12px] text-[30px] font-bold text-black">
          {i18n.t('Product.热销视频推荐')}
        </div>
        <div className="mb-[20px] text-[24px] text-gray8A">
          {i18n
            .t('Product.以下视频是该商品带货表现最好的Top 3')
            .replace('{{number}}', videos.length)}
        </div>

        <div className={'overflow-x-scroll hide-scrollbar'}>
          <div className="flex w-max">
            {videos.map((_, index) => (
              <>
                {index > 0 && <div className="w-[20px]"></div>}

                <div
                  className="relative w-[246px] touch-opacity"
                  onClick={() => onPreviewVideo(index)}
                >
                  <div className="relative h-[328px] w-[246px]">
                    <Image
                      className="h-[328px] w-[246px] object-cover"
                      src={videos[index].coverImg}
                    ></Image>
                    <div className="absolute top-0 flex h-[328px] w-[246px] items-center justify-center">
                      <Image className="size-[60px]" src={video_play}></Image>
                    </div>
                  </div>

                  <div className="flex items-center bg-background p-[8px]">
                    <div className="flex-1 px-[8px]">
                      <span className="line-clamp-2 text-ellipsis break-all">
                        {videos[index].videoDescription}
                      </span>
                    </div>
                  </div>

                  {index < 3 ? (
                    <Image
                      className="absolute left-0 top-0 size-[40px]"
                      src={[video_1, video_2, video_3][index]}
                    ></Image>
                  ) : (
                    <div className="absolute left-0 top-0 ">
                      <Image className="size-[40px]" src={video_4}></Image>
                      <div className="absolute left-0 top-0 size-[40px] text-center text-[24px] font-bold leading-[40px] text-white">
                        {index + 1}
                      </div>
                    </div>
                  )}
                </div>
              </>
            ))}
          </div>
        </div>

        <div className={'mt-[24px] flex items-center'}>
          <Image className="size-[24px]" src={light_tips}></Image>
          <div className={'w-[8px]'}></div>
          <span className={'text-[22px] text-gray9A'}>
            {i18n.t('Product.数据来源于TikTok官方平台')}
          </span>
        </div>
      </div>
    </>
  ) : null
}

export default VideosCell
