'use client'
/* eslint-disable @next/next/no-img-element */
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { isLanguageTH, isRegionTH } from '@/lib/utils'
import { formatCount, formatPrice } from '@/lib/format'
import { i18n } from '@/lib/client/i18n'
import React from 'react'
import { Trans } from 'react-i18next'
import { useMounted } from '@/lib/hooks/useMounted'

const RewardsCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  const mounted = useMounted()
  return isRegionTH() && mounted ? (
    <div className="px-[24px] pb-[28px]">
      <div className={'pb-[24px] text-[30px] font-bold text-black02'}>
        {i18n.t('Sample.投流奖励说明')}
      </div>
      <p className="text-[24px] text-gray6E">
        <Trans
          i18nKey="Product.您有潜力获得฿xx.xx广告投流奖励！满足30天内销售该商品xx件，GMV达到฿xxx.xx且发布的视频播放量达到xxxx"
          values={{
            rewards: formatPrice(
              itemInfo.flowManagementInfoVo?.flowAmount,
              true
            ),
            gmv: formatPrice(itemInfo.flowManagementInfoVo?.gmv, true),
            count: formatCount(itemInfo.flowManagementInfoVo?.ordersCount),
            playCount: formatCount(
              itemInfo.flowManagementInfoVo?.videoPlayCount
            )
          }}
          components={{
            red: <span className="font-bold text-primary" />
          }}
        />
      </p>

      {/* <div className="mt-[10px] border-2 border-solid border-grayEE">
        <div className="flex border-b-2 border-solid border-grayEE">
          <div className="flex h-[82px] w-[272px] items-center justify-center border-r-2 border-solid border-grayEE">
            <span className="text-[24px] text-black">
              {i18n.t('Sample.商品出单量')}
            </span>
          </div>
          <div className="flex h-[82px] flex-1 items-center justify-center">
            <span className="text-[24px] font-bold text-black">
              {formatCount(itemInfo.flowManagementInfoVo?.ordersCount)}
            </span>
          </div>
        </div>

        <div className="flex border-b-2 border-solid border-grayEE">
          <div className="flex h-[82px] w-[272px] items-center justify-center border-r-2 border-solid border-grayEE">
            <span className="text-[24px] text-black">
              {i18n.t('Sample.GMV')}
            </span>
          </div>
          <div className="flex h-[82px] flex-1 items-center justify-center">
            <span className="text-[24px] font-bold text-black">
              {formatPrice(itemInfo.flowManagementInfoVo?.gmv, true)}
            </span>
          </div>
        </div>

        <div className="flex">
          <div className="flex h-[82px] w-[272px] items-center justify-center border-r-2 border-solid border-grayEE">
            <span className="text-[24px] text-black">
              {i18n.t('Sample.视频播放量')}
            </span>
          </div>
          <div className="flex h-[82px] flex-1 items-center justify-center">
            <span className="text-[24px] text-black">
              {formatCount(itemInfo.flowManagementInfoVo?.videoPlayCount)}
            </span>
          </div>
        </div>
      </div> */}
    </div>
  ) : (
    <div className="h-[24px]"></div>
  )
}

export default RewardsCell
