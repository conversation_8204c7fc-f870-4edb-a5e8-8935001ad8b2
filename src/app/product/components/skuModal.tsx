/* eslint-disable @next/next/no-img-element */
import { ItemInfoDto, SkuList } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { CountInput } from '@/components/CountInput'
import {
  getToken,
  makeSureInApp,
  px2rem,
  resizeImageUrl
} from '@/lib/client/utils'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import { AppColors } from '@/lib/const'
import { formatPrice } from '@/lib/format'
import { ImagePreview } from 'react-vant'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { TikTokItemDetailVo } from '@/app/api/api-uchoice/tiktok/item/detail/dtos'
import { getGoodsInWhitelist } from '@/app/api/api-uchoice/order/user/getGoodsInWhitelist/request'
import { WebviewEvents } from '@/lib/client/webview/events'
import { getSampleAudit } from '@/app/api/api-uchoice/order/user/getSampleAudit/request'
import { Popup } from 'antd-mobile'
import { getGoAppBarHeight } from '@/components/GoAppBar'

interface SkuButtonProps {
  title: string
  selectedTitle?: string
  icon?: string
  onSelected: (title: string) => void
}

const SkuButton = ({
  selectedTitle,
  title,
  icon,
  onSelected
}: SkuButtonProps) => {
  return (
    <div
      className="relative mb-[24px] mr-[24px] flex h-[80px] items-center rounded-[2px] border-2 border-solid bg-background pl-[16px] pr-[40px]"
      style={{
        borderColor:
          title === selectedTitle ? AppColors.primary : 'rgba(0,0,0,0)'
      }}
      onClick={() => {
        onSelected(title)
      }}
    >
      {icon ? (
        <img
          src={resizeImageUrl(icon, 64)}
          className="mr-[8px] size-[64px] rounded-[2px] "
        />
      ) : (
        <div className="w-[30px]"></div>
      )}

      <span
        className="text-[26px]"
        style={{
          color: title === selectedTitle ? AppColors.primary : AppColors.black
        }}
      >
        {title}
      </span>
      {title === selectedTitle && (
        <img
          className="absolute bottom-0 right-0 size-[44px]"
          src="/images/product/detail/sku_btn_corner.png"
        ></img>
      )}
    </div>
  )
}

interface Props {
  visible: boolean
  toggle(): void
  itemInfo: ItemInfoDto | TikTokItemDetailVo
  onSelectedSku(sku: SkuList, count: number): void
}
export const SkuModal = ({
  visible,
  toggle,
  itemInfo,
  onSelectedSku
}: Props) => {
  const [limitSampleAmount, setLimitSampleAmount] = useState(0)
  const [count, setCount] = useState(1)
  const [isWhitelistProduct, setIsWhitelistProduct] = useState<Boolean>()
  const scrollRef = useRef(null)

  // 新增门槛弹窗相关状态
  const [pendingSelectedSku, setPendingSelectedSku] = useState<SkuList | null>(
    null
  )

  useEffect(() => {
    fetchIsWhitelistProduct()
    fetchSampleAudit()
  }, [])

  const fetchIsWhitelistProduct = async () => {
    if (!(await getToken())) {
      return
    }
    const { code, result } = await getGoodsInWhitelist(itemInfo.productId)
    setIsWhitelistProduct(code === 200 && result)
  }

  const fetchSampleAudit = async () => {
    const {
      code,
      result: { sampleAmount }
    } = await getSampleAudit()
    if (code === 200) {
      setLimitSampleAmount(sampleAmount)
    }
  }

  const [selectedPrimarySpecName, setSelectedPrimarySpecName] = useState<
    string | undefined
  >()
  const [selectedSecondarySpecName, setSelectedSecondarySpecName] = useState<
    string | undefined
  >()

  const specMap = useMemo(() => {
    const map = {}

    itemInfo.skuList.map(sku => {
      const primarySpecLabel = sku.skuAttrList[0]
        ? sku.skuAttrList[0].attributesValue
        : ''
      const secondarySpecLabel = sku.skuAttrList[1]
        ? `-${sku.skuAttrList[1].attributesValue}`
        : ''
      map[primarySpecLabel + secondarySpecLabel] = sku
    })

    return map
  }, [itemInfo])

  const primarySpecLabel = useMemo(() => {
    const sku = itemInfo.skuList.filter(sku => sku.skuAttrList.length > 0)
    return sku ? sku[0].skuAttrList[0].specName : null
  }, [itemInfo])

  const primarySpecValues = useMemo(() => {
    const values = {}

    itemInfo.skuList.map(sku => {
      if (sku.skuAttrList[0]) {
        if (selectedSecondarySpecName) {
          if (
            sku.skuAttrList[1].attributesValue === selectedSecondarySpecName
          ) {
            values[sku.skuAttrList[0].attributesValue] =
              'isShowImg' in itemInfo && itemInfo.isShowImg
                ? sku.skuImage
                : null
          }
        } else {
          values[sku.skuAttrList[0].attributesValue] =
            'isShowImg' in itemInfo && itemInfo.isShowImg ? sku.skuImage : null
        }
      }
    })

    return values
  }, [itemInfo, selectedSecondarySpecName])

  const secondarySpecLabel = useMemo(() => {
    const sku = itemInfo.skuList.filter(sku => sku.skuAttrList.length > 1)
    return sku.length > 0 ? sku[0].skuAttrList[1].specName : null
  }, [itemInfo])

  const secondarySpecValues = useMemo(() => {
    const names = itemInfo.skuList.map(sku => {
      if (sku.skuAttrList[0] && selectedPrimarySpecName) {
        return sku.skuAttrList[1] &&
          sku.skuAttrList[0].attributesValue === selectedPrimarySpecName
          ? sku.skuAttrList[1].attributesValue
          : null
      } else {
        return sku.skuAttrList[1] ? sku.skuAttrList[1].attributesValue : null
      }
    })
    return Array.from(new Set(names.filter(name => name)))
  }, [itemInfo, selectedPrimarySpecName])

  const selectedSku = useMemo(() => {
    return specMap[
      (selectedPrimarySpecName || '') +
        (selectedSecondarySpecName ? '-' : '') +
        (selectedSecondarySpecName || '')
    ]
  }, [selectedPrimarySpecName, selectedSecondarySpecName, specMap])

  useEffect(() => {
    if (Object.keys(specMap).length > 0) {
      const skus = Object.values(specMap) as any
      skus.sort((a, b) => a.price - b.price)
      const sku = skus[0] as any
      setSelectedPrimarySpecName(sku.skuAttrList[0].attributesValue)
      if (sku.skuAttrList.length == 2) {
        setSelectedSecondarySpecName(sku.skuAttrList[1].attributesValue)
      }
    }
  }, [specMap])

  const rateStr = () => {
    if ('commissionRate' in itemInfo) {
      return itemInfo.commissionRate
    }
    if ('creatorCommissionPercentStr' in itemInfo) {
      return itemInfo.creatorCommissionPercentStr
    }
    return ''
  }

  const totalPrice = useMemo(() => {
    return selectedSku ? selectedSku.actualFee * count : 0
  }, [selectedSku, count])

  const warning = useMemo(() => {
    return totalPrice > limitSampleAmount
  }, [totalPrice, limitSampleAmount])

  useEffect(() => {
    if (warning && scrollRef.current) {
      // @ts-ignore
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight
    }
  }, [warning])

  return (
    <>
      <Popup
        visible={visible}
        onMaskClick={toggle}
        position="bottom"
        style={{
          borderTopLeftRadius: px2rem(8),
          borderTopRightRadius: px2rem(8)
        }}
        // overlayStyle={{ backgroundColor: 'rgba(0,0,0,0.5)' }}
      >
        <div
          ref={scrollRef}
          className="overflow-scroll p-[24px] pt-0"
          style={{
            maxHeight: webview
              ? window.innerHeight - webview?.getData().topSafeArea - 44 - 24
              : window.innerHeight - getGoAppBarHeight() - 24
          }}
          onTouchMove={event => {
            // antd内部会监听这个事件但是它处理的又有问题导致卡顿，所以组织它监听
            event.stopPropagation()
          }}
        >
          <div className="sticky inset-x-0 top-0 z-10 bg-white">
            <div
              className="flex items-end pt-[24px] touch-opacity"
              onClick={async () => {
                const imageUrl = selectedPrimarySpecName
                  ? primarySpecValues[selectedPrimarySpecName] ||
                    itemInfo.homeImgUrl
                  : itemInfo.homeImgUrl

                if (
                  !!(await webview?.send(WebviewEvents.hasJavaScriptHandler, {
                    handlerName: WebviewEvents.previewImages
                  }))
                ) {
                  webview?.send(WebviewEvents.previewImages, {
                    urls: imageUrl,
                    initialIndex: 0
                  })
                } else {
                  ImagePreview.open({
                    images: [imageUrl],
                    closeable: true
                  })
                }
              }}
            >
              <img
                src={resizeImageUrl(
                  selectedPrimarySpecName
                    ? primarySpecValues[selectedPrimarySpecName] ||
                        itemInfo.homeImgUrl
                    : itemInfo.homeImgUrl,
                  240
                )}
                className="mr-[20px] size-[240px] rounded-[4px]"
              />
              <div>
                <p className="text-[30px] font-bold text-black">
                  {selectedSku
                    ? `${formatPrice(selectedSku.actualFee, true)}`
                    : itemInfo.minPrice === itemInfo.maxPrice
                    ? `${formatPrice(itemInfo.minPrice, true)}`
                    : `${formatPrice(itemInfo.minPrice, true)} ~ ${formatPrice(
                        itemInfo.maxPrice,
                        true
                      )}`}
                </p>

                <div className="my-[20px] flex h-[36px] flex-row rounded-[2px]">
                  <div className="flex items-center justify-center bg-[rgba(197,68,93,0.1)] px-[10px]">
                    <span className="text-[22px] font-bold text-secondary">
                      {i18n.t('Product.CommissionRate')} {rateStr()}
                    </span>
                  </div>
                </div>

                <div className="flex items-end">
                  <div className="flex h-[44px] items-center rounded-l-[4px] bg-primary px-[10px]">
                    <span className="text-[26px] text-white">
                      {i18n.t('Product.Earn')}
                    </span>
                  </div>
                  <img
                    src="/images/product/detail/earn_bg_tail.png"
                    className="h-[44px] w-[32px]"
                  ></img>
                  <div className="flex h-[36px] flex-1 items-center bg-[#FFF0EC] pl-[6px] pr-[14px]">
                    <span className="line-clamp-1 text-[28px] font-bold text-primary">
                      {selectedSku
                        ? `${formatPrice(
                            (Number(rateStr().split('%')[0]) / 100) *
                              selectedSku.actualFee,
                            true
                          )}`
                        : itemInfo.minEarn === itemInfo.maxEarn
                        ? `${formatPrice(itemInfo.minEarn, true)}`
                        : `${formatPrice(
                            itemInfo.minEarn,
                            true
                          )} ~ ${formatPrice(itemInfo.maxEarn, true)}`}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="mb-[28px] mt-[24px] h-[2px] w-full bg-background"></div>
          </div>

          {primarySpecLabel && (
            <>
              <p className="mb-[20px] text-[30px] text-black">
                {primarySpecLabel}
              </p>

              <div className="flex flex-wrap">
                {Object.keys(primarySpecValues).map((key, index) => (
                  <SkuButton
                    key={index}
                    title={key}
                    selectedTitle={selectedPrimarySpecName}
                    icon={Object.values(primarySpecValues)[index] as string}
                    onSelected={name => {
                      setSelectedPrimarySpecName(
                        name === selectedPrimarySpecName ? undefined : name
                      )
                    }}
                  ></SkuButton>
                ))}
              </div>
            </>
          )}

          {secondarySpecLabel && (
            <>
              <div className="mb-[28px] h-[2px] w-full bg-background"></div>

              <p className="mb-[20px] text-[30px] text-black">
                {secondarySpecLabel}
              </p>

              <div className="flex flex-wrap">
                {secondarySpecValues.map((title, index) => (
                  <SkuButton
                    key={index}
                    title={title as string}
                    selectedTitle={selectedSecondarySpecName}
                    onSelected={name => {
                      setSelectedSecondarySpecName(
                        name === selectedSecondarySpecName ? undefined : name
                      )
                    }}
                  ></SkuButton>
                ))}
              </div>
            </>
          )}

          <div className="mb-[28px] h-[2px] w-full bg-background"></div>

          <div className="flex justify-between">
            <div className="flex items-center">
              <span className="text-[30px] text-black">
                {i18n.t('Sample.数量')}:
              </span>
              {/* <span className="ml-[8px] text-[26px] text-gray9A">
                {i18n.t('Sample.最多申请件', {
                  count: isWhitelistProduct ? 50 : 10
                })}
              </span> */}
            </div>

            {/* <CountInput
              value={count}
              onChange={setCount}
              max={isWhitelistProduct ? 50 : 10}
              warning={warning}
            ></CountInput> */}
            <span className="text-[30px] text-black30">1</span>
          </div>

          <div className="mb-[24px] mt-[28px] flex items-center justify-between">
            <span className="text-[30px] text-black">
              {i18n.t('Product.样品总金额')}
            </span>
            <span className="text-[30px] font-bold text-primary">
              {formatPrice(totalPrice, true)}
            </span>
          </div>

          {warning && (
            <div
              className="mb-[24px] mt-[40px] text-[24px] text-black"
              dangerouslySetInnerHTML={{
                __html: i18n.t(
                  'Product.金额超过，通过率低！建议减少数量或申请其他商品！',
                  {
                    money: formatPrice(limitSampleAmount, true)
                  }
                )
              }}
            ></div>
          )}

          <div
            className="h-[80px]"
            style={{
              paddingBottom: webview
                ? px2rem(webview?.getData().bottomSafeArea + 80 + 24)
                : px2rem(80 + 24)
            }}
          ></div>

          <div className="fixed bottom-0 bg-white">
            <div
              onClick={async () => {
                if (selectedSku) {
                  await makeSureInApp()
                  toggle()
                  onSelectedSku?.(selectedSku, count)
                }
              }}
              className="mb-[24px] flex h-[80px] w-[702px] items-center justify-center rounded-[4px] touch-opacity"
              style={{
                backgroundColor: selectedSku
                  ? AppColors.primary
                  : 'rgba(254, 109, 69, 0.5)'
              }}
            >
              <span className="text-[32px] text-white">
                {i18n.t('Product.requestSample')}
              </span>
            </div>

            <div
              style={{
                height: webview?.getData().bottomSafeArea
              }}
            ></div>
          </div>

          <div
            className="absolute right-0 top-0 z-10 p-[24px] touch-opacity"
            onClick={toggle}
          >
            <img
              className="right-0 size-[32px]"
              src="/images/common/modal_close.png"
            ></img>
          </div>
        </div>
      </Popup>

      {/* 商家免费样品门槛弹窗 */}
    </>
  )
}
