'use client'
import React, { useEffect, useRef, useState } from 'react'
import { BottomBar as InsideBottomBar } from '../../components/bottomBar'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { GetSamplePopTypeVoType } from '@/app/api/api-uchoice/order/user/getSamplePopTypeVo/dtos'
import { BottomBarType } from './type'

const BottomBar = ({
  item,
  samplePopTypeVoType,
  type
}: {
  item: ItemInfoDto
  samplePopTypeVoType?: GetSamplePopTypeVoType
  type: BottomBarType
}) => {
  const [itemInfo, setItemInfo] = useState<ItemInfoDto>(item)

  const bottomBarRef = useRef()

  const onChangedItemInfo = value => {
    setItemInfo(value)
    const { id, isCollected } = value
    webview?.send(WebviewEvents.toggleCollect, {
      id,
      isCollected: `${isCollected}`
    })
  }

  return (
    <>
      <InsideBottomBar
        type={type}
        ref={bottomBarRef}
        itemInfo={itemInfo}
        onChangedItemInfo={onChangedItemInfo}
        samplePopTypeVoType={samplePopTypeVoType}
      ></InsideBottomBar>
    </>
  )
}

export default BottomBar
