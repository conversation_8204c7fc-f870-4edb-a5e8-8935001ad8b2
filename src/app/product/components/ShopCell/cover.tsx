'use client'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import React from 'react'
import { useRouter } from '@/lib/hooks/useRouter'
import { isRegionTH } from '@/lib/utils'

export const Cover = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  const router = useRouter()
  return (
    <div
      className="absolute inset-0"
      onClick={() => {
        if (isRegionTH()) {
          router.push(`/tiktok_data/shop/${itemInfo.tikTokSeller.id}`)
        }
      }}
    ></div>
  )
}
