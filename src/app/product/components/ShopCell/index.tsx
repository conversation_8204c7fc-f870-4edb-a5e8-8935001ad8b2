import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { Image } from '@/components/Image'
import React from 'react'
// import shop from '@/../public/images/product/detail/shop.png'
import show_arrow_icon from '@/../public/images/product/detail/show_arrow_icon.png'
import { i18n } from '@/lib/client/i18n'
import { isRegionTH, isRegionVN } from '@/lib/utils'
import { Cover } from './cover'

const ShopCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  return (isRegionTH() && itemInfo.tikTokSeller?.id) ||
    (isRegionVN() && itemInfo.shopName) ? (
    <div className="relative touch-opacity">
      <div className="flex items-center">
        <div className="flex flex-1 items-center">
          <span className="text-[24px] text-gray8A">
            {i18n.t('Product.店铺')}
          </span>
          {itemInfo.tikTokSeller?.image ? (
            <div className="mx-[16px] flex size-[48px] items-center justify-center rounded-[2px]">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <Image
                src={itemInfo.tikTokSeller?.image}
                className="size-[48px]"
              ></Image>
            </div>
          ) : (
            <div className="w-[16px]"></div>
          )}
          <span className="text-[28px] text-black">{itemInfo.shopName}</span>
        </div>
        {isRegionTH() && (
          <Image src={show_arrow_icon} className="h-[24px] w-[16px]"></Image>
        )}
      </div>
      <Cover itemInfo={itemInfo}></Cover>
    </div>
  ) : null
}

export default ShopCell
