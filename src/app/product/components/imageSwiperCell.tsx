'use client'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { Swiper } from 'react-vant'
import React, { useMemo } from 'react'
import { ImagePreview } from 'react-vant'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { Image } from '@/components/Image'

export const ImageSwiperCell = ({ itemInfo }: { itemInfo?: ItemInfoDto }) => {
  const urls = useMemo(() => {
    if (
      itemInfo?.skuResDtoList &&
      itemInfo.skuResDtoList[0]?.imgList.length > 0
    ) {
      if (itemInfo.itemSubsidyUrl) {
        return [
          itemInfo.itemSubsidyUrl,
          ...(itemInfo.skuResDtoList[0]?.imgList || {})
        ]
      } else {
        return itemInfo.skuResDtoList[0]?.imgList
      }
    }

    return []
  }, [itemInfo])

  return itemInfo ? (
    <Swiper
      indicator={(total, current) => (
        <div className="absolute bottom-[20px] left-[24px] flex h-[40px] items-center rounded-[32px] bg-[rgba(0,0,0,0.5)] px-[18px] text-red">
          <span className="text-[24px] text-white">{`${
            current + 1
          }/${total}`}</span>
        </div>
      )}
    >
      {urls.map((url, index) => (
        <Swiper.Item key={index}>
          <div
            onClick={async () => {
              if (
                !!(await webview?.send(WebviewEvents.hasJavaScriptHandler, {
                  handlerName: WebviewEvents.previewImages
                }))
              ) {
                webview?.send(WebviewEvents.previewImages, {
                  urls: urls.join(','),
                  initialIndex: index
                })
              } else {
                ImagePreview.open({
                  images: urls,
                  startPosition: index,
                  closeable: true
                })
              }
            }}
          >
            <Image className="size-[750px]" src={url}></Image>
          </div>
        </Swiper.Item>
      ))}
    </Swiper>
  ) : null
}
