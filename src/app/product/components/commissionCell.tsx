'use client'

/* eslint-disable @next/next/no-img-element */
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { formatPrice } from '@/lib/format'
import { i18n } from '@/lib/client/i18n'
import commission_cell_bg from '@/../public/images/product/detail/commission_cell_bg.png'
import commisssion_tiktok_uchoice from '@/../public/images/product/detail/commisssion_tiktok_uchoice.png'
import commisssion_tiktok from '@/../public/images/product/detail/commisssion_tiktok.png'
import upgrade_rocket from '@/../public/images/product/detail/upgrade_rocket.png'
import { Image } from '@/components/Image'

const CommissionCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  return (
    <div>
      {itemInfo.planCommission === itemInfo.commissionRate ? (
        <div className="px-[24px]">
          <div className="h-[232px] w-full rounded-[8px]">
            <div className="absolute">
              <Image
                src={commission_cell_bg}
                className=" h-[232px] w-[702px]"
              ></Image>
            </div>

            <div className="absolute flex h-[232px] w-[702px] flex-col justify-between">
              <div className="flex items-center pl-[16px] pt-[16px]">
                <div
                  className="mb-[4px] text-[26px] font-bold leading-[26px] text-black"
                  dangerouslySetInnerHTML={{
                    __html: i18n.t('Product.升级后 每件赚', {
                      earn:
                        itemInfo.minEarn === itemInfo.maxEarn
                          ? `${formatPrice(itemInfo.minEarn, true)}`
                          : `${formatPrice(
                              itemInfo.minEarn,
                              true
                            )} ~ ${formatPrice(itemInfo.maxEarn, true)}`
                    })
                  }}
                ></div>
              </div>

              <div className="flex flex-1 items-center">
                <div className="pl-[80px] pr-[92px]">
                  <Image
                    src={commisssion_tiktok_uchoice}
                    className="mr-[12px] mt-[4px] h-[32px] w-[104px]"
                  ></Image>
                </div>
                <div className="flex-1">
                  <div>
                    <p className="text-[28px] text-black">
                      {i18n.t('Product.独家高佣')}
                    </p>
                    <p className="text-[32px] font-bold text-primary">
                      {itemInfo.commissionRate}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="px-[24px]">
          <div className="h-[232px] w-full rounded-[8px]">
            <div className="absolute">
              <Image
                src={commission_cell_bg}
                className=" h-[232px] w-[702px]"
              ></Image>
            </div>

            <div className="absolute flex h-[232px] w-[702px] flex-col">
              <div className="flex h-[76px] items-center pl-[16px]">
                <div
                  className="mb-[4px] text-[26px] font-bold leading-[26px] text-black"
                  dangerouslySetInnerHTML={{
                    __html: i18n.t('Product.升级后 每件赚', {
                      earn:
                        itemInfo.minEarn === itemInfo.maxEarn
                          ? `${formatPrice(itemInfo.minEarn, true)}`
                          : `${formatPrice(
                              itemInfo.minEarn,
                              true
                            )} ~ ${formatPrice(itemInfo.maxEarn, true)}`
                    })
                  }}
                ></div>
              </div>

              <div className="flex flex-1 items-center justify-center pb-[20px]">
                <div className="flex px-[24px]">
                  <div className="flex">
                    <Image
                      src={commisssion_tiktok}
                      className="mr-[12px] mt-[4px] size-[32px]"
                    ></Image>
                    <div className="flex max-w-[200px] flex-col">
                      <p className="text-[28px] text-gray6E">
                        {i18n.t('Product.公开佣金')}
                      </p>
                      <p className="text-[28px] text-gray6E">
                        {itemInfo.planCommission}
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col justify-center px-[24px] pt-[8px]">
                    {/* <p className="text-[26px] text-black">upgrade</p> */}
                    <Image
                      src={upgrade_rocket}
                      className="h-[60px] w-[46px]"
                    ></Image>
                  </div>

                  <div className="flex">
                    <Image
                      src={commisssion_tiktok_uchoice}
                      className="mr-[12px] mt-[4px] h-[32px] w-[104px]"
                    ></Image>
                    <div className="flex max-w-[200px] flex-col">
                      <p className="text-[28px] text-black">
                        {i18n.t('Product.独家高佣')}
                      </p>
                      <p className="text-[32px] font-bold text-primary">
                        {itemInfo.commissionRate}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CommissionCell
