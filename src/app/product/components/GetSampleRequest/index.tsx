'use client'

import checkbox_on_green from '@/../public/images/common/checkbox_on_green.png'
import tip_icon from '@/../public/images/common/tip_icon.png'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { Image } from '@/components/Image'
import { i18n } from '@/lib/client/i18n'
import { formatCount } from '@/lib/format'
import { useLogined } from '@/lib/hooks/useLogined'
import { useMounted } from '@/lib/hooks/useMounted'
import { Trans } from 'react-i18next'

const GetSampleRequest = ({ itemInfo }: { itemInfo?: ItemInfoDto }) => {
  const logined = useLogined()
  const mounted = useMounted()

  return mounted && itemInfo?.shopReviewCriteria ? (
    <div className="px-[24px] pb-[32px] pt-[28px]">
      <div className="text-[30px] font-bold text-black30">
        {i18n.t('Product.商家领样要求')}
      </div>
      <div className="mt-[12px] text-[24px] text-gray6E">
        {i18n.t('Product.低于以下标准可能会被商家拒绝发样')}
      </div>

      {itemInfo.rate && itemInfo.rate.length > 0 && (
        <div className="mt-[20px] flex items-center rounded-[4px] bg-[rgba(255,141,19,0.14)] px-[16px] py-[14px]">
          <Image src={tip_icon} className="h-[24px] w-[24px]"></Image>
          <span className="ml-[8px] text-[24px] text-[#FF8D13]">
            <Trans
              i18nKey="Product.您的销售等级为Llevel，该商品Llevel通过率为percent%"
              components={{
                main: <span className="text-[#FF8D13]"></span>,
                red: <span className="text-[#FF3141]"></span>
              }}
              values={{
                level: formatCount(itemInfo.tier),
                percent: itemInfo.rate
              }}
            ></Trans>
          </span>
        </div>
      )}

      <div className="mt-[20px] rounded-[8px] bg-background px-[24px] py-[20px]">
        <div className="flex items-center">
          <Image src={checkbox_on_green} className="h-[28px] w-[28px]"></Image>
          <span className="ml-[8px] text-[24px] text-gray6E">
            {i18n.t('Product.粉丝量≥count', {
              count: formatCount(itemInfo?.shopReviewCriteria.fansThreshold)
            })}
          </span>
        </div>
        <div className="mt-[22px] flex items-center">
          <Image src={checkbox_on_green} className="h-[28px] w-[28px]"></Image>
          <span className="ml-[8px] text-[24px] text-gray6E">
            {i18n.t('Product.近30天销量≥count', {
              count: formatCount(itemInfo?.shopReviewCriteria.salesThreshold)
            })}
          </span>
        </div>
      </div>
    </div>
  ) : null
}

export default GetSampleRequest
