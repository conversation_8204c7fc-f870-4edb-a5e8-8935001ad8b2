'use client'

import { StickyHeader } from '@/components/StickyHeader'
import { Tabs } from '@/app/components/Tabs'
import { i18n } from '@/lib/client/i18n'
import { useEffect, useMemo, useState } from 'react'
import { getHotSellingVideos } from '@/app/api/api-uchoice/showcase/getHotSellingVideos/request'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { useMounted } from '@/lib/hooks/useMounted'
import { useDelayRender } from '@/lib/hooks/useDelayRender'

export const StickyTabs = ({
  itemInfo,
  notShowFreeSample
}: {
  itemInfo: ItemInfoDto
  notShowFreeSample: boolean
}) => {
  const mounted = useMounted()
  const [hasVideo, setHasVideo] = useState<boolean | null>()
  const delayed = useDelayRender(1000)

  useEffect(() => {
    fetchVideos()
  }, [])

  const fetchVideos = async () => {
    const { code, result } = await getHotSellingVideos({
      productId: itemInfo.productId
    })
    setHasVideo(code === 200 && result.length > 0)
  }

  const titles = useMemo(() => {
    const result = [
      i18n.t('Product.Tabs视频推荐'),
      notShowFreeSample
        ? i18n.t('Sample.购样流程')
        : i18n.t('Product.Tabs领样流程'),
      i18n.t('Product.Tabs免费素材'),
      i18n.t('Product.Tabs详情描述')
    ]
    if (!hasVideo) {
      result.shift()
    }

    if (!itemInfo.sourceMaterial) {
      const index = result.indexOf(i18n.t('Product.Tabs免费素材'))
      if (index !== -1) {
        result.splice(index, 1)
      }
    }
    return result
  }, [hasVideo, itemInfo.sourceMaterial])

  return mounted && delayed && hasVideo != null ? (
    <StickyHeader>
      <div className="bg-white pt-[12px]">
        <Tabs
          titles={titles}
          firstTabScrollToTop={false}
          lastTabScrollToBottom={false}
        ></Tabs>
        <div className="h-[2px] w-full bg-background"></div>
      </div>
    </StickyHeader>
  ) : null
}
