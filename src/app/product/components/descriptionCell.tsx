'use client'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { i18n } from '@/lib/client/i18n'
import React from 'react'

const DescriptionCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  return (
    <div className="px-[24px]">
      <div className="flex items-center justify-center pb-[24px] pt-[28px]">
        <div className="h-[2px] w-[80px] bg-grayEE"></div>
        <span className="text-[30px] text-black">
          {i18n.t('Product.ProductDescription')}
        </span>
        <div className="h-[2px] w-[80px] bg-grayEE"></div>
      </div>
      <div
        className="break-words"
        dangerouslySetInnerHTML={{
          __html: itemInfo.itemExtensionDto?.content || ''
        }}
      ></div>
    </div>
  )
}

export default DescriptionCell
