'use client'
import { i18n } from '@/lib/client/i18n'
import { copyText } from '@/lib/client/utils'
import React from 'react'
import { toast } from '@/lib/client/toast'
import { useMounted } from '@/lib/hooks/useMounted'
import { TtaItemSourceMaterialDetailVo } from '@/app/api/api-uchoice/tt/item/info/dtos'

export const Name = ({
  name,
  sourceMaterial,
  isHighPassRateProduct
}: {
  name: string
  sourceMaterial?: TtaItemSourceMaterialDetailVo
  isHighPassRateProduct: boolean
}) => {
  const mounted = useMounted()

  return (
    <div>
      <div className="flex items-center">
        {mounted && isHighPassRateProduct && (
          <div className="mb-[20px] mr-[12px] flex h-[32px] items-center justify-center rounded-[4px] bg-[rgba(34,179,94,0.10)] px-[10px]">
            <span className="text-[20px] font-bold leading-[22px] text-[#22B35E]">
              {i18n.t('Product.Easily get sample')}
            </span>
          </div>
        )}
        {mounted && sourceMaterial && (
          <div className="mb-[20px] flex h-[32px] items-center justify-center rounded-[4px] bg-[rgba(254,109,69,0.10)] px-[10px]">
            <span className="text-[20px] font-bold leading-[22px] text-primary">
              {i18n.t('Product.免费素材')}
            </span>
          </div>
        )}
      </div>

      <span
        className="break-words text-[30px] leading-[44px] text-black touch-opacity"
        onClick={() => {
          copyText(name)
        }}
      >
        {name}
      </span>
    </div>
  )
}
