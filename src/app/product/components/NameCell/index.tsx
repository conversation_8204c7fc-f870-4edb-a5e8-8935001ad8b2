import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { formatCount, formatMinMax, formatPrice } from '@/lib/format'
import React from 'react'
import { i18nS } from '@/lib/server/i18n'
import { isLanguageEN } from '@/lib/utils'
// @ts-ignore
import { Name } from './name'
// @ts-ignore
import { ProductId } from './productId'
import fire from '@/../public/images/product/list/fire.png'
import { Image } from '@/components/Image'

const NameCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  return (
    <div className="px-[24px]">
      <div className="mt-[20px]">
        <Name
          name={isLanguageEN() ? itemInfo.nameEn : itemInfo.name}
          sourceMaterial={itemInfo.sourceMaterial}
          isHighPassRateProduct={itemInfo.isHighPassRateProduct}
        ></Name>

        <div className="mt-[16px] flex justify-between">
          <span className="text-[32px] font-bold text-black">
            {formatMinMax(
              formatPrice(itemInfo.minPrice, true),
              formatPrice(itemInfo.maxPrice, true)
            )}
          </span>

          <div className="flex items-center">
            <Image className="mr-[8px] h-[24px] w-[20px]" src={fire}></Image>
            <span className="text-[24px] text-black">
              {i18nS.t('Product.xxxSold', { xxx: itemInfo.salesStr })}
            </span>
          </div>
        </div>

        <div className="mt-[24px] flex items-center">
          <span className="mr-[12px] text-[26px] text-gray6E">
            {i18nS.t('Product.ProductID')} {itemInfo.productId}
          </span>
          <ProductId productId={itemInfo.productId}></ProductId>
        </div>

        <div className="mt-[24px] flex flex-wrap gap-x-[24px] gap-y-[8px]">
          <div className="flex items-center">
            <span className="text-[24px] text-gray6E">
              {i18nS.t('Product.30-Day Sales')}
            </span>
            <div className="ml-[8px] flex h-[34px] items-center justify-center bg-[rgba(197,68,93,0.1)] px-[6px]">
              <span className="text-[24px] text-secondary">
                {itemInfo.salesForLast30DaysStr}
              </span>
            </div>
          </div>

          {itemInfo.salesForTwoWeeksRate !== '0.0%' &&
            !itemInfo.salesForTwoWeeksRate.includes('-') && (
              <div className="flex items-center">
                <span className="text-[24px] text-gray6E">
                  {i18nS.t('Product.增长率')}
                </span>
                <div className="ml-[8px] flex h-[34px] items-center justify-center bg-[rgba(197,68,93,0.1)] px-[6px]">
                  <span className="text-[24px] text-secondary">
                    {itemInfo.salesForTwoWeeksRate}
                  </span>
                </div>
              </div>
            )}

          <div className="flex items-center">
            <span className="text-[24px] text-gray6E">
              {i18nS.t('Product.Stock')}
            </span>
            <div className="ml-[8px] flex h-[34px] items-center justify-center bg-[rgba(197,68,93,0.1)] px-[6px]">
              <span className="text-[24px] text-secondary">
                {formatCount(itemInfo.totalStock)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NameCell
