'use client'
import { i18n } from '@/lib/client/i18n'
import { copyText } from '@/lib/client/utils'
import React from 'react'
import copy from '@/../public/images/common/copy.png'
import { Image } from '@/components/Image'
import { toast } from '@/lib/client/toast'

export const ProductId = ({ productId }: { productId: string }) => {
  return (
    <div
      className="touch-opacity"
      onClick={() => {
        copyText(productId)
      }}
    >
      <Image src={copy} className="size-[32px]"></Image>
    </div>
  )
}
