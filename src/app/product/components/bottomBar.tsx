'use client'
/* eslint-disable @next/next/no-img-element */
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useLayoutEffect,
  useRef,
  useState
} from 'react'
import { webview } from '@/lib/client/webview'
import { getToken, makeSureInApp, px2rem } from '@/lib/client/utils'
import { WebViewToPages, WebviewEvents } from '@/lib/client/webview/events'
import { updateMemberCollect } from '@/app/api/api-uchoice/tt/h5/updateMemberCollect'
import { loading } from '@/lib/client/loading'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { i18n } from '@/lib/client/i18n'
import { AppColors } from '@/lib/const'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { SkuModal } from './skuModal'
import { CopiedModal } from './copiedModal'
import { TikTokItemDetailVo } from '@/app/api/api-uchoice/tiktok/item/detail/dtos'
import qs from 'qs'
import { useRouter } from '@/lib/hooks/useRouter'
import liked from '@/../public/images/product/detail/liked.png'
import like from '@/../public/images/product/detail/like.png'
import buy from '@/../public/images/product/detail/buy.png'
import link from '@/../public/images/product/detail/link.png'
import popover from '@/../public/images/product/detail/popover.png'
import { Image } from '@/components/Image'
import { buySampleOrder } from '@/app/api/api-uchoice/order/user/buySampleOrder/request'
import { addToShowCase, checkAuth } from '@/lib/actions'
import { getSamplePopTypeVo } from '@/app/api/api-uchoice/order/user/getSamplePopTypeVo/request'
import { GetSamplePopTypeVoType } from '@/app/api/api-uchoice/order/user/getSamplePopTypeVo/dtos'
import { FirstBuyAlert } from './FirstBuyAlert'
import { UnlockAlert } from './UnlockAlert'
import { UnlockedAlert } from './UnlockedAlert'
import { manualCrawlShowcaseProduct } from '@/app/api/api-uchoice/order/user/manualCrawlShowcaseProduct/request'
import { BottomBarType } from './BottomBar/type'
import gift_box from '@/../public/images/product/detail/gift_box.png'
import { isLanguageEN, isLanguageTH } from '@/lib/utils'
import { toast } from '@/lib/client/toast'
import { NewSampleProcessAlert } from './NewSampleProcessAlert'
import { FreeSampleThresholdModal } from './FreeSampleThresholdModal'
import { checkSampleItemThreshold } from '@/app/api/api-uchoice/order/user/checkSampleItemThreshold/request'
import { CheckSampleThresholdVo } from '@/app/api/api-uchoice/order/user/checkSampleItemThreshold/dtos'

const tiktokDataStatisticPrefix = 'tiktok_data_'

export interface BottomBarProps {
  type: BottomBarType
  itemInfo: ItemInfoDto | TikTokItemDetailVo
  onChangedItemInfo(itemInfo: ItemInfoDto | TikTokItemDetailVo): void
  samplePopTypeVoType?: GetSamplePopTypeVoType
}

const Index = (
  {
    itemInfo,
    onChangedItemInfo,
    type = BottomBarType.pdp,
    samplePopTypeVoType
  }: BottomBarProps,
  ref
) => {
  const [skuModalVisible, setSkuModalVisible] = useState(false)
  const toggleSkuModal = () => setSkuModalVisible(visible => !visible)

  const [copiedModalVisible, setCopiedModalVisible] = useState(false)

  const popupRef = useRef<boolean>(false)

  const router = useRouter()

  const [buyAlertType, setBuyAlertType] = useState<
    GetSamplePopTypeVoType | undefined
  >(samplePopTypeVoType)
  const [firstBuyAlertVisible, setFirstBuyAlertVisible] = useState(false)

  const [newSampleProcessRequested, setNewSampleProcessRequested] =
    useState(true)

  // 新增门槛弹窗相关状态
  const [showThresholdModal, setShowThresholdModal] = useState(false)
  const [thresholdData, setThresholdData] =
    useState<CheckSampleThresholdVo | null>(null)

  useEffect(() => {
    const query = window.location.search.replace('?', '')
    const popup = qs.parse(query)['popup']
    popupRef.current = popup === 'true'

    // @ts-ignore
    window.showSkuModal = () => {
      handleOpenSkuModal()
    }
  }, [])

  useEffect(() => {
    if (
      popupRef.current &&
      itemInfo.id &&
      itemInfo.sampleTotal &&
      itemInfo.sampleTotal > 0
    ) {
      popupRef.current = false
      handleOpenSkuModal()
    }
  }, [popupRef.current, itemInfo])

  const toggleCopiedModal = () => {
    if (!copiedModalVisible) {
      statistic({
        eventName: handledStatisticEventName(EventName.pdp_copy_link),
        param: {
          id: itemInfo.id
        }
      })
    }

    setCopiedModalVisible(visible => !visible)
  }

  useImperativeHandle(ref, () => ({
    showSkuModal: () => setSkuModalVisible(true)
  }))

  const handledStatisticEventName = eventName => {
    const prefix =
      type === BottomBarType.tiktokDataPdp ? tiktokDataStatisticPrefix : ''
    return `${prefix}${eventName}`
  }

  const toggleCollect = async () => {
    statistic({
      eventName: handledStatisticEventName(EventName.pdp_like),
      param: {
        id: itemInfo.id
      }
    })

    await makeSureInApp()

    loading.show({ userInteractive: true })
    const { code } = await updateMemberCollect({
      id: itemInfo.id,
      productId: itemInfo.productId,
      operation: itemInfo.isCollected ? 2 : 1
    })
    loading.hide()

    if (code === 200) {
      onChangedItemInfo({
        ...itemInfo,
        isCollected: !itemInfo.isCollected
      })
    }
  }

  const onBuy = async () => {
    const link = itemInfo.affiliateShareLink || itemInfo.sourceLink

    if (webview) {
      webview?.send(WebviewEvents.launch, {
        url: link
      })
    } else {
      window.location.href = link
    }
  }

  const handleOpenSkuModal = () => {
    statistic({
      eventName: handledStatisticEventName(EventName.pdp_apply_samples),
      param: {
        id: itemInfo.id
      }
    })
    checkAuth(async () => {
      if (!itemInfo.isNewSample) {
        // 先检查用户是否需要展示门槛弹窗
        try {
          const token = await getToken()
          if (token) {
            const { code, result } = await checkSampleItemThreshold({
              id:
                'isTap' in itemInfo && itemInfo.isTap
                  ? itemInfo.ttaItemId
                  : itemInfo.id
            })

            if (code === 200 && result) {
              if (result.showTip) {
                // 需要展示门槛弹窗
                setThresholdData(result)
                setShowThresholdModal(true)
                return
              }
              // 不需要展示门槛弹窗，直接打开SKU弹窗
            }
          }
        } catch (error) {
          console.error('Failed to check threshold:', error)
          // 发生错误，为避免阻塞流程，直接打开SKU弹窗
        }

        // 无门槛或检查失败，直接打开SKU弹窗
        setSkuModalVisible(true)
      } else {
        // 首次体验新申样流程就显示弹窗

        let requested
        if (
          !!webview?.send(WebviewEvents.hasJavaScriptHandler, {
            handlerName: WebviewEvents.getStorage
          })
        ) {
          requested = await webview?.send(WebviewEvents.getStorage, {
            key: 'newSampleProcessRequested'
          })
        } else {
          requested = localStorage.getItem('newSampleProcessRequested')
        }

        if (requested) {
          if (webview) {
            webview?.send(WebviewEvents.launch, {
              url: itemInfo.link
            })
          } else {
            // 为了引导下载app，然后再去打开tiktok这个link
            await makeSureInApp()
          }
        } else {
          if (
            !!webview?.send(WebviewEvents.hasJavaScriptHandler, {
              handlerName: WebviewEvents.setStorage
            })
          ) {
            webview?.send(WebviewEvents.setStorage, {
              key: 'newSampleProcessRequested',
              value: '1'
            })
          } else {
            localStorage.setItem('newSampleProcessRequested', '1')
          }

          setNewSampleProcessRequested(requested)
        }
      }
    })
  }

  // 处理"仍要申请"按钮点击
  const handleStillApply = () => {
    setShowThresholdModal(false)
    // 关闭门槛弹窗后打开SKU弹窗
    setSkuModalVisible(true)
  }

  // 处理"我知道了"按钮点击
  const handleKnow = () => {
    setShowThresholdModal(false)
  }

  useEffect(() => {
    fetchSamplePopTypeVo()

    window.appIntoForground = () => fetchSamplePopTypeVo()
  }, [])

  const fetchSamplePopTypeVo = async () => {
    if (await getToken()) {
      const { code, result } = await getSamplePopTypeVo(itemInfo.productId)
      if (code === 200 && result?.type) {
        setBuyAlertType(result.type)
      }
    }
  }

  return buyAlertType ? (
    <>
      <div className="fixed bottom-0 z-10">
        {(itemInfo.itemSubsidyDescEn || itemInfo.itemSubsidyDescTh) && (
          <div
            className="w-[750px]"
            style={{
              background:
                'linear-gradient(270deg, #FFEAE3 0%, #FEECE6 17%, #FFF5F3 27%, #FEE8E8 40%, #FFE1E1 58%, #F5E5ED 81%, #E8E8FC 100%)',
              border: '0.05rem solid', // 2px / 40
              borderImage:
                'linear-gradient(270deg, rgba(255, 228, 233, 1), rgba(252, 221, 227, 1), rgba(255, 228, 233, 1)) 0.05 0.05', // 2px / 40
              display: 'flex',
              alignItems: 'center',
              padding: '0.4rem 0.5rem' // 16px / 40 and 20px / 40
            }}
          >
            <Image className="mr-[16px] w-[32px]" src={gift_box}></Image>
            <div className="flex-1">
              <div
                className="line-clamp-6 text-ellipsis break-all text-[24px] font-bold text-black30"
                dangerouslySetInnerHTML={{
                  // @ts-ignore
                  __html: (isLanguageEN()
                    ? itemInfo.itemSubsidyDescEn
                    : itemInfo.itemSubsidyDescTh
                  ).replace(/\n/g, '<br />')
                }}
              />
            </div>
          </div>
        )}

        {itemInfo &&
          itemInfo.id &&
          (!('isTap' in itemInfo) || itemInfo.isTap) && (
            <div className="w-[750px]">
              <div className=" w-[750px] bg-white">
                <div className="flex h-[124px] items-center pr-[24px]">
                  <div
                    className="flex flex-1 pl-[28px] pr-[24px]"
                    style={{
                      justifyContent:
                        type == BottomBarType.pdpWithOutRequestSample ||
                        type == BottomBarType.tiktokDataPdpWithOutRequestSample
                          ? 'normal'
                          : 'space-between'
                    }}
                  >
                    <div
                      className="flex flex-col items-center touch-opacity"
                      onClick={toggleCollect}
                    >
                      <Image
                        className="size-[48px]"
                        src={itemInfo.isCollected ? liked : like}
                      ></Image>
                      <span className="mt-[4px] text-center text-[20px] leading-[22px] text-gray6E">
                        {itemInfo.isCollected
                          ? i18n.t('Product.收藏')
                          : i18n.t('Product.收藏')}
                      </span>
                    </div>
                    {type !== BottomBarType.pdpWithOutRequestSample &&
                    type !== BottomBarType.tiktokDataPdpWithOutRequestSample ? (
                      <div
                        className="flex flex-col items-center touch-opacity"
                        onClick={() => {
                          statistic({
                            eventName: handledStatisticEventName(
                              EventName.pdp_buy
                            ),
                            param: {
                              id: itemInfo.id
                            }
                          })
                          checkAuth(onBuy)
                        }}
                      >
                        <Image src={buy} className="size-[48px]"></Image>
                        <span className="mt-[4px] text-center text-[20px] leading-[22px] text-gray6E">
                          {i18n.t('Product.购买样品')}
                        </span>
                      </div>
                    ) : (
                      <div className="w-[36px]"></div>
                    )}
                    <div
                      className="flex flex-col items-center touch-opacity"
                      onClick={toggleCopiedModal}
                    >
                      <Image src={link} className="size-[48px]"></Image>
                      <span className="mt-[4px] text-center text-[20px]  leading-[22px] text-gray6E">
                        {i18n.t('Product.复制链接')}
                      </span>
                    </div>
                  </div>
                  {type === BottomBarType.pdpWithOutRequestSample ||
                  type === BottomBarType.tiktokDataPdpWithOutRequestSample ? (
                    <div className="flex">
                      <div className="relative">
                        <div
                          className="flex h-[80px] w-[260px] items-center justify-center rounded-l-[4px] border-2 border-r-0 border-solid border-primary px-[14px] touch-opacity"
                          onClick={() => {
                            statistic({
                              eventName: handledStatisticEventName(
                                EventName.pdp_buy
                              ),
                              param: {
                                id: itemInfo.id
                              }
                            })
                            checkAuth(() => {
                              buySampleOrder({
                                productId: itemInfo.productId,
                                // @ts-ignore
                                campaignId: itemInfo.campaignId
                              })
                              if (
                                buyAlertType === GetSamplePopTypeVoType.firstBuy
                              ) {
                                setFirstBuyAlertVisible(true)
                              } else {
                                onBuy()
                              }
                            })
                          }}
                        >
                          <span className="line-clamp-2 text-center text-[24px] font-bold text-primary">
                            {i18n.t('Sample.成本购样')}
                          </span>
                        </div>
                        {/* 悬浮 */}
                        {!(
                          itemInfo.itemSubsidyDescEn ||
                          itemInfo.itemSubsidyDescTh
                        ) &&
                          itemInfo.isShowBuyTip && (
                            <div className="absolute left-[-40px] top-[-162px]">
                              <Image
                                src={popover}
                                className="h-[176px] w-[360px]"
                              ></Image>
                              <div className="absolute inset-x-[30px] top-[54px] text-[24px] text-primary">
                                {i18n.t('Sample.❗️出单送申样机会+出单教程❗️')}
                              </div>
                            </div>
                          )}
                      </div>
                      <div
                        className="flex h-[80px] w-[260px] items-center justify-center rounded-r-[4px] bg-primary px-[14px] touch-opacity"
                        onClick={() => {
                          statistic({
                            eventName: handledStatisticEventName(
                              EventName.pdp_add_to_showcase
                            ),
                            param: {
                              id: itemInfo.id
                            }
                          })
                          checkAuth(() => addToShowCase(itemInfo, 'pdp'))
                        }}
                      >
                        <span className="line-clamp-2 text-center text-[24px] font-bold text-white">
                          {i18n.t('Product.添加橱窗')}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="flex">
                      <div
                        className="flex h-[80px] w-[234px] items-center justify-center rounded-l-[4px] border-2 border-r-0 border-solid border-primary px-[14px] touch-opacity"
                        onClick={() => {
                          statistic({
                            eventName: handledStatisticEventName(
                              EventName.pdp_add_to_showcase
                            ),
                            param: {
                              id: itemInfo.id
                            }
                          })
                          checkAuth(() => addToShowCase(itemInfo, 'pdp'))
                        }}
                      >
                        <span className="line-clamp-2 text-center text-[24px] font-bold text-primary">
                          {i18n.t('Product.添加橱窗')}
                        </span>
                      </div>
                      <div className="relative">
                        <div
                          className="flex h-[80px] w-[234px] items-center justify-center rounded-r-[4px] px-[14px] touch-opacity"
                          style={{
                            backgroundColor:
                              itemInfo?.skuList?.length == 0
                                ? AppColors.grayD9
                                : AppColors.primary
                          }}
                          onClick={handleOpenSkuModal}
                        >
                          <span className="line-clamp-2 text-center text-[24px] font-bold text-white">
                            {i18n.t('Product.requestSample')}
                          </span>
                        </div>
                        {itemInfo.isNewSample && (
                          <div className="absolute left-[12px] top-[-28px] flex h-[48px] items-center justify-center rounded-[24px] rounded-br-[0] bg-[#4ECD2E] px-[20px]">
                            <span className="text-[24px] font-medium text-white">
                              {i18n.t('Product.极速')}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                <div
                  style={{
                    height: webview
                      ? `${px2rem(webview?.getData().bottomSafeArea)}`
                      : 0
                  }}
                ></div>
              </div>
            </div>
          )}
        {itemInfo && 'isTap' in itemInfo && !itemInfo.isTap && (
          <>
            <div className="w-[750px]">
              <div className="bg-white">
                <div className="flex h-[124px] items-center pr-[24px]">
                  <div
                    className="flex flex-1 flex-col items-center touch-opacity"
                    onClick={toggleCollect}
                  >
                    <Image
                      className="size-[48px]"
                      src={itemInfo.isCollected ? liked : like}
                    ></Image>
                    <span className="mt-[4px] text-[20px] text-gray6E">
                      {itemInfo.isCollected
                        ? i18n.t('Product.收藏')
                        : i18n.t('Product.收藏')}
                    </span>
                  </div>
                  <div
                    className="flex h-[80px] w-[194px] items-center justify-center rounded-[8px] border-2 border-primary touch-opacity"
                    onClick={toggleCopiedModal}
                  >
                    <span className="text-[30px] font-bold text-primary">
                      {i18n.t('Product.复制链接')}
                    </span>
                  </div>
                  <div className="w-[20px]"></div>
                  <div
                    className="flex h-[80px] w-[394px] items-center justify-center rounded-[8px] bg-primary touch-opacity"
                    onClick={() =>
                      router.push(`/similar/${itemInfo.productId}`)
                    }
                  >
                    <span className="text-center text-[30px] font-bold text-white">
                      {i18n.t('Product.相似更高佣')}
                    </span>
                  </div>
                </div>

                <div
                  style={{
                    height: webview
                      ? `${px2rem(webview?.getData().bottomSafeArea)}`
                      : 0
                  }}
                ></div>
              </div>
            </div>
          </>
        )}
      </div>

      {itemInfo?.skuList?.length > 0 && (
        <SkuModal
          visible={skuModalVisible}
          toggle={toggleSkuModal}
          itemInfo={itemInfo}
          onSelectedSku={async (sku, count) => {
            // 因为数据详情和老的详情的接口字段不一样，做下兼容
            const item = itemInfo as TikTokItemDetailVo

            let convertedItemInfo = {}

            if ('isTap' in itemInfo) {
              convertedItemInfo = {
                id: `${item.ttaItemId}`,
                productId: item.productId,
                name: item.productName,
                nameEn: item.productName,
                itemType: 1,
                campaignId: '',
                homeImgUrl: item.homeImgUrl,
                skuResDtoList: [],
                itemExtensionDto: {
                  id: '',
                  content: ''
                },
                skuList: item.skuList,
                visible: true,
                totalStock: 1,
                promotionEndTime: 1,
                salesStr: '',
                commissionRate: item.creatorCommissionPercentStr,
                planCommission: '',
                maxPrice: 1.0,
                minPrice: 1.0,
                minEarn: 1.0,
                maxEarn: 1.0,
                isCollected: false,
                link: '',
                sourceLink: '',
                isShowImg: false,
                shopCode: '',
                shopName: '',
                itemLabels: []
              }
            } else {
              convertedItemInfo = {
                ...itemInfo,
                sellerId: itemInfo.sellerId ? `${itemInfo.sellerId}` : undefined
              }
            }

            await webview?.send(WebviewEvents.toPage, {
              name: WebViewToPages.sampleApply,
              params: {
                itemInfo: convertedItemInfo,
                sku,
                count: `${count}`
              }
            })
          }}
        ></SkuModal>
      )}

      <CopiedModal
        itemInfo={itemInfo}
        visible={copiedModalVisible}
        toggle={toggleCopiedModal}
      ></CopiedModal>

      <FirstBuyAlert
        visible={
          buyAlertType === GetSamplePopTypeVoType.firstBuy &&
          firstBuyAlertVisible
        }
        onClose={() => {
          setFirstBuyAlertVisible(false)
        }}
        onBuy={() => {
          setFirstBuyAlertVisible(false)
          onBuy()
        }}
      ></FirstBuyAlert>

      <UnlockAlert
        productId={itemInfo.id}
        visible={buyAlertType === GetSamplePopTypeVoType.needUnlock}
        onClose={() => setBuyAlertType(GetSamplePopTypeVoType.none)}
        onAddToShowCase={() => {
          statistic({
            eventName: handledStatisticEventName(EventName.pdp_add_to_showcase),
            param: {
              id: itemInfo.id
            }
          })
          checkAuth(() =>
            addToShowCase(itemInfo, 'pdp_unlock_alert', () => {
              manualCrawlShowcaseProduct({
                productId: itemInfo.productId,
                // @ts-ignore
                campaignId: itemInfo.campaignId
              })
            })
          )
        }}
      ></UnlockAlert>

      <UnlockedAlert
        productId={itemInfo.productId}
        id={itemInfo.id}
        visible={buyAlertType === GetSamplePopTypeVoType.unlocked}
        onClose={() => setBuyAlertType(GetSamplePopTypeVoType.none)}
      ></UnlockedAlert>

      {!newSampleProcessRequested && itemInfo.isNewSample && (
        <NewSampleProcessAlert></NewSampleProcessAlert>
      )}

      <FreeSampleThresholdModal
        visible={showThresholdModal}
        toggle={() => setShowThresholdModal(false)}
        onStillApply={handleStillApply}
        onKnow={handleKnow}
        thresholdData={thresholdData || undefined}
      ></FreeSampleThresholdModal>
    </>
  ) : null
}

export const BottomBar = forwardRef(Index)
