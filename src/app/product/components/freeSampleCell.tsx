'use client'
/* eslint-disable @next/next/no-img-element */
import { i18n } from '@/lib/client/i18n'
import React from 'react'
import { useRouter } from '@/lib/hooks/useRouter'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { TransComponent } from '@/app/components/Trans'

export const FreeSampleCell = () => {
  const router = useRouter()

  return (
    <div className="px-[24px]">
      <div className="mb-[24px]  flex items-center justify-between">
        <TransComponent
          en={
            <img
              className=" h-[48px] w-[176px]"
              src="/images/product/detail/free_sample_en.png"
            ></img>
          }
          th={
            <img
              className=" h-[48px] w-[218px]"
              src="/images/product/detail/free_sample_th.png"
            ></img>
          }
          vi={
            <img
              className=" h-[48px] w-[205px]"
              src="/images/product/detail/free_sample_vi.png"
            ></img>
          }
        ></TransComponent>
        <div
          className="flex  items-center justify-between"
          onClick={() => {
            statistic({
              eventName: EventName.pdp_show_detail_sample
            })
            router.push('/sample_guide/page')
          }}
        >
          <span className="  text-[24px]  text-black">
            {i18n.t('Product.新申样路径')}
          </span>
          <img
            className="ml-[8px] h-[24px] w-[16px]"
            src="/images/product/detail/show_arrow_icon.png"
          ></img>
        </div>
      </div>

      <div className="relative h-[284px] w-full">
        <img
          src="/images/product/detail/reward_bg.png"
          className="absolute left-[36px] top-[22px] h-[160px] w-[650px]"
        />
        <div className="absolute h-[284px] w-full pr-[58px]">
          <div className="flex h-[142px] justify-between ">
            <div className="flex flex-col items-center">
              <img
                src="/images/product/detail/reward_icon0.png"
                className="size-[64px]"
              ></img>
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Product.申请样品')}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <img
                src="/images/product/detail/reward_icon1.png"
                className="size-[64px]"
              ></img>
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Product.跳转TikTok')}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <img
                src="/images/product/detail/reward_icon2.png"
                className="size-[64px]"
              ></img>
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Product.TikTok申样')}
              </span>
            </div>
          </div>

          <div className="flex h-[142px] justify-between">
            <div className="flex flex-col items-center">
              <img
                src="/images/product/detail/reward_icon3.png"
                className="size-[64px]"
              ></img>
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Product.完成带货')}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <img
                src="/images/product/detail/reward_icon4.png"
                className="size-[64px]"
              ></img>
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black">
                {i18n.t('Product.发视频/直播')}
              </span>
            </div>
            <div className="flex flex-col items-center opacity-0">
              <span className="mt-[12px] w-[140px] text-center text-[24px] leading-[34px] text-black"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
