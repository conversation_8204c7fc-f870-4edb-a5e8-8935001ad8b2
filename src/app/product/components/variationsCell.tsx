'use client'
/* eslint-disable @next/next/no-img-element */
import { i18n } from '@/lib/client/i18n'
import React from 'react'

const VariationsCell = () => {
  return (
    <div>
      <div className="h-[12px] w-full bg-background"></div>

      <div
        className="flex h-[88px] items-center justify-between px-[24px] touch-opacity"
        // @ts-ignore
        onClick={() => window?.showSkuModal()}
      >
        <div className="flex items-center">
          {/* <img
          className="mr-[16px] size-[48px]"
          src="/images/product/detail/variations.png"
        ></img> */}
          <span className="text-[30px] font-bold text-black02">
            {i18n.t('Product.Variation')}
          </span>
        </div>

        <img
          src="/images/common/arrow_more.png"
          className="h-[24px] w-[16px]"
        />
      </div>
      <div className="h-[12px] w-full bg-background"></div>
    </div>
  )
}

export default VariationsCell
