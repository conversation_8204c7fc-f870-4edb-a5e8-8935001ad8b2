'use client'

import grow from '@/../public/images/product/detail/grow.png'
import { Image } from '@/components/Image'
import { useState, useEffect } from 'react'
import { Tabs } from './components/Tabs'
import { SellingPoints } from './components/SellingPoints'
import { MediaMaterial } from './components/MediaMaterial'
import { ScriptsReference } from './components/ScriptsReference'
import { useMounted } from '@/lib/hooks/useMounted'
import { HappyAlert } from '@/components/HappyAlert'
import { toast } from '@/lib/client/toast'
import { i18n } from '@/lib/client/i18n'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { Trans } from 'react-i18next'
import { formatCount } from '@/lib/format'
import { TabSection } from '@/app/components/Tabs/TabSection'

const MaterialCell = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  const { sourceMaterial, id } = itemInfo

  const mounted = useMounted()
  const [tabIndex, setTabIndex] = useState(0)
  const [copyModalVisible, setCopyModalVisible] = useState(false)
  const [imageDownloadModalVisible, setImageDownloadModalVisible] =
    useState(false)
  const [videoDownloadModalVisible, setVideoDownloadModalVisible] =
    useState(false)
  const [unlockModalVisible, setUnlockModalVisible] = useState(false)

  const handleShowCopyModal = () => {
    const hasShown = localStorage.getItem('copyModalShown')
    if (!hasShown) {
      setCopyModalVisible(true)
      localStorage.setItem('copyModalShown', 'true')
    } else {
      toast(i18n.t('Common.Copied'))
    }
  }

  const handleCloseCopyModal = () => {
    setCopyModalVisible(false)
  }

  const handleShowImageDownloadModal = () => {
    const hasShown = localStorage.getItem('imageDownloadModalShown')
    if (!hasShown) {
      setImageDownloadModalVisible(true)
      localStorage.setItem('imageDownloadModalShown', 'true')
    }
  }

  const handleCloseImageDownloadModal = () => {
    setImageDownloadModalVisible(false)
  }

  const handleShowVideoDownloadModal = () => {
    const hasShown = localStorage.getItem('videoDownloadModalShown')
    if (!hasShown) {
      setVideoDownloadModalVisible(true)
      localStorage.setItem('videoDownloadModalShown', 'true')
    }
  }

  const handleCloseVideoDownloadModal = () => {
    setVideoDownloadModalVisible(false)
  }

  const handleShowUnlockModal = () => {
    const hasShown = localStorage.getItem('unlockModalShown')
    if (!hasShown) {
      setUnlockModalVisible(true)
      localStorage.setItem('unlockModalShown', 'true')
    }
  }

  const handleCloseUnlockModal = () => {
    setUnlockModalVisible(false)
  }

  return mounted && sourceMaterial ? (
    <>
      <TabSection></TabSection>

      <div className="px-[24px] pb-[24px]">
        <div className="rounded-[4px] border-[2px] border-grayEE px-[24px] pb-[24px] pt-[20px]">
          <div className="mb-[20px] flex items-center">
            <span className="mr-[16px] text-[28px] font-bold text-black30">
              {i18n.t('Product.创作助力，提升销量')}
            </span>
            <Image src={grow} className="h-[40px] w-[40px]"></Image>
          </div>
          <span className="text-[22px] text-primary">
            <Trans
              i18nKey="Product.xx达人使用，累计出单xx件！"
              values={{
                creators: formatCount(sourceMaterial.anchorCount),
                orders: formatCount(sourceMaterial.sales)
              }}
            ></Trans>
          </span>

          <Tabs tabIndex={tabIndex} onChange={setTabIndex}></Tabs>

          {tabIndex === 0 && (
            <SellingPoints
              sellPointList={sourceMaterial?.sellPointList || []}
              onCopied={handleShowCopyModal}
            ></SellingPoints>
          )}
          {tabIndex === 1 && (
            <MediaMaterial
              sources={
                sourceMaterial?.imageSourceMaterialList?.map(
                  item => item.imageUrl
                ) || []
              }
              covers={
                sourceMaterial?.imageSourceMaterialList?.map(
                  item => item.imageUrl
                ) || []
              }
              onDownload={handleShowImageDownloadModal}
            ></MediaMaterial>
          )}
          {tabIndex === 2 && (
            <MediaMaterial
              sources={
                sourceMaterial?.videoSourceMaterialList?.map(
                  item => item.videoUrl
                ) || []
              }
              covers={
                sourceMaterial?.videoSourceMaterialList?.map(
                  item => item.coverUrl
                ) || []
              }
              sourceType="video"
              onDownload={handleShowVideoDownloadModal}
            ></MediaMaterial>
          )}
          {tabIndex === 3 && (
            <ScriptsReference
              briefAccounts={sourceMaterial?.briefAccount || []}
              brief={sourceMaterial?.brief || '' || ''}
              onUnlock={handleShowUnlockModal}
            ></ScriptsReference>
          )}
        </div>
      </div>
      <HappyAlert
        visible={copyModalVisible}
        title={i18n.t('Product.复制成功')}
        content={
          <div className="flex flex-col justify-center">
            <div>
              <Trans
                i18nKey="Product.①可用于视频/直播销售话术"
                components={{
                  red: <span className="text-primary"></span>
                }}
              ></Trans>
            </div>
            <div>
              <Trans
                i18nKey="Product.②粘贴至内容description中"
                components={{
                  red: <span className="text-primary"></span>
                }}
              ></Trans>
            </div>
          </div>
        }
        confirmBtn={{ onClick: handleCloseCopyModal }}
        onMaskClick={handleCloseCopyModal}
      ></HappyAlert>
      <HappyAlert
        visible={imageDownloadModalVisible}
        title={i18n.t('Product.下载成功')}
        content={
          <Trans
            i18nKey={'Product.可用于视频剪辑中，更有利于展示商品细节！'}
            components={{
              red: <span className="text-primary"></span>
            }}
          ></Trans>
        }
        confirmBtn={{ onClick: handleCloseImageDownloadModal }}
        onMaskClick={handleCloseImageDownloadModal}
      ></HappyAlert>
      <HappyAlert
        visible={videoDownloadModalVisible}
        title={i18n.t('Product.下载成功')}
        content={
          <Trans
            i18nKey={'Product.可用于视频剪辑中，更有利于展示商品细节！'}
            components={{
              red: <span className="text-primary"></span>
            }}
          ></Trans>
        }
        confirmBtn={{ onClick: handleCloseVideoDownloadModal }}
        onMaskClick={handleCloseVideoDownloadModal}
      ></HappyAlert>
      <HappyAlert
        visible={unlockModalVisible}
        title={i18n.t('Product.脚本已解锁')}
        content={
          <Trans
            i18nKey={
              'Product.按照uChoice提供的脚本拍摄，有希望发布后7天内出单！'
            }
            components={{
              red: <span className="text-primary"></span>
            }}
          ></Trans>
        }
        confirmBtn={{ onClick: handleCloseUnlockModal }}
        onMaskClick={handleCloseUnlockModal}
      ></HappyAlert>
    </>
  ) : null
}

export default MaterialCell
