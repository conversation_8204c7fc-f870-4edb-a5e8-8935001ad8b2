import { i18n } from '@/lib/client/i18n'
import { AppColors } from '@/lib/const'

export const Tabs = ({
  tabIndex,
  onChange
}: {
  tabIndex: number
  onChange: (index: number) => void
}) => {
  const labels = [
    i18n.t('Product.商品卖点'),
    i18n.t('Product.图片素材'),
    i18n.t('Product.视频素材'),
    i18n.t('Product.脚本参考')
  ]

  return (
    <div className="mt-[20px] flex rounded-[8px] bg-background p-[4px]">
      {labels.map((label, i) => (
        <div
          key={i}
          className="flex flex-1 items-center justify-center rounded-[4px] px-[4px] py-[12px]"
          style={{
            background:
              tabIndex === i
                ? i === 0
                  ? 'linear-gradient( 270deg, #F7450F 0%, #FF8736 100%)'
                  : AppColors.primary
                : 'transparent'
          }}
          onClick={() => onChange(i)}
        >
          <span
            className="text-center text-[22px] font-bold leading-[26px]"
            style={{ color: tabIndex === i ? '#fff' : AppColors.gray6E }}
          >
            {label}
          </span>
        </div>
      ))}
    </div>
  )
}
