'use client'

import FrostedGlass from '@/components/FrostedGlass'
import { Image } from '@/components/Image'
import { AppColors } from '@/lib/const'
import { useState } from 'react'
import video_play from '@/../public/images/product/detail/video_play.png'
import finger from '@/../public/images/product/detail/finger.png'
import { makeSureInApp, px2rem } from '@/lib/client/utils'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { i18n } from '@/lib/client/i18n'
import { videoPreviewer } from '@/components/VideoPreviewer'
import { ImagePreview } from 'react-vant'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { useLogined } from '@/lib/hooks/useLogined'
import { toLogin } from '@/lib/actions'
import { updateAlert } from '@/components/UpdateAlert'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'

interface Props {
  sources: string[]
  covers: string[]
  sourceType?: 'picture' | 'video'
  onDownload?: () => void
}

export const MediaMaterial = ({
  sources = [],
  covers = [],
  sourceType = 'picture',
  onDownload
}: Props) => {
  const [selectedSources, setSelectedSources] = useState<string[]>([])
  const logined = useLogined()

  return (
    <div className=" pt-[20px]">
      <div className="relative">
        <div className="grid grid-cols-3 gap-[20px]">
          {sources.map((source, index) => {
            return (
              <div
                className="relative size-[204px] rounded-[4px]"
                key={index}
                onClick={async () => {
                  if (sourceType === 'picture') {
                    // TODO: flutter预览图片容易失败
                    // if (
                    //   !!(await webview?.send(
                    //     WebviewEvents.hasJavaScriptHandler,
                    //     {
                    //       handlerName: WebviewEvents.previewImages
                    //     }
                    //   ))
                    // ) {
                    //   webview?.send(WebviewEvents.previewImages, {
                    //     urls: sources,
                    //     initialIndex: index
                    //   })
                    // } else {

                    // }

                    ImagePreview.open({
                      images: sources,
                      startPosition: index,
                      showIndicators: true,
                      closeable: true
                    })
                  } else {
                    videoPreviewer.play(source)
                  }
                }}
              >
                <Image
                  className="size-[204px] rounded-[4px] object-cover"
                  src={covers[index]}
                  withPlaceholder
                ></Image>

                <div
                  className="absolute left-0 top-0 flex size-[204px] items-center justify-center rounded-[4px]"
                  style={{
                    backgroundColor:
                      sourceType === 'video' ? 'rgba(0,0,0,0.4)' : 'transparent'
                  }}
                >
                  {sourceType === 'video' && (
                    <Image src={video_play} className="size-[48px]"></Image>
                  )}
                </div>

                <div
                  className="absolute right-[12px] top-[12px] flex size-[32px] items-center justify-center rounded-[16px]"
                  style={{
                    backgroundColor: selectedSources.includes(source)
                      ? AppColors.primary
                      : 'transparent',
                    borderColor: selectedSources.includes(source)
                      ? 'transparent'
                      : AppColors.background,
                    borderWidth: selectedSources.includes(source)
                      ? 0
                      : px2rem(2)
                  }}
                  onClick={e => {
                    e.stopPropagation()
                    if (selectedSources.includes(source)) {
                      setSelectedSources(
                        selectedSources.filter(item => item !== source)
                      )
                    } else {
                      setSelectedSources([...selectedSources, source])
                    }
                  }}
                >
                  {selectedSources.includes(source) && (
                    <span className="text-[24px] text-white">
                      {selectedSources.indexOf(source) + 1}
                    </span>
                  )}
                </div>
              </div>
            )
          })}
        </div>

        {sources.length > 0 && !logined && (
          <div
            className="absolute left-0 top-0 h-full w-full"
            onClick={toLogin}
          >
            <FrostedGlass>
              <div className="flex items-center justify-center">
                <span className="mr-[12px] text-[24px] text-primary">
                  {i18n.t('Product.Log in to unlock and view')}
                </span>
                <Image src={finger} className="h-[72px] w-[60px]"></Image>
              </div>
            </FrostedGlass>
          </div>
        )}

        {sources.length === 0 && (
          <div className="flex h-[400px] w-full items-center justify-center">
            <StatusView status={StatusViewType.empty}></StatusView>
          </div>
        )}
      </div>

      {sources.length > 0 && (
        <div
          className="mt-[24px] flex h-[72px] w-full items-center justify-center rounded-[4px] border-[2px]"
          style={{
            borderColor:
              selectedSources.length > 0 ? AppColors.primary : '#BBBBBD'
          }}
          onClick={async () => {
            statistic({
              eventName:
                sourceType === 'picture'
                  ? EventName.material_images_download
                  : EventName.material_videos_download
            })

            if (
              webview?.send(WebviewEvents.hasJavaScriptHandler, {
                handlerName: WebviewEvents.downloadFiles
              }) &&
              selectedSources.length > 0
            ) {
              const success = await webview?.send(WebviewEvents.downloadFiles, {
                urls: selectedSources
              })
              if (success) {
                onDownload?.()
              }
            } else {
              if (webview) {
                updateAlert.show()
              } else {
                await makeSureInApp()
              }
            }
          }}
        >
          <span
            className="text-[28px]"
            style={{
              color:
                selectedSources.length > 0 ? AppColors.primary : AppColors.black
            }}
          >
            {i18n.t('Product.下载素材')}（{selectedSources.length}）
          </span>
        </div>
      )}
    </div>
  )
}
