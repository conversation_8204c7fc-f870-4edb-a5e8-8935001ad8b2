import { Image } from '@/components/Image'
import finger from '@/../public/images/product/detail/finger.png'
import scripts_reference_bg from '@/../public/images/product/detail/scripts_reference_bg.png'
import { TtaItemSourceMaterialBriefAccountVo } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { Trans } from 'react-i18next'
import { i18n } from '@/lib/client/i18n'
import { useRouter } from '@/lib/hooks/useRouter'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { useLogined } from '@/lib/hooks/useLogined'
import { toLogin } from '@/lib/actions'
import './index.css'
import { Swiper } from 'antd-mobile'
import { px2rem } from '@/lib/client/utils'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'

export const ScriptsReference = ({
  briefAccounts,
  brief,
  onUnlock
}: {
  briefAccounts: TtaItemSourceMaterialBriefAccountVo[]
  brief: string
  onUnlock?: () => void
}) => {
  const router = useRouter()
  const logined = useLogined()

  return (
    <div className="mt-[20px]">
      {brief ? (
        <>
          <div className="relative">
            <Image
              src={scripts_reference_bg}
              className="h-[118px] w-[654px]"
            ></Image>
            <div
              className="absolute left-0 top-0 flex h-[118px] w-[654px] items-center justify-center"
              onClick={() => {
                if (logined) {
                  statistic({
                    eventName: EventName.material_brief_unlock
                  })

                  router.push(brief)
                  setTimeout(() => {
                    onUnlock?.()
                  }, 500)
                } else {
                  toLogin()
                }
              }}
            >
              {logined ? (
                <span className="mr-[12px] text-[24px] font-bold text-primary">
                  {i18n.t('Product.点击即可解锁brief>')}
                </span>
              ) : (
                <span className="mr-[12px] text-[24px] font-bold text-primary">
                  {i18n.t('Product.Log in to unlock and view')}
                </span>
              )}
              <Image src={finger} className="h-[72px] w-[60px]"></Image>
            </div>
          </div>

          {briefAccounts.length === 0 ? (
            <div className="flex h-[400px] w-full items-center justify-center">
              <StatusView status={StatusViewType.empty}></StatusView>
            </div>
          ) : (
            <div>
              <Swiper
                autoplay
                autoplayInterval={2000}
                loop
                allowTouchMove={false}
                direction="vertical"
                style={{ height: px2rem(84) }}
                indicator={() => null}
              >
                {briefAccounts.map((account, index) => (
                  <Swiper.Item key={index}>
                    <div
                      className="mt-[20px] flex h-[64px] items-center"
                      key={index}
                    >
                      <Image
                        src={account.avatar}
                        className="size-[64px] rounded-[32px]"
                      ></Image>

                      <span className="flex-1 pl-[12px] text-[24px] leading-[28px] text-black">
                        {account.type === 0 && (
                          <Trans
                            i18nKey={'Product.name按脚本拍摄，销量提升value%！'}
                            values={{
                              name: `${account.displayName}`,
                              value: account.value
                            }}
                            components={{
                              main: <span className="text-black" />,
                              red: <span className="text-primary" />
                            }}
                          ></Trans>
                        )}

                        {account.type === 1 && (
                          <Trans
                            i18nKey={'Product.name按脚本拍摄，1天出单value件！'}
                            values={{
                              name: `${account.displayName}`,
                              value: account.value
                            }}
                            components={{
                              main: <span className="text-black" />,
                              red: <span className="text-primary" />
                            }}
                          ></Trans>
                        )}

                        {account.type === 2 && (
                          <Trans
                            i18nKey={
                              'Product.name按脚本拍摄，累计出单value件！'
                            }
                            values={{
                              name: `${account.displayName}`,
                              value: account.value
                            }}
                            components={{
                              main: <span className="text-black" />,
                              red: <span className="text-primary" />
                            }}
                          ></Trans>
                        )}
                      </span>
                    </div>
                  </Swiper.Item>
                ))}
              </Swiper>
            </div>
          )}
        </>
      ) : (
        <div className="flex h-[400px] w-full items-center justify-center">
          <StatusView status={StatusViewType.empty}></StatusView>
        </div>
      )}
    </div>
  )
}
