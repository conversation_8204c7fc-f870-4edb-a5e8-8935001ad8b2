import copy_deep_gray from '@/../public/images/common/copy_deep_gray.png'
import dot from '@/../public/images/common/dot.png'
import { Image } from '@/components/Image'
import { i18n } from '@/lib/client/i18n'
import gray_arrow_down from '@/../public/images/common/gray_arrow_down.png'
import { copyText } from '@/lib/client/utils'
import { TtaItemSourceMateriaSellPointVo } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { useState } from 'react'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'

interface Props {
  sellPointList: TtaItemSourceMateriaSellPointVo[]
  onCopied: () => void
}

export const SellingPoints = ({ sellPointList, onCopied }: Props) => {
  const [expanded, setExpanded] = useState(false)

  return (
    <div className="relative pt-[20px]">
      {sellPointList
        .slice(0, expanded ? sellPointList.length : 3)
        .map((item, index) => {
          return (
            <div key={index}>
              <div className="flex w-full pr-[60px]">
                <div className="flex h-[34px] items-center">
                  <Image src={dot} className="size-[24px]" />
                </div>
                <span className="line-clamp-999 ml-[8px] w-full flex-1 text-ellipsis break-words text-[24px] font-bold leading-[34px] text-black">
                  {item.sellPointTitle}
                </span>
              </div>
              <span className="line-clamp-999 mt-[8px] text-ellipsis break-words text-[22px] text-gray6E">
                {item.sellPointDesc}
              </span>
            </div>
          )
        })}
      {sellPointList.length > 0 && (
        <div
          className=" absolute right-0 top-0 py-[20px] pl-[20px] touch-opacity"
          onClick={() => {
            copyText(
              sellPointList
                .map(
                  (item, index) =>
                    `${index + 1}. ${item.sellPointTitle}: \n${
                      item.sellPointDesc
                    }`
                )
                .join('\n'),
              onCopied
            )

            statistic({ eventName: EventName.material_selling_points_copy })
          }}
        >
          <Image src={copy_deep_gray} className="h-[24px] w-[24px]"></Image>
        </div>
      )}
      {sellPointList.length > 3 && !expanded && (
        <>
          <div
            className="flex items-center justify-center touch-opacity"
            onClick={() => setExpanded(true)}
          >
            <div className="mr-[8px] text-[20px] text-gray8A">
              {i18n.t('Product.查看更多')}
            </div>
            <Image src={gray_arrow_down} className="h-[20px] w-[20px]"></Image>
          </div>
        </>
      )}
      {sellPointList.length === 0 && (
        <div className="flex h-[400px] w-full items-center justify-center">
          <StatusView status={StatusViewType.empty}></StatusView>
        </div>
      )}
    </div>
  )
}
