'use client'
import { Image } from '@/components/Image'
import React, { useEffect, useMemo, useState } from 'react'
import red_box_small from '@/../public/images/product/detail/red_box_small.png'
import red_box_big from '@/../public/images/product/detail/red_box_big.png'
import { getToken, getUserInfo } from '@/lib/client/utils'
import { webview } from '@/lib/client/webview'
import { useMounted } from '@/lib/hooks/useMounted'
import { Trans } from 'react-i18next'
import { getTotalCount } from '@/app/api/api-uchoice/order/user/getTotalCount/request'
import { TotalSampleOrderVo } from '@/app/api/api-uchoice/order/user/getTotalCount/dto'
import { MemberVo } from '@/app/api/api-uchoice/uChoice/account/current/dtos'
import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { formatCount } from '@/lib/format'
import { matchLanguage } from '@/lib/utils'

const Box = ({
  top,
  bottom,
  size
}: {
  top: string
  bottom: string
  size: 'big' | 'small'
}) => {
  return (
    <div className="relative mr-[12px] h-[68px] w-[150px]">
      <Image
        src={size === 'small' ? red_box_small : red_box_big}
        className="h-[68px] w-[150px]"
      ></Image>
      <div className="absolute top-0 flex h-[68px] w-[150px] flex-col items-center justify-center text-[20px] leading-[22px] text-[#FF0000]">
        <span className="">{top}</span>
        <span className="">{bottom}</span>
      </div>
    </div>
  )
}

const UserLevelTips = ({ itemInfo }: { itemInfo: ItemInfoDto }) => {
  const mounted = useMounted()
  const [userInfo, setUserInfo] = useState<MemberVo | null>()
  const [totalSampleOrder, setTotalSampleOrder] = useState<TotalSampleOrderVo>()

  useEffect(() => {
    fetch()
  }, [])

  const fetch = async () => {
    if (!(await getToken())) {
      return
    }

    setUserInfo(await getUserInfo())

    const { code, result } = await getTotalCount()
    if (code === 200) {
      setTotalSampleOrder(result)
    }
  }

  const showGetFreeSamples = useMemo(
    () =>
      itemInfo.isCooperate &&
      userInfo?.remainSampleCount != undefined &&
      userInfo?.remainSampleCount > 0,
    [userInfo]
  )

  const showGetOrders = useMemo(() => {
    return (
      itemInfo.isCooperate && itemInfo.sampleTotal === 0 && totalSampleOrder
    )
  }, [totalSampleOrder])

  return mounted && (showGetFreeSamples || showGetOrders) ? (
    <div className="px-[24px]">
      <div
        className="flex w-full items-center rounded-[4px] px-[16px] py-[20px]"
        style={{
          background: 'linear-gradient( 270deg, #FDBAAA 0%, #D2E0FD 100%)'
        }}
      >
        {showGetFreeSamples && (
          <Box
            top={matchLanguage({
              en: 'Get Free',
              th: 'ตัวอย่าง',
              vi: 'mẫu miễn phí'
            })}
            bottom={matchLanguage({
              en: 'Samples',
              th: 'ขอฟรี',
              vi: 'nhận mẫu'
            })}
            size="big"
          ></Box>
        )}

        {showGetOrders && (
          <Box
            top={matchLanguage({
              en: 'Buy It And',
              th: 'สั่งซื้อ',
              vi: 'mua mẫu'
            })}
            bottom={matchLanguage({
              en: 'Get Order',
              th: 'ตัวอย่าง',
              vi: 'xuất đơn'
            })}
            size="big"
          ></Box>
        )}

        <div className="flex-1">
          {showGetFreeSamples && (
            <Trans
              i18nKey="Product.还有x次申样机会，多申样，多爆单！"
              components={{
                main: (
                  <span className="line-clamp-3 text-[24px] text-black30"></span>
                ),
                red: <span className="text-[#FF0032]"></span>
              }}
              values={{ count: userInfo!.remainSampleCount }}
            ></Trans>
          )}

          {showGetOrders && (
            <Trans
              i18nKey="Product.peopleCount人已购样出单，成功升级L1，获得count个免费样品！"
              components={{
                main: (
                  <span className="line-clamp-3 text-[24px] text-black30"></span>
                ),
                red: <span className="text-[#FF0032]"></span>
              }}
              values={{
                peopleCount: formatCount(totalSampleOrder!.totalOrderCount),
                count: totalSampleOrder!.nextFreeOrderCount
              }}
            ></Trans>
          )}
        </div>
      </div>
    </div>
  ) : null
}

export default UserLevelTips
