/* eslint-disable @next/next/no-img-element */
'use client'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { isLanguageTH } from '@/lib/utils'
import { i18n } from '@/lib/client/i18n'
import { TransComponent } from '@/app/components/Trans'
import { RegionComponent } from '@/app/components/Region'

export default function Index() {
  return (
    <TransparentNavPage
      title={i18n.t('Product.添加商品链接到TikTok橱窗')}
      transparent={false}
    >
      <div className="h-[32px] bg-background"></div>
      <TransComponent
        en={
          <RegionComponent
            TH={
              <img
                src={'/images/product/detail/copy_link_guide_th_en.png'}
                className="h-[2696px] w-full"
              ></img>
            }
            VN={
              <img
                src={'/images/product/detail/copy_link_guide_vn_en.png'}
                className="h-[2696px] w-full"
              ></img>
            }
          ></RegionComponent>
        }
        th={
          <img
            src={'/images/product/detail/copy_link_guide_th.png'}
            className="h-[2696px] w-full"
          ></img>
        }
        vi={
          <img
            src={'/images/product/detail/copy_link_guide_vn.png'}
            className="h-[2696px] w-full"
          ></img>
        }
      ></TransComponent>
    </TransparentNavPage>
  )
}
