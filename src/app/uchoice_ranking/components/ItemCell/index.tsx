import {
  GetTapRankingListType,
  TapRankItemVo
} from '@/app/api/api-uchoice/rankings/getTapRankingList/dtos'
import { Rank } from '@/app/components/Rank'
import { Earn } from '@/components/Earn'
import { Image } from '@/components/Image'
import { YoupikHighCom } from '@/components/YoupikHighCom'
import { makeSureInApp, resizeImageUrl } from '@/lib/client/utils'
import { formatCount, formatPrice, formatSales } from '@/lib/format'
import React, { useState } from 'react'
import { Trans } from 'react-i18next'
import liked from '@/../public/images/ranking/liked.png'
import like from '@/../public/images/ranking/like.png'
import rank_item_cell from '@/../public/images/ranking/rank_item_cell.png'
import { loading } from '@/lib/client/loading'
import { updateMemberCollect } from '@/app/api/api-uchoice/tt/h5/updateMemberCollect'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'
import { Notice } from '../Notice'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { addToShowCase, checkAuth } from '@/lib/actions'
import { TapRankItemInfoVo } from '@/app/api/api-uchoice/rankings/getTapRankingListV2/dtos'

interface Props {
  index: number
  type: GetTapRankingListType
  item: TapRankItemInfoVo
  onToggleCollected(id: string): void
}

export const ItemCell = ({ index, type, item, onToggleCollected }: Props) => {
  const router = useRouter()

  const toPdp = () => {
    router.push(`/product/${item.id}`)
  }

  const toggleLike = async e => {
    e.stopPropagation()

    statistic({
      eventName: EventName.ranking_collect,
      param: {
        id: item.id
      }
    })

    await makeSureInApp()

    loading.show({ userInteractive: true })
    const { code } = await updateMemberCollect({
      id: `${item.id}`,
      productId: `${item.productId}`,
      operation: item.isCollected ? 2 : 1
    })
    loading.hide()

    if (code === 200) {
      onToggleCollected(item.id)

      webview?.send(WebviewEvents.toggleCollect, {
        id: `${item.id}`,
        isCollected: `${!item.isCollected}`
      })
    }
  }

  const onAddToShowCase = async () => {
    statistic({
      eventName: EventName.ranking_add_to_showcase,
      param: {
        id: item.id
      }
    })
    checkAuth(() => addToShowCase(item, 'ucohice_rank'))
  }

  const onRequestSample = e => {
    e.stopPropagation()

    statistic({
      eventName: EventName.ranking_sample_apply,
      param: {
        id: item.id
      }
    })

    router.push(`/product/${item.id}`)
  }

  return (
    <>
      <div
        className="relative flex items-center justify-center bg-[#140801] pb-[4px]"
        id={`top_${item.rn}`}
      >
        <Image src={rank_item_cell} className="h-[414px] w-[702px]"></Image>
        <div
          className="absolute top-[26px] w-[662px] rounded-[16px] bg-white p-[20px]"
          onClick={toPdp}
        >
          <div className="flex">
            <img
              src={resizeImageUrl(item.image, 212)}
              className="size-[212px] rounded-[8px]"
            ></img>
            <div className="flex flex-1 flex-col justify-between overflow-hidden pl-[20px]">
              <div className="">
                <div className="truncate text-[28px] text-black">
                  {item.productName}
                </div>
                <span className="mt-[8px] text-[24px] text-gray8A">
                  <Trans
                    i18nKey="Product.soldMonth"
                    values={{
                      sold: formatSales(item.salesForLast30Days)
                    }}
                    components={{
                      b: <span className="text-[24px] text-gray8A" />
                    }}
                  />
                </span>
                {/* {(item.shopScore || item.shopName) && (
                  <div className="mt-[8px] flex items-center">
                    {item.shopScore != null && item.shopScore > 0 && (
                      <div className="mr-[12px]">
                        <Score score={item.shopScore}></Score>
                      </div>
                    )}
                    {item.shopName && (
                      <div className=" flex-1 truncate text-[24px] text-gray8A">
                        {item.shopName}
                      </div>
                    )}
                  </div>
                )} */}
                <div className="mt-[8px]">
                  <YoupikHighCom
                    commission={`${Math.round(item.commissionRate * 100)}%`}
                  ></YoupikHighCom>
                </div>
              </div>
              <span className="mt-[4px] text-[26px] text-black">
                {item.minPrice === item.maxPrice
                  ? `${formatPrice(item.minPrice, true)}`
                  : `${formatPrice(item.minPrice, true)} ~ ${formatPrice(
                      item.maxPrice,
                      true
                    )}`}
              </span>
              {/* <Earn
            earn={
              item.minEarn === item.maxEarn
                ? `${formatPrice(item.maxEarn, true)}`
                : `${formatPrice(item.minEarn, true)} ~ ${formatPrice(
                    item.maxEarn,
                    true
                  )}`
            }
          ></Earn> */}
              <div className="text-[26px] font-bold text-primary">
                {i18n.t('Product.Earn')}{' '}
                {item.minEarn === item.maxEarn
                  ? `${formatPrice(item.maxEarn, true)}`
                  : `${formatPrice(item.minEarn, true)} ~ ${formatPrice(
                      item.maxEarn,
                      true
                    )}`}
              </div>
            </div>
          </div>

          {(type === GetTapRankingListType.BestSellers &&
            item.anchorSalesInfoVoList &&
            item.anchorSalesInfoVoList.length > 0) ||
          (type !== GetTapRankingListType.BestSellers &&
            item.relevantQuantity > 0) ? (
            <div className="">
              <Notice
                type={type}
                anchorCount={item.relevantQuantity}
                anchorSalesInfoVoList={item.anchorSalesInfoVoList}
              ></Notice>
            </div>
          ) : (
            <div className="h-[24px]"></div>
          )}

          <div className="flex items-center justify-end">
            {/* <div
              className="flex h-[76px] w-[152px] items-center justify-center rounded-[4px] border-2 border-primary touch-opacity"
              onClick={toggleLike}
            >
              <Image
                src={item.isCollected ? liked : like}
                className="size-[32px]"
              />
              <span className="text ml-[4px] text-[24px] font-bold text-primary">
                {item.isCollected
                  ? i18n.t('Product.收藏')
                  : i18n.t('Product.收藏')}
              </span>
            </div> */}
            <div
              className="flex h-[56px] items-center justify-center rounded-[4px] border-2 border-primary px-[20px] touch-opacity"
              onClick={e => {
                e.stopPropagation()

                checkAuth(onAddToShowCase)
              }}
            >
              <span className="text text-center text-[24px] font-bold text-primary">
                {i18n.t('Product.添加橱窗')}
              </span>
            </div>
            <div className="w-[20px]"></div>
            <div
              className="flex h-[56px] items-center justify-center rounded-[4px] border-2 border-primary bg-primary px-[20px] touch-opacity"
              onClick={onRequestSample}
            >
              <span className="text text-center text-[24px] font-bold text-white">
                {i18n.t('Product.requestSample')}
              </span>
            </div>
          </div>
        </div>
        <div className="absolute left-[20px] top-[9px]">
          {item.rn < 30 && <Rank index={item.rn - 1}></Rank>}
        </div>
      </div>
    </>
  )
}
