'use client'
import {
  GetTapRankingListType,
  TapRankItemAnchorVo
} from '@/app/api/api-uchoice/rankings/getTapRankingList/dtos'
import { ItemNoticeSwiper } from '@/app/components/ItemNoticeSwiper'
import { formatCount, formatPrice, formatSales } from '@/lib/format'
import React, { ReactNode, useMemo } from 'react'
import { Trans } from 'react-i18next'

interface Props {
  type: GetTapRankingListType
  anchorCount: number
  anchorSalesInfoVoList?: TapRankItemAnchorVo[]
}
export const Notice = ({ type, anchorCount, anchorSalesInfoVoList }: Props) => {
  const notices = useMemo(() => {
    let theNotices = [] as ReactNode[]

    if (type !== GetTapRankingListType.BestSellers && anchorCount > 0) {
      theNotices.push(
        <Trans
          i18nKey={
            {
              '1': 'Ranking.已有count达人申请该商品的免费样品',
              '2': 'Ranking.已有count达人将该商品添加到收藏夹',
              '3': 'Ranking.已有count达人将该商品添加到橱窗'
            }[type]
          }
          values={{
            count: formatSales(anchorCount)
          }}
          components={{
            b: <span className="text-primary" />
          }}
        />
      )
    } else {
      for (const salesInfo of anchorSalesInfoVoList || []) {
        theNotices.push(
          <Trans
            i18nKey="Ranking.anchor售卖orders单，赚了money佣金"
            values={{
              anchor: salesInfo.displayName,
              orders: formatCount(salesInfo.orderCount),
              money: `${formatPrice(salesInfo.earn, true)}`
            }}
            components={{
              b: <span className="text-primary" />
            }}
          />
        )
      }
    }

    return theNotices
  }, [type, anchorCount, anchorSalesInfoVoList])

  return <ItemNoticeSwiper notices={notices}></ItemNoticeSwiper>
}
