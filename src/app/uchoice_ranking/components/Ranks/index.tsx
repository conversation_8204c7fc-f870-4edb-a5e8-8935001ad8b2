import React, { useEffect, useState } from 'react'
import rank_category_1 from '@/../public/images/ranking/rank_category_1.png'
import rank_category_2 from '@/../public/images/ranking/rank_category_2.png'
import rank_category_3 from '@/../public/images/ranking/rank_category_3.png'
import rank_category_4 from '@/../public/images/ranking/rank_category_4.png'
import rank_category_selected_1 from '@/../public/images/ranking/rank_category_on_1.png'
import rank_category_selected_2 from '@/../public/images/ranking/rank_category_on_2.png'
import rank_category_selected_3 from '@/../public/images/ranking/rank_category_on_3.png'
import rank_category_selected_4 from '@/../public/images/ranking/rank_category_on_4.png'
import { Image } from '@/components/Image'
import { px2rem } from '@/lib/client/utils'
import { isRegionVN } from '@/lib/utils'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { i18n } from '@/lib/client/i18n'

interface BtnProps {
  selected: boolean
  selectedColor: string
  icon: any
  selectedIcon: any
  title: string
  onClick: any
}

const Btn = ({
  selected,
  selectedColor,
  icon,
  selectedIcon,
  title,
  onClick
}: BtnProps) => {
  return (
    <div className="flex min-w-max" onClick={onClick}>
      <div
        className="flex h-[64px] items-center rounded-[8px] border-grayEE pl-[6px] pr-[14px]"
        style={{
          borderWidth: selected ? 0 : px2rem(2),
          backgroundColor: selected ? 'white' : 'rgba(0,0,0,0)'
        }}
      >
        <Image
          src={selected ? selectedIcon : icon}
          className="h-[52px] w-[60px]"
        ></Image>
        <span
          className="ml-[10px] text-[26px]"
          style={{
            color: selected ? selectedColor : 'white',
            fontWeight: selected ? 'bold' : 'normal'
          }}
        >
          {title}
        </span>
      </div>
    </div>
  )
}

interface Props {
  onChange(index: number): void
}

export const Ranks = ({ onChange }: Props) => {
  const params = useRouteParams<{ index: string }>()
  const [selectedIndex, selectIndex] = useState(0)

  useEffect(() => {
    if (params?.index && params.index !== '0') {
      beforeSelectIndex(Number(params?.index), true)
    }
  }, [])

  const beforeSelectIndex = (index, initial = false) => {
    selectIndex(index)

    if (index === 0 || index === 2) {
      scrollToLeft()
    } else {
      scrollToRight()
    }

    if (!initial) {
      statistic({
        eventName: [
          EventName.ranking_type_1,
          EventName.ranking_type_2,
          EventName.ranking_type_3,
          EventName.ranking_type_4
        ][index]
      })

      onChange(index)
    }
  }

  const scrollToLeft = () => {
    const div = document.getElementById('ranks')
    div!.scrollLeft = 0
  }

  const scrollToRight = () => {
    const div = document.getElementById('ranks')
    div!.scrollLeft = div!.scrollWidth
  }

  return isRegionVN() ? (
    <div id="ranks" className="overflow-x-scroll pt-[40px] hide-scrollbar">
      <div className="flex whitespace-nowrap">
        <Btn
          title={i18n.t('Ranking.uChoice样品榜')}
          icon={rank_category_1}
          selectedIcon={rank_category_selected_1}
          selectedColor="#DD840D"
          selected={selectedIndex === 0}
          onClick={() => beforeSelectIndex(0)}
        ></Btn>

        <div className="ml-[22px] pr-[24px]">
          <Btn
            title={i18n.t('Ranking.uChoice畅销榜单')}
            icon={rank_category_4}
            selectedIcon={rank_category_selected_4}
            selectedColor="#FE2C55"
            selected={selectedIndex === 3}
            onClick={() => beforeSelectIndex(3)}
          ></Btn>
        </div>
      </div>
    </div>
  ) : (
    <div id="ranks" className="overflow-x-scroll pt-[40px] hide-scrollbar">
      <div className="flex whitespace-nowrap">
        <Btn
          title={i18n.t('Ranking.uChoice样品榜')}
          icon={rank_category_1}
          selectedIcon={rank_category_selected_1}
          selectedColor="#DD840D"
          selected={selectedIndex === 0}
          onClick={() => beforeSelectIndex(0)}
        ></Btn>

        <div className="ml-[22px] pr-[24px]">
          <Btn
            title={i18n.t('Ranking.uChoice收藏夹榜单')}
            icon={rank_category_2}
            selectedIcon={rank_category_selected_2}
            selectedColor="#5245E5"
            selected={selectedIndex === 1}
            onClick={() => beforeSelectIndex(1)}
          ></Btn>
        </div>
      </div>
      <div className="mt-[22px] flex whitespace-nowrap">
        <Btn
          title={i18n.t('Ranking.uChoice橱窗榜单')}
          icon={rank_category_3}
          selectedIcon={rank_category_selected_3}
          selectedColor="#FE6D45"
          selected={selectedIndex === 2}
          onClick={() => beforeSelectIndex(2)}
        ></Btn>

        <div className="ml-[22px] pr-[24px]">
          <Btn
            title={i18n.t('Ranking.uChoice畅销榜单')}
            icon={rank_category_4}
            selectedIcon={rank_category_selected_4}
            selectedColor="#FE2C55"
            selected={selectedIndex === 3}
            onClick={() => beforeSelectIndex(3)}
          ></Btn>
        </div>
      </div>
    </div>
  )
}
