'use client'

import {
  TransparentNavPage,
  TopTransparentCard
} from '@/components/TransparentNavPage'
import { useEffect, useMemo, useState } from 'react'
import { Image } from '@/components/Image'
import ranking_header_bg from '@/../public/images/ranking/rank_title_bg.png'
import rank_title_left from '@/../public/images/ranking/rank_title_left.png'
import rank_title_right from '@/../public/images/ranking/rank_title_right.png'
import { Ranks } from './components/Ranks'
import { CategoryBar } from './components/CategoryBar'
import { webview } from '@/lib/client/webview'
import { ItemCell } from './components/ItemCell'
import { useBlackBody, useGrayBody } from '@/lib/hooks/useGrayBody'
import { getTapRankingList } from '../api/api-uchoice/rankings/getTapRankingList/request'
import {
  GetTapRankingListType,
  TapRankListVo,
  TtCustomCategory
} from '../api/api-uchoice/rankings/getTapRankingList/dtos'
import StatusView, { StatusViewType } from '../components/StatusView'
import { useRequest } from 'ahooks'
import { i18n } from '@/lib/client/i18n'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { isRegionTH, isRegionVN } from '@/lib/utils'
import { getTtaCategoryInfo } from '../api/api-mall/tt/item/categoryInfo'
import { getTapRankingListV2 } from '../api/api-uchoice/rankings/getTapRankingListV2/request'
import { TapRankItemInfoVo } from '../api/api-uchoice/rankings/getTapRankingListV2/dtos'
import { isIOS } from '@/lib/client/utils'

const useFetchTapRankingList = () => {
  const [data, setData] = useState<TapRankListVo>()
  const [type, setType] = useState<GetTapRankingListType>(
    GetTapRankingListType.SampleApplication
  )
  const [categoryList, setCategoryList] = useState<TtCustomCategory[]>([])
  const [items, setItems] = useState<TapRankItemInfoVo[]>([])
  const [loading, setLoading] = useState(true)

  const fetchTapRankingList = async (rankId: number, categoryId) => {
    // if (loading) {
    //   return
    // }

    setType(rankId)

    if (categoryList.length === 0) {
      let categories: TtCustomCategory[] = []
      if (categories.length === 0) {
        const categoriesRes = await getTtaCategoryInfo()
        if (categoriesRes.code === 200) {
          categories = categoriesRes.result
        }
      }
      setCategoryList([
        {
          name: isRegionTH() ? 'ทั้งหมด' : 'Tất cả',
          nameEn: 'All',
          id: 0
        },
        ...categories
      ])
    }

    setLoading(true)
    const { code, result } = await getTapRankingListV2(rankId, categoryId)
    setLoading(false)
    if (code === 200) {
      setItems(result || [])
    }
  }

  const { run } = useRequest(fetchTapRankingList, {
    manual: true
  })

  // useEffect(() => {
  //   if (loading) {
  //     setCategoryId(undefined)
  //   }
  // }, [loading])

  const onToggleCollected = id => {
    setData({
      ...data,
      itemVoList: data?.itemVoList?.map(item => {
        if (item.id === id) {
          item.isCollected = !item.isCollected
        }
        return item
      })
    })
  }

  return {
    data,
    loading,
    fetchTapRankingList: run,
    type,
    onToggleCollected,
    categoryList,
    items
  }
}

export function Inner() {
  useBlackBody()

  const [categoryId, setCategoryId] = useState(0)
  const [rankId, setRankId] = useState(1)
  const params = useRouteParams<{ index: string; categoryId: string }>()

  const {
    loading,
    fetchTapRankingList,
    type,
    onToggleCollected,
    items,
    categoryList
  } = useFetchTapRankingList()

  useEffect(() => {
    const theRankId =
      params?.index && params.index !== '0' ? Number(params?.index) + 1 : 1
    const theCategoryId = params?.categoryId || 0

    if (theRankId) {
      setRankId(theRankId)
    }

    if (theCategoryId) {
      setCategoryId(Number(theCategoryId))
    }

    fetchTapRankingList(theRankId, theCategoryId)
  }, [])

  const title = useMemo(() => {
    const ranks = [
      { id: 1, name: i18n.t('Product.Most sample requests') },
      { id: 2, name: i18n.t('Product.Most add wishlists') },
      { id: 3, name: i18n.t('Product.Most add showcase') },
      { id: 4, name: i18n.t('Product.Most sold units') }
    ]

    return ranks.find(rank => rank.id === rankId)?.name
  }, [rankId])

  return (
    <TransparentNavPage
      hideTitleWhenTransparent
      showShareButton
      hide={!webview}
      title={title}
      theme="light"
    >
      <div className="w-[750px] bg-[#140801]">
        <div className="relative">
          <div className="h-[100px] w-[750px] bg-[#140801]"></div>
          {!isIOS() && (
            <div
              className="absolute left-0 top-0 w-full bg-white"
              style={{
                height: webview ? `${webview?.getData().topSafeArea}px` : 0
              }}
            ></div>
          )}
        </div>
        <Image src={ranking_header_bg} className="h-[220px] w-[750px]"></Image>
        <div className="absolute top-[100px] h-[220px] w-[750px]">
          <TopTransparentCard>
            <div className="flex items-center justify-center pt-[60px]">
              <Image
                src={rank_title_left}
                className="h-[76px] w-[36px]"
              ></Image>
              <span className="mx-[24px] text-[40px] font-bold leading-[48px] text-[#FFDBB2]">
                {title}
              </span>
              <Image
                src={rank_title_right}
                className="h-[76px] w-[36px]"
              ></Image>
            </div>
          </TopTransparentCard>
        </div>
      </div>

      {/* @ts-ignore */}
      {categoryList.length > 0 && (
        <CategoryBar
          categories={categoryList}
          onChange={(id: number) => {
            setCategoryId(id)
            fetchTapRankingList(rankId, id)
          }}
          onRankChange={(id: number) => {
            setRankId(id)
            fetchTapRankingList(id, categoryId)
          }}
        ></CategoryBar>
      )}

      <div className="bg-[#140801] px-[24px]">
        {loading ? (
          <div className="pt-[200px]">
            <StatusView status={StatusViewType.loading} dark></StatusView>
          </div>
        ) : items.length === 0 ? (
          <div className="pt-[200px]">
            <StatusView status={StatusViewType.empty} dark></StatusView>
          </div>
        ) : (
          items?.map(item => (
            <ItemCell
              key={`${type}_${item.id}`}
              type={type}
              index={item.rank - 1}
              item={item}
              onToggleCollected={onToggleCollected}
            ></ItemCell>
          ))
        )}
      </div>
    </TransparentNavPage>
  )
}
