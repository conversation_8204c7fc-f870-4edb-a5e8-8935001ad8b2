'use client'
import { useState } from 'react'
import Image from 'next/image'
import ComponentCard from '@/app/components/ComponentCard'
import Segmented, { TabItem } from '@/app/components/Segmented'
import icon_calendar from '@/app/images/hotspot/icon_calendar.png'
import styles from './index.module.scss'

const tabList: TabItem[] = [
  {
    label: 'Yesterday',
    value: 'yesterday'
  },
  {
    label: '7 Days',
    value: 'week'
  },
  {
    label: '15 Days',
    value: 'half_month'
  },
  {
    label: '30 Days',
    value: 'month'
  }
]

const SegmentedCard = () => {
  const [active, setActive] = useState<string>('yesterday')

  const handleSegmentChange = (val: string) => {
    setActive(val)
  }

  const handleCalendarClick = () => {
    alert('calendar')
  }

  return (
    <ComponentCard title="Segmented" style={{ marginBottom: '3.2vw' }}>
      <Segmented
        active={active}
        tabList={tabList}
        onChange={handleSegmentChange}
        marginBottom="3.2vw"
        extraItem={
          <Image
            onClick={handleCalendarClick}
            src={icon_calendar}
            className={styles.icon_calendar}
            alt=""
          ></Image>
        }
      ></Segmented>
      <Segmented
        block
        active={active}
        tabList={tabList}
        onChange={handleSegmentChange}
        activeTextStyle={{
          color: '#303030',
          fontWeight: 'bold'
        }}
      ></Segmented>
    </ComponentCard>
  )
}

export default SegmentedCard
