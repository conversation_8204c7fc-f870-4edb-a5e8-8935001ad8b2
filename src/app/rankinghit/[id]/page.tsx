import { metadataTemplate, inApp } from '@/lib/server/utils'
import FmcgPage from './inner'
import { i18nS } from '@/lib/server/i18n'

interface Props {
  params: {
    id: string
  }
}

export default async function Index(props: Props) {
  return <FmcgPage id={props.params.id}></FmcgPage>
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: '' })
  }

  return metadataTemplate({
    title: i18nS.t('Activity.拼曝光 赢BYD汽车'),
    icon: `${process.env.NEXT_PUBLIC_URL}/images/fmcg/activity_byd.png`,
    description: i18nS.t(
      'Activity.参与活动发布视频，就有机会免费赢BYD汽车或黄金！'
    )
  })
}
