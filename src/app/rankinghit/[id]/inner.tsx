/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import { useMounted } from '@/lib/hooks/useMounted'
import { webview } from '@/lib/client/webview'
import { Toast } from 'react-vant'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useRouter } from '@/lib/hooks/useRouter'
import FmcgHeader from './components/FmcgHeader'
import FmcgCountDown from './components/FmcgCountDown'
import FmcgAuthCard from './components/FmcgAuthCard'
import FmcgRankCard from './components/FmcgRankCard'
import FmcgShareCard from './components/FmcgShareCard'
import ImgWithPreload from './components/ImgWithPreload'
import { NotAboutApple } from './components/NotAboutApple'
import FmcgShareModal from './components/FmcgShareModal'
import FmcgShareBtn from './components/FmcgShareBtn'
import main_placeholder from '@/../public/images/fmcg/main_placeholder.png'
import { getSellerCampaignDetail } from '@/app/api/api-uchoice/seller/campaigns/detail'
import { showInviteDoor } from '@/app/api/api-uchoice/invite/code/showInviteDoor'
import { hasTikTokAccount } from '@/app/api/api-uchoice/uChoice/account/hasTiktokAccount'
import { getAccountListV2 } from '@/app/api/api-uchoice/showcase/getAccountListV2'
import { useShareBtnVisible } from './useShareBtnVisible'
import { getToken, makeSureInApp, rpxToPx } from '@/lib/client/utils'
import styles from './index.module.scss'

interface FmcgPageProps {
  id: string
}

const FmcgPage: React.FC<FmcgPageProps> = props => {
  const mounted = useMounted()
  const router = useRouter()

  const { id } = props
  const [navHeight, setNavHeight] = useState(40)

  const [detail, setDetail] = useState<any>({})
  const [fetchLoading, setFetchLoading] = useState(false)

  const [shareModalVisible, setShareModalVisible] = useState(false)
  const [inviteDoorVisible, setInviteDoorVisible] = useState(false)

  const [logined, setLogined] = useState(false)
  const shareBtnVisible = useShareBtnVisible()

  const handleLoginStatus = async () => {
    const token = await getToken()
    if (token) {
      setLogined(true)
    }
  }

  useEffect(() => {
    handleLoginStatus()
  }, [])

  const getShowInviteDoor = async () => {
    if (!webview || !(await getToken())) {
      return
    }
    try {
      const res: any = await showInviteDoor(id)
      if (res.code == 200) {
        setInviteDoorVisible(!!res.result)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const getCampaignDetail = async () => {
    try {
      setFetchLoading(true)
      if (webview) {
        webview.send(WebviewEvents.showLoading, {
          userInteractive: true
        })
      } else {
        Toast.loading({})
      }
      const res: any = await getSellerCampaignDetail(id)
      if (res.code == 200 && res.result) {
        const { result } = res
        setDetail(result)
      }
      setFetchLoading(false)
      if (webview) {
        webview.send(WebviewEvents.hideLoading)
      } else {
        Toast.clear()
      }
    } catch (error) {
      setFetchLoading(false)
      if (webview) {
        webview.send(WebviewEvents.hideLoading)
      } else {
        Toast.clear()
      }
    }
  }

  useEffect(() => {
    getShowInviteDoor()
    getCampaignDetail()
  }, [])

  useEffect(() => {
    if (webview) {
      setNavHeight(webview.getData().topSafeArea)
    }
  }, [])

  const handleToRules = () => {
    router.push(`/activity/goojodoq_rule`)
  }

  const handleInviteCode = async () => {
    if (webview) {
      if (!(await getToken())) {
        await webview?.send(WebviewEvents.makeSureLogined)
        window.location.reload()
        return
      }
      const { userRankInfo } = detail
      const { isAuth } = userRankInfo || {}
      if (isAuth) {
        setShareModalVisible(true)
      } else {
        await webview?.send(WebviewEvents.makeSureTikTokAuthed)
        window.location.reload()
      }
    } else {
      makeSureInApp()
    }
  }

  const handleBindSuccess = () => {
    getShowInviteDoor()
  }

  const handleShare = async () => {
    if (webview) {
      if (!(await getToken())) {
        await webview?.send(WebviewEvents.makeSureLogined)
        window.location.reload()
        return
      }
      const { userRankInfo } = detail
      const { unionId, isAuth } = userRankInfo || {}
      if (isAuth) {
        let url = `/activity/goojodoq_share?campaignId=${id}`
        if (unionId) {
          url += `&unionId=${unionId}`
        }
        router.push(url)
      } else {
        await webview?.send(WebviewEvents.makeSureTikTokAuthed)
        window.location.reload()
      }
    } else {
      makeSureInApp()
    }
  }

  return mounted ? (
    <div className={styles.fmcg_page_container} id="fmcg_page_container">
      <div
        className={styles.top_placeholder}
        style={{ height: navHeight }}
      ></div>
      <FmcgHeader stickHeight={navHeight}></FmcgHeader>
      {/* 否则会和GoAppBar冲突 */}
      {webview && <div className={styles.padding_top_container}></div>}
      <NotAboutApple style={{ paddingTop: 6 }}></NotAboutApple>
      <div className={styles.top_container}>
        <ImgWithPreload
          className={styles.banner}
          src={detail.bannerUrl}
        ></ImgWithPreload>
        <FmcgCountDown
          detail={detail}
          getCampaignDetail={getCampaignDetail}
        ></FmcgCountDown>
        <div className={styles.rules_container} onClick={handleToRules}>
          <div className={styles.rules_label}>
            {i18n.t('Activity.活动规则')}
          </div>
        </div>
      </div>
      <FmcgAuthCard
        detail={detail}
        getCampaignDetail={getCampaignDetail}
      ></FmcgAuthCard>
      <FmcgShareCard handleShare={handleShare}></FmcgShareCard>
      {detail.startTime && detail.status !== 1 && (
        <FmcgRankCard id={id} detail={detail}></FmcgRankCard>
      )}
      <div style={{ width: '100%', height: rpxToPx(168) }}></div>
      {(inviteDoorVisible || !logined) && <div className={styles.invite_code_container} onClick={handleInviteCode}>
        <div className={styles.invite_code_label}>
          {i18n.t('Activity.填写邀请码')}
        </div>
      </div>}
      <FmcgShareModal visible={shareModalVisible} onClose={() => setShareModalVisible(false)} bindSuccess={handleBindSuccess}></FmcgShareModal>
      {shareBtnVisible && <FmcgShareBtn handleShare={handleShare}></FmcgShareBtn>}
    </div>
  ) : (
    <Image src={main_placeholder} alt=""></Image>
  )
}

export default FmcgPage

