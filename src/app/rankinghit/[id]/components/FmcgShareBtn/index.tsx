import React from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import bg_share_btn from '@/../public/images/fmcg/score/bg_share_btn.png'
import styles from './index.module.scss'
import { EventName } from '@/lib/statistic/const'
import { statistic } from '@/lib/statistic'

interface IFmcgShareBtnProps {
  handleShare: () => void
}

const FmcgShareBtn: React.FC<IFmcgShareBtnProps> = ({ handleShare }) => {
  const handleToShare = () => {
    statistic({
      eventName: EventName.rankinghit_share_btn_click,
    })
    handleShare()
  }

  return <div className={styles.fmcg_share_btn_container} onClick={handleToShare}>
    <Image src={bg_share_btn} alt="" className={styles.bg_share_btn}></Image>
    <div className={styles.title}>{i18n.t('Activity.邀请好友，赢积分')}</div>
  </div>
}

export default FmcgShareBtn
