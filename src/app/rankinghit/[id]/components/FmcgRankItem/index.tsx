/* eslint-disable @next/next/no-img-element */
'use client'
import React from 'react'
import Image from 'next/image'
import { formatNumberWithCommas } from '@/lib/utils'
import placeholder from '@/../public/images/common/placeholder.png'
import styles from './index.module.scss'

type FmcgRankItemProps = {
  item: any
  background: string
} & BaseComponentProps

const FmcgRankItem: React.FC<FmcgRankItemProps> = props => {
  const { item, background } = props
  const [error, setError] = React.useState(false)

  return (
    <div className={styles.fmcg_rank_item_container} style={{ background }}>
      <div className={styles.rank_label}>TOP{item.rank ? (item.rank > 999 ? '999+' : item.rank) : 0}</div>
      {!error && item.avatar ? (
        <img
          className={styles.avatar}
          src={item.avatar}
          onError={() => {
            setError(true)
          }}
        ></img>
      ) : (
        <Image src={placeholder} className={styles.avatar} alt=""></Image>
      )}
      <div className={styles.account}>{item.displayName || ''}</div>
      <div className={styles.gmv}>
        {formatNumberWithCommas(parseFloat(item.score || 0))}
      </div>
    </div>
  )
}

export default FmcgRankItem
