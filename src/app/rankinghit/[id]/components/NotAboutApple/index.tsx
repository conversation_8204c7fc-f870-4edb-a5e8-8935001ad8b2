import { isIOS } from '@/lib/client/utils'
import { i18n } from '@/lib/client/i18n'
import React from 'react'

interface Props {
  style?: any
}
export const NotAboutApple = ({ style }: Props) => {
  return isIOS() && (
    <div style={style}>
      <div className="pt-[12px] pb-[12px] pl-[12px] pr-[12px] text-center text-[22px] text-white">
        {i18n.t('Activity.活动与苹果无关')}
      </div>
    </div>
  )
}
