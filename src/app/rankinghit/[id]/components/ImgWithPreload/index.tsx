/* eslint-disable @next/next/no-img-element */
'use client'
import { useState } from 'react'
import Image from 'next/image'
import pic_placeholder from '@/../public/images/fmcg/pic_placeholder.png'
import styles from './index.module.scss'
import { rpxToPx } from '@/lib/client/utils'

const ImgWithPreload = ({ src, className, style }: any) => {
  const [isImageLoaded, setIsImageLoaded] = useState(false)

  const handleImageLoad = img => {
    setIsImageLoaded(true)
  }

  return (
    <div
      className={`${styles.pic_container} ${className}`}
      style={{ height: isImageLoaded ? 'auto' : rpxToPx(750) }}
    >
      <img
        src={src}
        onLoad={handleImageLoad}
        style={style}
        className={styles.pic_real}
      />
      {!isImageLoaded && (
        <div className={styles.pic_placeholder2_container}>
          <Image
            alt=""
            src={pic_placeholder}
            className={styles.pic_placeholder2}
          />
        </div>
      )}
    </div>
  )
}

export default ImgWithPreload
