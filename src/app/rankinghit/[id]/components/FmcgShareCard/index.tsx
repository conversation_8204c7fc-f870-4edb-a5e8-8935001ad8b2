import React from 'react'
import Image from 'next/image'
import bg_share_card from '@/../public/images/fmcg/score/bg_share_card.png'
import icon_share_arrow from '@/../public/images/fmcg/score/icon_share_arrow.png'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { i18n } from '@/lib/client/i18n'
import { isLanguageTH, isRegionTH } from '@/lib/utils'
import { rpxToPx } from '@/lib/client/utils'
import styles from './index.module.scss'

interface IFmcgShareCardProps {
  handleShare: () => void
}

const FmcgShareCard: React.FC<IFmcgShareCardProps> = ({ handleShare }) => {
  const handleToShare = () => {
    statistic({
      eventName: EventName.rankinghit_share_card_click,
    })
    handleShare()
  }

  return <div className={styles.fmcg_share_card_container} onClick={handleToShare}>
    <Image src={bg_share_card} alt="" className={styles.bg_share_card} />
    <div className={styles.content_container}>
      <div className={styles.title} style={{ fontSize: isLanguageTH() ? rpxToPx(32) : rpxToPx(30) }} dangerouslySetInnerHTML={{ __html: isLanguageTH() ? i18n.t('Activity.邀请好友，赢5,000积分') : i18n.t('Activity.邀请好友，赢5,000积分1') }}></div>
      <Image src={icon_share_arrow} alt="" className={styles.icon_share_arrow} />
    </div>
  </div>
}

export default FmcgShareCard
