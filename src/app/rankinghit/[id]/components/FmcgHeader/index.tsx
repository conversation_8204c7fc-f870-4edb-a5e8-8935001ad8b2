'use client'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import Image from 'next/image'
import { webview } from '@/lib/client/webview'
import iocn_back from '@/../public/images/fmcg/icon_back.png'
import icon_share from '@/../public/images/fmcg/icon_share.png'
import icon_contact from '@/../public/images/fmcg/icon_contact.png'
import { WebviewEvents } from '@/lib/client/webview/events'
import styles from './index.module.scss'

type FmcgHeaderProps = {
  stickHeight: number
} & BaseComponentProps

const FmcgHeader: React.FC<FmcgHeaderProps> = props => {
  const { stickHeight } = props
  const mounted = useMounted()

  const handleBack = () => {
    webview?.send(WebviewEvents.back)
  }

  const handleShare = () => {
    webview?.send(WebviewEvents.shareUrl, {
      url: window.location.href,
      desc: '「💥สะสมแต้มลุ้นรับรถ BYD มูลค่า 569,900 บาท✨เข้า uChoice Pro และรับสิทธิ์ด่วน!」',
    })
  }

  const handleH5Back = () => {
    window.history.back()
  }

  const handleContact = () => {
    webview?.send(WebviewEvents.contactUs)
  }

  return (
    <div className={styles.fmcg_header_container} style={{ top: stickHeight }}>
      <div>
        {mounted && webview && (
          <div onClick={handleBack}>
            <Image className={styles.icon_back} alt="" src={iocn_back}></Image>
          </div>
        )}
        {mounted && !webview && window.history.length > 1 && (
          <div onClick={handleH5Back}>
            <Image className={styles.icon_back} alt="" src={iocn_back}></Image>
          </div>
        )}
      </div>
      <div className={styles.title}>
        {i18n.t('Activity.BYD汽车活动商品')}
      </div>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {mounted && webview && (
          <div onClick={handleShare}>
            <Image
              className={styles.icon_share}
              alt=""
              src={icon_share}
            ></Image>
          </div>
        )}
        {mounted && webview && (
          <div onClick={handleContact}>
            <Image
              className={styles.icon_contact}
              alt=""
              src={icon_contact}
            ></Image>
          </div>
        )}
      </div>
    </div>
  )
}

export default FmcgHeader
