/* eslint-disable @next/next/no-img-element */
import React from 'react'
import styles from './index.module.scss'
import FmcgRankItem from '../FmcgRankItem'
import { i18n } from '@/lib/client/i18n'
import { formatNumberWithCommas } from '@/lib/utils'

const FmcgTopRankItem = ({ logo, title, needScore, list, background }) => {
  return (
    <div className={styles.fmcg_top_rank_item_container}>
      <div className={styles.title_container}>
        {logo && <img className={styles.icon_price} src={logo} alt="" />}
        <div className={styles.title}>{title}{needScore && needScore !== '0' ? ` | ${formatNumberWithCommas(parseFloat(needScore))} ${i18n.t('Activity.积分')}` : ''}</div>
      </div>
      {list && list.length !== 0 ? (
        list.map((item, index) => {
          return (
            <FmcgRankItem
              key={index}
              item={item}
              background={background}
            ></FmcgRankItem>
          )
        })
      ) : (
        <div className={styles.empty_label}>
          {i18n.t('Activity.暂无人活动')}
        </div>
      )}
    </div>
  )
}

export default FmcgTopRankItem
