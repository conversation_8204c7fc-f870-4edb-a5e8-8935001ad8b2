import React, { use<PERSON>allback, useEffect, useRef, useState } from 'react'
import Image from 'next/image'
import classNames from 'classnames'
import _ from "lodash";
import { i18n } from '@/lib/client/i18n'
import { useRouter } from '@/lib/hooks/useRouter';
import { Popup, Input } from 'antd-mobile'
import icon_confirm from '@/../public/images/fmcg/score/icon_confirm.png'
import icon_error from '@/../public/images/fmcg/score/icon_error.png'
import icon_close from '@/../public/images/fmcg/score/icon_close.png'
import icon_paste from '@/../public/images/fmcg/score/icon_paste.png'
import placeholder from '@/../public/images/common/placeholder.png'
import { getUserInfoFromInviteCode } from '@/app/api/api-uchoice/invite/code/info'
import { formatNumberStr } from '@/lib/utils';
import { bindInviteCode } from '@/app/api/api-uchoice/invite/code/bind'
import { webview } from '@/lib/client/webview';
import { isIOS, rpxToPx } from '@/lib/client/utils';
import { cancelGetUserInfoRequests } from '@/lib/client/request';
import styles from './index.module.scss'

interface IFmcgShareModalProps {
  visible: boolean
  onClose: () => void
  bindSuccess: () => void
}

const FmcgShareModal: React.FC<IFmcgShareModalProps> = ({ visible, onClose, bindSuccess }) => {
  const router = useRouter()
  const inputRef = useRef<any>(null)
  const [inviteCode, setInviteCode] = useState<string>('')
  const [inviteCodeErrorType, setInviteCodeErrorType] = useState<number>(-1)
  const [inviteCodeErrorDisplayName, setInviteCodeErrorDisplayName] = useState<string>('')
  const [avatarError, setAvatarError] = useState<boolean>(false)
  const [userInfo, setUserInfo] = useState<any>(null)
  const [userInfoLoading, setUserInfoLoading] = useState<boolean>(false)
  const [bindLoading, setBindLoading] = useState<boolean>(false)
  const [isBindSuccess, setIsBindSuccess] = useState<boolean>(false)

  // 使用 useCallback 保证 debounce 只创建一次
  const debouncedChangeHandler = useCallback(
    _.debounce((val: string) => {
      if (val === '') {
        setUserInfo(null)
        setUserInfoLoading(false)
        return
      }
      setUserInfo(null)
      setUserInfoLoading(true)
      getUserInfoFromInviteCode(val, `getUserInfo_${Date.now()}`).then(res => {
        setUserInfoLoading(false)
        if (res.code === 200 && res.result) {
          const { status, displayName } = res.result // status 1-正常 2-失效 3-已绑定下级 4-其他原因
          if (status === 1) {
            setInviteCodeErrorType(-1)
            setInviteCodeErrorDisplayName('')
            setUserInfo(res.result)
          } else {
            setInviteCodeErrorType(status)
            setInviteCodeErrorDisplayName(displayName)
          }
        }
      }).catch(() => {
        setUserInfo(null)
        setUserInfoLoading(false)
      })
    }, 500), // 500ms 防抖
    []
  );

  const handleBindInviteCode = async () => {
    if (!userInfo || bindLoading) {
      return
    }
    try {
      setBindLoading(true)
      const res = await bindInviteCode(inviteCode)
      setBindLoading(false)
      if (res.code === 200 && res.result) {
        const { status, displayName } = res.result // status 1-正常 2-失效 3-已绑定下级 4-其他原因
        if (status === 1) {
          setUserInfo(null)
          setIsBindSuccess(true)
          setInviteCodeErrorType(-1)
          setInviteCodeErrorDisplayName('')
          bindSuccess()
        } else {
          setUserInfo(null)
          setIsBindSuccess(false)
          setInviteCodeErrorType(status)
          setInviteCodeErrorDisplayName(displayName)
        }
      }
    } catch (error) {
      setBindLoading(false)
    }
  }

  useEffect(() => {
    if (!visible) {
      setInviteCode('')
      setAvatarError(false)
      setInviteCodeErrorType(-1)
      setUserInfo(null)
      setUserInfoLoading(false)
      setIsBindSuccess(false)
      setBindLoading(false)
    }
  }, [visible])

  const handleToActivityProcuct = () => {
    router.push('/activity/goojodoq_product')
  }

  const handlePaste = () => {
    console.log(navigator)
    navigator.clipboard.readText()
      .then(text => {
        setInviteCode(text)
      })
      .catch(err => {
        console.error("无法获取剪切板内容:", err);
      });
  }

  return <Popup
    visible={visible}
    onMaskClick={onClose}
    position={webview && !isIOS() ? 'top' : 'bottom'}
    bodyStyle={webview && !isIOS() ? {
      borderBottomLeftRadius: '8px',
      borderBottomRightRadius: '8px',
      borderTopLeftRadius: '8px',
      borderTopRightRadius: '8px',
      marginTop: '18vh',
      width: rpxToPx(702),
      marginLeft: rpxToPx(24),
    } : {
      borderTopLeftRadius: '8px',
      borderTopRightRadius: '8px',
    }}
    destroyOnClose
  >
    <div id="fmcg_share_modal_container" className={styles.fmcg_share_modal_container} style={{ paddingBottom: webview && !isIOS() ? rpxToPx(48) : rpxToPx(84) }}>
      <div className={styles.header_container}>
        <div></div>
        <div className={styles.title}>{i18n.t('Activity.绑定邀请人')}</div>
        <div className={styles.close_container} onClick={onClose}>
          <Image src={icon_close} alt="" className={styles.icon_close}></Image>
        </div>
      </div>
      <div className={styles.content_container}>
        {!isBindSuccess && <div className={styles.invite_code_label}>{i18n.t('Activity.邀请码')}</div>}
        {!isBindSuccess && <div className={styles.input_container}>
          <Input
            ref={inputRef}
            autoFocus={!!(webview && !isIOS())}
            className={styles.input}
            placeholder={i18n.t('Activity.填写邀请码')}
            value={inviteCode}
            maxLength={16}
            onChange={val => {
              const filteredValue = val.replace(/[^a-zA-Z0-9]/g, '');
              if (filteredValue !== inviteCode) {
                cancelGetUserInfoRequests()
                setInviteCodeErrorType(-1)
                setInviteCodeErrorDisplayName('')
                setInviteCode(filteredValue)
                debouncedChangeHandler(filteredValue);
              }
            }}
            onEnterPress={() => {
              inputRef.current.blur()
            }}
            clearable
            style={{ borderColor: inviteCodeErrorType !== -1 ? '#FF3141' : '#FE6D45' }}
          />
          {/* {webview && !isIOS() && <div className={styles.icon_paste_container} onClick={handlePaste}>
            <Image src={icon_paste} alt="" className={styles.icon_paste}></Image>
          </div>} */}
        </div>}
        {userInfo && <div className={styles.user_info_container}>
          {!avatarError ? <img onError={() => setAvatarError(true)} src={userInfo.avatar} alt='' className={styles.avatar}></img> : <Image src={placeholder} alt='' className={styles.avatar}></Image>}
          <div className={styles.right_container}>
            <div className={styles.name_container}>
              <div className={styles.name}>{userInfo.displayName}</div>
              <div className={styles.tag}>Level {userInfo.tier}</div>
            </div>
            <div className={styles.followers}>Followers {userInfo.followerCount ? formatNumberStr(userInfo.followerCount) : 0}</div>
          </div>
        </div>}
        {
          userInfoLoading && <div className={styles.loading_label}>{i18n.t('Common.Loading')}</div>
        }
        {inviteCodeErrorType !== -1 && <div className={styles.error_container}>
          <Image src={icon_error} alt="" className={styles.icon_error}></Image>
          {inviteCodeErrorType === 2 && <div className={styles.error_desc}>{i18n.t('Activity.无效邀请码，请联系邀请人索要~')}</div>}
          {inviteCodeErrorType === 3 && <div className={styles.error_desc}>{i18n.t('Activity.x已经是你的下级，无法绑定！', { name: inviteCodeErrorDisplayName })}</div>}
          {inviteCodeErrorType === 4 && <div className={styles.error_desc}>{i18n.t('Activity.无法绑定自己的邀请码')}</div>}
        </div>}
        {isBindSuccess && <div className={styles.success_container}>
          <div className={styles.success_top_container}>
            <Image src={icon_confirm} alt="" className={styles.icon_confirm}></Image>
            <div className={styles.bind_success}>{i18n.t('Activity.绑定成功')}</div>
          </div>
          <div className={styles.success_desc}>{i18n.t('Activity.按活动要求完成视频拍摄，即可获得5,000积分！')}</div>
        </div>}
      </div>
      {!isBindSuccess && <div onClick={handleBindInviteCode} className={classNames({ [styles.btn_confirm]: true, [styles.btn_disabled]: !userInfo || bindLoading || inviteCodeErrorType !== -1 })}>{i18n.t('Activity.确认绑定')}</div>}
      {isBindSuccess && <div onClick={handleToActivityProcuct} className={styles.btn_confirm}>{i18n.t('Activity.查看活动商品')}</div>}
    </div>
  </Popup >
}

export default FmcgShareModal
