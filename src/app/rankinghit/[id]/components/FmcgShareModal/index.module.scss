.fmcg_share_modal_container {
  display: flex;
  flex-direction: column;
  padding: 28px 24px 84px;

  .header_container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 48px;

    .title {
      font-weight: bold;
      font-size: 36px;
      color: #020202;
      line-height: 50px;
    }

   .close_container {
    width: 28px;
    height: 28px;
    
    .icon_close {
      width: 28px;
      height: 28px;
    }
   }
  }

  .btn_confirm {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FE6D45;
    border-radius: 4px;
    font-weight: bold;
    font-size: 32px;
    color: #FFFFFF;
    line-height: 44px;
  }

  .btn_disabled {
    background: #ccc;
  }

  .content_container {
    margin-bottom: 64px;

    .invite_code_label {
      font-size: 32px;
      color: #020202;
      line-height: 44px;
      margin-bottom: 24px;
    }
  
    .input_container {
      display: flex;
      align-items: center;
      margin-bottom: 40px;

      .input {
        padding: 0 24px;
        font-size: 28px;
        height: 80px;
        background: #FFFFFF;
        border-radius: 8px;
        border: 2px solid #FE6D45;
      } 


      .icon_paste_container {
        margin-left: 24px;
        .icon_paste {
          width: 42px;
          height: 42px;
        }
      }
    }

    .user_info_container {
      display: flex;
      align-items: center;

      .avatar {
        width: 120px;
        height: 120px;
        border-radius: 60px;
        margin-right: 24px;
      }

      .right_container {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .name_container {
          display: flex;
          align-items: center;
          margin-bottom: 24px;
          
          .name {
            font-size: 32px;
            color: #303030;
            line-height: 38px;
            margin-right: 24px;
            white-space: nowrap;       /* 防止换行 */
            overflow: hidden;          /* 隐藏溢出部分 */
            text-overflow: ellipsis;   /* 显示省略号 */
            max-width: 312px;              /* 需要指定宽度 */

          }

          .tag {
            background: #342524;
            border-radius: 20px;
            border: 1px solid #C48B77;
            font-size: 24px;
            color: #FED7B5;
            line-height: 32px;
            padding: 0 20px;
          }
        }

        .followers {
          font-size: 28px;
          color: #8A8A8A;
          line-height: 32px;
        }
      }
    }
  }

  .error_container {
    display: flex;
    align-items: center;
    
    .icon_error {
      width: 32px;
      height: 32px;
      margin-right: 20px;
    }

    .error_desc {
      font-size: 24px;
      color: #FF3141;
      line-height: 34px;
    }
  }

  .success_container {
    display: flex;
    flex-direction: column;
    padding: 18px 24px 0;

    .success_top_container {
      display: flex;
      align-items: center;
      margin-bottom: 32px;

      .icon_confirm {
        width: 48px;
        height: 48px;
        margin-right: 20px;
      }

      .bind_success {
        font-weight: bold;
        font-size: 36px;
        color: #020202;
        line-height: 50px;
      }
    }

    .success_desc {
      font-size: 28px;
      color: #6E6E6E;
      line-height: 40px;
    }
  }
}