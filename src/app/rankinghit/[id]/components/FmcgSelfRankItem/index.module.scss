.fmcg_rank_item_container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F5F5F5;
  margin-bottom: 50px;
  padding: 10px 24px 12px;

  .rank_label {
    font-weight: bold;
    font-size: 24px;
    color: #202020;
    line-height: 28px;
    margin-right: 24px;
  }

  .avatar_container {
    border-radius: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 24px;

    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 24px;
    }

    .tag {
      height: 24px;
      margin-top: -4px;
      border: 2px solid #FE6D45;
      border-radius: 4px;
      padding: 0 8px;
      font-size: 16px;
      line-height: 18px;
      color: #FE6D45;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
    }
  }

  .middle_container {
    display: flex;
    flex-direction: column;
    margin-right: 24px;

    .account {
      font-size: 26px;
      color: #303030;
      line-height: 36px;
      white-space: nowrap; /* 让文本不换行 */
      overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; /* 使用省略号表示被省略的文本 */
      max-width: 196px; /* 设置最大宽度为100px */
    }

    .btn_switch {
      font-size: 20px;
      color: #FE6D45;
      line-height: 28px;
    }
  }

  .gmv {
    font-size: 28px;
    color: #303030;
    line-height: 32px;
    font-weight: bold;
    margin-right: 24px;
  }

  .score_label {
    font-size: 20px;
    color: #478BFF;
    line-height: 28px;
  }
}