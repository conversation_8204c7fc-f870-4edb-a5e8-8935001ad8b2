/* eslint-disable @next/next/no-img-element */
'use client'
import React, { useState, useEffect, useMemo } from 'react'
import Image from 'next/image'
import { useRouter } from '@/lib/hooks/useRouter'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { i18n } from '@/lib/client/i18n'
import placeholder from '@/../public/images/common/placeholder.png'
import styles from './index.module.scss'
import { formatNumberWithCommas } from '@/lib/utils'

type FmcgSelfRankItemProps = {
  id: string
  item: any
  dataVersion: string
} & BaseComponentProps

const bgColors = [
  'rgba(255,217,103,0.1)',
  'rgba(148,207,255,0.1)',
  'rgba(255,165,74,0.1)',
  'rgba(196,196,196,0.1)'
]

const FmcgSelfRankItem: React.FC<FmcgSelfRankItemProps> = props => {
  const { id, dataVersion, item } = props
  const router = useRouter()

  const [error, setError] = React.useState(false)
  const [hasSwitchTiktokAccount, setHasSwitchTiktokAccount] = useState(false)

  const makeSureJsHandler = async () => {
    if (webview) {
      const res = await webview.send(WebviewEvents.hasJavaScriptHandler, {
        handlerName: WebviewEvents.switchTiktokAccount
      })
      setHasSwitchTiktokAccount(!!res)
    }
    setHasSwitchTiktokAccount(true)
  }

  useEffect(() => {
    makeSureJsHandler()
  }, [])

  const handleToScoreDetail = () => {
    router.push(`/rankinghit_score/${id}?dataVersion=${dataVersion}`)
  }

  const handleSwitchTiktokAccount = () => {
    if (webview) {
      webview.send(WebviewEvents.switchTiktokAccount)
    }
  }

  const bgColor = useMemo(() => {
    if (item.rewardLevel === 1) {
      return bgColors[0]
    }
    if (item.rewardLevel === 2) {
      return bgColors[1]
    }
    if (item.rewardLevel === 3) {
      return bgColors[2]
    }
    return bgColors[3]
  }, [item])

  return (
    <div
      className={styles.fmcg_rank_item_container}
      style={{ background: bgColor }}
    >
      <div className={styles.rank_label}>
        TOP{item.rank ? (item.rank > 999 ? '999+' : item.rank) : 0}
      </div>
      <div className={styles.avatar_container}>
        {!error && item.avatar ? (
          <img
            className={styles.avatar}
            src={item.avatar}
            onError={() => {
              setError(true)
            }}
          ></img>
        ) : (
          <Image src={placeholder} className={styles.avatar} alt=""></Image>
        )}
        <div className={styles.tag}>{i18n.t('Activity.Me')}</div>
      </div>
      <div className={styles.middle_container}>
        <div className={styles.account}>{item.displayName || ''}</div>
        {hasSwitchTiktokAccount && (
          <div
            className={styles.btn_switch}
            onClick={handleSwitchTiktokAccount}
          >
            {i18n.t('Activity.换账号')}
          </div>
        )}
      </div>
      <div className={styles.gmv}>
        {formatNumberWithCommas(parseFloat(item.score || 0))}
      </div>
      <div className={styles.score_label} onClick={handleToScoreDetail}>
        {`${i18n.t('Activity.积分明细')}>`}
      </div>
    </div>
  )
}

export default FmcgSelfRankItem
