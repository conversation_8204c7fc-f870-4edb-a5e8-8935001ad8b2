'use client'
import React, { useMemo } from 'react'
import { webview } from '@/lib/client/webview'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'
import { getToken, makeSureInApp, rpxToPx } from '@/lib/client/utils'
import { WebviewEvents } from '@/lib/client/webview/events'
import { formatNumberWithCommas, isRegionTH } from '@/lib/utils'
import icon_alarm from '@/../public/images/fmcg/icon_alarm.png'
import styles from './index.module.scss'
import Image from 'next/image'

type FmcgAuthCardProps = {
  detail: any
  getCampaignDetail: any
} & BaseComponentProps

const lineUrl = 'https://line.me/R/ti/p/@932acdjr'

const FmcgAuthCard: React.FC<FmcgAuthCardProps> = props => {
  const router = useRouter()
  const { detail, getCampaignDetail } = props
  // status 0-已关闭 1-活动未开始 2-活动中 3-活动结束
  const { status, userRankInfo } = detail
  const {
    currentRewardName, // 当前可获得的奖品名称
    nextRewardName, // 下一个奖品名称 (未授权时，此奖为终极大奖)
    pointsToNextReward, // 差多少积分可以领取上一个大奖
    totalUsers, // 参与活动的总人数
    isInWinningList, // 是否进入获奖名单
    canClaimFinalReward, // 保持当前积分即可领取终极大奖
    isAuth // 是否授权
  } = userRankInfo || {}

  const showDescStatus = useMemo(() => {
    // 没有开始时间为异常情况，正常情况下应该不存在
    if (!detail.startTime) {
      return -1
    }
    let tempShowDescStatus = 1
    // 活动已关闭或活动已结束
    if (status === 0 || status === 3) {
      tempShowDescStatus = 6
    }
    // 活动未开始
    if (status === 1) {
      tempShowDescStatus = 5
    }
    if (status === 2 && !isAuth) {
      tempShowDescStatus = 1
    } else if (isAuth && status === 2 && canClaimFinalReward) {
      tempShowDescStatus = 4
    } else if (isAuth && status === 2 && isInWinningList) {
      tempShowDescStatus = 3
    } else if (isAuth && status === 2) {
      tempShowDescStatus = 2
    }
    return tempShowDescStatus
  }, [detail.startTime, status, isAuth, canClaimFinalReward, isInWinningList])

  const showBtnStatus = useMemo(() => {
    if (!detail.startTime) {
      return -1
    }
    let tempShowBtnStatus = -1
    if (status === 2 && isAuth) {
      tempShowBtnStatus = 2
    } else if (status === 2 && !isAuth) {
      tempShowBtnStatus = 1
    } else if (status === 1) {
      tempShowBtnStatus = 3
    }
    return tempShowBtnStatus
  }, [detail.startTime, status, isAuth]) // 1 立即授权加入 2 查看活动商品 3 添加LINE群

  const handleBtnClick = async () => {
    if (showBtnStatus === 1) {
      if (webview) {
        if (!(await getToken())) {
          await webview?.send(WebviewEvents.makeSureLogined)
          window.location.reload()
          return
        }
        await webview?.send(WebviewEvents.makeSureTikTokAuthed)
        window.location.reload()
      } else {
        makeSureInApp()
      }
    } else if (showBtnStatus === 2) {
      router.push('/activity/goojodoq_product')
    } else if (showBtnStatus === 3) {
      if (webview) {
        webview?.send(WebviewEvents.launch, {
          url: lineUrl
        })
      } else {
        location.href = lineUrl
      }
    }
  }

  const addAlarm = (content: any) => (
    <div
      className={styles.auth_content_container}
      style={{ marginBottom: showBtnStatus === -1 ? 0 : rpxToPx(32) }}
    >
      <Image className={styles.icon_alarm} src={icon_alarm} alt="" />
      {content}
    </div>
  )

  return (
    showDescStatus != -1 && (
      <div className={styles.fmcg_auth_card_container}>
        {showDescStatus === 1 &&
          addAlarm(
            <div
              className={styles.auth_desc}
              dangerouslySetInnerHTML={{
                __html: i18n.t(
                  'Activity.1,997个TikTok达人已加入活动，立即授权TikTok账号，越早加入，获得价值569,900泰铢的BYD汽车的几率更高！',
                  {
                    totalUsers: formatNumberWithCommas(totalUsers || 0),
                    nextPrice: nextRewardName || ''
                  }
                )
              }}
            ></div>
          )}
        {showDescStatus === 2 &&
          addAlarm(
            <div
              className={styles.auth_desc}
              dangerouslySetInnerHTML={{
                __html: i18n.t(
                  'Activity.您暂时未进入获奖名单，距离获得大奖只差 6,000积分，快去推广指定活动商品！',
                  {
                    nextPoints: formatNumberWithCommas(parseFloat(pointsToNextReward || 0))
                  }
                )
              }}
            ></div>
          )}
        {showDescStatus === 3 &&
          addAlarm(
            <div
              className={styles.auth_desc}
              dangerouslySetInnerHTML={{
                __html: i18n.t(
                  'Activity.恭喜您，已经入获奖名单！再获得5,999积分，升级奖品至฿2,000!',
                  {
                    nextPoints: formatNumberWithCommas(parseFloat(pointsToNextReward || 0)),
                    nextPrice: nextRewardName || ''
                  }
                )
              }}
            ></div>
          )}
        {showDescStatus === 4 &&
          addAlarm(
            <div
              className={styles.auth_desc}
              dangerouslySetInnerHTML={{
                __html: i18n.t(
                  'Activity.恭喜您！即将获得价值569,900泰铢的BYD汽车！继续提升积分，防止被其他优秀达人超越！',
                  {
                    nextPrice: nextRewardName || ''
                  }
                )
              }}
            ></div>
          )}
        {showDescStatus === 5 &&
          addAlarm(
            <div
              className={styles.auth_desc}
              dangerouslySetInnerHTML={{
                __html: i18n.t(
                  'Activity.活动即将开始！添加LINE群获取第一手活动消息，早参加，价值569,900泰铢的BYD汽车也许就是你的了！',
                  {
                    nextPrice: nextRewardName || ''
                  }
                )
              }}
            ></div>
          )}
        {showDescStatus === 6 &&
          addAlarm(
            <div>
              <div
                className={styles.auth_desc}
                dangerouslySetInnerHTML={{
                  __html: i18n.t(
                    'Activity.如对积分有疑问，可在5月2日之前通过添加LINE反馈，修正积分，逾时不补！'
                  )
                }}
              ></div>
              <div
                className={styles.auth_desc}
                dangerouslySetInnerHTML={{
                  __html: i18n.t(
                    'Activity.积分反馈时间：2025年5月1日-2025年5月2日'
                  )
                }}
              ></div>
            </div>
          )}
        {showBtnStatus !== -1 && (
          <div
            className={styles.btn_auth}
            style={{
              backgroundColor: showBtnStatus === 3 ? '#70B603' : '#FE6D45'
            }}
            onClick={handleBtnClick}
          >
            {showBtnStatus === 1 && (
              <div className={styles.btn_label}>
                {i18n.t('Activity.立即授权加入')}
              </div>
            )}
            {showBtnStatus === 2 && (
              <div className={styles.btn_label}>
                {i18n.t('Activity.查看活动商品')}
              </div>
            )}
            {showBtnStatus === 3 && (
              <div className={styles.btn_label}>
                {isRegionTH() ? i18n.t('Activity.添加LINE群') : i18n.t('Activity.添加Zalo群')}
              </div>
            )}
          </div>
        )}
      </div>
    )
  )
}

export default FmcgAuthCard
