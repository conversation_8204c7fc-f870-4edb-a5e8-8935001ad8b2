/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import React, { useEffect, useState } from 'react'
import { InfiniteScroll } from 'antd-mobile'
import dayjs from 'dayjs'
import { i18n } from '@/lib/client/i18n'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import FmcgSelfRankItem from '../FmcgSelfRankItem'
import { Dialog } from 'react-vant'
import icon_branch_left from '@/../public/images/fmcg/icon_branch_left.png'
import icon_branch_right from '@/../public/images/fmcg/icon_branch_right.png'
import icon_tip from '@/../public/images/fmcg/icon_tip.png'
import Image from 'next/image'
import FmcgTopRankItem from '../FmcgTopRankItem'
import { getSellerCampaignRankGroups } from '@/app/api/api-uchoice/seller/campaigns/rankGroups'
import { getSellerCampaignRank } from '@/app/api/api-uchoice/seller/campaigns/rank'
import styles from './index.module.scss'

type FmcgRankCardProps = {
  id: string
  detail: any
} & BaseComponentProps

const bgColors = [
  'rgba(255,217,103,0.1)',
  'rgba(148,207,255,0.1)',
  'rgba(255,165,74,0.1)',
  'rgba(196,196,196,0.1)'
]

const pageSize = 10

const InfiniteScrollContent = ({ hasMore }: { hasMore?: boolean }) => {
  return (
    <>
      {hasMore ? (
        <div className={styles.no_more}>Loading...</div>
      ) : (
        <div className={styles.no_more}>{i18n.t('TiktokData.无更多数据')}</div>
      )}
    </>
  )
}

const FmcgRankCard: React.FC<FmcgRankCardProps> = props => {
  const { id, detail } = props
  const { userRankInfo, dataVersion } = detail

  const rankGroupsTempRef = React.useRef<any[]>([])
  const [rankGroups, setRankGroups] = useState<any[]>([])
  const [rankGroupsLoading, setRankGroupsLoading] = useState<boolean>(false)
  const [rankLoading, setRankLoading] = useState<boolean>(false)
  const rankLoadingRef = React.useRef(rankLoading)
  const [hasMore, setHasMore] = useState<boolean>(true)
  const rewardIdRef = React.useRef<number>(0)
  const pageNoRef = React.useRef<number>(1)

  const handleSetRankGroups = (rankGroupsTemp: any[]) => {
    const rankGroupsTempTemp: any[] = []
    let hasMoreTemp: boolean = false
    for (let i = 0; i < rankGroupsTemp.length; i++) {
      const group: any = rankGroupsTemp[i]
      rankGroupsTempTemp.push(group)
      if (group.hasMoreData) {
        rewardIdRef.current = group.rewardId
        pageNoRef.current = group.rankList.length / pageSize + 1
        hasMoreTemp = true
        break
      }
    }
    setHasMore(hasMoreTemp)
    setRankGroups([...rankGroupsTempTemp])
  }

  const requestSellerCampaignRankGroups = async () => {
    try {
      setRankGroupsLoading(true)
      const result: any = await getSellerCampaignRankGroups(id, dataVersion)
      setRankGroupsLoading(false)
      if (result.code === 200 && result.result) {
        rankGroupsTempRef.current = result.result
        handleSetRankGroups(rankGroupsTempRef.current)
      }
    } catch (error) {
      setRankGroupsLoading(false)
    }
  }

  const requestSellerCampaignRank = async () => {
    if (rankLoadingRef.current) {
      return
    }
    if (!hasMore) {
      return
    }
    try {
      setRankLoading(true)
      rankLoadingRef.current = true
      const result: any = await getSellerCampaignRank(id, dataVersion, {
        pageNo: pageNoRef.current,
        pageSize,
        rewardId: rewardIdRef.current
      })
      setRankLoading(false)
      rankLoadingRef.current = false
      if (result.code === 200 && result.result) {
        const { list, total } = result.result
        if (list && list.length > 0) {
          const group: any = rankGroupsTempRef.current.find(
            (group: any) => group.rewardId === rewardIdRef.current
          )
          group.rankList = [...group.rankList, ...list]
          group.hasMoreData = group.rankList.length < total
          handleSetRankGroups(rankGroupsTempRef.current)
        }
      }
    } catch (error) {
      setRankLoading(false)
      rankLoadingRef.current = false
    }
  }

  useEffect(() => {
    requestSellerCampaignRankGroups()
  }, [])

  const handleShowDialog = tip => {
    Dialog.alert({
      title: i18n.t('Activity.积分排行榜'),
      message: tip,
      confirmButtonText: i18n.t('Activity.确定'),
      confirmButtonColor: '#FE6D45'
    })
  }

  return (
    <div className={styles.fmcg_rank_card_container}>
      <div className={styles.title_container}>
        <div></div>
        <div className={styles.content_container}>
          <Image className={styles.icon_branch} src={icon_branch_left} alt="" />
          <div className={styles.title}>{i18n.t('Activity.积分排行榜')}</div>
          <Image
            className={styles.icon_branch}
            src={icon_branch_right}
            alt=""
          />
        </div>
        <div
          className={styles.icon_tip_container}
          onClick={() =>
            handleShowDialog(
              i18n.t(
                'Activity.参与积分活动的达人，通过发布带货视频、出单获取积分，积分数据非实时，每日将更新昨日获得的积分。如有疑问，请点击页面右上角客服'
              )
            )
          }
        >
          <Image alt="" src={icon_tip} className={styles.icon_tip}></Image>
        </div>
      </div>
      <div className={styles.desc}>
        <span style={{ marginRight: 4 }}>{i18n.t('Activity.数据更新')}</span>
        <span>
          {dayjs(dataVersion || Date.now()).format('HH:mm:ss DD/MM/YYYY')}
        </span>
      </div>
      <div className={styles.rank_list}>
        {userRankInfo && userRankInfo.displayName && (
          <FmcgSelfRankItem
            id={id}
            dataVersion={dataVersion}
            item={userRankInfo}
          ></FmcgSelfRankItem>
        )}
        {rankGroups.length > 0 &&
          rankGroups.map((group, index) => (
            <FmcgTopRankItem
              key={index}
              logo={group.logoUrl || ''}
              title={group.rewardName || ''}
              needScore={group.needScore || 0}
              list={group.rankList || []}
              background={
                group.rewardId !== 0
                  ? bgColors[group.rewardLevel < 4 ? group.rewardLevel - 1 : 3]
                  : '#ffffff'
              }
            ></FmcgTopRankItem>
          ))}
        {!rankGroupsLoading && rankGroups.length === 0 && (
          <StatusView
            status={StatusViewType.empty}
            className={styles.status_view}
          ></StatusView>
        )}
        {rankGroupsLoading && (
          <StatusView
            status={StatusViewType.loading}
            className={styles.status_view}
          ></StatusView>
        )}
        {rankGroups.length !== 0 && (
          <InfiniteScroll
            loadMore={requestSellerCampaignRank}
            hasMore={hasMore}
          >
            <InfiniteScrollContent hasMore={hasMore} />
          </InfiniteScroll>
        )}
      </div>
    </div>
  )
}

export default FmcgRankCard
