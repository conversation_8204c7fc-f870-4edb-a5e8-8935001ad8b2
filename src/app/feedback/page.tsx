import { metadataTemplate } from '@/lib/server/utils'
import React, { Suspense } from 'react'
import { i18nS } from '@/lib/server/i18n'
import dynamic from 'next/dynamic'

const Inner = dynamic(() => import('./inner'), {
  ssr: false
})

export async function generateMetadata() {
  return metadataTemplate({
    showNativeNavBar: true,
    nativeNavBarTitle: i18nS.t('Feedback.帮助与反馈')
  })
}

export default async function Index() {
  return (
    <Suspense>
      <Inner></Inner>
    </Suspense>
  )
}
