'use client'

import { i18n } from '@/lib/client/i18n'
import { useMemo, useState, useEffect } from 'react'
import { Image } from '@/components/Image'
import pink_tips from '@/../public/images/common/pink_tips.png'
import success from '@/../public/images/common/success.png'
import './index.css'
import { px2rem } from '@/lib/client/utils'
import { webview } from '@/lib/client/webview'
import { AppColors } from '@/lib/const'
import { FileUploaders, UploadedData } from '@/components/FileUploader'
import { loading } from '@/lib/client/loading'
import { feedbackSubmit } from '../api/member/feedback/submit/request'
import { useRouter } from '@/lib/hooks/useRouter'
import { Input } from 'react-vant'

const SuccessComponent = () => {
  return (
    <div className="flex flex-col items-center px-[48px] pt-[98px]">
      <Image src={success} className="size-[140px]"></Image>
      <span className="mt-[24px] text-[32px] text-black02">
        {i18n.t('Feedback.反馈成功！')}
      </span>
      <span className="mt-[12px] text-[28px] text-gray6E">
        {i18n.t('Feedback.感谢您的反馈，我们会认真评估')}
      </span>
    </div>
  )
}

const Inner = () => {
  const router = useRouter()

  const [content, setContent] = useState('')
  const [uploadedDatas, setUploadedDatas] = useState<UploadedData[]>([])
  const [submited, setSubmitted] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      // Close keyboard on scroll
      if (document.activeElement instanceof HTMLElement) {
        document.activeElement.blur()
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const overContent = useMemo(() => {
    return content.length > 800
  }, [content])

  const onSubmit = async () => {
    if (content.length === 0) {
      return
    }

    loading.show()
    const res = await feedbackSubmit({
      content,
      videoUrlList: uploadedDatas.map(item => item.resourceId)
    })
    loading.hide()
    if (res.code === 200) {
      setSubmitted(true)
    }
  }

  return (
    <div className="feedback pl-[24px]">
      {submited ? (
        <SuccessComponent></SuccessComponent>
      ) : (
        <>
          <div className="pr-[24px]">
            <div
              className="mt-[26px] w-full rounded-[16px] p-[24px]"
              style={{
                backgroundColor: overContent
                  ? 'rgba(254, 44, 85, 0.08)'
                  : AppColors.background,
                borderColor: '#FE2C55',
                borderWidth: overContent ? px2rem(2) : 0
              }}
            >
              <Input.TextArea
                className="h-[260px] text-[24px]"
                onChange={value => setContent(value)}
                placeholder={i18n.t(
                  'Feedback.请详细描述您在使用uchoice时遇到的问题，或者您对uchoice功能的建议，以便于我们为您提供更好的服务'
                )}
                rows={7}
                autoSize={false}
                style={{
                  // @ts-ignore
                  '--rv-input-text-color': overContent ? 'black' : '#8A8A8A',
                  '--rv-input-textarea-height': px2rem(260)
                }}
              />
            </div>

            {overContent ? (
              <div className="flex items-center pt-[24px]">
                <Image src={pink_tips} className="size-[32px]"></Image>
                <span className="ml-[8px] text-[24px] text-[#FE2C55]">
                  {i18n.t('Feedback.最多不超过800字符')}
                </span>
              </div>
            ) : (
              <div className="pt-[24px] text-[24px] text-grayCC">
                {i18n.t('Feedback.最多不超过800字符')}
              </div>
            )}
          </div>

          <div>
            <div className="mb-[28px] mt-[24px] flex items-center">
              <span className="text-[32px] font-bold text-black02">
                {i18n.t('Upload Images')}
              </span>
              <span className="text-[28px] text-grayCC">
                （{i18n.t('Feedback.不超过50MB')}）
              </span>
            </div>
            <FileUploaders
              onChangeUploadedDatas={setUploadedDatas}
            ></FileUploaders>
          </div>
        </>
      )}

      <div
        className="absolute flex h-[80px] w-[702px] items-center justify-center rounded-[8px] bg-primary"
        style={{
          backgroundColor: submited
            ? AppColors.primary
            : content.length > 0
            ? AppColors.primary
            : AppColors.grayD9,
          bottom: webview
            ? `${px2rem(webview?.getData().bottomSafeArea + 12)}`
            : 12
        }}
        onClick={() => {
          if (submited) {
            router.back()
          } else {
            onSubmit()
          }
        }}
      >
        <span className="text-[32px] text-white">
          {submited ? i18n.t('Common.Confirm') : i18n.t('Feedback.提交')}
        </span>
      </div>
    </div>
  )
}

export default Inner
