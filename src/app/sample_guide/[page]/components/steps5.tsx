import { i18n } from '@/lib/client/i18n'
import styles from '../index.module.scss'
import Image from 'next/image'
import LangsImg from './langs_img'
import { isRegionTH } from '@/lib/utils'

const Stepfive = () => {
  const arr = [
    i18n.t('Showcase.商家发货之后'),
    i18n.t('Showcase.达人收到样品后')
  ]

  return (
    <div className={styles.stepfive_content}>
      <div className={styles.title}>{i18n.t('Showcase.收样品进行履约')}</div>
      {arr.map((item, index) => (
        <div className={styles.direction} key={index}>
          {item}
        </div>
      ))}
      <div className={styles.tips}>
        <span className={styles.attention}>{i18n.t('Showcase.注意')} </span>
        {i18n.t('Showcase.获得视频投流奖励')}
      </div>
      <div className={styles.stepfour_content}>
        <div className={styles.four_img}>
          <LangsImg imgIndex={9}></LangsImg>
        </div>
        <div className={styles.stepfour_arrow}>
          <Image
            alt=""
            src="/images/showcase/arrow_6.png"
            className={styles.stepfour_arrow_img}
            width={58}
            height={11}
          ></Image>
        </div>
        <div className={styles.four_img}>
          <LangsImg imgIndex={10}></LangsImg>
        </div>
      </div>
      <div className={styles.five_arrow_img}>
        <Image
          alt=""
          src="/images/showcase/arrow_2.png"
          className={styles.arrow_img}
          width={135}
          height={116}
        ></Image>
      </div>
      <div className={styles.stepfour_submit}>
        <div className={styles.four_img}>
          <LangsImg imgIndex={11}></LangsImg>
          <div className={styles.direction}>{i18n.t('Showcase.提交之后')}</div>
        </div>

        <div className={styles.four_img}>
          <LangsImg imgIndex={12}></LangsImg>
        </div>
      </div>
      <div className={styles.five_arrow_img}>
        <Image
          alt=""
          src="/images/showcase/arrow_2.png"
          className={styles.arrow_img}
          width={135}
          height={116}
        ></Image>
      </div>
      <div className={styles.stepfour_submit}>
        <div className={styles.four_img}>
          <LangsImg imgIndex={13}></LangsImg>
        </div>

        <div className={styles.four_img}>
          <LangsImg imgIndex={14}></LangsImg>
        </div>
      </div>
      <div className={styles.direction}>{i18n.t('Showcase.履约审核成功')}</div>
      <div
        className={
          isRegionTH() ? styles.stepfour_success : styles.stepfour_success_vi
        }
      >
        <LangsImg imgIndex={15} width={100} height={211}></LangsImg>

        {isRegionTH() ? (
          <>
            <LangsImg imgIndex={16} width={100} height={211}></LangsImg>
            <LangsImg imgIndex={17} width={100} height={211}></LangsImg>
          </>
        ) : null}
      </div>
      <div className={styles.direction}>
        {i18n.t('Showcase.释放一个样品名额')}
      </div>
    </div>
  )
}

export default Stepfive
