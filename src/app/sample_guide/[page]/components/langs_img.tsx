import { i18n } from '@/lib/client/i18n'
import styles from '../index.module.scss'
import Image from 'next/image'
import { rpxToPx } from '@/lib/client/utils'
import { isLanguageTH, isLanguageVI, isRegionTH } from '@/lib/utils'
import { ImagePreview } from 'react-vant'
const LangsImg = ({ imgIndex, width = 126, height = 268 }) => {
  const lang: any = localStorage.getItem('language')
  const imgPathImg = `/images/showcase/img_${imgIndex}.png`
  const imgPathThImg = `/images/showcase/img_th_${imgIndex}.png`
  const imgPathViImg = `/images/showcase/img_vi_${imgIndex}.png`
  const imgPathViEnImg = `/images/showcase/img_${imgIndex}_vi_en.png`
  if (isRegionTH()) {
    if (isLanguageTH()) {
      return (
        <div
          onClick={() => {
            ImagePreview.open({
              images: [imgPathThImg],
              closeable: true
            })
          }}
        >
          <Image
            alt={imgPathThImg}
            src={imgPathThImg}
            className={styles.step_img}
            style={{
              width: rpxToPx(width * 2),
              height: rpxToPx(height * 2)
            }}
            width={width}
            height={height}
          ></Image>
        </div>
      )
    } else {
      return (
        <div
          onClick={() => {
            ImagePreview.open({
              images: [imgPathImg],
              closeable: true
            })
          }}
        >
          <Image
            alt={imgPathImg}
            src={imgPathImg}
            className={styles.step_img}
            style={{
              width: rpxToPx(width * 2),
              height: rpxToPx(height * 2)
            }}
            width={width}
            height={height}
          ></Image>
        </div>
      )
    }
  } else {
    if (isLanguageVI()) {
      return (
        <>
          {imgIndex != 16 && imgIndex != 17 ? (
            <div
              onClick={() => {
                ImagePreview.open({
                  images: [imgPathViImg],
                  closeable: true
                })
              }}
            >
              <Image
                alt={imgPathViImg}
                src={imgPathViImg}
                className={styles.step_img}
                style={{
                  width: rpxToPx(width * 2),
                  height: rpxToPx(height * 2)
                }}
                width={width}
                height={height}
              ></Image>
            </div>
          ) : null}
        </>
      )
    } else {
      return (
        <>
          {imgIndex != 16 && imgIndex != 17 ? (
            <div
              onClick={() => {
                ImagePreview.open({
                  images: [imgPathViEnImg],
                  closeable: true
                })
              }}
            >
              <Image
                alt={imgPathViEnImg}
                src={imgPathViEnImg}
                className={styles.step_img}
                style={{
                  width: rpxToPx(width * 2),
                  height: rpxToPx(height * 2)
                }}
                width={width}
                height={height}
              ></Image>
            </div>
          ) : null}
        </>
      )
    }
  }
}

export default LangsImg
