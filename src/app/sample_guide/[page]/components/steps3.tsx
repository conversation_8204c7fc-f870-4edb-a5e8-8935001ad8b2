import { i18n } from '@/lib/client/i18n'
import styles from '../index.module.scss'
import Image from 'next/image'
import LangsImg from './langs_img'
import { WebViewToPages, WebviewEvents } from '@/lib/client/webview/events'
import { webview } from '@/lib/client/webview'
import { isLanguageTH } from '@/lib/utils'

const StepThree = ({ imgIndex }) => {
  return (
    <>
      <div className={styles.stepOne_content}>
        <div className={styles.stepThree_left}>
          <div className={styles.title}>
            {i18n.t('Showcase.填写申请样品信息')}
          </div>

          <div className={styles.direction}>
            {i18n.t('Showcase.在样品申请页面，您需要：填写或者检查样品')}
            <br />

            <span>{i18n.t('Showcase.step3_2')}</span>
            <br />
            <span>{i18n.t('Showcase.step3_3')}</span>
            {isLanguageTH() && (
              <span
                className={styles.sample_btn}
                onClick={() => {
                  if (webview) {
                    webview.send(WebviewEvents.toPage, {
                      name: WebViewToPages.sampleApplyRule
                    })
                  }
                }}
              >
                {i18n.t('Showcase.样品申请规则')}
              </span>
            )}
            {!isLanguageTH() && (
              <>
                <span
                  className={styles.sample_btn}
                  onClick={() => {
                    if (webview) {
                      webview.send(WebviewEvents.toPage, {
                        name: WebViewToPages.sampleApplyRule
                      })
                    }
                  }}
                >
                  {i18n.t('Showcase.样品申请规则')}
                </span>
                <span>].</span>
              </>
            )}
            <br />
            {i18n.t('Showcase.核对申请样品数量')}

            <br />
            <span>{i18n.t('Showcase.step3_4')}</span>
            {i18n.t('Showcase.step3_5') && (
              <div>{i18n.t('Showcase.step3_5')}</div>
            )}
          </div>
          <div className={styles.direction}>
            {i18n.t('Showcase.点击申请样品按钮')}
          </div>
        </div>
        <div className={styles.stepThree_right}>
          <LangsImg imgIndex={imgIndex}></LangsImg>
        </div>
      </div>
      <div className={styles.arrow_img}>
        <Image
          alt=""
          src="/images/showcase/arrow_2.png"
          className={styles.arrow_img}
          width={135}
          height={116}
        ></Image>
      </div>
      <div className={styles.stepfour_content}>
        <div className={styles.four_img}>
          <LangsImg imgIndex={4}></LangsImg>
        </div>
        <div className={styles.stepfour_arrow}>
          <Image
            alt=""
            src="/images/showcase/arrow_4.png"
            className={styles.stepThree_arrow_img}
            width={43}
            height={11}
          ></Image>
        </div>
        <div className={styles.four_img}>
          <LangsImg imgIndex={5}></LangsImg>
        </div>
      </div>
      <div className={styles.arrow_img}>
        <Image
          alt=""
          src="/images/showcase/arrow_1.png"
          className={styles.arrow_img}
          width={135}
          height={116}
        ></Image>
      </div>
    </>
  )
}

export default StepThree
