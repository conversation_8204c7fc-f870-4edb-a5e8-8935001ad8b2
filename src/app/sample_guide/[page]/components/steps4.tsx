import { i18n } from '@/lib/client/i18n'
import styles from '../index.module.scss'
import Image from 'next/image'
import LangsImg from './langs_img'

const Stepfour = ({ imgIndex }) => {
  const arr = [i18n.t('Showcase.重新打开app')]

  return (
    <div>
      <div className={styles.stepTwo_content}>
        <div className={styles.stepTwo_left}>
          <LangsImg imgIndex={imgIndex}></LangsImg>
        </div>
        <div className={styles.stepTwo_right}>
          <div className={styles.title}>{i18n.t('Showcase.等待审核')}</div>
          {arr.map((item, index) => (
            <div className={styles.direction} key={index}>
              {item}
            </div>
          ))}
        </div>
      </div>
      <div className={styles.stepfour_box}>
        <div className={styles.stepfour_direction}>
          <div className={styles.direction} style={{ marginRight: '20px' }}>
            {i18n.t('Showcase.平台审核')}
          </div>
          <div className={styles.direction}>{i18n.t('Showcase.关闭状态')}</div>
        </div>
        <div className={styles.stepfour_content}>
          <div className={styles.four_img}>
            <LangsImg imgIndex={7}></LangsImg>
          </div>
          <div className={styles.stepfour_arrow}>
            <Image
              alt=""
              src="/images/showcase/arrow_8.png"
              className={styles.stepfour_arrow_img}
              width={58}
              height={11}
            ></Image>
          </div>
          <div className={styles.four_img}>
            <LangsImg imgIndex={8}></LangsImg>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Stepfour
