import { i18n } from '@/lib/client/i18n'
import styles from '../index.module.scss'
import Image from 'next/image'
import LangsImg from './langs_img'

const StepOne = ({ imgIndex }) => {
  const arr = [
    i18n.t('Showcase.选择想申请的样品'),
    i18n.t('Showcase.进入商品详情页'),
    i18n.t('Showcase.点击申请样品按钮')
  ]

  return (
    <div className={styles.stepOne_content}>
      <div className={styles.stepOne_left}>
        <div className={styles.title}>{i18n.t('Showcase.选择样品')}</div>
        {arr.map((item, index) => (
          <div className={styles.direction} key={index}>
            {item}
          </div>
        ))}
      </div>
      <div className={styles.stepOne_right}>
        <LangsImg imgIndex={imgIndex}></LangsImg>
      </div>
    </div>
  )
}

export default StepOne
