import { i18n } from '@/lib/client/i18n'
import styles from '../index.module.scss'
import Image from 'next/image'
import LangsImg from './langs_img'

const StepTwo = ({ imgIndex }) => {
  const arr = [
    i18n.t('Showcase.step2_1'),
    i18n.t('Showcase.如果您的账号没有授权'),
    i18n.t('Showcase.则进入申请样品页面')
  ]

  return (
    <div className={styles.stepTwo_content}>
      <div className={styles.stepTwo_left}>
        <LangsImg imgIndex={imgIndex}></LangsImg>
      </div>
      <div className={styles.stepTwo_right}>
        <div className={styles.title}>{i18n.t('Showcase.授权')}</div>
        {arr.map(
          (item, index) =>
            item && (
              <div className={styles.direction} key={index}>
                {item}
              </div>
            )
        )}
      </div>
    </div>
  )
}

export default StepTwo
