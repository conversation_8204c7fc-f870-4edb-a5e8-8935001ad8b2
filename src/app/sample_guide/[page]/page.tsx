'use client'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import React, { useEffect, useRef, useState } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import { webview } from '@/lib/client/webview'
import { getBaseH5Url } from '@/lib/utils'
import { useRouter } from '@/lib/hooks/useRouter'
import StepOne from './components/steps1'
import Image from 'next/image'
import StepTwo from './components/steps2'
import StepThree from './components/steps3'
import Stepfour from './components/steps4'
import Stepfive from './components/steps5'
import { WebViewToPages, WebviewEvents } from '@/lib/client/webview/events'
import { getNewerWaterfallConfig } from '@/app/api/api-uchoice/samplePool/getConfig'
import { loading as Loading } from '@/lib/client/loading'
import styles from './index.module.scss'

const SampleGuide = () => {
  const mounted = useMounted()
  const router = useRouter()
  const [showBtn, setShowBtn] = useState<boolean>(false)
  const collectionIdRef = useRef(null)

  useEffect(() => {
    // NewerWaterfallConfig()
  }, [])
  const NewerWaterfallConfig = () => {
    try {
      Loading.show()
      getNewerWaterfallConfig()
        .then(res => {
          Loading.hide()
          if (res.code === 200 && res.result) {
            setShowBtn(true)
            collectionIdRef.current = res.result
          }
        })
        .catch(() => {
          Loading.hide()
        })
    } catch (e) {
      Loading.hide()
    }
  }
  const toNewComerProductPage = () => {
    if (collectionIdRef.current) {
      router.push(`/newer/collections/${collectionIdRef.current}`)
    }
  }

  return (
    mounted && (
      <TransparentNavPage
        title={i18n.t('Showcase.申样流程')}
        transparent={false}
        hide={!webview}
      >
        <div className={styles.container}>
          <StepOne imgIndex={1} />
          <div className={styles.arrow_img}>
            <Image
              alt=""
              src="/images/showcase/arrow_1.png"
              className={styles.arrow_img}
              width={135}
              height={116}
            ></Image>
          </div>
          <StepTwo imgIndex={2} />
          <div className={styles.arrow_img}>
            <Image
              alt=""
              src="/images/showcase/arrow_2.png"
              className={styles.arrow_img}
              width={135}
              height={116}
            ></Image>
          </div>
          <StepThree imgIndex={3} />
          <Stepfour imgIndex={6} />
          <div className={styles.arrow_img}>
            <Image
              alt=""
              src="/images/showcase/arrow_2.png"
              className={styles.arrow_img}
              width={135}
              height={116}
            ></Image>
          </div>
          <Stepfive />
          {showBtn && (
            <div
              className={styles.sample_guide_footer}
              onClick={toNewComerProductPage}
            >
              <div className={styles.common_btn}>
                {i18n.t('Showcase.立即前往新人精选样品池')}
              </div>
            </div>
          )}
          {showBtn && (
            <div className={styles.footer_tag}>
              {i18n.t('Showcase.新人95%+通过率')}
            </div>
          )}
        </div>
      </TransparentNavPage>
    )
  )
}

export default SampleGuide
