.container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-top: 2px solid #eee;
    overflow-y: scroll;
    padding: 48px 24px 164px;
}

.step_img {
    width: 252px;
    height: 536px;
    object-fit: contain;
}

.stepOne_content {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.sample_btn {
    font-weight: normal;
    font-size: 30px;
    color: #FE6D45;
}

.stepOne_left {
    flex: 1;
    margin-left: 72px;
    word-wrap: break-word;
}

.stepThree_left {
    flex: 1;
    margin-left: 18px;
    word-wrap: break-word;
}

.stepThree_right {
    margin-left: 72px;
    word-wrap: break-word;
}


.stepTwo_content {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    word-wrap: break-word;
}

.stepTwo_left {
    width: 252px;
    word-wrap: break-word;
}

.stepTwo_right {
    flex: 1;
    margin-left: 72px;
    word-wrap: break-word;
}

.stepfour_content {
    display: flex;
    align-items: center;
    justify-content: center;
    word-wrap: break-word;
}

.stepfour_submit {
    display: flex;
    justify-content: space-between;
    margin: 0 60px;
}

.stepfour_success_vi {
    display: flex;
    justify-content: center;
    justify-items: center;
    margin-top: 40px;
}

.stepfour_success {
    display: flex;
    justify-content: space-between;
    justify-items: center;
    margin-top: 40px;
}

.stepfour_box {
    margin-top: 40px;
}

.stepfour_direction {
    display: flex;
    padding: 0 42px;
    margin-bottom: 20px;
    word-wrap: break-word;
    white-space: 'normal';
}

.stepOne_right {
    margin-left: 36px;
    word-wrap: break-word;
}

.tips {
    font-weight: normal;
    font-size: 24px;
    color: #6e6e6e;
    line-height: 34px;
    margin: 16px 0;
    word-wrap: break-word;

}

.attention {
    color: #FE2C55 !important;
}

.five_arrow_img {
    display: flex;
    justify-content: center;
    align-items: center;
}

.title {
    font-weight: bold;
    font-size: 30px;
    color: #FE6D45;
    line-height: 42px;
    word-wrap: break-word;
}

.stepfive_content {
    flex: 1;
    word-wrap: break-word;
}

.direction {
    white-space: 'normal';
    font-weight: normal;
    font-size: 28px;
    color: #303030;
    line-height: 40px;
    margin-top: 20px;
    overflow-wrap: break-word;

}

.arrow_img {
    width: 270px;
    height: 232px;
    margin: 4px 0 8px 0;
}

.stepfour_arrow_img {
    width: 116px;
    height: 22px;
    margin: 0 4px;
}

.stepThree_arrow_img {
    width: 86px;
    height: 22px;
    margin: 0 4px;
}

.four_img {
    width: 252px;
}

.sample_guide_footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 116px;
    border-top: #eeeeee solid 2px;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.common_btn {
    width: 648px;
    height: 80px;
    background: #FE6D45;
    border-radius: 4px;
    font-weight: bold;
    font-size: 28px;
    color: #FFFFFF;
    line-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.footer_tag {
    position: fixed;
    bottom: 92px;
    right: 106px;
    height: 56px;
    background: #FFD100;
    border: 2px solid #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 18px;
    border-top-left-radius: 28px;
    border-bottom-left-radius: 28px;
    border-top-right-radius: 28px;
    font-size: 24px;
    color: #303030;
    line-height: 34px;
}