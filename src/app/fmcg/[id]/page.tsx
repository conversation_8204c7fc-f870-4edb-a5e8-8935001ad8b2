import { metadataTemplate, inApp } from '@/lib/server/utils'
import { getCampaignRankingsInfo } from '@/app/api/api-uchoice/campaignRewards/getCampaignRankingsInfo'
import FmcgPage from './inner'
import { FmcgHome } from './home'
import { i18nS } from '@/lib/server/i18n'
import { getCampaignAssemblyListServer } from '@/app/api/api-uchoice/campaignRewards/getCampaignAssemblyList/request'
import { getCampaignListServer } from '@/app/api/api-uchoice/fmcg/campaigns/list'
import { requestCampaignDetailServer } from '@/app/api/api-uchoice/fmcg/campaigns/detail'

interface Props {
  params: {
    id: string
  },
  searchParams: any
}

export default async function Index(props: Props) {
  const { params, searchParams } = props
  return params.id === 'home' ? (
    <FmcgHome id={searchParams.id || ''}></FmcgHome>
  ) : (
    <FmcgPage id={params.id}></FmcgPage>
  )
}

export async function generateMetadata({ params, searchParams }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: '' })
  }

  if (params.id === 'home') {
    if (!searchParams.id) {
      return metadataTemplate({
        title: i18nS.t('Activity.拼销量赢iPhone'),
        icon: `${process.env.NEXT_PUBLIC_URL}/images/logo/logo.png`
      })
    }
    const res: any = await getCampaignListServer(searchParams.id)
    if (res.code === 200 && res.result && res.result.length > 0) {
      return metadataTemplate({
        title: res.result[0].title || '',
        icon: res.result[0].listImage ? res.result[0].listImage.split('?')[0] : undefined
      })
    }
    return metadataTemplate({
      title: i18nS.t('Activity.拼销量赢iPhone'),
      icon: `${process.env.NEXT_PUBLIC_URL}/images/logo/logo.png`
    })
  }

  const res: any = await requestCampaignDetailServer(params.id)
  if (res.code === 200 && res.result) {
    const description = ''
    const title = res.result.title || ''
    const icon = res.result.bannerUrl ? res.result.bannerUrl.split('?')[0] : undefined

    return metadataTemplate({ title, description, icon })
  }

  return metadataTemplate({
    title: i18nS.t('Activity.拼销量赢iPhone'),
    icon: `${process.env.NEXT_PUBLIC_URL}/images/logo/logo.png`
  })
}
