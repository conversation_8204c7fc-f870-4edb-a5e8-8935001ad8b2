'use client'
import React from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import rank_first from '@/../public/images/fmcg/rank_first.png'
import rank_second from '@/../public/images/fmcg/rank_second.png'
import rank_third from '@/../public/images/fmcg/rank_third.png'
import {
  formatNumberWithCommas,
  formatNumberWithCommasFixed2
} from '@/lib/utils'
import { formatPrice } from '@/lib/format'
import styles from './index.module.scss'

type FmcgRankItemProps = {
  index: number
  item: any
  campaignType: string
} & BaseComponentProps

const FmcgRankItem: React.FC<FmcgRankItemProps> = props => {
  const { item, index, campaignType } = props

  const getRankPic = idx => {
    if (idx == 0) {
      return rank_first
    } else if (idx == 1) {
      return rank_second
    } else if (idx == 2) {
      return rank_third
    }
    return rank_first
  }

  return (
    <div className={styles.fmcg_rank_item_container}>
      <div className={styles.left_container}>
        <div className={styles.pic_reward_container}>
          {item.logoUrl && (
            <img className={styles.pic_reward} src={item.logoUrl}></img>
          )}
        </div>
        <div className={styles.pic_rank_container}>
          {index < 3 ? (
            <Image
              className={styles.pic_rank}
              alt=""
              src={getRankPic(index)}
            ></Image>
          ) : (
            <div className={styles.rank_label}>{index + 1}</div>
          )}
        </div>
        <img className={styles.avatar} src={item.avatar}></img>
      </div>
      <div className={styles.account}>{item.displayName || ''}</div>
      {campaignType !== 'video_count' && campaignType !== 'order_count' && (
        <div className={styles.gmv}>
          {formatPrice(item.metricValue, true)}
        </div>
      )}
      {campaignType === 'video_count' && (
        <div className={styles.video_count}>
          {`${formatNumberWithCommas(item.metricValue ? parseInt(item.metricValue) : 0)} ${i18n.t('Activity.视频')}`}
        </div>
      )}
      {campaignType === 'order_count' && (
        <div className={styles.video_count}>
          {`${formatNumberWithCommas(item.metricValue ? parseInt(item.metricValue) : 0)} ${i18n.t('Activity.单')}`}
        </div>
      )}
    </div>
  )
}

export default FmcgRankItem
