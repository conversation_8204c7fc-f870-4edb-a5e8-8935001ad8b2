'use client'
import React, { useMemo } from 'react'
import { webview } from '@/lib/client/webview'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'
import { getToken, makeSureInApp } from '@/lib/client/utils'
import { WebviewEvents } from '@/lib/client/webview/events'
import {
  formatNumberWithCommas,
  formatNumberWithCommasFixed2,
  getBaseH5OldUrl,
  isRegionTH,
  isRegionVN
} from '@/lib/utils'
import { formatPrice } from '@/lib/format'
import styles from './index.module.scss'

type FmcgAuthCardProps = {
  campaignType: string
  detail: any
  rankCardActive: any
  getCampaignDetail: any
} & BaseComponentProps

const FmcgAuthCard: React.FC<FmcgAuthCardProps> = props => {
  const router = useRouter()
  const { detail, rankCardActive, getCampaignDetail, campaignType } =
    props
  const {
    status, // 状态：0-已关闭 1-活动中 2-活动未开始 3-活动结束
    estimateUserRankInfo,
    actualUserRankInfo,
    itemSetIds,
  } = detail
  const {
    currentRewardName, // 当前可获得的奖品名称
    nextRewardName, // 下一个奖品名称 (未授权时，此奖为终极大奖)
    valueToNextReward, // 差多少积分可以领取上一个大奖
    totalUsers, // 参与活动的总人数
    isInWinningList, // 是否进入获奖名单
    canClaimFinalReward, // 保持当前积分即可领取终极大奖
    isAuth // 是否授权
  } =
    (rankCardActive === 'estimate'
      ? estimateUserRankInfo
      : actualUserRankInfo) || {}

  const showDescStatus = useMemo(() => {
    // 没有开始时间为异常情况，正常情况下应该不存在
    if (!detail.startTime) {
      return -1
    }
    let tempShowDescStatus = 1
    // 活动已关闭或活动已结束
    if (status === 0 || status === 3) {
      tempShowDescStatus = 6
    }
    // 活动未开始
    if (status === 2) {
      tempShowDescStatus = 5
    }
    if (status === 1 && !isAuth) {
      tempShowDescStatus = 1
    } else if (isAuth && status === 1 && canClaimFinalReward) {
      tempShowDescStatus = 4
    } else if (isAuth && status === 1 && isInWinningList) {
      tempShowDescStatus = 3
    } else if (isAuth && status === 1) {
      tempShowDescStatus = 2
    }
    return tempShowDescStatus
  }, [detail.startTime, status, isAuth, canClaimFinalReward, isInWinningList]) // 1 展示未授权 2 未进入获奖名单 3 即将获得终极大奖 4 终极大奖 5 活动即将开始！ 6 活动已经结束

  const showBtnStatus = useMemo(() => {
    if (!detail.startTime) {
      return -1
    }
    let tempShowBtnStatus = 1
    if (status === 1 && isAuth) {
      tempShowBtnStatus = 2
    } else if (status === 1 && !isAuth) {
      tempShowBtnStatus = 1
    } else {
      tempShowBtnStatus = 3
    }
    return tempShowBtnStatus
  }, [status, isAuth, detail.startTime]) // 1 立即授权加入 2 查看活动商品 3 添加LINE群

  const handleBtnClick = async () => {
    if (showBtnStatus === 1) {
      if (webview) {
        if (!(await getToken())) {
          await webview?.send(WebviewEvents.makeSureLogined)
          window.location.reload()
          return
        }
        await webview?.send(WebviewEvents.makeSureTikTokAuthed)
        window.location.reload()
      } else {
        makeSureInApp()
      }
    } else if (showBtnStatus === 2) {
      router.push(`${getBaseH5OldUrl()}/pages/tiktok/selection?id=${itemSetIds[0]}`)
    } else if (showBtnStatus === 3) {
      const url = isRegionVN() ? 'https://zalo.me/g/aryibq966' : 'https://line.me/R/ti/g/3c8qgMyG4y'
      if (webview) {
        webview?.send(WebviewEvents.launch, {
          url,
        })
      } else {
        location.href = url
      }
    }
  }

  const status2Desc = useMemo(() => {
    if (rankCardActive === 'estimate' && campaignType === 'video_count') {
      return 'Activity.您暂时未进入获奖名单，距离获奖名额只差预估GMV ฿xxxx，快去推广指定活动商品！_video'
    }
    if (rankCardActive === 'estimate' && campaignType === 'order_count') {
      return 'Activity.您暂时未进入获奖名单，距离获奖名额只差预估GMV ฿xxxx，快去推广指定活动商品！_order'
    }
    if (rankCardActive === 'estimate') {
      return 'Activity.您暂时未进入获奖名单，距离获奖名额只差预估GMV ฿xxxx，快去推广指定活动商品！'
    }
    if (rankCardActive !== 'estimate' && campaignType === 'video_count') {
      return 'Activity.您暂时未进入获奖名单，距离获奖名额只差实际GMV ฿xxxx，快去推广指定活动商品！_video'
    }
    if (rankCardActive !== 'estimate' && campaignType === 'order_count') {
      return 'Activity.您暂时未进入获奖名单，距离获奖名额只差实际GMV ฿xxxx，快去推广指定活动商品！_order'
    }
    if (rankCardActive !== 'estimate') {
      return 'Activity.您暂时未进入获奖名单，距离获奖名额只差实际GMV ฿xxxx，快去推广指定活动商品！'
    }
  }, [rankCardActive, campaignType])

  const status3Desc = useMemo(() => {
    if (rankCardActive === 'estimate' && campaignType === 'video_count') {
      return 'Activity.恭喜您！保持当前名次，即将获得xxxx的奖励！继续卖预估GMV ฿xxxx，xxx就是你的了！_video'
    }
    if (rankCardActive === 'estimate' && campaignType === 'order_count') {
      return 'Activity.恭喜您！保持当前名次，即将获得xxxx的奖励！继续卖预估GMV ฿xxxx，xxx就是你的了！_order'
    }
    if (rankCardActive === 'estimate') {
      return 'Activity.恭喜您！保持当前名次，即将获得xxxx的奖励！继续卖预估GMV ฿xxxx，xxx就是你的了！'
    }
    if (rankCardActive !== 'estimate' && campaignType === 'video_count') {
      return 'Activity.恭喜您！保持当前名次，即将获得xxxx的奖励！继续卖实际GMV ฿xxxx，xxx就是你的了！_video'
    }
    if (rankCardActive !== 'estimate' && campaignType === 'order_count') {
      return 'Activity.恭喜您！保持当前名次，即将获得xxxx的奖励！继续卖实际GMV ฿xxxx，xxx就是你的了！_order'
    }
    if (rankCardActive !== 'estimate') {
      return 'Activity.恭喜您！保持当前名次，即将获得xxxx的奖励！继续卖实际GMV ฿xxxx，xxx就是你的了！'
    }
  }, [rankCardActive, campaignType])

  return (
    showDescStatus != -1 && (
      <div className={styles.fmcg_auth_card_container}>
        {showDescStatus === 1 && (
          <div className={styles.auth_desc}>
            <span
              dangerouslySetInnerHTML={{
                __html: i18n.t(
                  'Activity.xxxx个TikTok达人已加入活动，立即授权TikTok账号，越早加入，获得iPhone的几率更高！',
                  {
                    num: formatNumberWithCommas(totalUsers ? parseInt(totalUsers) : 0),
                    finalPrize: nextRewardName
                  }
                )
              }}
            ></span>
          </div>
        )}
        {showDescStatus === 2 && (
          <div className={styles.auth_desc}>
            <span
              dangerouslySetInnerHTML={{
                __html: i18n.t(
                  status2Desc as string,
                  {
                    remainingGmv:
                      campaignType === 'video_count' || campaignType === 'order_count'
                        ? formatNumberWithCommas(valueToNextReward ? parseInt(valueToNextReward) : 0)
                        : formatPrice(valueToNextReward || 0, true)
                  }
                )
              }}
            ></span>
          </div>
        )}
        {showDescStatus === 3 && (
          <div className={styles.auth_desc}>
            <span
              dangerouslySetInnerHTML={{
                __html: i18n.t(
                  status3Desc as string,
                  {
                    prize: currentRewardName,
                    remainingGmv:
                      campaignType === 'video_count' || campaignType === 'order_count'
                        ? formatNumberWithCommas(valueToNextReward ? parseInt(valueToNextReward) : 0)
                        : formatPrice(valueToNextReward || 0, true),
                    finalPrize: nextRewardName
                  }
                )
              }}
            ></span>
          </div>
        )}
        {showDescStatus === 4 && (
          <div className={styles.auth_desc}>
            <span>
              {i18n.t(
                'Activity.恭喜您！保持当前名次，即将免费领iPhone 15一台！',
                { finalPrize: nextRewardName }
              )}
            </span>
          </div>
        )}
        {showDescStatus === 5 && (
          <div className={styles.auth_desc}>
            <span>
              {i18n.t('Activity.活动即将开始！')}
              {isRegionTH() ? i18n.t(
                'Activity.添加LINE群获取第一手活动消息，早参加，iPhone 15也许就是你的了！',
                { finalPrize: nextRewardName }
              ) : i18n.t(
                'Activity.添加Zalo群获取第一手活动消息，早参加，iPhone 15也许就是你的了！',
                { finalPrize: nextRewardName }
              )}
            </span>
          </div>
        )}
        {showDescStatus === 6 && (
          <div className={styles.auth_desc}>
            <span>{isRegionTH() ? i18n.t('Activity.添加LINE群，未来更多惊喜等着你！') : i18n.t('Activity.添加Zalo群，未来更多惊喜等着你！')}</span>
          </div>
        )}
        <div
          className={styles.btn_auth}
          style={{
            backgroundColor: showBtnStatus === 3 ? (isRegionTH() ? '#70B603' : '#0573ff') : '#FE6D45'
          }}
          onClick={handleBtnClick}
        >
          {showBtnStatus === 1 && (
            <div className={styles.btn_label}>
              {i18n.t('Activity.立即授权加入')}
            </div>
          )}
          {showBtnStatus === 2 && (
            <div className={styles.btn_label}>
              {i18n.t('Activity.查看活动商品')}
            </div>
          )}
          {showBtnStatus === 3 && (
            <div className={styles.btn_label}>
              {isRegionTH() ? i18n.t('Activity.添加LINE群，立即参与') : i18n.t('Activity.添加Zalo群，立即参与')}
            </div>
          )}
        </div>
      </div>
    )
  )
}

export default FmcgAuthCard
