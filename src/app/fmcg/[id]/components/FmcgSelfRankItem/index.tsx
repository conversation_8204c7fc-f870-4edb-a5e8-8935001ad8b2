'use client'
import React, { useState, useEffect } from 'react'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { i18n } from '@/lib/client/i18n'
import rank_first from '@/../public/images/fmcg/rank_first.png'
import rank_second from '@/../public/images/fmcg/rank_second.png'
import rank_third from '@/../public/images/fmcg/rank_third.png'
import {
  formatNumberWithCommas,
  formatNumberWithCommasFixed2
} from '@/lib/utils'
import { formatPrice } from '@/lib/format'
import styles from './index.module.scss'

type FmcgSelfRankItemProps = {
  item: any
  campaignType: string
} & BaseComponentProps

const FmcgSelfRankItem: React.FC<FmcgSelfRankItemProps> = props => {
  const { item, campaignType } = props

  const [hasSwitchTiktokAccount, setHasSwitchTiktokAccount] = useState(false)

  const makeSureJsHandler = async () => {
    if (webview) {
      const res = await webview.send(WebviewEvents.hasJavaScriptHandler, {
        handlerName: WebviewEvents.switchTiktokAccount
      })
      setHasSwitchTiktokAccount(!!res)
    }
    setHasSwitchTiktokAccount(true)
  }

  useEffect(() => {
    makeSureJsHandler()
  }, [])

  const handleSwitchTiktokAccount = () => {
    if (webview) {
      webview.send(WebviewEvents.switchTiktokAccount)
    }
  }

  return (
    <div className={styles.fmcg_rank_item_container}>
      <div className={styles.left_container}>
        <div className={styles.rank_label}>
          {!item.rank || item.rank > 100 ? '99+' : item.rank}
        </div>
        <div className={styles.avatar_container}>
          <img className={styles.avatar} src={item.avatar}></img>
          <div className={styles.tag}>{i18n.t('Activity.Me')}</div>
        </div>
      </div>
      <div className={styles.middle_container}>
        <div className={styles.account}>{item.displayName || ''}</div>
        {campaignType !== 'video_count' && campaignType !== 'order_count' && (
          <div className={styles.gmv}>
            {formatPrice(item.metricValue || 0, true)}
          </div>
        )}
        {campaignType === 'video_count' && (
          <div className={styles.gmv}>
            {`${formatNumberWithCommas(item.metricValue ? parseInt(item.metricValue) : 0)} ${i18n.t(
              'Activity.视频'
            )}`}
          </div>
        )}
        {campaignType === 'order_count' && (
          <div className={styles.gmv}>
            {`${formatNumberWithCommas(item.metricValue ? parseInt(item.metricValue) : 0)} ${i18n.t(
              'Activity.单'
            )}`}
          </div>
        )}
      </div>
      {hasSwitchTiktokAccount && (
        <div className={styles.btn_switch} onClick={handleSwitchTiktokAccount}>
          {i18n.t('Activity.换账号')}
        </div>
      )}
    </div>
  )
}

export default FmcgSelfRankItem
