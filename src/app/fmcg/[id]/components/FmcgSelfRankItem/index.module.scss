.fmcg_rank_item_container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F5F5F5;
  margin-bottom: 28px;
  padding: 16px 24px;

  .left_container {
    display: flex;
    justify-content: center;
    align-items: center;

    .rank_label {
      font-size: 28px;
      font-weight: bold;
      color: #303030;
      line-height: 32px;
      margin-right: 26px;
    }

    .avatar_container {
      width: 96px;
      height: 96px;
      border-radius: 48px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .avatar {
        width: 96px;
        height: 96px;
        border-radius: 48px;
      }

      .tag {
        height: 24px;
        background-color: #303030;
        margin-top: -24px;
        background: #FE6D45;
        border-radius: 2px;
        padding: 0 8px;
        font-size: 22px;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .middle_container {
    display: flex;
    flex-direction: column;

    .account {
      font-size: 26px;
      color: #303030;
      line-height: 36px;
      margin-bottom: 6px;
      white-space: nowrap; /* 让文本不换行 */
      overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; /* 使用省略号表示被省略的文本 */
      max-width: 296px; /* 设置最大宽度为100px */
    }
  
    .gmv {
      font-size: 26px;
      color: #303030;
      line-height: 38px;
      font-weight: bold;
    }
  }

  .btn_switch {
    border-radius: 2px;
    border: 2px solid #FE6D45;
    font-size: 24px;
    color: #FE6D45;
    height: 40px;
    line-height: 40px;
    padding: 0 8px;
  }
}