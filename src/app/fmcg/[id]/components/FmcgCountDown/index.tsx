'use client'
import React, { useEffect, useState } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'

function calculateTimeRemaining(remainingTime) {
  // 计算剩余时间中的天数、小时数、分钟数和秒数
  const days = Math.floor(remainingTime / (1000 * 60 * 60 * 24))
  const hours = Math.floor(
    (remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
  )
  const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000)

  // 添加补零逻辑
  const formattedDays = days < 10 ? `0${days}` : `${days}`
  const formattedHours = hours < 10 ? `0${hours}` : `${hours}`
  const formattedMinutes = minutes < 10 ? `0${minutes}` : `${minutes}`
  const formattedSeconds = seconds < 10 ? `0${seconds}` : `${seconds}`

  return {
    restDays: formattedDays,
    restHours: formattedHours,
    restMinutes: formattedMinutes,
    restSeconds: formattedSeconds
  }
}

type FmcgCountDownProps = {
  currentDetail: any
  getCampaignDetail: any
} & BaseComponentProps

const FmcgCountDown: React.FC<FmcgCountDownProps> = props => {
  const { currentDetail, getCampaignDetail } = props
  const { status, startTime, endTime } = currentDetail // 状态：0-已关闭 1-活动中 2-活动未开始 3-活动结束
  const [days, setDays] = useState('0')
  const [hours, setHours] = useState('0')
  const [minutes, setMinutes] = useState('0')
  const [seconds, setSeconds] = useState('0')

  useEffect(() => {
    let interval: any = null
    if (status === 2) {
      interval = setInterval(() => {
        const remainingTime = startTime - Date.now()
        if (remainingTime > 0) {
          const { restDays, restHours, restMinutes, restSeconds } =
            calculateTimeRemaining(remainingTime)
          setDays(restDays)
          setHours(restHours)
          setMinutes(restMinutes)
          setSeconds(restSeconds)
        } else {
          // 活动已经开始
          clearInterval(interval)
          interval = null
          getCampaignDetail()
        }
      }, 1000)
    } else if (status === 1) {
      interval = setInterval(() => {
        const remainingTime = endTime - Date.now()
        if (remainingTime > 0) {
          const { restDays, restHours, restMinutes, restSeconds } =
            calculateTimeRemaining(remainingTime)
          setDays(restDays)
          setHours(restHours)
          setMinutes(restMinutes)
          setSeconds(restSeconds)
        } else {
          // 活动已经结束
          clearInterval(interval)
          interval = null
          getCampaignDetail()
        }
      }, 1000)
    }
    return () => {
      if (interval) {
        clearInterval(interval)
        interval = null
      }
    }
  }, [status, endTime])

  return (
    <div className={styles.fmcg_count_down_container}>
      {(status === 0 || status === 3) && (
        <div className={styles.end_container}>
          {i18n.t('Activity.本次开奖活动已结束！')}
        </div>
      )}
      {(status === 1 || status === 2) && (
        <div className={styles.progress_container}>
          <div className={styles.desc_first}>
            {status === 2
              ? i18n.t('Activity.Start in')
              : i18n.t('Activity.Ending in')}
          </div>
          <div className={styles.num_card}>{days}</div>
          <div className={styles.desc}>{i18n.t('Activity.xx day')}</div>
          <div className={styles.num_card}>{hours}</div>
          <div className={styles.seperator}>:</div>
          <div className={styles.num_card}>{minutes}</div>
          <div className={styles.seperator}>:</div>
          <div className={styles.num_card}>{seconds}</div>
        </div>
      )}
    </div>
  )
}

export default FmcgCountDown
