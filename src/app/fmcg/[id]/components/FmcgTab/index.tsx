'use client'
import React from 'react'
import Image from 'next/image'
import classNames from 'classnames'
import { i18n } from '@/lib/client/i18n'
import { Dialog } from 'react-vant'
import icon_tip from '@/../public/images/fmcg/icon_tip.png'
import styles from './index.module.scss'

export type TabItem = {
  label: string
  value: string
  tip?: string
}

type FmcgTabProps = {
  active: string
  list: TabItem[]
  onChange: (value: string) => void
  indicator?: boolean
} & BaseComponentProps

const FmcgTab: React.FC<FmcgTabProps> = props => {
  const { active, list, style, onChange, indicator = false } = props

  const handleShowDialog = tip => {
    Dialog.alert({
      message: tip,
      confirmButtonText: i18n.t('Activity.确定'),
      confirmButtonColor: '#FE6D45'
    })
  }

  return (
    <div className={styles.fmcg_tab_container} style={style}>
      {list.map((item, index) => {
        const isActive = active == item.value
        return (
          <div
            className={classNames([
              styles.tab_item,
              {
                [styles.tab_item_first]: index === 0
              }
            ])}
            key={item.value}
          >
            <div className={styles.tab_label_container}>
              <div
                onClick={() => {
                  onChange(item.value)
                }}
                className={isActive ? styles.label_active : styles.label_normal}
              >
                {item.label}
              </div>
              {item.tip && (
                <div className={styles.icon_tip_container} onClick={() => handleShowDialog(item.tip)}>
                  <Image
                    alt=""
                    src={icon_tip}
                    className={styles.icon_tip}
                  ></Image>
                </div>
              )}
            </div>
            {indicator ? (
              isActive ? (
                <div className={styles.indicator}></div>
              ) : (
                <div className={styles.indicator_placeholder}></div>
              )
            ) : null}
          </div>
        )
      })}
    </div>
  )
}

export default FmcgTab
