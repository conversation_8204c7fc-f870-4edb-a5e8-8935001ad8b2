'use client'
import React, { useMemo } from 'react'
import dayjs from 'dayjs'
import { i18n } from '@/lib/client/i18n'
import FmcgTab from '../FmcgTab'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import FmcgRankItem from '../FmcgRankItem'
import FmcgSelfRankItem from '../FmcgSelfRankItem'
import { formatNumberWithCommas } from '@/lib/utils'
import { formatPrice } from '@/lib/format'
import styles from './index.module.scss'

type FmcgRankCardProps = {
  detail: any
  rankCardActive: string
  campaignType: string
  onChange: (value: string) => void
} & BaseComponentProps

const FmcgRankCard: React.FC<FmcgRankCardProps> = props => {
  const { campaignType, rankCardActive, onChange, detail } = props
  const {
    estimateGmvRankings,
    actualGmvRankings,
    actualUserRankInfo,
    estimateUserRankInfo,
    dataVersion,
    actualUnAwardRank,
    estimateUnAwardRank,
    minAwardValue,
  } = detail
  const showRankings =
    (rankCardActive === 'estimate' ? estimateGmvRankings : actualGmvRankings) ||
    []
  const showSelfRanking =
    (rankCardActive === 'estimate' ? estimateUserRankInfo : actualUserRankInfo) || {}

  const list = useMemo(() => {
    if (campaignType === 'video_count') {
      return [
        {
          label: i18n.t('Activity.视频数量'),
          value: 'real',
          tip: ''
        }
      ]
    }
    if (campaignType === 'order_count') {
      return [
        {
          label: i18n.t('Activity.预估订单榜'),
          value: 'estimate',
          tip: i18n.t(
            'Activity.计算达人在活动期间销售的指定活动商品的订单，包含退货退款的订单。最终结果以实际订单为准。'
          )
        },
        {
          label: i18n.t('Activity.实际订单榜'),
          value: 'real',
          tip: i18n.t(
            'Activity.计算达人在活动期间销售的指定活动商品的订单，不包含退货退款的订单。最终结果以实际订单为准。'
          )
        }
      ]
    }
    return [
      {
        label: i18n.t('Activity.预估GMV榜'),
        value: 'estimate',
        tip: i18n.t(
          'Activity.计算达人在活动期间销售的指定活动商品的GMV，包含退货退款的订单。最终结果以实际GMV为准。'
        )
      },
      {
        label: i18n.t('Activity.实际GMV榜'),
        value: 'real',
        tip: i18n.t(
          'Activity.计算达人在活动期间销售的指定活动商品的GMV，不包含退货退款的订单。最终结果以实际GMV为准。'
        )
      }
    ]
  }, [campaignType])

  const awardTypeName = useMemo(() => {
    if (campaignType === 'video_count') {
      return i18n.t('Activity.视频数量')
    } else if (campaignType === 'video') {
      return i18n.t('Activity.视频GMV')
    } else if (campaignType === 'total') {
      return i18n.t('TiktokData.总销售额')
    }
    return ''
  }, [campaignType])

  return (
    <div className={styles.fmcg_rank_card_container}>
      <FmcgTab
        active={rankCardActive}
        list={list}
        onChange={onChange}
        indicator
      ></FmcgTab>
      <div className={styles.desc}>
        <span style={{ marginRight: 4 }}>{i18n.t('Activity.数据更新')}</span>
        <span style={{ marginRight: 8 }}>
          {dayjs(dataVersion || Date.now()).format('HH:mm:ss DD/MM/YYYY')}
        </span>
        <span>{i18n.t('Activity.最终结果以实际榜单为准')}</span>
      </div>
      <div className={styles.rank_list}>
        {showSelfRanking.displayName && (
          <FmcgSelfRankItem
            item={showSelfRanking}
            campaignType={campaignType}
          ></FmcgSelfRankItem>
        )}
        {showRankings.map((item, index) => {
          const minRank = rankCardActive === 'estimate' ? estimateUnAwardRank : actualUnAwardRank
          const element = <FmcgRankItem
            key={index}
            item={item}
            index={index}
            campaignType={campaignType}
          ></FmcgRankItem>

          return index === minRank ? <div>
            <div className={styles.reward_divider} dangerouslySetInnerHTML={{
              __html: i18n.t('Activity.以下排名暂未获奖，获奖达人xxx≥xxx', {
                type: awardTypeName,
                value: campaignType === 'video_count' || campaignType === 'order_count'
                  ? formatNumberWithCommas(minAwardValue ? parseInt(minAwardValue) : 0)
                  : formatPrice(minAwardValue || 0, true)
              })
            }}></div>
            {element}
          </div> : element
        })}
        {showRankings.length === 0 && (
          <StatusView
            status={StatusViewType.empty}
            className={styles.status_view}
          ></StatusView>
        )}
        {showRankings.length > 0 && (
          <div className="pb-[24px] text-center font-[24px] text-grayCC">
            {i18n.t('TiktokData.无更多数据')}
          </div>
        )}
      </div>
    </div>
  )
}

export default FmcgRankCard
