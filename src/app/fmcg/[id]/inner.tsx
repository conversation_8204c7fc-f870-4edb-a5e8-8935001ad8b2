'use client'
import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import { useMounted } from '@/lib/hooks/useMounted'
import { webview } from '@/lib/client/webview'
import { Toast } from 'react-vant'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'
import { isLanguageTH, isLanguageVI } from '@/lib/utils'
import { getCampaignRankings } from '@/app/api/api-uchoice/campaignRewards/getCampaignRankings'
import { requestCampaignDetail } from '@/app/api/api-uchoice/fmcg/campaigns/detail'
import { requestCampaignList } from '@/app/api/api-uchoice/fmcg/campaigns/rankList'
import FmcgHeader from './components/FmcgHeader'
import FmcgCountDown from './components/FmcgCountDown'
import FmcgAuthCard from './components/FmcgAuthCard'
import FmcgRankCard from './components/FmcgRankCard'
import ImgWithPreload from './components/ImgWithPreload'
import main_placeholder from '@/../public/images/fmcg/main_placeholder.png'
import { NotAboutApple } from '@/app/components/NotAboutApple'
import styles from './index.module.scss'

interface FmcgPageProps {
  id: string
}

const rules_en = '/images/fmcg/rules_en.png'
const rules_th = '/images/fmcg/rules_th.png'
const rules_vn = '/images/fmcg/rules_vn.png'

const FmcgPage: React.FC<FmcgPageProps> = props => {
  const mounted = useMounted()
  const router = useRouter()

  const { id } = props
  const [navHeight, setNavHeight] = useState(40)
  const [headerTitle, setHeaderTitle] = useState<string>('')
  const [campaignType, setCampaignType] = useState('video')
  const [rankCardActive, setRankCardActive] = useState('estimate')

  const [detail, setDetail] = useState<any>([])
  const [fetchLoading, setFetchLoading] = useState(false)

  const pic_rules = isLanguageTH() ? rules_th : isLanguageVI() ? rules_vn : rules_en

  const getCampaignDetail = async () => {
    try {
      setFetchLoading(true)
      if (webview) {
        webview.send(WebviewEvents.showLoading, {
          userInteractive: true
        })
      } else {
        Toast.loading({})
      }
      const res: any = await requestCampaignDetail(id)
      // const res: any = {
      //   "code": 200,
      //   "message": "string",
      //   "result": {
      //     "actualUnAwardRank": 0,
      //     "actualUserRankInfo": {
      //       "avatar": "https://p16-oec-sg.ibyteimg.com/tos-alisg-avt-0068/d3a847445f87988f1cf819dea9b14f9b~tplv-aphluv4xwc-resize-jpeg:300:300.jpeg?from=826294292",
      //       "canClaimFinalReward": true,
      //       "currentRewardName": "泰铢现金2,000",
      //       "displayName": "Me",
      //       "isAuth": false,
      //       "isInWinningList": true,
      //       "metricValue": 5000,
      //       "nextRewardName": "1g金戒指",
      //       "rank": 6,
      //       "rewardLevel": 1,
      //       "totalUsers": 10000,
      //       "unionId": "123123",
      //       "valueToNextReward": 6000
      //     },
      //     "bannerUrl": "https://file.uchoice.pro/system/20241121/5574ba411bdd4860b5235020539804bb.jpg",
      //     "dataVersion": 0,
      //     "endTime": 1744612214753 + 86400000,
      //     "estimateUnAwardRank": 0,
      //     "estimateUserRankInfo": {
      //       "avatar": "https://p16-oec-sg.ibyteimg.com/tos-alisg-avt-0068/d3a847445f87988f1cf819dea9b14f9b~tplv-aphluv4xwc-resize-jpeg:300:300.jpeg?from=826294292",
      //       "canClaimFinalReward": true,
      //       "currentRewardName": "泰铢现金2,000",
      //       "displayName": "Me",
      //       "isAuth": false,
      //       "isInWinningList": true,
      //       "metricValue": 5000,
      //       "nextRewardName": "1g金戒指",
      //       "rank": 6,
      //       "rewardLevel": 1,
      //       "totalUsers": 10000,
      //       "unionId": "123123",
      //       "valueToNextReward": 6000
      //     },
      //     "itemSetIds": [
      //       "123123"
      //     ],
      //     "minAwardValue": "5000",
      //     "startTime": 1744612214753,
      //     "status": 1,
      //     "type": 2,
      //   },
      //   "success": true
      // }
      if (res.code == 200 && res.result) {
        const { campaignType: type, dataVersion } = res.result
        let title = ''
        if (type === 1) {
          title = i18n.t('Activity.视频数量')
          setCampaignType('video_count')
        } else if (type === 2) {
          title = i18n.t('Activity.视频gmv榜')
          setCampaignType('video')
        } else if (type === 3) {
          title = i18n.t('TiktokData.总销售额')
          setCampaignType('total')
        } else if (type === 4) {
          title = i18n.t('Activity.订单榜标题')
          setCampaignType('order_count')
        }
        setHeaderTitle(title)
        setDetail(res.result)
        getCampaignRankings(res.result, dataVersion)
      }
      setFetchLoading(false)
      if (webview) {
        webview.send(WebviewEvents.hideLoading)
      } else {
        Toast.clear()
      }
    } catch (error) {
      setFetchLoading(false)
      if (webview) {
        webview.send(WebviewEvents.hideLoading)
      } else {
        Toast.clear()
      }
    }
  }

  const getCampaignRankings = async (data: any, dataVersion: number) => {
    try {
      const res: any = await requestCampaignList(
        id,
        dataVersion
      )
      // const res = {
      //   "code": 200,
      //   "message": "string",
      //   "result": {
      //     "actualRankList": [
      //       {
      //         "avatar": "https://p16-oec-sg.ibyteimg.com/tos-alisg-avt-0068/d3a847445f87988f1cf819dea9b14f9b~tplv-aphluv4xwc-resize-jpeg:300:300.jpeg?from=826294292",
      //         "displayName": "Me",
      //         "logoUrl": "https://example.com/logo.png",
      //         "metricValue": "5000",
      //         "rank": 6
      //       }
      //     ],
      //     "estimateRankList": [
      //       {
      //         "avatar": "https://p16-oec-sg.ibyteimg.com/tos-alisg-avt-0068/d3a847445f87988f1cf819dea9b14f9b~tplv-aphluv4xwc-resize-jpeg:300:300.jpeg?from=826294292",
      //         "displayName": "Me",
      //         "logoUrl": "https://example.com/logo.png",
      //         "metricValue": "5000",
      //         "rank": 6
      //       }
      //     ]
      //   },
      //   "success": true
      // }
      if (res.code == 200 && res.result) {
        const { actualRankList, estimateRankList } = res.result
        data.estimateGmvRankings = estimateRankList
        data.actualGmvRankings = actualRankList
        setDetail(JSON.parse(JSON.stringify(data)))
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getCampaignDetail()
    if (webview) {
      setNavHeight(webview.getData().topSafeArea)
    }
  }, [])

  const handleOnFmcgRankCardTabChange = value => {
    setRankCardActive(value)
  }

  const handleToRules = () => {
    let ruleType = 1
    if (campaignType === 'video') {
      ruleType = 1
    } else if (campaignType === 'live') {
      ruleType = 2
    } else if (campaignType === 'video_count') {
      ruleType = 4
    } else if (campaignType === 'order_count') {
      ruleType = 5
    } else {
      ruleType = 3
    }
    router.push(`/activity_rules/page?type=${ruleType}&campaignId=${id}`)
  }

  return mounted ? (
    <div className={styles.fmcg_page_container}>
      <div
        className={styles.top_placeholder}
        style={{ height: navHeight }}
      ></div>
      <FmcgHeader
        title={headerTitle}
        stickHeight={navHeight}
      ></FmcgHeader>
      {/* 否则会和GoAppBar冲突 */}
      {webview && <div className={styles.padding_top_container}></div>}
      <NotAboutApple style={{ paddingTop: 10 }}></NotAboutApple>
      <div className={styles.top_container}>
        <ImgWithPreload
          className={styles.banner}
          src={detail.bannerUrl}
        ></ImgWithPreload>
        <FmcgCountDown
          currentDetail={detail}
          getCampaignDetail={getCampaignDetail}
        ></FmcgCountDown>
        <div className={styles.rules_container} onClick={handleToRules}>
          <img
            className={styles.rules}
            src={pic_rules}
          ></img>
        </div>
      </div>
      <FmcgAuthCard
        campaignType={campaignType}
        detail={detail}
        rankCardActive={campaignType === 'video_count' ? 'real' : rankCardActive}
        getCampaignDetail={getCampaignDetail}
      ></FmcgAuthCard>
      {detail.startTime && detail.status !== 2 && (
        <FmcgRankCard
          detail={detail}
          campaignType={campaignType}
          rankCardActive={campaignType === 'video_count' ? 'real' : rankCardActive}
          onChange={handleOnFmcgRankCardTabChange}
        ></FmcgRankCard>
      )}
    </div>
  ) : (
    <Image src={main_placeholder} alt=""></Image>
  )
}

export default FmcgPage
