/* eslint-disable @next/next/no-img-element */
'use client'

import { useEffect, useState } from 'react'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { webview } from '@/lib/client/webview'
import { i18n } from '@/lib/client/i18n'
import { getCampaignAssemblyList } from '@/app/api/api-uchoice/campaignRewards/getCampaignAssemblyList/request'
import { CampaignAssemblyListResult } from '@/app/api/api-uchoice/campaignRewards/getCampaignAssemblyList/dtos'
import { getCampaignList } from '@/app/api/api-uchoice/fmcg/campaigns/list'
import { formatTime } from '@/lib/format'
import { useRouter } from '@/lib/hooks/useRouter'
import { NotAboutApple } from '@/app/components/NotAboutApple'
import branch_card from '@/../public/images/fmcg/branch_card.png'
import { Image } from '@/components/Image'
import { useGrayBody } from '@/lib/hooks/useGrayBody'
import { rpxToPx } from '@/lib/client/utils'

interface Props {
  id: string
}

export function FmcgHome({ id }) {
  const [data, setData] = useState<any>()
  const router = useRouter()
  useGrayBody()

  useEffect(() => {
    fetch()
  }, [])

  const fetch = async () => {
    const { code, result } = await getCampaignList(id)
    if (code === 200) {
      setData(result)
    }
  }

  // 状态：0-已关闭 1-活动中 2-活动未开始 3-活动结束
  const statusStyle = status => {
    return {
      '0': {
        text: i18n.t('Activity.已结束'),
        bg: 'rgba(187, 187, 189, 1)',
        childBg: 'rgba(187, 187, 189, 0.7)'
      },
      '2': {
        text: i18n.t('Activity.即将开始'),
        bg: 'rgba(0, 0, 0, 1)',
        childBg: 'rgba(0, 0, 0, 0.3)'
      },
      '1': {
        text: i18n.t('Activity.进行中'),
        bg: 'rgba(33, 178, 106, 1)',
        childBg: 'rgba(33, 178, 106, 0.7)'
      },
      '3': {
        text: i18n.t('Activity.已结束'),
        bg: 'rgba(187, 187, 189, 1)',
        childBg: 'rgba(187, 187, 189, 0.7)'
      }
    }[status]
  }

  return (
    <TransparentNavPage
      title={'Creator Challenge'}
      transparent={false}
      hide={!webview}
      showShareButton
    >
      <div className="bg-background px-[24px] pt-[20px]">
        <NotAboutApple></NotAboutApple>
        {data &&
          data.length > 0 &&
          data.map((item, i) => {
            return (
              <div
                className="pb-[40px]"
                key={`section_${i}`}
                onClick={() =>
                  router.push(`/fmcg/${item.campaignId}`)
                }
              >
                <div className="relative">
                  <img
                    className="w-[702px] object-cover"
                    style={{ minHeight: rpxToPx(240) }}
                    src={item.listImage}
                  ></img>
                  <div
                    className="absolute right-0 top-0 flex h-[36px] items-center justify-center rounded-[4px] px-[16px]"
                    style={{
                      backgroundColor: statusStyle(item.status).bg
                    }}
                  >
                    <span className="text-[22px] text-white">
                      {statusStyle(item.status).text}
                    </span>
                  </div>
                </div>
              </div>
            )
          })}
      </div>
    </TransparentNavPage>
  )
}
