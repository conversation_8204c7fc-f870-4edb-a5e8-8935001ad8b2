'use client'
import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import { Tabs } from 'antd-mobile'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { webview } from '@/lib/client/webview'
import { getToken, makeSureInApp, scaledPx } from '@/lib/client/utils'
import { isLanguageEN } from '@/lib/utils'
import { WebViewToPages, WebviewEvents } from '@/lib/client/webview/events'
import { i18n } from '@/lib/client/i18n'
import bg_login from '@/../public/images/newer/bg_login.png'
import bg_nologin from '@/../public/images/newer/bg_nologin.png'
import bg_nologin_share from '@/../public/images/newer/bg_nologin_share.png'
import login_btn_arrow from '@/../public/images/newer/login_btn_arrow.png'
import ProductItem from '../components/ProductItem'
import { getSamplePoolItemList } from '@/app/api/api-uchoice/samplePool/getWebItemListByPage'
import { getUserInfo } from '@/app/api/api-uchoice/uChoice/account/current/request'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import styles from './inner.module.scss'

interface Props {
  id: string
}

const Inner: React.FC<Props> = ({ id }) => {
  const [logined, setLogined] = useState<boolean>(false)
  const [activeKey, setActiveKey] = useState<string>('')
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [categoryList, setCategoryList] = useState<any>([])
  const [productList, setProductList] = useState([])
  const [currentProductList, setCurrentProductList] = useState([])
  const [isQualityUser, setQualityUser] = useState(true)

  const handleLogined = async () => {
    if (webview) {
      const token = await getToken()
      if (token) {
        setLogined(true)
      }
    }
  }

  const handleUserLevel = async () => {
    if (webview) {
      const token = await getToken()
      if (token) {
        const res: any = await getUserInfo()
        if (res.code == 200 && res.result) {
          const { isQualityUser } = res.result
          setQualityUser(!!isQualityUser)
        }
      }
    }
  }

  const requestData = () => {
    setFetchLoading(true)
    getSamplePoolItemList({ id })
      .then((res: any) => {
        setFetchLoading(false)
        if (res.code == 200 && res.result) {
          const { categoryList, waterfallProductsList } = res.result
          const tempCategoryList = categoryList.map((item: any) => {
            const { name, nameEn, id } = item
            return {
              label: isLanguageEN() ? nameEn : name,
              value: `${id}`
            }
          })
          setCategoryList([
            { label: i18n.t('HotSpot.全部'), value: '' },
            ...tempCategoryList
          ])
          setProductList(waterfallProductsList || [])
          setCurrentProductList(waterfallProductsList || [])
        }
      })
      .catch((err: any) => {
        setFetchLoading(false)
      })
  }

  useEffect(() => {
    handleUserLevel()
    requestData()
    handleLogined()
  }, [])

  const toLogin = async () => {
    if (webview) {
      if (!(await getToken())) {
        await webview?.send(WebviewEvents.makeSureLogined)
        window.location.reload()
      }
    } else {
      makeSureInApp()
    }
  }

  const handleOnTabChange = value => {
    setActiveKey(value)
    setCurrentProductList(
      value === ''
        ? productList
        : productList.filter(
          (item: any) => `${item.customCategoryId}` === value
        )
    )
  }

  return (
    <TransparentNavPage
      title={i18n.t('Product.新客专享 精选样品')}
      transparent
      showBackButton
      showShareButton
      onShared={() =>
        statistic({ eventName: EventName.newer_collections_share })
      }
    >
      <div className={styles.inner_container}>
        <div className={styles.top_container}>
          <div>
            <Image
              src={logined ? bg_login : webview ? bg_nologin : bg_nologin_share}
              className={styles.bg_login}
              alt=""
            ></Image>
          </div>
          <div className={styles.top_content_container}>
            {logined && (
              <div className={styles.logined_container}>
                <div
                  className={styles.login_tip_label}
                  dangerouslySetInnerHTML={{
                    __html: i18n.t('Product.极速申样，最快7天达')
                  }}
                />
                <div
                  className={styles.login_desc_label}
                  dangerouslySetInnerHTML={{
                    __html: i18n.t('Product.通过率95%+，3w达人都在卖!')
                  }}
                />
              </div>
            )}
            {!logined && (
              <div className={styles.nologin_container}>
                <div className={styles.login_tip_container}>
                  <div
                    className={styles.login_tip_label}
                    dangerouslySetInnerHTML={{
                      __html: i18n.t('Product.免费升级1个样品额度')
                    }}
                  />
                </div>
                <div className={styles.login_btn_container} onClick={toLogin}>
                  <div className={styles.login_btn_label}>
                    {i18n.t('Product.新客登录，立刻升级')}
                  </div>
                  <Image
                    src={login_btn_arrow}
                    className={styles.login_btn_arrow}
                    alt=""
                  ></Image>
                </div>
              </div>
            )}
            <div className={styles.tab_top_border}></div>
          </div>
        </div>
        {categoryList.length !== 0 && (
          <div
            className={styles.tab_container}
            style={{
              top: webview
                ? webview?.getData().topSafeArea + scaledPx(44)
                : scaledPx(84)
            }}
          >
            <Tabs
              activeKey={activeKey}
              style={{
                '--active-line-color': '#FE6D45',
                '--active-line-height': '3px',
                '--active-title-color': '#FE6D45',
                '--title-font-size': '14px',
                '--active-line-border-radius': '0'
              }}
              onChange={handleOnTabChange}
            >
              {categoryList.map((item: any, index: number) => (
                <Tabs.Tab title={item.label} key={item.value}></Tabs.Tab>
              ))}
            </Tabs>
          </div>
        )}
        {fetchLoading && (
          <StatusView
            status={StatusViewType.loading}
            style={{ padding: '48px 0' }}
          ></StatusView>
        )}
        {!fetchLoading && currentProductList.length === 0 && (
          <StatusView
            status={StatusViewType.empty}
            style={{ padding: '48px 0' }}
          ></StatusView>
        )}
        {currentProductList.map((item, index) => (
          <ProductItem key={index} item={item} isQualityUser={isQualityUser}></ProductItem>
        ))}
      </div>
    </TransparentNavPage>
  )
}

export default Inner
