import React from 'react'
import Inner from './inner'
import { metadataTemplate, inApp } from '@/lib/server/utils'
import { i18nS } from '@/lib/server/i18n'
import styles from './index.module.scss'

interface Props {
  params: {
    id: string
  }
}

export default async function Index(props: Props) {
  return <Inner id={props.params.id} />
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: '' })
  }

  const description = ''
  const title = i18nS.t('Product.新客专享 精选样品')
  const icon = `${process.env.NEXT_PUBLIC_URL}/images/newer/single.png`

  return metadataTemplate({ title, description, icon })
}
