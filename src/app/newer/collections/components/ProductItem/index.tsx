import React from 'react'
import Image from 'next/image'
import { ProgressBar } from 'antd-mobile'
import { i18n } from '@/lib/client/i18n'
import { useRouter } from '@/lib/hooks/useRouter'
import { formatPrice } from '@/lib/format'
import { isRegionVN } from '@/lib/utils'
import icon_price_hot from '@/../public/images/newer/icon_price_hot.png'
import icon_price_hot_vn from '@/../public/images/newer/icon_price_hot_vn.png'
import { addToShowCase, checkAuth } from '@/lib/actions'
import styles from './index.module.scss'

const ProductItem = ({ item, isQualityUser }) => {
  const router = useRouter()

  const toGet = () => {
    router.push(`${window.location.origin}/product/${item.id}?popup=true`)
  }

  const toPdp = () => {
    router.push(`${window.location.origin}/product/${item.id}`)
  }

  const handleBtnClick = e => {
    e.stopPropagation()
    if (isQualityUser) {
      // free sample
      toGet()
    } else {
      // add showcase
      checkAuth(() => addToShowCase(item, 'newer_collections'))
    }
  }

  const showEarn = (minEarn: number, maxEarn: number) => {
    if (minEarn === maxEarn) {
      return `${formatPrice(minEarn || 0, true)}`
    }
    return `${formatPrice(minEarn || 0, true)}-${formatPrice(
      maxEarn || 0,
      true
    )}`
  }

  return (
    <div className={styles.product_item_container} onClick={toPdp}>
      <div className={styles.content_container}>
        <div className={styles.top_container}>
          <img src={item.image || ''} className={styles.thumbnail}></img>
          <div className={styles.right_container}>
            <div className={styles.title}>{item.productName || ''}</div>
            <div className={styles.commission_container}>
              <div style={{ zIndex: 1 }}>
                <Image
                  src={isRegionVN() ? icon_price_hot_vn : icon_price_hot}
                  alt=""
                  className={styles.icon_price_hot}
                ></Image>
              </div>
              <div className={styles.commission_container}>
                <div className={styles.commission_label}>
                  {i18n.t('Product.High Commissions')}
                </div>
                <div className={styles.commission_value}>
                  {item.creatorCommissionPercentStr}
                </div>
              </div>
            </div>
            <div className={styles.earn_container}>
              <div className={styles.label}>{i18n.t('Product.Earn')}</div>
              <div className={styles.earn}>
                {showEarn(item.minEarn || 0, item.maxEarn || 0)}
              </div>
            </div>
            <div className={styles.price_container}>
              <div className={styles.price}>
                {formatPrice(item.minPrice || 0, true)}
              </div>
              <div className={styles.sold}>
                {i18n.t('Product.已售', {
                  num: item.salesForLast30DaysStr || ''
                })}
              </div>
            </div>
          </div>
        </div>
        <div className={styles.bottom_container}>
          <div
            className={styles.residue}
            dangerouslySetInnerHTML={{
              __html: i18n.t('Product.样品剩余xxx个', {
                num: item.remainSampleCount || 0
              })
            }}
          ></div>
          <div className={styles.progress_container}>
            <ProgressBar
              percent={100 - (item.remainSampleCount || 0) * 2}
              style={{
                '--fill-color': '#FE6D45',
                '--track-color': '#FFEFEB',
                '--track-width': '6px'
              }}
            ></ProgressBar>
          </div>
          <div onClick={handleBtnClick} className={styles.btn_request}>
            {isQualityUser
              ? i18n.t('Product.申领')
              : i18n.t('Product.添加橱窗2')}
          </div>
        </div>
      </div>
      <div className={styles.divider}></div>
    </div>
  )
}

export default ProductItem
