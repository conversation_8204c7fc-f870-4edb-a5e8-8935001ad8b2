.product_item_container {
  display: flex;
  flex-direction: column;

  .content_container {
    padding: 24px;

    .top_container {
      display: flex;
      margin-bottom: 32px;

      .thumbnail {
        width: 192px;
        height: 192px;
        border-radius: 4px;
        object-fit: cover;
        margin-right: 20px;
      }

      .right_container {
        flex: 1;

        .title {
          width: 490px;
          font-size: 28px;
          color: #303030;
          line-height: 42px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 2px;
        }

        .commission_container {
          display: flex;
          align-items: flex-end;
          margin-bottom: 14px;
          
          .icon_price_hot {
            width: 62px;
            height: 52px;
          }

          .commission_container {
            height: 36px;
            background: #FFEFEF;
            border-radius: 2px;
            padding: 0 8px 0 36px;
            margin-left: -24px;
            margin-bottom: 2px;
          
            .commission_label {
              font-size: 22px;
              color: #C5445D;
              line-height: 36px;
              margin-right: 4px;
            }
  
            .commission_value {
              font-weight: bold;
              font-size: 24px;
              color: #C5445D;
              line-height: 36px;
            }
          }
        }

        .earn_container {
          display: flex;
          align-items: center;
          margin-bottom: 14px;

          .label {
            font-size: 24px;
            color: #FE6D45;
            line-height: 36px;
            margin-right: 12px;
          }

          .earn {
            font-size: 24px;
            color: #FE6D45;
            line-height: 36px;
            font-weight: bold;
          }
        }

        .price_container {
          display: flex;
          justify-content: space-between;

          .price {
            font-weight: bold;
            color: #303030;
            line-height: 36px;
            font-size: 24px;
          }

          .sold {
            font-size: 22px;
            color: #8A8A8A;
            line-height: 32px;
          }
        }
      }
    }

    .bottom_container {
      display: flex;
      align-items: center;

      .residue {
        font-size: 24px;
        color: #6E6E6E;
      }

      .progress_container {
        flex: 1;
        padding: 0 24px;
      }

      .btn_request {
        min-width: 144px;
        text-align: center;
        height: 48px;
        background: #FE6D45;
        border-radius: 2px;
        padding: 0 24px;
        font-weight: bold;
        font-size: 24px;
        color: #FFFFFF;
        line-height: 48px;
      }
    }
  }

  .divider {
    width: 100%;
    height: 12px;
    background-color: #F5F5F5; 
  }
}