.product_item_container {
  display: flex;
  flex-direction: column;
}
.product_item_container .content_container {
  margin-left: 24px;
  margin-right: 24px;
  margin-bottom: 24px;
  padding: 20px;
  border-radius: 4px;
  background-color: #ffffff;
}
.product_item_container .content_container .top_container {
  display: flex;
}
.product_item_container .content_container .top_container .thumbnail {
  width: 240px;
  height: 240px;
  border-radius: 4px;
  -o-object-fit: cover;
     object-fit: cover;
  margin-right: 20px;
}
.product_item_container .content_container .top_container .right_container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.product_item_container .content_container .top_container .right_container .title {
  width: 402px;
  font-size: 28px;
  color: #303030;
  line-height: 42px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 8px;
}
.product_item_container .content_container .top_container .right_container .desc_container {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.product_item_container .content_container .top_container .right_container .desc_container .star_container {
  background: #E9F7F0;
  border-radius: 2px;
  border: 1px solid #C1E9D5;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  margin-right: 12px;
}
.product_item_container .content_container .top_container .right_container .desc_container .star_container .icon_star {
  width: 22px;
  height: 22px;
  margin-right: 8px;
}
.product_item_container .content_container .top_container .right_container .desc_container .star_container .star_value {
  font-size: 24px;
  color: #303030;
  line-height: 24px;
}
.product_item_container .content_container .top_container .right_container .desc_container .shop_name {
  font-size: 24px;
  color: #6E6E6E;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product_item_container .content_container .top_container .right_container .commission_container {
  display: flex;
  align-items: center;
  background: #FFEDE9;
  border-radius: 2px;
  border: 1px solid #FFA48B;
  margin-bottom: 16px;
}
.product_item_container .content_container .top_container .right_container .commission_container .icon_trend {
  width: 24px;
  height: 16px;
  margin-left: 12px;
  margin-right: 8px;
}
.product_item_container .content_container .top_container .right_container .commission_container .commission_label {
  font-size: 22px;
  color: #FE6D45;
  line-height: 36px;
  margin-right: 4px;
}
.product_item_container .content_container .top_container .right_container .commission_container .commission_value {
  font-size: 24px;
  font-weight: bold;
  color: #FE6D45;
  margin-right: 12px;
}
.product_item_container .content_container .top_container .right_container .earn_container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.product_item_container .content_container .top_container .right_container .earn_container .label {
  font-size: 24px;
  color: #FE6D45;
  line-height: 36px;
  margin-right: 12px;
}
.product_item_container .content_container .top_container .right_container .earn_container .earn {
  font-size: 24px;
  color: #FE6D45;
  line-height: 36px;
  font-weight: bold;
}
.product_item_container .content_container .top_container .right_container .price_container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.product_item_container .content_container .top_container .right_container .price_container .sold {
  flex: 1;
  font-size: 22px;
  color: #8A8A8A;
  line-height: 32px;
}
.product_item_container .content_container .top_container .right_container .price_container .btn_sample {
  min-width: 144px;
  height: 48px;
  padding: 0 12px;
  line-height: 48px;
  text-align: center;
  background: #FE6D45;
  border-radius: 2px;
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
}
.product_item_container .content_container .bottom_container {
  display: flex;
  align-items: center;
}
.product_item_container .content_container .bottom_container .residue {
  font-size: 24px;
  color: #6E6E6E;
}
.product_item_container .content_container .bottom_container .progress_container {
  flex: 1;
  padding: 0 24px;
}
.product_item_container .content_container .bottom_container .btn_request {
  min-width: 144px;
  text-align: center;
  height: 48px;
  background: #FE6D45;
  border-radius: 2px;
  padding: 0 24px;
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
  line-height: 48px;
}/*# sourceMappingURL=index.module.css.map */