import React from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import { useRouter } from '@/lib/hooks/useRouter'
import { formatPrice } from '@/lib/format'
import icon_trend from '@/../public/images/newer/icon_trend.png'
import icon_star from '@/../public/images/newer/icon_star.png'
import { addToShowCase, checkAuth } from '@/lib/actions'
import styles from './index.module.scss'

const ProductItem = ({ item, isQualityUser }) => {
  const router = useRouter()

  const toPdp = () => {
    router.push(`${window.location.origin}/product/${item.id}`)
  }

  const showEarn = (minEarn: number, maxEarn: number) => {
    if (minEarn === maxEarn) {
      return `${formatPrice(minEarn || 0, true)}`
    }
    return `${formatPrice(minEarn || 0, true)}-${formatPrice(
      maxEarn || 0,
      true
    )}`
  }

  const handleBtnClick = e => {
    e.stopPropagation()
    if (isQualityUser) {
      // free sample
      toPdp()
    } else {
      // add showcase
      checkAuth(() => addToShowCase(item, 'newer_arrival'))
    }
  }

  return (
    <div className={styles.product_item_container} onClick={toPdp}>
      <div className={styles.content_container}>
        <div className={styles.top_container}>
          <img src={item.image || ''} className={styles.thumbnail}></img>
          <div className={styles.right_container}>
            <div className={styles.title}>{item.productName || ''}</div>
            <div className={styles.desc_container}>
              <div className={styles.star_container}>
                <Image
                  src={icon_star}
                  alt=""
                  className={styles.icon_star}
                ></Image>
                <div className={styles.star_value}>{item.shopScore || 0}</div>
              </div>
              <div className={styles.shop_name}>{item.shopName || ''}</div>
            </div>
            <div className={styles.commission_container}>
              <div style={{ zIndex: 1 }}>
                <Image
                  src={icon_trend}
                  alt=""
                  className={styles.icon_trend}
                ></Image>
              </div>
              <div className={styles.commission_label}>{'Commissions | '}</div>
              <div className={styles.commission_value}>
                {item.commissionRate}
              </div>
            </div>
            <div className={styles.earn_container}>
              <div className={styles.label}>{i18n.t('Product.Earn')}</div>
              <div className={styles.earn}>
                {showEarn(item.minEarn || 0, item.maxEarn || 0)}
              </div>
            </div>
            <div className={styles.price_container}>
              <div className={styles.sold}>
                {i18n.t('Showcase.Sold/Month', {
                  sold: item.salesForLast30DaysStr || ''
                })}
              </div>
              <div onClick={handleBtnClick} className={styles.btn_sample}>
                {isQualityUser
                  ? i18n.t('Product.免费领样')
                  : i18n.t('Product.添加橱窗2')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductItem
