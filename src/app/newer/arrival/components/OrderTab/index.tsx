import React from 'react'
import { Tabs } from 'antd-mobile'
import styles from './index.module.scss'

interface IOrderTabProps {
  categoryList: any
  current: string
  onChange: (value: string) => void
}

const OrderTab: React.FC<IOrderTabProps> = props => {
  const { categoryList, current, onChange } = props

  return (
    <div className={styles.order_tab_container}>
      <Tabs
        activeKey={current}
        style={{
          '--active-line-color': '#FE6D45',
          '--active-line-height': '0px',
          '--title-font-size': '14px',
          '--active-line-border-radius': '0'
        }}
        onChange={onChange}
      >
        {categoryList.map((item: any, index: number) => (
          <Tabs.Tab title={item.label} key={item.value}></Tabs.Tab>
        ))}
      </Tabs>
    </div>
  )
}

export default OrderTab
