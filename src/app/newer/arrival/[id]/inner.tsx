'use client'
import React, { useEffect, useRef, useState } from 'react'
import Image from 'next/image'
import { Tabs, InfiniteScroll } from 'antd-mobile'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { webview } from '@/lib/client/webview'
import { getToken, px2rem, scaledPx } from '@/lib/client/utils'
import { isLanguageEN, isRegionTH } from '@/lib/utils'
import { i18n } from '@/lib/client/i18n'
import icon_new from '@/../public/images/newer/icon_new.png'
import bg_arrival_top from '@/../public/images/newer/bg_arrival_top.png'
import bg_arrival_top_short from '@/../public/images/newer/bg_arrival_top_short.png'
import icon_tab_modal_collapsed from '@/../public/images/newer/icon_tab_modal_collapsed.png'
import icon_tab_modal_not_collapsed from '@/../public/images/newer/icon_tab_modal_not_collapsed.png'
import OrderTab from '../components/OrderTab'
import ProductItem from '../components/ProductItem'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { getWebItemListByPage } from '@/app/api/api-uchoice/tt/getWebItemListByPage/request'
import { getUserInfo } from '@/app/api/api-uchoice/uChoice/account/current/request'
import { getCategoryByLastDay } from '@/app/api/api-uchoice/tt/category/getCategoryByLastDay'
import styles from './inner.module.scss'
import classNames from 'classnames'

interface Props {
  id: string
}

const InfiniteScrollContent = ({ hasMore }: { hasMore?: boolean }) => {
  return (
    <>
      {hasMore ? (
        <div className={styles.no_more}>Loading...</div>
      ) : (
        <div className={styles.no_more}>{i18n.t('TiktokData.无更多数据')}</div>
      )}
    </>
  )
}

function getQueryParams() {
  const search = window.location.search.substring(1); // 去掉 ?
  return Object.fromEntries(new URLSearchParams(search));
}

const Inner: React.FC<Props> = ({ id }) => {
  const [activeKey, setActiveKey] = useState<string>('')
  const activeKeyRef = useRef<string>('')
  const [currentOrderTabIndex, setCurrentOrderTabIndex] =
    useState<string>('sampleOrderCount')
  const currentOrderTabIndexRef = useRef<string>('sampleOrderCount')

  const [fetchCategoryLoading, setFetchCategoryLoading] =
    useState<boolean>(false)
  const categoryLoadingRef = useRef<boolean>(false)
  const [categoryList, setCategoryList] = useState<any>([])
  const [tabModalCollapsed, setTabModalCollapsed] = useState<boolean>(true)
  const [modalFixedTop, setModalFixedTop] = useState<number>(0)

  const [fetchListLoading, setFetchListLoading] = useState<boolean>(false)
  const fetchLoadingRef = useRef<boolean>(false)
  const pageSize = 10
  const [pageNo, setPageNo] = useState<number>(1)
  const pageNoRef = useRef<number>(1)
  const [total, setTotal] = useState<number>(0)
  const totalRef = useRef<number>(0)
  const [productList, setProductList] = useState([])
  const productListRef = useRef<any>([])

  const [isQualityUser, setQualityUser] = useState(false)

  const orderTabList: any = [
    {
      label: i18n.t('Showcase.申样热度'),
      value: 'sampleOrderCount',
      key: 'hot_samples',
    },
    {
      label: i18n.t('Showcase.30天销量'),
      value: 'salesForLast30Days',
      key: '30days_sales',
    },
    {
      label: i18n.t('Showcase.最新'),
      value: 'createTime',
      key: 'newest',
    },
    {
      label: i18n.t('Showcase.店铺评分'),
      value: 'shopScore',
      key: 'shop_rating',
    },
    {
      label: i18n.t('Showcase.佣金率%'),
      value: 'creatorCommissionPercent',
      key: 'high_comm',
    }
  ]

  const requestList = async (refresh = false) => {
    if (fetchLoadingRef.current) {
      return
    }
    if (
      !refresh &&
      productListRef.current.length !== 0 &&
      productListRef.current.length === totalRef.current
    ) {
      return
    }
    fetchLoadingRef.current = true
    setFetchListLoading(true)
    if (refresh) {
      pageNoRef.current = 1
      setPageNo(1)
      totalRef.current = 0
      setTotal(0)
      productListRef.current = []
      setProductList([])
    }
    const params: any = {
      lastDay: 30,
      pageNo: pageNoRef.current,
      pageSize,
      sortBy: currentOrderTabIndexRef.current,
      sortDirection: 'desc'
    }
    if (activeKeyRef.current) {
      params.categoryId = activeKeyRef.current
    }
    try {
      const res = await getWebItemListByPage(params)
      fetchLoadingRef.current = false
      setFetchListLoading(false)
      if (res.code == 200 && res.result) {
        const { list, total } = res.result
        productListRef.current = productListRef.current.concat(...(list || []))
        setProductList(productListRef.current)
        totalRef.current = total
        setTotal(totalRef.current)
        pageNoRef.current = pageNoRef.current + 1
        setPageNo(pageNoRef.current)
      }
    } catch (error) {
      fetchLoadingRef.current = false
      setFetchListLoading(false)
    }
  }

  const getCategoryInfo = async () => {
    if (categoryLoadingRef.current) {
      return
    }
    categoryLoadingRef.current = true
    setFetchCategoryLoading(true)
    const result: any = await getCategoryByLastDay({
      lastDay: 30
    })
    setFetchCategoryLoading(false)
    categoryLoadingRef.current = false
    if (result.code == 200 && result.result) {
      const allObj = {
        name: isRegionTH() ? 'ทั้งหมด' : 'Toàn bộ',
        nameEn: 'All',
        id: '',
        emoji: '🍜'
      }
      const list = [allObj, ...(result.result || [])].map(item => ({
        label: isLanguageEN() ? item.nameEn : item.name,
        value: `${item.id}`
      }))
      setCategoryList(list)
    }
  }

  const handleUserLevel = async () => {
    if (webview) {
      const token = await getToken()
      if (token) {
        const res: any = await getUserInfo()
        if (res.code == 200 && res.result) {
          const { isQualityUser } = res.result
          setQualityUser(!!isQualityUser)
        }
      }
    }
  }

  useEffect(() => {
    handleQuery()
    handleUserLevel()
    getCategoryInfo()
    requestList(true)
  }, [])

  const handleQuery = () => {
    const queryParams = getQueryParams();
    const { rank, index } = queryParams
    if (rank) {
      const order = orderTabList.find(item => item.key === rank)
      if (order) {
        setCurrentOrderTabIndex(order.value)
        currentOrderTabIndexRef.current = order.value
      }
    }
    if (index) {
      setActiveKey(index)
      activeKeyRef.current = index
    }
  }

  const handleOnTabChange = value => {
    if (fetchListLoading) {
      return
    }
    window.scrollTo({ top: 0 })
    activeKeyRef.current = value
    setActiveKey(value)
    requestList(true)
    if (!tabModalCollapsed) {
      toggleTabModalCollapsed()
    }
  }

  const handleOrderTabChange = value => {
    if (fetchListLoading) {
      return
    }
    window.scrollTo({ top: 0 })
    currentOrderTabIndexRef.current = value
    setCurrentOrderTabIndex(value)
    requestList(true)
  }

  const toggleTabModalCollapsed = () => {
    if (categoryList.length === 0) {
      return
    }
    setTabModalCollapsed(!tabModalCollapsed)
    if (tabModalCollapsed) {
      const top = document
        .getElementById('icon_category_collapsed_container')
        ?.getBoundingClientRect().bottom
      setModalFixedTop((top || 0) + scaledPx(6))
      document.documentElement.style.overflow = 'hidden'
    } else {
      document.documentElement.style.overflow = 'auto'
    }
  }

  return (
    <TransparentNavPage
      title={i18n.t('Showcase.New Arrival')}
      hide={!webview}
      transparent
      showBackButton
      showShareButton
      onShared={() => statistic({ eventName: EventName.newer_arrival_share })}
    >
      <div className={styles.inner_container}>
        <div
          className={styles.top_container}
          onClick={() => {
            if (!tabModalCollapsed) {
              toggleTabModalCollapsed()
            }
          }}
        >
          <div>
            <Image
              src={webview ? bg_arrival_top : bg_arrival_top_short}
              className={styles.bg_arrival_top}
              alt=""
            ></Image>
          </div>
          <div className={styles.top_content_container}>
            <div className={styles.top_content}>
              <div className={styles.real_content_container}>
                <div className={styles.title_container}>
                  <div className={styles.title}>
                    {i18n.t('Showcase.新品来袭 优先申样！')}
                  </div>
                  <div className={styles.desc}>
                    🔥{i18n.t('Showcase.火爆热销中…')}
                  </div>
                </div>
                <Image
                  src={icon_new}
                  alt=""
                  className={styles.icon_new}
                ></Image>
              </div>
            </div>
            <div className={styles.tab_top_border}></div>
          </div>
        </div>
        {categoryList.length !== 0 && (
          <div
            className={styles.tab_container}
            style={{
              top: webview
                ? webview?.getData().topSafeArea + scaledPx(44)
                : scaledPx(40)
            }}
          >
            <div className={styles.top_tab_container}>
              <div className={styles.top_tab_left}>
                <Tabs
                  activeKey={activeKey}
                  style={{
                    '--active-line-color': '#FE6D45',
                    '--active-line-height': '3px',
                    '--title-font-size': '14px',
                    '--active-line-border-radius': '0'
                  }}
                  onChange={handleOnTabChange}
                >
                  {categoryList.map((item: any, index: number) => (
                    <Tabs.Tab title={item.label} key={item.value}></Tabs.Tab>
                  ))}
                </Tabs>
              </div>
              <div
                className={styles.icon_category_collapsed_container}
                id="icon_category_collapsed_container"
                onClick={toggleTabModalCollapsed}
              >
                <Image
                  src={
                    tabModalCollapsed
                      ? icon_tab_modal_collapsed
                      : icon_tab_modal_not_collapsed
                  }
                  className={styles.icon_category_collapsed}
                  alt=""
                />
              </div>
            </div>
            {categoryList.length !== 0 && (
              <OrderTab
                categoryList={orderTabList}
                current={currentOrderTabIndex}
                onChange={handleOrderTabChange}
              ></OrderTab>
            )}
          </div>
        )}
        {!tabModalCollapsed && categoryList.length !== 0 && (
          <div
            className={styles.tab_modal_container}
            style={{
              top: modalFixedTop
            }}
            onClick={toggleTabModalCollapsed}
          >
            <div
              className={styles.category_list_container}
              onClick={e => e.stopPropagation()}
            >
              {categoryList.map(item => {
                return (
                  <div
                    onClick={() => {
                      handleOnTabChange(item.value)
                    }}
                    className={classNames({
                      [styles.category_item]: true,
                      [styles.category_item_active]: activeKey === item.value
                    })}
                    key={item.value}
                  >
                    {item.label}
                  </div>
                )
              })}
            </div>
          </div>
        )}
        {productList.length === 0 && fetchListLoading && (
          <StatusView
            status={StatusViewType.loading}
            style={{ padding: '48px 0' }}
          ></StatusView>
        )}
        {productList.length === 0 && !fetchListLoading && (
          <StatusView
            status={StatusViewType.empty}
            style={{ padding: '48px 0' }}
          ></StatusView>
        )}
        {productList.map((item, index) => (
          <ProductItem
            key={index}
            item={item}
            isQualityUser={isQualityUser}
          ></ProductItem>
        ))}
        {productList.length !== 0 && (
          <InfiniteScroll
            loadMore={requestList}
            hasMore={productList.length !== total}
          >
            <InfiniteScrollContent hasMore={productList.length !== total} />
          </InfiniteScroll>
        )}
      </div>
    </TransparentNavPage>
  )
}

export default Inner
