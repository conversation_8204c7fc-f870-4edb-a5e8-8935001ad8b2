.inner_container {
  min-height: 100vh;
  background-color: #C4CDFF;

  .no_more {
    color: #ffffff;
    text-align: center;
  }

  .tab_modal_container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 9;

    .category_list_container {
      display: flex;
      flex-wrap: wrap;
      padding-top: 28px;
      padding-left: 24px;
      background: linear-gradient( 180deg, #C4CDFF 0%, #EEF1FE 100%);
      border-radius: 0px 0px 30px 30px;

      .category_item {
        margin-right: 24px;
        margin-bottom: 28px;
        height: 60px;
        line-height: 60px;
        text-align: center;
        background: #EDF0FF;
        border-radius: 30px;
        border: 2px solid #7F828F;
        padding: 0 24px;
        min-width: 196px;
        font-size: 24px;
        color: #303030;
      }

      .category_item_active {
        background: #FFF1ED;
        border: 2px solid #FE6D45;
        font-weight: bold;
        color: #FE6D45;
      }
    }
  }

  .tab_container {
    position: sticky;
    background-color: #C4CDFF;
    z-index: 2;

    .top_tab_container {
      display: flex;
      align-items: center;

      :global {
        .adm-tabs-tab {
          color: #303030;
        }
    
        .adm-tabs-tab-active {
          color: #303030;
          font-weight: bold;
        }
  
        .adm-tabs-header {
          .adm-tabs-header-mask-left {
            background: linear-gradient(to right, #C4CDFF, rgba(255, 255, 255, 0));
          }
  
          .adm-tabs-header-mask-right {
            background: linear-gradient(to left, #C4CDFF, rgba(255, 255, 255, 0));
          }
        }
  
        .adm-tabs-header {
          border-bottom: none;
        }
      }

      .top_tab_left {
        width: 654px;
      }

      .icon_category_collapsed_container {
        width: 96px;
        height: 60px;

        .icon_category_collapsed {
          width: 96px;
          height: 60px;
        }
      }
    }
  }

  .top_container {
    position: relative;

    .top_content_container {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;

      .real_content_container {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-bottom: 26px;

        .title_container {
          display: flex;
          flex-direction: column;
          align-items: center;

          .title {
            font-weight: bold;
            font-size: 44px;
            color: #020202;
            margin-bottom: 24px;
            text-align: center;
            padding: 0 24px;
            line-height: 48px;
          }

          .desc {
            width: 446px;
            height: 60px;
            line-height: 60px;
            text-align: center;
            font-size: 32px;
            color: #000000;
            background: linear-gradient( 270deg, rgba(87,53,212,0) 0%, rgba(14,31,195,0.15) 51%, rgba(87,53,212,0) 100%);
border: 1px solid;
            border-image: linear-gradient(270deg, rgba(99, 119, 145, 0.1), rgba(244, 244, 255, 0.8), rgba(230, 235, 245, 0.1)) 1 1;
          }
        }

        .icon_new {
          width: 168px;
          height: 160px;
          margin-right: 24px;
        }
      }
    }
    
    .tab_top_border {
      width: 100%;
      height: 20px;
      background-color:#C4CDFF;
      border-top: 1px solid #ffffff;
      border-left: 1px solid #ffffff;
      border-right: 1px solid #ffffff;
      border-radius: 22px 22px 0px 0px;
    }
  }
}