import React from 'react'
import Inner from './inner'
import { metadataTemplate, inApp } from '@/lib/server/utils'
import { isLanguageEN, isLanguageTH, isLanguageVI } from '@/lib/utils'
import { i18nS } from '@/lib/server/i18n'

interface Props {
  params: {
    id: string
  }
}

export default async function Index(props: Props) {
  return <Inner id={props.params.id} />
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({ title: '' })
  }

  const bg_arrival_top_vi = `${process.env.NEXT_PUBLIC_URL}/images/newer/bg_arrival_top_vi.png`
  const bg_arrival_top_th = `${process.env.NEXT_PUBLIC_URL}/images/newer/bg_arrival_top_th.png`
  const bg_arrival_top_en = `${process.env.NEXT_PUBLIC_URL}/images/newer/bg_arrival_top_en.png`
  let bg_arrival_top = bg_arrival_top_en
  if (isLanguageVI()) {
    bg_arrival_top = bg_arrival_top_vi
  }
  if (isLanguageTH()) {
    bg_arrival_top = bg_arrival_top_th
  }

  const description = ''
  const title = i18nS.t('Showcase.New Arrival')
  const icon = bg_arrival_top

  return metadataTemplate({ title, description, icon })
}
