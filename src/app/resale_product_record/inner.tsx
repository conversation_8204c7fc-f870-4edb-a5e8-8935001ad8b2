'use client'
import React, { useEffect, useState, useRef, Suspense } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import ImageWithPreload from '../components/ImageWithPreload'
import sigh from '@/../public/images/promotion/sigh.png'
import { useRouter } from '@/lib/hooks/useRouter'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { PullToRefresh } from '@/app/components/PullToRefresh'
import {
  TopGrandientCard,
  TransparentNavPage
} from '@/components/TransparentNavPage'
import { StickyHeader } from '@/components/StickyHeader'
import {
  ReSaleRecordListDto,
  ReSaleRecordListParams,
  ShowcaseStatisticsDto
} from '../api/api-uchoice/tt/item/promotionInfo/dtos'
import { loading } from '@/lib/client/loading'
import {
  getActiveUser,
  getReSaleRecordList,
  getShowcaseStatistics
} from '../api/api-uchoice/tt/item/promotionInfo/request'
import StatusView, { StatusViewType } from '../components/StatusView'
import { LoadMore } from '../components/LoadMore'
import ProductIntro from './components/productIntro'
import Item from './components/item'
import BtnTabs from '../components/BtnTabs'
import Button from '../components/Button'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { getTiktokAuthorizeUser } from '../api/api-uchoice/showcase/getTiktokAuthorizeUser'
import MaskModal from '@/components/MaskModal'
import { Popup } from 'react-vant'
import { webview } from '@/lib/client/webview'
interface Props {
  tt_user_id: string
  productId: string
}
const Inner = () => {
  const [itemList, setItemList] = useState<ReSaleRecordListDto[]>([])
  const sortByRef = useRef('video_create_time')
  const pageNoRef = useRef(1)
  const [ttUserId, setTtUserId] = useState('')
  const ttUserIdRef = useRef('')
  const router = useRouter()
  const totalRef = useRef(0)
  const [status, setStatus] = useState(StatusViewType.loading)
  const noMoreRef = useRef(false)
  const routerParams = useRouteParams<Props>()
  const [tabIndex, setTabIndex] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)
  const buttonRefs = useRef<(HTMLDivElement | null)[]>([])
  const [buttonWidths, setButtonWidths] = useState<number[]>([])
  const [productItem, setProductItem] = useState<ShowcaseStatisticsDto>({})
  const isToMyProRef = useRef('')
  const mounted = useMounted()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const btns = [
    {
      title: i18n.t('Promotion.recentlyPublished'),
      value: 'video_create_time',
      id: 0
    },
    { title: i18n.t('Promotion.GMV'), value: 'gmv', id: 1 },
    { title: i18n.t('Promotion.orders'), value: 'order_count', id: 2 },
    { title: i18n.t('Promotion.commission'), value: 'commission', id: 3 },
    { title: i18n.t('Promotion.views'), value: 'view_count', id: 4 }
  ]

  useEffect(() => {
    // 确保在组件渲染完成后再获取宽度
    const timer = setTimeout(() => {
      const widths = buttonRefs.current.map(ref => ref?.offsetWidth || 0)
      setButtonWidths(widths)
    }, 0) // 使用 0 毫秒延迟确保 DOM 元素已经渲染完毕

    return () => clearTimeout(timer) // 清理定时器
  }, [btns]) // 确保依赖 btns

  useEffect(() => {
    setTtUserId(routerParams.tt_user_id)
    ttUserIdRef.current = routerParams.tt_user_id
    fetchItems()
    handleTiktokAuthor()
  }, [])
  const loadMore = () => {
    if (noMoreRef.current) return
    fetchVideoItems()
  }

  const fetchItems = async () => {
    const { code, result } = await getShowcaseStatistics({
      tiktokUserId: routerParams?.tt_user_id
    })

    if (code === 200) {
      setProductItem(result)
    }
  }

  const scrollLeft = id => {
    let scrollIndex = id == 0 ? 2 : id + 1

    if (containerRef.current) {
      if (id == 0) {
        containerRef.current.scrollTo({
          left: 0,
          behavior: 'smooth' // 平滑滚动
        })
        return
      }
      if (id == btns.length - 1) {
        return
      }
      containerRef.current.scrollBy({
        left:
          id < 2
            ? -(scrollIndex - id) * buttonWidths[id]
            : (scrollIndex - id) * buttonWidths[id],
        behavior: 'smooth'
      })
    }
  }

  const handleTiktokAuthor = () => {
    getTiktokAuthorizeUser({
      tiktokUserId: ttUserIdRef.current || routerParams?.tt_user_id
    }).then(res => {
      isToMyProRef.current = res.result
      console.log(res.result, 'handle')
    })
  }
  const fetchVideoItems = async () => {
    if (pageNoRef.current > 1) loading.show({ userInteractive: true })

    loading.show()
    const params: ReSaleRecordListParams = {
      pageNo: pageNoRef.current,
      pageSize: 10,
      sortBy: sortByRef.current,
      sortDirection: 'desc',
      tiktokUserId: ttUserIdRef.current
    }
    const { code, result } = await getReSaleRecordList(params)
    loading.hide()

    if (code === 200) {
      const list = result.list
      let isMore = result.total === list.length || list.length === 0
      noMoreRef.current = isMore
      if (result.list.length > 0) {
        pageNoRef.current == 1
          ? setItemList([...result.list])
          : setItemList(itemList.concat(result.list))
        totalRef.current = result.total
        setStatus(
          result.total === 0 && pageNoRef.current === 1
            ? StatusViewType.empty
            : StatusViewType.loading
        )
      } else {
        setStatus(StatusViewType.empty)
      }

      pageNoRef.current++
    }
  }
  const onTabChange = (item: any) => {
    pageNoRef.current = 1
    sortByRef.current != item.value && scrollLeft(item?.id)
    sortByRef.current = item.value
    setTabIndex(item?.id)

    fetchVideoItems()
  }

  const handleRefresh = async () => {
    pageNoRef.current = 1
    await fetchVideoItems()
    await fetchItems()
  }
  const handleToPromoteContent = () => {
    console.log(isToMyProRef.current, 'isToMyProRef.current')

    statistic({
      eventName: EventName.click_resale__record_to_my_promotion,
      param: {
        tt_user_id: isToMyProRef.current
      }
    })
    router.push(
      `/my_promotion?union_id=${isToMyProRef.current}&isProSaleData=true`
    )
  }
  return mounted ? (
    <TransparentNavPage
      title={i18n.t('Promotion.showcaseResaleRecords')}
      transparent={true}
      hide={!webview}
    >
      <div className={styles.box}>
        <TopGrandientCard>
          <ProductIntro
            productItem={productItem}
            handleToPromoteContent={handleToPromoteContent}
            isToMyPro={isToMyProRef.current}
          />
        </TopGrandientCard>

        <div className={styles.item_box}>
          <StickyHeader zIndex="3">
            <div className={styles.btn_bg} ref={containerRef}>
              {btns.map((item, index) => {
                return (
                  <BtnTabs
                    ref={el => (buttonRefs.current[index] = el)}
                    btnIndex={tabIndex}
                    item={item}
                    key={index}
                    onClick={onTabChange}
                  ></BtnTabs>
                )
              })}
            </div>
          </StickyHeader>
          <PullToRefresh onRefresh={handleRefresh}>
            <div className={styles.content}>
              {itemList.length > 0 ? (
                <div>
                  {itemList.map((item, index) => {
                    return (
                      <div key={index}>
                        <Item item={item} tt_user_id={ttUserId}></Item>
                      </div>
                    )
                  })}
                  <div className={styles.fix_bottom}>
                    <div className={styles.sigh_box}>
                      <ImageWithPreload width={12} height={12} src={sigh} />
                      <span className={styles.sigh}>
                        {i18n.t('Promotion.videoDataSource')}
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className={styles.status_bg}>
                  <StatusView status={status}></StatusView>
                </div>
              )}
            </div>
          </PullToRefresh>
        </div>
        <LoadMore
          onLoadMore={loadMore}
          noMore={noMoreRef.current && itemList.length > 0}
        ></LoadMore>
      </div>
    </TransparentNavPage>
  ) : null
}

export default Inner
