.item_container {
    background-color: #fff;
    padding: 28px 24px;
    margin: 0 24px 24px 24px;
    border-radius: 8px;
    height: auto;
    border: 1px solid #fff;

}

.item_box {
    display: flex;
    justify-content: space-between;

}

.item_content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item_number {
    display: flex;
    justify-content: space-between;
}

.item_commission {
    font-size: 24px;
    color: #303030;

}

.item_comm {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    color: #8A8A8A;
    font-size: 24px;

}

.com_price {
    color: #FE6D45;
}

.video_img {
    width: 160px !important;
    height: 200px !important;
    margin-right: 16px;
    border-radius: 8px;
    position: relative;
    z-index: 1;
}

.item_img {
    width: 160px !important;
    height: 160px !important;
    margin-right: 16px;
    border-radius: 8px;
    margin-top: 8px;

}


.item_title_box,
.video_title_box {
    display: flex;
    flex-direction: column;
}

.icon_play_img {
    width: 64px !important;
    height: 64px !important;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -32px;
    margin-top: -32px;
    z-index: 2;
}

.video_title {
    flex: 1;
    box-sizing: border-box;
    overflow-wrap: break-word;
    font-weight: normal;
    font-size: 28px;
    color: #303030;
    margin-top: 8px;
    font-size: 28px;
    color: #333;
    height: 84px;
    line-height: 42px;
    /* 隐藏溢出部分的文本 */
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    overflow: hidden;
}

.item_title {
    flex: 1;
    box-sizing: border-box;
    overflow-wrap: break-word;
    font-weight: normal;
    font-size: 28px;
    color: #303030;
    margin-top: 8px;
    font-size: 28px;
    color: #333;
    height: 40px;
    line-height: 42px;
    /* 隐藏溢出部分的文本 */
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;
    overflow: hidden;

}

.item_time {
    font-weight: normal;
    font-size: 24px;
    color: #8A8A8A;
    margin-top: 8px;

}

.btn_click {
    border-radius: 4px;
    border: 1px solid #FE6D45;
    font-size: 24px;
    color: #FE6D45;
    padding: 6px 16px;

}