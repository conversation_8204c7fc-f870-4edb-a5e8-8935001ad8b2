'use client'
import React, { useEffect, useState, useRef } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import ImageWithPreload from '@/app/components/ImageWithPreload'
import icon_play_btn from '@/../public/images/promotion/icon_play_btn.png'
import { formatPercentSign, formatPrice, formatTime } from '@/lib/format'
import ItemBottom from '../itemBottom'
import Button from '@/app/components/Button'
import { useRouter } from '@/lib/hooks/useRouter'
import { showcaseInfo } from '@/app/api/api-mall/tt/item/categoryInfo'
const Item = ({ item, tt_user_id }) => {
  const [modalVisible, setModalVisible] = useState(false)
  const router = useRouter()
  const getItemId = productId => {
    showcaseInfo({ productId: productId }).then(res => {
      console.log('res: ' + JSON.stringify(res))
      if (res.code == 200) {
        router.push(`/product/${res?.result?.id}`)
      }
    })
  }
  return (
    <div className={styles.item_container}>
      <div className={styles.item_box}>
        <div
          className={styles.video_img}
          onClick={() => {
            // statistic({
            //   eventName: EventName.click_promote_play,
            //   param: {
            //     id: item.videoId
            //   }
            // })
            item.sourceUrl &&
              router.push(
                `https://www.tiktok.com/player/v1/${item.videoId}?id=${item.videoId}`
              )
          }}
        >
          {item.type == 1 ? (
            <ImageWithPreload
              width={160}
              height={200}
              src={icon_play_btn}
              className={styles.icon_play_img}
            />
          ) : null}
          <ImageWithPreload
            width={160}
            height={200}
            src={item.coverImg}
            className={styles.video_img}
          />
        </div>
        <div className={styles.item_content}>
          <div className={styles.video_title_box}>
            <div className={styles.video_title}>
              {item.videoDescription
                ? item.videoDescription
                : i18n.t('Promotion.defaultTitle')}
            </div>
          </div>
          <div className={styles.item_number}>
            <div className={styles.item_time}>
              <span>{i18n.t('Promotion.releaseTime')}</span>{' '}
              <span>{formatTime(item.videoCreateTime)}</span>
            </div>
          </div>
        </div>
      </div>
      <ItemBottom item={item}></ItemBottom>
      <div className={styles.item_box}>
        <div
          className={styles.item_img}
          onClick={() => {
            item.productId && getItemId(item.productId)
          }}
        >
          <ImageWithPreload
            width={160}
            height={160}
            src={item.image}
            className={styles.item_img}
          />
        </div>
        <div className={styles.item_content}>
          <div className={styles.item_title_box}>
            <div className={styles.item_title}>{item.productName}</div>
          </div>
          <div className={styles.item_commission}>
            <div className={styles.item_commission}>
              <span>{formatPrice(item.minPrice, true)}</span>
            </div>

            <div className={styles.item_comm}>
              <div>
                <span>{i18n.t('Product.佣金率')}</span>{' '}
                <span className={styles.com_price}>
                  {formatPercentSign(item.commissionRate)}
                </span>
              </div>
              <Button
                title={i18n.t('Promotion.clickToViewPopularVideos')}
                onClick={() => {
                  router.push(
                    `/promotion_details?tt_user_id=${tt_user_id}&productId=${item.productId}`
                  )
                }}
                className={styles.btn_click}
              ></Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Item
