'use client'
import React from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { formatSales } from '@/lib/format'
const ItemBottom = ({ item }) => {
  const bottomArr = [
    {
      title: i18n.t('Promotion.GMV'),
      value: formatSales(item.gmv, '', true)
    },
    {
      title: i18n.t('Promotion.orders'),
      value: formatSales(item.orderCount, '', true)
    },
    {
      title: i18n.t('Promotion.commission'),
      value: formatSales(item.commission, '', true)
    },
    {
      title: i18n.t('Promotion.views'),
      value: formatSales(item.viewCount, '', true)
    }
  ]
  return (
    <div className={styles.item_bottom}>
      {bottomArr.map((item, index) => {
        return (
          <div key={index} className={styles.item}>
            <div className={styles.item}>
              <div className={styles.item_value}>
                <span>{item.value}</span>
              </div>
            </div>
            <div className={styles.item_title}>
              <span>{item.title}</span>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default ItemBottom
