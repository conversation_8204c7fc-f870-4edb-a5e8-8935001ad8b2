'use client'
import React, { useEffect, useState, useRef } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { ConfigProvider, Popup, Skeleton } from 'react-vant'
import { formatPrice, formatSales } from '@/lib/format'
import InsertColoredText from '@/components/InsertColoredText'
import ImageWithPreload from '@/app/components/ImageWithPreload'
import icon_sign from '@/../public/images/promotion/icon_sign.png'
import icon_close from '@/../public/images/promotion/icon_close_tip.png'
import { rpxToPx } from '@/lib/client/utils'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { useRouter } from '@/lib/hooks/useRouter'
const ProductIntro = ({ productItem, handleToPromoteContent, isToMyPro }) => {
  const [modalVisible, setModalVisible] = useState<boolean>(false)
  const modalArr = [
    // {
    //   title: i18n.t('Promotion.增加佣金商品数'),
    //   value: i18n.t('Promotion.橱窗里通过该功能')
    // },
    // {
    //   title: i18n.t('Promotion.增加佣金值'),
    //   value: i18n.t('Promotion.商品增加佣金差值的总和')
    // },
    {
      title: i18n.t('Promotion.重新推广商品数'),
      value: i18n.t('Promotion.重新推广商品数_tips')
    },
    {
      title: i18n.t('Promotion.重新推广商品次数'),
      value: i18n.t('Promotion.重新推广商品次数_tips')
    },
    {
      title: i18n.t('Promotion.重新推广商品GMV'),
      value: i18n.t('Promotion.重新推广商品GMV_tips')
    },
    {
      title: i18n.t('Promotion.重新销售所获佣金'),
      value: i18n.t('Promotion.重新销售所获佣金_tips')
    }
  ]

  const bottomArr1 = [
    // //已增加佣金商品数
    // {
    //   title: i18n.t('Promotion.addcomitems'),
    //   value: formatSales(productItem.totalCount, '', true) || '-'
    // },
    //重新销售商品次数
    {
      title: i18n.t('Promotion.resaletimes'),
      value:
        productItem?.promotionCount || productItem?.promotionCount == 0
          ? formatSales(productItem?.promotionCount, '', true)
          : '-'
    },
    //重新销售商品数
    {
      title: i18n.t('Promotion.resaleitems'),
      value: formatSales(productItem?.joinPlanCount, '', true) || '-'
    }
    // //替换后佣金累计
    // {
    //   title: i18n.t('Promotion.comincreased'),
    //   value:
    //     productItem.totalCommission || productItem.totalCommission == 0
    //       ? formatPrice(productItem.totalCommission, true)
    //       : '-'
    // }
  ]
  const bottomArr2 = [
    //重新销售GMV
    {
      title: i18n.t('Promotion.resalegMV'),
      value:
        productItem?.promotionGmv || productItem?.promotionGmv == 0
          ? formatPrice(productItem?.promotionGmv, true)
          : '-'
    },
    //重新销售所获佣金 promotionCommission
    {
      title: i18n.t('Promotion.resalecom'),
      value:
        productItem?.promotionCommission ||
        productItem?.promotionCommission == 0
          ? formatPrice(productItem?.promotionCommission, true)
          : '-'
    }
  ]
  const router = useRouter()
  const handleCancel = () => {
    setModalVisible(false)
  }

  return (
    <div className={styles.boxs}>
      {productItem ? (
        <div className={styles.contents}>
          <div className={styles.title_box}>
            <div className={styles.title}>
              {i18n.t('Promotion.overviewOfResalePerformance')}
            </div>
            <div
              onClick={() => {
                setModalVisible(true)
              }}
            >
              <ImageWithPreload
                width={13}
                height={13}
                src={icon_sign}
                className={styles.icon_sign}
              />
            </div>
          </div>
          <div className={styles.data_bottom}>
            {bottomArr1.map((item, index) => {
              return (
                <div key={index} className={styles.item}>
                  <div className={styles.item}>
                    <div className={styles.item_value}>
                      <span>{item.value}</span>
                    </div>
                  </div>
                  <div className={styles.item_title}>
                    <span>{item.title}</span>
                  </div>
                </div>
              )
            })}
          </div>
          <div className={styles.data_bottom}>
            {bottomArr2.map((item, index) => {
              return (
                <div key={index} className={styles.item}>
                  <div className={styles.item}>
                    <div className={styles.item_value}>
                      <span>{item.value}</span>
                    </div>
                  </div>
                  <div className={styles.item_title}>
                    <span>{item.title}</span>
                  </div>
                </div>
              )
            })}
          </div>
          {isToMyPro && (
            <div
              className={styles.goToMyPromotion}
              onClick={() => {
                handleToPromoteContent()
              }}
            >
              {i18n.t('Profit.viewmyAllPromotion')}
            </div>
          )}
          {productItem.promotionCommissionIncrease ? (
            <div className={styles.item_congraduation}>
              <div className={styles.congradulations}>
                {i18n.t('Promotion.Congradulations')}
              </div>
              <div className={styles.cong_text}>
                <InsertColoredText
                  text={i18n.t('Promotion.throughAddingCommission')}
                  className={styles.insert_title}
                >
                  <span className={styles.color_words}>
                    {' '}
                    {formatPrice(
                      productItem.promotionCommissionIncrease,
                      true
                    )}{' '}
                  </span>
                  <span className={styles.congradulations}>
                    {' '}
                    {i18n.t('Promotion.keepTip')}
                  </span>
                </InsertColoredText>
              </div>
            </div>
          ) : null}
        </div>
      ) : (
        <Skeleton avatar avatarSize={90}></Skeleton>
      )}
      <Popup
        destroyOnClose={true}
        visible={modalVisible}
        style={{
          height: rpxToPx(1008),
          borderRadius: '8px 8px 0px 0px',
          overflow: 'scroll'
        }}
        position="bottom"
        onClose={handleCancel}
      >
        <div className={styles.modal_box}>
          <div className={styles.modal_content}>
            <div className={styles.modal_title_box}>
              <div></div>
              <div className={styles.modal_title}>
                {i18n.t('Promotion.关于指标')}
              </div>
              <div
                onClick={() => {
                  setModalVisible(false)
                }}
              >
                <ImageWithPreload
                  width={32}
                  height={32}
                  src={icon_close}
                  className={styles.icon_close}
                />
              </div>
            </div>
            <div className={styles.modal_item_box}>
              {modalArr.map(item => {
                return (
                  <div key={item.title} className={styles.modal_item}>
                    <div className={styles.modal_item_title}>{item.title}</div>
                    <div className={styles.modal_item_value}>{item.value}</div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </Popup>
    </div>
  )
}

export default ProductIntro
