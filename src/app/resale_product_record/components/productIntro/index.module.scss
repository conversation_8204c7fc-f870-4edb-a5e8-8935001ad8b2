.contents {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 44px 24px 32px 24px;
}

.title {
    font-weight: bold;
    font-size: 36px;
    color: #303030;
    display: flex;
    align-items: flex-start;
}

.data_bottom {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto auto;
    gap: 2px;
    padding: 0 24px;
    margin-top: 48px;
}

.item {
    display: flex;
    flex-direction: column;
    // justify-content: space-between;
    align-items: center;
}

.item_value {
    font-weight: bold;
    font-size: 36px;
    color: #303030;
}

.item_additonal {
    font-size: 20px;
    color: #FE6D45;
}

.item_title {
    font-size: 24px;
    color: #6E6E6E;
    text-align: center;
}

.item_congraduation {
    background: #FFF2EE;
    border-radius: 8px;
    border: 1px solid #FE6D45;
    padding: 24px;
    margin-top: 24px;
}

.goToMyPromotion {
    color: #FE6D45;
    font-size: 24px;
    margin-top: 24px;
}

.congradulations {
    font-weight: bold;
    font-size: 24px;
    color: #303030;
    margin-bottom: 8px;
}

.insert_title {
    font-size: 24px;
    color: #6E6E6E;
    display: inline;

}

.color_words {
    font-size: 24px;
    color: #FE6D45;
    display: inline;

}

.icon_sign {
    width: 26px !important;
    height: 26px !important;
    margin-left: 12px;
}

.title_box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;

}

.modal_box {
    position: relative;
}


.modal_title {
    font-weight: bold;
    font-size: 36px;
    color: #303030;
    text-align: center;

}

.modal_content {
    padding: 0 24px;
}

.modal_item {
    margin-top: 36px;
}

.modal_item_title {
    font-weight: bold;
    font-size: 30px;
    color: #303030;
    margin-bottom: 16px;
}

.modal_item_value {
    font-size: 26px;
    color: #6E6E6E
}

.icon_close {
    width: 32px !important;
    height: 32px !important;
}

.modal_title_box {
    width: 702px;
    flex: 1;
    display: flex;
    justify-content: space-between;
    // position: fixed;
    align-items: center;
    background-color: #fff;
    padding: 24px;
    box-sizing: border-box;
    z-index: 2;

}

.modal_item_box {
    height: 862px;
    padding: 0px 0 160px 0;
    overflow-y: scroll;
}

.modal_item_box::-webkit-scrollbar {
    display: none;
}