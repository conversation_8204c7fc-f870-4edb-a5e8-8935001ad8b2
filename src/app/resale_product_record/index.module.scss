.btn_bg {
    white-space: nowrap;
    overflow-x: auto;
    display: flex;
    padding: 28px 10px 24px 24px;
    -ms-overflow-style: none;
    width: 100%;
    -ms-overflow-style: none;
    background-color: #f5f5f5;
}

.btn_bg::-webkit-scrollbar {
    display: none;
}

.box {
    height: 100vh;
    width: 750px;
    background: #F5F5F5;
}



.item_box {
    display: flex;
    flex-direction: column;
    width: 750px;
    background: #F5F5F5;
}

.my_pro_title {
    padding: 0 8px;
    height: 44px;
    border-radius: 4px;
    border: 2px solid #FE6D45;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    color: #fe6d45;
}

.status_bg {
    margin-top: 150px;
    height: 50vh;
}

.content {
    display: flex;
    flex: 1;
    flex-direction: column;
}

.sigh_box {
    display: flex;
    justify-content: center;
    align-items: center;
}

.sigh {
    font-weight: normal;
    font-size: 24px;
    color: #9A9A9A;
    margin-left: 8px;
}

.status_i18n {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80vh;
}