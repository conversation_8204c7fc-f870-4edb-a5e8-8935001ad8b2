'use client'
import React, { useEffect, useState } from 'react'
import styles from './index.module.scss'
import Image from 'next/image'
import icon_back from '@/../public/images/theme/icon_back.png'
import icon_share from '@/../public/images/theme/icon_share.png'
import { useRouter } from '@/lib/hooks/useRouter'
import { webview } from '@/lib/client/webview'

const NavCard = ({ title, isShare }) => {
  const router = useRouter()
  return (
    <div
      className={styles.nav_box}
      style={{
        marginTop: webview ? `${webview?.getData().topSafeArea + 24}px` : '0',
        paddingTop: webview ? `${webview?.getData().topSafeArea + 24}px` : '0'
      }}
    >
      <div
        onClick={() => {
          router.back()
        }}
      >
        <Image src={icon_back} alt="icon" className={styles.icon_back} />
      </div>
      <span className={styles.title}>{title}</span>
      {isShare ? (
        <Image src={icon_share} alt="icon" className={styles.icon_share} />
      ) : (
        <div></div>
      )}
    </div>
  )
}

export default NavCard
