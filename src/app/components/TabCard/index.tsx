import React, { useEffect, useState } from 'react'
import { Tabs } from 'react-vant'
import './index.scss'
import styles from './index.module.scss'
import { getTtaCategoryInfo } from '@/app/api/api-mall/tt/item/categoryInfo'
import { useMounted } from '@/lib/hooks/useMounted'
import { px2rem } from '@/lib/client/utils'
import { isLanguageEN, isRegionTH } from '@/lib/utils'

const CommonTab = ({ handleTabClick }) => {
  const [categoryInfo, setCategoryInfo] = useState<any>([])
  const mounted = useMounted()
  useEffect(() => {
    getCategoryInfo()
  }, [])

  // 获取分类
  const getCategoryInfo = async () => {
    const data = await getTtaCategoryInfo()
    if (data.code == 200) {
      let list: any = data.result || []
      if ((list || []).length > 0) {
        const obj = {
          name: isRegionTH() ? 'ทั้งหมด' : 'Toàn bộ',
          nameEn: 'All',
          id: 0,
          emoji: '🍜'
        }
        setCategoryInfo([obj, ...list])
      }
    }
  }

  return (
    mounted && (
      <div className={styles.tab_container}>
        <div className={styles.tab_box}>
          <Tabs
            defaultActive={0}
            swipeThreshold={5}
            duration={800}
            lineWidth={px2rem(32)}
            scrollspy={false}
            onClickTab={async event => {
              let tabIndex = await event.index
              const item: any = (await categoryInfo)
                ? categoryInfo[tabIndex]
                : ''
              handleTabClick(item)
            }}
          >
            {(categoryInfo || []).map((item: any) => (
              <Tabs.TabPane
                key={item.id}
                title={isLanguageEN() ? item.nameEn : item.name}
                titleClass={styles.tab_title}
              ></Tabs.TabPane>
            ))}
          </Tabs>
        </div>
      </div>
    )
  )
}

export default CommonTab
