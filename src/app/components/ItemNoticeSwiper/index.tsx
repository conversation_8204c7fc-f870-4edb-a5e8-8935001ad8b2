import { px2rem } from '@/lib/client/utils'
import { Swiper } from 'antd-mobile'
import React, { ReactNode } from 'react'

interface Props {
  notices: ReactNode[]
}

export const ItemNoticeSwiper = ({ notices }: Props) => {
  return (
    <div className="rounded-[4px flex h-[60px] items-center px-[20px]">
      <Swiper
        autoplay
        autoplayInterval={2000}
        loop
        allowTouchMove={false}
        direction="vertical"
        style={{ height: px2rem(30) }}
        indicator={() => null}
      >
        {notices.map((notice, index) => (
          <Swiper.Item key={index}>
            <div className="truncate text-[24px] leading-[30px] text-black">
              {notice}
            </div>
          </Swiper.Item>
        ))}
      </Swiper>
    </div>
  )
}
