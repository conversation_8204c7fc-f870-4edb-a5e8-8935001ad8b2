// import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'
import main_logo from '../../images/defaultpage/main_logo.png'
import styles from './index.module.scss'
import { ImagePreview } from 'react-vant'
import { Image } from 'antd-mobile'
import default_img from '@/../public/images/common/default.png'
const ImageWithPreload = ({
  src,
  alt = '',
  width = 0,
  height = 0,
  className = '',
  imageStyle = {},
  preview = false
}) => {
  const [isImageLoaded, setIsImageLoaded] = useState(true)
  const srcRef = useRef(src)
  useEffect(() => {
    srcRef.current = src
  }, [src])
  const handleImageLoad = () => {
    setIsImageLoaded(true)
  }
  const getImagePreview = () => {
    ImagePreview.open({
      images: [srcRef.current],
      closeable: true
    })
  }
  return (
    <div
      onClick={() => {
        preview && srcRef.current && getImagePreview()
      }}
    >
      {!isImageLoaded && (
        // 占位符，可以使用 loading 动画或者其他内容
        <div
          style={{ width: width, height: height, backgroundColor: '#fffffff' }}
          className={styles.placeholder_box}
        >
          <Image
            src={typeof main_logo === 'string' ? main_logo : main_logo.src}
            width={width}
            height={height}
            className={styles.placeholder_img}
            alt={''}
          />
        </div>
      )}

      {/* 图片回显 */}
      {srcRef.current ? (
        <Image
          src={
            typeof srcRef.current === 'string'
              ? srcRef.current
              : srcRef.current.src
          }
          alt=""
          className={className}
          width={width}
          height={height}
          onLoad={handleImageLoad}
          style={imageStyle}
          onError={e => {
            srcRef.current = default_img
          }}
        />
      ) : (
        <Image
          src={typeof default_img === 'string' ? default_img : default_img.src}
          alt=""
          className={className}
          width={width}
          height={height}
          style={imageStyle}
        />
      )}
    </div>
  )
}

export default ImageWithPreload
