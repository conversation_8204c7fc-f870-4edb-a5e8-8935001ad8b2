import { i18n } from '@/lib/client/i18n'
import React from 'react'

interface Props {
  maleRate: number
  femaleRate: number
}
export const SexChart = ({ maleRate, femaleRate }: Props) => {
  return (
    <div>
      <div className="flex justify-between pb-[12px]">
        <span className="text-[26px] text-[#038BFC]">
          {i18n.t('TiktokData.男')}
          {maleRate}%
        </span>
        <span className="text-[26px] text-[#FF3CA2]">
          {i18n.t('TiktokData.女')}
          {femaleRate}%
        </span>
      </div>

      <div className="relative h-[20px] w-full rounded-[10px] bg-[#FF3CA2]">
        <div
          className="absolute left-0 top-0 h-[20px] rounded-[10px] bg-[#038BFC]"
          style={{ width: `${(maleRate / (maleRate + femaleRate)) * 100}%` }}
        ></div>
      </div>
    </div>
  )
}
