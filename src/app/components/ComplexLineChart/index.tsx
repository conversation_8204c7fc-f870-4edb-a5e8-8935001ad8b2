'use client'
import { useEffect, useRef } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { nanoid } from 'nanoid'
import { rpxToPx } from '@/lib/client/utils'
import styles from './index.module.scss'
import StatusView, { StatusViewType } from '../StatusView'

type ComplexLineChartProps = {
  width?: string
  height?: string
  title?: string
} & BaseComponentProps

const colors = ['#FE6D45', '#38A7FF', '#00B8C9']

const formatter = function (value: number, index: number) {
  return value
}

const betweenWrapper = (marker: string, name: string, val: string) => {
  return `<div style="min-width:34.1333vw;display: flex;align-items:center;justify-content:space-between;">
    <div style="display: flex;align-items:center;justify-content:space-between;">
      ${marker}
      <div>${name}</div>
    </div>
    <div>${val}</div>
  </div>`
}

const ComplexLineChart: React.FC<ComplexLineChartProps> = props => {
  const { width, height, title } = props

  const mounted = useMounted()
  const idRef = useRef(nanoid())
  const myChartRef = useRef<echarts.ECharts | null>(null)

  const initChart = () => {
    myChartRef.current = echarts.init(document.getElementById(idRef.current))
    myChartRef.current.setOption({
      color: colors,
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: {
          color: '#ffffff',
          fontSize: rpxToPx(24)
        },
        formatter: function (params: any, index: number) {
          console.log('tooltip params', params)

          let tooltipContent = params[0].name + '<br/>'
          params.forEach(function (item: any) {
            tooltipContent += betweenWrapper(
              item.marker,
              item.seriesName,
              item.value
            )
          })

          tooltipContent += betweenWrapper(
            `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${colors[2]};"></span>`,
            '转化率',
            params[0].value + '/' + params[1].value
          )
          return tooltipContent
        }
      },
      legend: {
        icon: 'rect',
        data: ['总量', '增量'],
        type: 'scroll',
        orient: 'horizontal',
        left: 'left',
        lineStyle: null,
        itemWidth: rpxToPx(20),
        itemHeight: rpxToPx(20),
        textStyle: {
          color: '#6E6E6E',
          fontSize: rpxToPx(26)
        }
      },
      grid: {
        left: 0,
        right: 0,
        bottom: 0,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          color: '#9A9A9A',
          fontSize: rpxToPx(22),
          showMinLabel: true,
          showMaxLabel: true
        },
        axisLine: {
          lineStyle: {
            color: '#EEEEEE'
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          alignTicks: true,
          axisLabel: {
            color: colors[0],
            fontSize: rpxToPx(22),
            formatter
          },
          splitLine: {
            lineStyle: {
              width: 1,
              color: '#EEEEEE'
            }
          }
        },
        {
          type: 'value',
          position: 'right',
          alignTicks: true,
          axisLabel: {
            color: colors[1],
            fontSize: rpxToPx(22),
            formatter
          },
          splitLine: {
            lineStyle: {
              width: 1,
              color: '#EEEEEE'
            }
          }
        }
      ],
      series: [
        {
          name: '总量',
          type: 'line',
          yAxisIndex: 0,
          showSymbol: false,
          lineStyle: {
            width: 1
          }
        },
        {
          name: '增量',
          type: 'line',
          yAxisIndex: 1,
          showSymbol: false,
          lineStyle: {
            width: 1
          }
        }
      ]
    })
  }

  const initChartData = () => {
    myChartRef.current?.showLoading('default', {
      text: 'loading',
      color: colors[0],
      textColor: '#303030',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0
    })
    setTimeout(() => {
      myChartRef.current?.setOption({
        xAxis: {
          data: new Array(30)
            .fill(null)
            .map((_: any, index: number) =>
              dayjs(Date.now() + index * 60 * 60 * 24 * 1000).format('MM-DD')
            )
        },
        series: [
          {
            data: new Array(30)
              .fill(null)
              .map(() => Math.floor(Math.random() * 100))
          },
          {
            data: new Array(30)
              .fill(null)
              .map(() => Math.floor(Math.random() * 100))
          }
        ]
      })
      myChartRef.current?.hideLoading()
    }, 1000)
  }

  useEffect(() => {
    if (!mounted) {
      return
    }
    initChart()
    initChartData()
  }, [mounted])

  return !mounted ? (
    <StatusView status={StatusViewType.loading}></StatusView>
  ) : (
    <div className={styles.chart_container}>
      {title && <div className={styles.title}>{title}</div>}
      <div
        id={idRef.current}
        className={styles.line_chart}
        style={{ width: width ? width : '100%', height }}
      ></div>
    </div>
  )
}

export default ComplexLineChart
