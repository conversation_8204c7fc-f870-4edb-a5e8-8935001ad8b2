import React from 'react'
import icon_price_hot from '@/../public/images/newer/icon_price_hot.png'
import icon_price_hot_vn from '@/../public/images/newer/icon_price_hot_vn.png'
import { i18n } from '@/lib/client/i18n'
import { Image } from '@/components/Image'
import { RegionComponent } from '../Region'

export const HighComm = ({ commission }: { commission: string }) => {
  return (
    <div className="flex">
      <div className="relative flex h-[52px] items-end justify-center">
        <div className="absolute left-0">
          <RegionComponent
            TH={
              <Image src={icon_price_hot} className="h-[52px] w-[62px]"></Image>
            }
            VN={
              <Image
                src={icon_price_hot_vn}
                className="h-[52px] w-[62px]"
              ></Image>
            }
          ></RegionComponent>
        </div>
        <div className="flex h-[36px] items-center rounded-[2px] bg-secondary bg-opacity-10 pl-[68px]">
          <span className="text-[22px] leading-[32px] text-secondary">
            {i18n.t('Product.High Commissions')}
          </span>
          <span className="ml-[4px] mr-[8px] text-[22px] font-bold leading-[32px] text-secondary">
            {commission}
          </span>
        </div>
      </div>
    </div>
  )
}
