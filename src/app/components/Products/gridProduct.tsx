/* eslint-disable tailwindcss/migration-from-tailwind-2 */
/* eslint-disable @next/next/no-img-element */
import { SimilarProductDto } from '@/app/api/api-uchoice/tt/item/similarProductList/dtos'
import { resizeImageUrl } from '@/lib/client/utils'
import { i18n } from '@/lib/client/i18n'
import { useRouter } from '@/lib/hooks/useRouter'
import React from 'react'
import { Trans } from 'react-i18next'
import { HighComm } from './highComm'
import { formatPrice } from '@/lib/format'

export const GridProduct = ({ item }: { item: SimilarProductDto }) => {
  const router = useRouter()

  return (
    <div
      className="w-[352px] rounded-[4px] bg-white pb-[16px] touch-opacity"
      onClick={() => {
        router.push(`/product/${item.id}`)
      }}
    >
      <img
        className="size-[352px] rounded-t-[4px]"
        src={resizeImageUrl(item.image, 352)}
      ></img>
      <div className="px-[12px]">
        <div className="flex flex-col pt-[12px]">
          <HighComm commission={item.commissionRate}></HighComm>
          <span className="text-[28px] font-bold text-primary">
            {i18n.t('Product.Earn')}{' '}
            {item.minEarn === item.maxEarn
              ? formatPrice(item.maxEarn, true)
              : `${formatPrice(item.minEarn, true)}~${formatPrice(
                  item.maxEarn,
                  true
                )}`}
          </span>
        </div>
        <div className="line-clamp-2 overflow-ellipsis break-words pt-[4px] text-[24px] leading-[36px] text-black">
          {item.productName}
        </div>
        <div className="pt-[4px] text-[24px] font-bold text-black">
          {item.maxPrice === item.minPrice
            ? formatPrice(item.minPrice, true)
            : `${formatPrice(item.minPrice, true)}~${formatPrice(
                item.maxPrice,
                true
              )}`}
        </div>
        <div className="flex items-center">
          <img
            className="mr-[8px] h-[24px] w-[20px]"
            src="/images/product/list/fire.png"
          ></img>
          <span className="text-[22px] text-gray6E">
            <Trans
              i18nKey="Product.soldMonth"
              values={{
                sold: item.salesForLast30DaysStr
              }}
              components={{
                b: <span className="text-[22px] text-black" />
              }}
            />
          </span>
        </div>
        <div className="mt-[16px] flex h-[52px] w-full items-center justify-center rounded-[2px] bg-primary touch-opacity">
          <span className="text-[24px] font-bold text-white">
            {i18n.t('Product.免费领样')}
          </span>
        </div>
      </div>
    </div>
  )
}
