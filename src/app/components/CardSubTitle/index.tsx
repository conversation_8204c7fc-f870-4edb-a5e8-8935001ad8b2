import React from 'react'
import Image from 'next/image'
import classNames from 'classnames'
import { Dialog } from 'react-vant'
import icon_tips from '@/../public/images/hotspot/icon_tips.png'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'

type CardSubTitleProps = {
  title: string
  tips?: string
} & BaseComponentProps

const CardSubTitle: React.FC<CardSubTitleProps> = props => {
  const { className, style, title, tips } = props

  const handleShowDialog = () => {
    Dialog.alert({
      message: tips,
      confirmButtonText: i18n.t('Activity.确定'),
      confirmButtonColor: '#FE6D45'
    })
  }

  return (
    <div
      className={classNames([styles.card_sub_title_container, className])}
      style={style}
    >
      <div className={styles.title}>{title}</div>
      <div onClick={handleShowDialog}>
        <Image alt="" src={icon_tips} className={styles.icon_tips}></Image>
      </div>
    </div>
  )
}

export default CardSubTitle
