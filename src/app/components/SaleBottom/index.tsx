import React from 'react'
import styles from './index.module.scss'
import { i18n } from '@/lib/client/i18n'

const SaleBottom = ({ item, styleBg }: { item: any; styleBg?: any }) => {
  const saleArr = [
    {
      label: i18n.t('LowPrice.minSale30'),
      value: item.salesForLast30DaysStr
    },
    {
      label: i18n.t('LowPrice.sales'),
      value: item.salesStr
    },
    {
      label: i18n.t('LowPrice.SalesGrowth'),
      value: item.salesForTwoWeeksRate
    },
    {
      label: i18n.t('LowPrice.Influencers'),
      value: item.anchorCount
    }
  ]
  return (
    <div className={`${styleBg ? styleBg : styles.container}`}>
      {saleArr.map((item, i) => {
        if (item.value != '0.0%' && !item.value?.includes('-')) {
          return (
            <div className={styles.content} key={i}>
              <div className={styles.value}>{item.value}</div>
              <div className={styles.label}>{item.label}</div>
            </div>
          )
        }
      })}
    </div>
  )
}

export default SaleBottom
