.container {
  display: flex;
  justify-content: space-between;
  padding: 14px 24px;
  height: 100px;
  background: linear-gradient(180deg, #ece9ff 0%, #ffffff 100%);
  border-radius: 0px 0px 4px 4px;
}

.content {
  display: flex;
  flex:1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.value {
  font-size: 22px;
  font-weight: bold;
  color: #303030;
  line-height: 34px;
}

.label {
  font-size: 22px;
  font-weight: normal;
  color: #6e6e6e;
  line-height: 32px;
}
