import { i18n } from '@/lib/client/i18n'
import { useIntersectionObserver } from '@/lib/hooks/useIntersectionObserver'
import React, { useEffect } from 'react'

interface Props {
  noMore: boolean
  onLoadMore?: () => void
}
export const LoadMore = ({ noMore, onLoadMore }: Props) => {
  const { ref, isLeave } = useIntersectionObserver()
  useEffect(() => {
    if (!isLeave) {
      onLoadMore?.()
    }
  }, [isLeave])

  return (
    <div ref={ref}>
      {noMore ? (
        <div className="bg-background pb-[24px] text-center font-[24px] text-grayCC">
          {i18n.t('TiktokData.无更多数据')}
        </div>
      ) : (
        <div className="h-px"></div>
      )}
    </div>
  )
}
