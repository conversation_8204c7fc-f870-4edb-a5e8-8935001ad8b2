'use client'
import React, { useEffect, useRef } from 'react'
import { nanoid } from 'nanoid'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { rpxToPx } from '@/lib/client/utils'
import { formatSales } from '@/lib/format'

const colors = ['#FE6D45']

const formatter = function (value: number, index: number) {
  return formatSales(value)
}

interface Props {
  xAxis: number[]
  yAxis: number[]
  timeFormatter?: string
  label: string
}

export const SingleLineChart = ({
  xAxis,
  yAxis,
  timeFormatter = 'DD/MM/YYYY',
  label
}: Props) => {
  const idRef = useRef(nanoid())
  const myChartRef = useRef<echarts.ECharts | null>(null)

  const initChart = () => {
    myChartRef.current = echarts.init(document.getElementById(idRef.current))
    myChartRef.current.setOption({
      color: colors,
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: {
          color: '#ffffff',
          fontSize: rpxToPx(24)
        },
        extraCssText: 'z-index: 1;'
      },
      legend: {
        show: false
      },
      grid: {
        top: 16,
        left: 0,
        right: 16,
        bottom: 0,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          color: '#9A9A9A',
          fontSize: rpxToPx(22),
          showMinLabel: true,
          showMaxLabel: true
        },
        axisLine: {
          lineStyle: {
            color: '#EEEEEE'
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          alignTicks: true,
          axisLabel: {
            color: '#9A9A9A',
            fontSize: rpxToPx(22),
            formatter
          },
          splitLine: {
            lineStyle: {
              width: 1,
              color: '#EEEEEE'
            }
          }
        }
      ],
      series: [
        {
          name: label,
          type: 'line',
          yAxisIndex: 0,
          showSymbol: false,
          lineStyle: {
            width: 1
          }
        }
      ]
    })
  }

  useEffect(() => {
    initChart()
  }, [])

  useEffect(() => {
    if (idRef.current) {
      myChartRef.current?.setOption({
        xAxis: {
          data: xAxis.map(time => dayjs(time).format(timeFormatter))
        },
        series: [
          {
            data: yAxis
          }
        ]
      })
    }
  }, [xAxis, yAxis, timeFormatter])

  return (
    <div
      id={idRef.current}
      style={{ width: '100%', height: rpxToPx(412) }}
    ></div>
  )
}
