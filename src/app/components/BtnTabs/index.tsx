import { rpxToPx } from '@/lib/client/utils'
import styles from './index.module.scss'
import React, { forwardRef } from 'react'

interface BtnTabsProps {
  btnIndex: number
  item: {
    id: number
    title: string
    icon?: any
  }
  onClick: (item: { id: number; title: string }) => void
  className?: string
  btnStyle?: any
}

const BtnTabs = forwardRef<HTMLDivElement, BtnTabsProps>(
  ({ btnIndex, item, onClick, className = '', btnStyle }, ref) => {
    return (
      <div ref={ref}>
        <div
          key={btnIndex}
          onClick={() => {
            onClick(item)
          }}
          className={`${
            btnIndex === item.id
              ? styles.checked_btn
              : btnStyle || styles.unChecked_btn
          } ${styles.common_btn} ${className}`}
        >
          <span>{item.title}</span>
          {item?.icon ? (
            <img src={item.icon} className={styles.step_img} />
          ) : null}
        </div>
        <div className={styles.btn_width}></div>
      </div>
    )
  }
)

BtnTabs.displayName = 'BtnTabs'

export default BtnTabs
