import { ChildrenProps } from '@/lib/types/common'
import React from 'react'
import { PullRefresh } from 'react-vant'
import { Loading } from 'react-vant'

type Props = {
  backgroundColor?: string
  onRefresh: () => Promise<unknown> | void
} & ChildrenProps
export const PullToRefresh = ({
  children,
  onRefresh,
  backgroundColor = 'rgba(0,0,0,0)'
}: Props) => {
  const renderLoading = () => {
    return (
      <div
        className="flex size-full items-center justify-center"
        style={{ backgroundColor }}
      >
        <Loading type="spinner" size="16px" />
      </div>
    )
  }

  return (
    <PullRefresh
      headHeight={60}
      pullingText={renderLoading}
      loosingText={renderLoading}
      loadingText={renderLoading}
      onRefresh={onRefresh}
    >
      {children}
    </PullRefresh>
  )
}
