import React from 'react'
import score_icon from '@/../public/images/product/detail/score.png'
import { Image } from '@/components/Image'

const Score = ({ score }: { score: number }) => {
  return (
    <div className="flex">
      <div className="flex h-[30px] items-center rounded-[2px] border border-[#C1E9D5] bg-[#E9F7F0] px-[8px]">
        <Image src={score_icon} className="size-[22px]"></Image>
        <span className="ml-[8px] text-[24px] text-black">{score}</span>
      </div>
    </div>
  )
}

export default Score
