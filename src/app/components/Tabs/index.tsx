'use client'

import { getGoAppBarHeight } from '@/components/GoAppBar'
import { getNavHeight } from '@/components/TransparentNavPage'
import { UnderLineTabs } from '@/components/UnderLineTabs'
import { useSections } from '@/lib/hooks/useSections'
import React, { useState } from 'react'

export const Tabs = ({
  titles,
  lastTabScrollToBottom = true,
  firstTabScrollToTop = true
}: {
  titles: string[]
  lastTabScrollToBottom?: boolean
  firstTabScrollToTop?: boolean
}) => {
  const [tabIndex, setTabIndex] = useState(0)

  useSections({
    onSectionIndexChange(index) {
      setTabIndex(index)
    }
  })

  return (
    <div id="tabs">
      <UnderLineTabs
        titles={titles}
        index={tabIndex}
        onChange={index => {
          if (index == 0 && firstTabScrollToTop) {
            window.scrollTo({ top: 0 })
            setTabIndex(0)
            return
          }

          if (index == titles.length - 1 && lastTabScrollToBottom) {
            // 获取页面的总高度
            let documentHeight = document.documentElement.scrollHeight
            // 滚动到页面底部
            window.scrollTo(0, documentHeight)
            setTabIndex(titles.length - 1)
            return
          }

          setTabIndex(index)

          const element = document.getElementsByClassName('tab-section')[
            index
          ] as HTMLElement

          if (element) {
            element?.scrollIntoView()

            window.scrollTo({
              top:
                element!.offsetTop! -
                getGoAppBarHeight() -
                getNavHeight() -
                (document.getElementById('tabs')?.offsetHeight || 0)
            })
          }
        }}
      ></UnderLineTabs>
    </div>
  )
}
