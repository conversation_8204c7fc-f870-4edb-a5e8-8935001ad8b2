'use client'
import React, { useEffect, useRef } from 'react'
import { nanoid } from 'nanoid'
import * as echarts from 'echarts'
import styles from './index.module.scss'

const SimpleLineChart = props => {
  const { data } = props
  const idRef = useRef(nanoid())
  const myChartRef = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    myChartRef.current = echarts.init(document.getElementById(idRef.current))
    myChartRef.current.setOption({
      color: '#FF8361',
      grid: {
        left: 0,
        right: 0,
        bottom: 0,
        top: 0
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.map(item => item.time),
        axisLabel: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#0000000F'
          }
        }
      },
      series: [
        {
          data: data.map(item => item.value),
          type: 'line',
          showSymbol: false,
          lineStyle: {
            width: 1
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#FF582599'
              },
              {
                offset: 1,
                color: '#EFD2C900'
              }
            ])
          }
        }
      ]
    })
  }, [])

  return (
    <div
      id={idRef.current}
      className={styles.simple_line_chart_container}
    ></div>
  )
}

export default SimpleLineChart
