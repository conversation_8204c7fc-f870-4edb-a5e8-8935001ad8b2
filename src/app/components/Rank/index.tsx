import React from 'react'
import icon_top1 from '@/../public/images/theme/icon_top1.png'
import icon_top2 from '@/../public/images/theme/icon_top2.png'
import icon_top3 from '@/../public/images/theme/icon_top3.png'
import icon_top4 from '@/../public/images/theme/icon_top4.png'
import { Image } from '@/components/Image'

const colors = ['#B46F27', '#1C3E56', '#792E2F', '#4D5A6D']

interface Props {
  index: number
}

export const Rank = ({ index }: Props) => {
  return (
    <div className="relative">
      <Image
        src={
          [icon_top1, icon_top2, icon_top3, icon_top4][
            index < 4 ? index : Math.min(3, index)
          ]
        }
        className="h-[80px] w-[72px]"
      ></Image>
      <div
        className="absolute top-0 w-[72px] text-center text-[24px]"
        style={{
          color: colors[index < 4 ? index : Math.min(3, index)]
        }}
      >
        TOP
      </div>
      <div
        className="absolute top-[28px] w-[72px] text-center text-[28px] font-bold"
        style={{
          color: colors[index < 4 ? index : Math.min(3, index)]
        }}
      >
        {index + 1}
      </div>
    </div>
  )
}
