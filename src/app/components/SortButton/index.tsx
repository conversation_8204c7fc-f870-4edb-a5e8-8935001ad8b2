import React from 'react'
import sort_arrow_up from '@/../public/images/common/sort_arrow_up.png'
import sort_arrow_down from '@/../public/images/common/sort_arrow_down.png'
import Image from 'next/image'
import { AppColors } from '@/lib/const'

interface Props {
  title: string
  selected?: boolean
  onSelected: (selected: boolean) => void
}

export const SortButton = ({ title, selected, onSelected }: Props) => {
  return (
    <div className="flex items-center" onClick={() => onSelected(!selected)}>
      <span
        className="text-center text-[28px]"
        style={{ color: selected ? AppColors.primary : AppColors.gray6E }}
      >
        {title}
      </span>
      <Image
        alt=""
        src={selected ? sort_arrow_up : sort_arrow_down}
        className="ml-[8px] h-[8px] w-[14px]"
      ></Image>
    </div>
  )
}
