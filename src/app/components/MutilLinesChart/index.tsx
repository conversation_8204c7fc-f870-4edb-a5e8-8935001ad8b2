'use client'
import React, { useEffect, useRef } from 'react'
import { nanoid } from 'nanoid'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { rpxToPx } from '@/lib/client/utils'
import { formatSales } from '@/lib/format'

const formatter = function (value: number, index: number) {
  return formatSales(value)
}

interface Props {
  xData: number[]
  yDatas: number[][]
  tabs: {
    label: string
    color: string
  }[]
  timeFormatter?: string
}

export const MutilLinesChart = ({
  xData,
  yDatas,
  tabs,
  timeFormatter = 'DD/MM/YYYY'
}: Props) => {
  const idRef = useRef(nanoid())
  const myChartRef = useRef<echarts.ECharts | null>(null)

  const initChart = () => {
    myChartRef.current = echarts.init(document.getElementById(idRef.current))
  }

  useEffect(() => {
    initChart()
  }, [])

  useEffect(() => {
    if (idRef.current) {
      myChartRef.current?.setOption({
        color: tabs.map(it => it.color),
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.8)',
          textStyle: {
            color: '#ffffff',
            fontSize: rpxToPx(24)
          },
          extraCssText: 'z-index: 1;'
        },
        legend: {
          icon: 'roundRect',
          data: tabs.map(it => it.label),
          type: 'scroll',
          orient: 'horizontal',
          left: 'left',
          lineStyle: null,
          itemWidth: rpxToPx(20),
          itemHeight: rpxToPx(20),
          textStyle: {
            color: '#6E6E6E',
            fontSize: rpxToPx(26)
          }
        },
        grid: {
          left: 0,
          right: 0,
          bottom: 0,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            color: '#9A9A9A',
            fontSize: rpxToPx(22),
            showMinLabel: true,
            showMaxLabel: true
          },
          axisLine: {
            lineStyle: {
              color: '#EEEEEE'
            }
          },
          data: xData.map(time => dayjs(time).format(timeFormatter))
        },
        yAxis: tabs.map(it => ({
          type: 'value',
          alignTicks: true,
          axisLabel: {
            color: it.color,
            fontSize: rpxToPx(22),
            formatter
          },
          splitLine: {
            lineStyle: {
              width: 1,
              color: '#EEEEEE'
            }
          }
        })),
        series: tabs.map((it, i) => ({
          name: it.label,
          type: 'line',
          yAxisIndex: i,
          showSymbol: false,
          lineStyle: {
            width: 1
          },
          data: yDatas[i]
        }))
      })
    }
  }, [xData, yDatas, tabs, timeFormatter])

  return (
    <div
      id={idRef.current}
      style={{ width: '100%', height: rpxToPx(412) }}
    ></div>
  )
}
