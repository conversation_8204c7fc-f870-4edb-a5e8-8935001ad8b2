import {
  isLanguageEN,
  isLanguageTH,
  language,
  isLanguageVI,
  isRegionTH
} from '@/lib/utils'
import React, { useEffect, useState } from 'react'
import { Popover } from 'react-vant'
import change_language_white from '@/../public/images/common/change_language_white.png'
import { Image } from '@/components/Image'
import { statistic } from '@/lib/statistic'
import { i18n } from '@/lib/client/i18n'

export const ChangeLanguageButton = () => {
  const icon_lang_en = '/images/download/icon_lang_en.png'
  const icon_lang_th = '/images/download/icon_lang_th.png'
  const icon_lang_vi = '/images/download/icon_lang_vi.png'
  // const [_, forceUpdate] = useState(0)
  const actions = [
    {
      id: 0,
      text: 'English',
      icon: (
        <img style={{ width: '16px', height: '16px' }} src={icon_lang_en} />
      ),
      disabled: isLanguageEN()
    },
    {
      id: 1,
      text: 'ภาษาไทย',
      icon: (
        <img style={{ width: '16px', height: '16px' }} src={icon_lang_th} />
      ),
      disabled: isLanguageTH()
    },
    {
      id: 2,
      text: 'Tiếng Việt',
      icon: (
        <img style={{ width: '16px', height: '16px' }} src={icon_lang_vi} />
      ),
      disabled: isLanguageVI()
    }
  ]

  const select = option => {
    // @ts-ignore
    const url = new URL(window.location)

    if (option.id === 0) {
      url.searchParams.set('region', '')
      i18n.changeLanguage('en')
      statistic({
        eventName: 'change_language_en'
      })
    } else if (option.id === 1) {
      url.searchParams.set('region', 'TH')
      i18n.changeLanguage('th')
      url.searchParams.set('language', 'th')
      statistic({
        eventName: 'change_language_th'
      })
    } else if (option.id === 2) {
      url.searchParams.set('region', 'VN')
      i18n.changeLanguage('vi')
      url.searchParams.set('language', 'vi')
      statistic({
        eventName: 'change_language_th'
      })
    }
    // forceUpdate(prev => prev + 1)
    window.location.href = url.toString()

    // console.log(url.toString(), language(), 'jojiop')
  }

  return (
    <Popover
      placement="bottom-end"
      actions={actions}
      theme="dark"
      onSelect={select}
      reference={
        <Image className="size-[32px]" src={change_language_white}></Image>
      }
    />
  )
}
