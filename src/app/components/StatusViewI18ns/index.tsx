import React, { ReactNode, useMemo, useState } from 'react'
import classNames from 'classnames'
import Image from 'next/image'
import pic_empty from '@/app/images/components/pic_empty.png'
import pic_loading from '@/app/images/components/pic_loading.png'
import pic_net_error from '@/app/images/components/pic_net_error.png'
import pic_load_error from '@/app/images/components/pic_load_error.png'
import pic_product_error from '@/app/images/components/pic_product_error.png'
import pic_empty_dark from '@/app/images/components/pic_empty_dark.png'
import pic_loading_dark from '@/app/images/components/pic_loading_dark.png'
import pic_net_error_dark from '@/app/images/components/pic_net_error_dark.png'
import pic_load_error_dark from '@/app/images/components/pic_load_error_dark.png'
import pic_product_error_dark from '@/app/images/components/pic_product_error_dark.png'
import styles from './index.module.scss'
import { i18nS } from '@/lib/server/i18n'

export enum StatusViewType {
  loading,
  empty,
  net_error,
  load_error,
  product_error
}

type StatusViewProps = {
  status?: StatusViewType
  dark?: boolean
} & BaseComponentProps

const StatusViewI18ns: React.FC<StatusViewProps> = props => {
  const { status = StatusViewType.loading, style, className, dark } = props

  const getStatusPic = (val: StatusViewType) => {
    let picStatus = pic_loading
    switch (val) {
      case StatusViewType.loading:
        picStatus = dark ? pic_loading_dark : pic_loading
        break
      case StatusViewType.empty:
        picStatus = dark ? pic_empty_dark : pic_empty
        break
      case StatusViewType.net_error:
        picStatus = dark ? pic_net_error_dark : pic_net_error
        break
      case StatusViewType.load_error:
        picStatus = dark ? pic_load_error_dark : pic_load_error
        break
      case StatusViewType.product_error:
        picStatus = dark ? pic_product_error_dark : pic_product_error
        break
      default:
        break
    }
    return picStatus
  }

  const getStatusLabel = (val: StatusViewType) => {
    let picLabel = i18nS.t('Common.Loading')
    switch (val) {
      case StatusViewType.loading:
        picLabel = i18nS.t('Common.Loading')
        break
      case StatusViewType.empty:
        picLabel = i18nS.t('Common.No data')
        break
      case StatusViewType.net_error:
        picLabel = i18nS.t('Common.NetworkError')
        break
      case StatusViewType.load_error:
        picLabel = i18nS.t('Common.LoadFailed')
        break
      case StatusViewType.product_error:
        picLabel = i18nS.t('Common.ProductExpired')
        break
      default:
        break
    }
    return picLabel
  }

  return (
    <div
      className={classNames([styles.status_view_container, className])}
      style={style}
    >
      <Image
        src={getStatusPic(status)}
        className={styles.pic_status}
        alt=""
      ></Image>
      <div className={styles.status_label}>{getStatusLabel(status)}</div>
    </div>
  )
}

export default StatusViewI18ns
