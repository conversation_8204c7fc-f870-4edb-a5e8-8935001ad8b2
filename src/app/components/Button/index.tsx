'use client'
import React, { useEffect, useState, useRef } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import HandlerOnceTap from '@/lib/handlerOnceTap'

const Button = ({ title, onClick, className = '' }) => {
  return (
    <div
      className={className}
      onClick={() => {
        HandlerOnceTap(() => {
          return onClick()
        })
      }}
    >
      {title}
    </div>
  )
}

export default Button
