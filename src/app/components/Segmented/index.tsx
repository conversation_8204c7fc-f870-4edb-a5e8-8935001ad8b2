import React, { ReactNode, useMemo, useState } from 'react'
import classNames from 'classnames'
import styles from './index.module.scss'

export type TabItem = {
  label: string
  value: string
}

type SegmentedProps = {
  active: string
  tabList: TabItem[]
  onChange: (val: string) => void
  block?: boolean
  textStyle?: React.CSSProperties
  activeTextStyle?: React.CSSProperties
  extraItem?: ReactNode
  marginTop?: string
  marginBottom?: string
}

const Segmented: React.FC<SegmentedProps> = props => {
  const {
    active,
    tabList,
    onChange,
    block = false,
    textStyle = {},
    activeTextStyle = {},
    extraItem,
    marginTop,
    marginBottom
  } = props
  const [activeWidth, setActiveWidth] = useState<number>(0)
  const [activeBgHeight, setActiveBgHeight] = useState<number | string>(
    '6.933vw'
  )
  const [activeOffset, setActiveOffset] = useState<number>(0)

  const handleSegmentChange = (tab: TabItem) => {
    if (tab.value === 'extra_item') {
      return
    }
    onChange && onChange(tab.value)
  }

  const showTabList = useMemo(() => {
    if (extraItem) {
      return tabList.concat({ label: 'extra', value: 'extra_item' })
    }
    return tabList
  }, [tabList, extraItem])

  return (
    <div
      className={styles.segment_component_container}
      style={{
        display: block ? 'flex' : 'inline-block',
        marginTop: marginTop || 0,
        marginBottom: marginBottom || 0
      }}
    >
      <div
        className={styles.indicator}
        style={{
          width: activeWidth,
          left: activeOffset
        }}
        ref={(dom: HTMLDivElement) => {
          setActiveBgHeight(dom?.clientHeight)
        }}
      ></div>
      {showTabList.map((tab: TabItem) => (
        <div
          key={tab.value}
          className={classNames([
            styles.tab_item,
            {
              [styles.tab_item_active]: active === tab.value,
              [styles.tab_item_block]: block
            }
          ])}
          style={{
            height: activeBgHeight,
            ...(active === tab.value ? activeTextStyle : textStyle)
          }}
          onClick={() => handleSegmentChange(tab)}
          ref={(dom: HTMLDivElement) => {
            if (tab.value === active) {
              setActiveWidth(dom?.clientWidth)
              setActiveOffset(dom?.offsetLeft)
            }
          }}
        >
          <div className={styles.tab_item_inner}>
            {tab.value === 'extra_item' ? extraItem : tab.label}
          </div>
        </div>
      ))}
    </div>
  )
}

export default Segmented
