.segment_component_container {
  position: relative;
  height: 60px;
  padding: 4px;
  border-radius: 2px;
  background-color: #F5F5F5;

  .tab_item {
    min-width: 106px;
    position: relative;
    padding: 0 16px;
    display: inline-block;
    font-size: 26px;
    color: #8a8a8a;
    vertical-align: top;

    .tab_item_inner {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .tab_item_block {
    flex: 1;
  }

  .tab_item_active {
    position: relative;
    color: #fe6d45;
  }

  .indicator {
    position: absolute;
    left: 4px;
    top: 4px;
    bottom: 4px;
    border-radius: 2px;
    background-color: #fff;
    transition: all 0.3s;
  }
}
