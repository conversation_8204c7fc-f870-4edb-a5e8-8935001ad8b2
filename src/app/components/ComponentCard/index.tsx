import React, { ReactNode } from 'react'
import classNames from 'classnames'
import styles from './index.module.scss'

type ComponentCardProps = {
  showHeader?: boolean
  title?: string
  subTitle?: string
  customTitle?: ReactNode
  headerExtra?: ReactNode
  padding?: number
  headerClassName?: string
  headerStyle?: React.CSSProperties
  children?: ReactNode
  onClick?: () => void
} & BaseComponentProps

const ComponentCard: React.FC<ComponentCardProps> = props => {
  const {
    showHeader = true,
    title,
    subTitle,
    customTitle,
    headerExtra,
    className = '',
    style = {},
    headerClassName = '',
    headerStyle = {},
    children,
    onClick
  } = props

  return (
    <div
      className={classNames([styles.component_card_container, className])}
      style={{
        padding: '3.2vw',
        ...style
      }}
      onClick={() => {
        onClick && onClick()
      }}
    >
      {showHeader && (
        <div
          className={classNames([styles.header_container, headerClassName])}
          style={headerStyle}
        >
          {customTitle ? (
            customTitle
          ) : (
            <div className={styles.title_container}>
              {title && <div className={styles.title}>{title}</div>}
              {title && subTitle && (
                <div className={styles.sub_title}>{subTitle}</div>
              )}
            </div>
          )}
          {headerExtra ? headerExtra : <div />}
        </div>
      )}
      <div>{children}</div>
    </div>
  )
}

export default ComponentCard
