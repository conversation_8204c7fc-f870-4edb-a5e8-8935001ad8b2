import React from 'react'
import Image from 'next/image'
import one_left from '@/../public/images/high_commission/one_left.png'
import one_right from '@/../public/images/high_commission/one_right.png'
import two_left from '@/../public/images/high_commission/two_left.png'
import two_right from '@/../public/images/high_commission/two_right.png'
import three_left from '@/../public/images/high_commission/three_left.png'
import three_right from '@/../public/images/high_commission/three_right.png'
import styles from './index.module.scss'

const SerialItem = ({ item, index }) => {
  if (index === 0) {
    return (
      <div className={styles.item_serial_one}>
        <div className={styles.item_top}>Top</div>
        <div className={styles.serial_box}>
          <Image src={one_left} alt="title" className={styles.serial_img} />
          <span className={styles.serial_num}>{index + 1}</span>
          <Image src={one_right} alt="title" className={styles.serial_img} />
        </div>
      </div>
    )
  } else if (index == 1) {
    return (
      <div className={styles.item_serial_two}>
        <div className={styles.item_top}>Top</div>
        <div className={styles.serial_box}>
          <Image src={two_left} alt="title" className={styles.serial_img} />
          <span className={styles.serial_num}>{index + 1}</span>
          <Image src={two_right} alt="title" className={styles.serial_img} />
        </div>
      </div>
    )
  } else if (index == 2) {
    return (
      <div className={styles.item_serial_three}>
        <div className={styles.item_top}>Top</div>
        <div className={styles.serial_box}>
          <Image src={three_left} alt="title" className={styles.serial_img} />
          <span className={styles.serial_num}>{index + 1}</span>
          <Image src={three_right} alt="title" className={styles.serial_img} />
        </div>
      </div>
    )
  } else {
    return (
      <div className={styles.item_serial_other}>
        <div className={styles.serial_box}>
          <span className={styles.serial_num}>{index + 1}</span>
        </div>
      </div>
    )
  }
}

export default SerialItem
