'use client'
import { useMounted } from '@/lib/hooks/useMounted'
import Banner from './components/Banner'
import BrandIntro from './components/BrandIntro'
import ProductInfo from './components/ProductInfo'
import Requirements from './components/Requirements'
import PriceInfo from './components/PriceInfo'
import BottomActions from './components/BottomActions'
import { StickyHeader } from '@/components/StickyHeader'
import { Tabs } from '../components/Tabs'
import { TabSection } from '@/app/components/Tabs/TabSection'
import { useEffect, useState } from 'react'
import {
  getCommercialOrderDetail,
  CommercialOrderH5DetailVo
} from '../api/api-uchoice/h5/commercial/order/detail/request'
import { useRouteParams } from '@/lib/hooks/useRouteParams'

export const Inner = () => {
  const mounted = useMounted()
  const routeParams = useRouteParams<{
    inviteCode: string
  }>()
  const [commercialOrderDetail, setCommercialOrderDetail] =
    useState<CommercialOrderH5DetailVo>()

  const tabTitles = ['概览', '商品', '要求']

  useEffect(() => {
    fetch()
  }, [])

  const fetch = async () => {
    const res = await getCommercialOrderDetail({
      inviteCode: routeParams.inviteCode
    })
    if (res.code == 200 && res.result) {
      setCommercialOrderDetail(res.result)
    }
  }

  return mounted ? (
    <div className="w-[750px] bg-white">
      {/* Banner */}
      <Banner commercialOrderDetail={commercialOrderDetail} />

      {/* Tabs */}
      <StickyHeader withoutNavHeight>
        <div className="bg-white pt-[12px]">
          <Tabs
            titles={tabTitles}
            firstTabScrollToTop={false}
            lastTabScrollToBottom={false}
          ></Tabs>
          <div className="h-[2px] w-full bg-background"></div>
        </div>
      </StickyHeader>

      {/* Tab Content */}
      <div className="w-full p-[24px]">
        <TabSection></TabSection>
        <BrandIntro commercialOrderDetail={commercialOrderDetail} />

        <TabSection></TabSection>
        <ProductInfo commercialOrderDetail={commercialOrderDetail} />

        <TabSection></TabSection>
        <Requirements commercialOrderDetail={commercialOrderDetail} />

        <PriceInfo commercialOrderDetail={commercialOrderDetail} />
      </div>

      {/* Bottom Checkbox and Button */}
      <BottomActions
        commercialOrderDetail={commercialOrderDetail}
        toRefetch={fetch}
      />
    </div>
  ) : null
}
