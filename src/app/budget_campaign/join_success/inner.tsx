'use client'

import { useMounted } from '@/lib/hooks/useMounted'
import { useRouter } from '@/lib/hooks/useRouter'
import Image from 'next/image'
import { useRouteParams } from '@/lib/hooks/useRouteParams'

const Inner = () => {
  const mounted = useMounted()
  const router = useRouter()
  const routerParams = useRouteParams<{
    inviteCode: string
  }>()

  return mounted ? (
    <div className="mb-[24px] w-[750px] pt-[50px]">
      {/* 内容区域 */}
      <div className="flex flex-col items-center">
        <Image
          src="/images/budget_campaign/join_success/join_success.png"
          alt="join_success"
          width={750}
          height={330}
        />

        <span className="mt-[32px] text-[28px] text-black30">
          合作愉快！请尽快联系您的Manager申请样品～
        </span>

        <div
          className="mt-[96px] flex h-[80px] w-[600px] items-center justify-center rounded-[4px] bg-primary"
          onClick={() => {
            router.back()
          }}
        >
          <span className="text-[28px] font-bold text-white">申样入口</span>
        </div>

        <div
          className="mt-[24px] flex h-[80px] w-[600px] items-center justify-center rounded-[4px] border-[2px] border-primary bg-white"
          onClick={() => {
            router.push(`/budget_campaign/pdp?inviteCode=${routerParams.inviteCode}`)
          }}
        >
          <span className="text-[28px] font-bold text-primary">查看详情</span>
        </div>
      </div>
    </div>
  ) : null
}

export default Inner
