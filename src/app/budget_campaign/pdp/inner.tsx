'use client'

import { useMounted } from '@/lib/hooks/useMounted'
import Requirements from '../components/Requirements'
import { ProductInfo } from './components/ProductInfo'
import { BrandFlow } from './components/BrandFlow'
import { useGrayBody } from '@/lib/hooks/useGrayBody'
import { useEffect, useState } from 'react'
import {
  getCommercialOrderDetail,
  CommercialOrderH5DetailVo
} from '@/app/api/api-uchoice/h5/commercial/order/detail/request'
import { useRouteParams } from '@/lib/hooks/useRouteParams'

const Inner = () => {
  const mounted = useMounted()

  useGrayBody()

  const routeParams = useRouteParams<{
    inviteCode: string
  }>()
  const [commercialOrderDetail, setCommercialOrderDetail] =
    useState<CommercialOrderH5DetailVo>()


  useEffect(() => {
    fetch()
  }, [])

  const fetch = async () => {
    const res = await getCommercialOrderDetail({
      inviteCode: routeParams.inviteCode
    })
    if (res.code == 200 && res.result) {
      setCommercialOrderDetail(res.result)
    }
  }

  return mounted ? (
    <div className="w-[750px] py-[24px]">
      {/* 内容区域 */}
      <div>
        {/* 商品信息 */}
        <div className="mx-[24px] rounded-[16px] bg-white p-[24px]">
          <BrandFlow />
        </div>

        {/* 商品详情 */}
        <div className="mx-[24px] mt-[24px] rounded-[16px] bg-white p-[24px]">
          <ProductInfo commercialOrderDetail={commercialOrderDetail} />
        </div>

        {/* 参与要求 */}
        <div className="mx-[24px] mt-[24px] rounded-[16px] bg-white p-[24px]">
          <Requirements commercialOrderDetail={commercialOrderDetail} />
        </div>
      </div>
    </div>
  ) : null
}

export default Inner
