'use client'

import { ItemInfoDto } from '@/app/api/api-uchoice/tt/item/info/dtos'
import { info } from '@/app/api/api-uchoice/tt/item/info/request'
import { SkuModal } from '@/app/product/components/skuModal'
import { WebviewEvents } from '@/lib/client/webview/events'
import { webview } from '@/lib/client/webview'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { Image } from '@/components/Image'
import { FC, useEffect, useState } from 'react'
import { Loading, Skeleton } from 'react-vant'
import { isLanguageEN } from '@/lib/utils'
import { formatPrice } from '@/lib/format'
import { CommercialOrderH5DetailVo } from '@/app/api/api-uchoice/h5/commercial/order/detail/request'
import { makeSureInApp } from '@/lib/client/utils'

interface ProductInfoProps {
  commercialOrderDetail?: CommercialOrderH5DetailVo
}

export const ProductInfo: FC<ProductInfoProps> = ({
  commercialOrderDetail
}) => {
  const [itemInfo, setItemInfo] = useState<ItemInfoDto>()
  const [skuModalVisible, setSkuModalVisible] = useState(false)
  const toggleSkuModal = () => setSkuModalVisible(visible => !visible)

  useEffect(() => {
    fetch()
  }, [commercialOrderDetail?.itemVo?.id])

  const fetch = async () => {
    if (commercialOrderDetail?.itemVo?.id) {
      const { code, result } = await info({
        id: commercialOrderDetail?.itemVo?.id
      })
      if (code === 200) {
        setItemInfo(result)
      }
    }
  }

  return (
    <>
      {
        itemInfo ? 
        <div className="w-full">
          <div className="flex">
            {/* 商品图片 */}
            <div className="size-[200px] rounded-[8px]">
              <Image
                src={commercialOrderDetail?.itemVo?.image}
                width={200}
                height={200}
                className="size-[200px] rounded-[8px]"
              />
            </div>

            <div className="ml-[20px] flex flex-col justify-between">
              {/* 商品名称 */}
              <div className="flex flex-col">
                <span className="line-clamp-2 text-[24px] text-[#303030]">
                  {commercialOrderDetail?.itemVo?.productName}
                </span>
              </div>

              {/* 佣金比例 */}
              <div className="mt-[16px] flex">
                <div className="flex h-[40px] items-center rounded-[4px] bg-[#F5F5F5] px-[10px]">
                  <span className="text-[24px] text-[#303030]">达人佣金 </span>
                  <span className="ml-[4px] text-[24px] font-semibold text-[#303030]">
                    {(commercialOrderDetail?.itemVo?.partnerCommissionPercent / 100).toFixed(0)}%
                  </span>
                </div>
              </div>

              {/* 价格区间 */}
              <div className="mt-[16px] flex items-center">
                <span className="text-[28px] font-bold text-[#FE6D45]">
                  {commercialOrderDetail?.itemVo?.minPrice === commercialOrderDetail?.itemVo?.maxPrice
                    ? `${formatPrice(commercialOrderDetail?.itemVo?.minPrice, true)}`
                    : `${formatPrice(commercialOrderDetail?.itemVo?.minPrice, true)} ~ ${formatPrice(
                        commercialOrderDetail?.itemVo?.maxPrice,
                        true
                      )}`}
                </span>
              </div>
            </div>
          </div>
          <div
            className="mt-[24px] flex h-[80px] items-center justify-center rounded-[4px] bg-primary"
            onClick={toggleSkuModal}
          >
            <span className="text-[28px] font-bold text-white">去申样</span>
          </div>
        </div>
        :
        <Skeleton row={5}></Skeleton>
      }

      {itemInfo && (
        <SkuModal
          visible={skuModalVisible}
          toggle={toggleSkuModal}
          itemInfo={itemInfo}
          onSelectedSku={async (sku, count) => {
            // 因为数据详情和老的详情的接口字段不一样，做下兼容
            const item = itemInfo as any

            let convertedItemInfo = {
              id: `${item.ttaItemId}`,
              productId: item.productId,
              name: item.productName,
              nameEn: item.productName,
              itemType: 1,
              campaignId: '',
              homeImgUrl: item.homeImgUrl,
              skuResDtoList: [],
              itemExtensionDto: {
                id: '',
                content: ''
              },
              skuList: item.skuList,
              visible: true,
              totalStock: 1,
              promotionEndTime: 1,
              salesStr: '',
              commissionRate: item.creatorCommissionPercentStr,
              planCommission: '',
              maxPrice: 1.0,
              minPrice: 1.0,
              minEarn: 1.0,
              maxEarn: 1.0,
              isCollected: false,
              link: '',
              sourceLink: '',
              isShowImg: false,
              shopCode: '',
              shopName: '',
              itemLabels: []
            }

            // await webview?.send(WebviewEvents.toPage, {
            //   name: WebViewToPages.sampleApply,
            //   params: {
            //     itemInfo: convertedItemInfo,
            //     sku,
            //     count: `${count}`
            //   }
            // })

            if (!webview) {
              makeSureInApp({
                route: 'Web',
                params: {
                  initialURL: `${window.location.origin}/rankinghit/${campaignId || ''
                    }?region=${isRegionVN() ? 'VN' : 'TH'}`,
                },
                desc: '「💥สะสมแต้มลุ้นรับรถ BYD มูลค่า 569,900 บาท✨เข้า uChoice Pro และรับสิทธิ์ด่วน!」',
              })
            }

          }}
        ></SkuModal>
      )}
    </>
  )
}
