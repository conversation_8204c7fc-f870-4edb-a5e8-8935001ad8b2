'use client'

import { FC } from 'react'
import Image from 'next/image'

interface RequirementsProps {}

export const Requirements: FC<RequirementsProps> = () => {
  return (
    <div className="w-full">
      <h2 className="mb-[24px] text-[30px] font-bold text-black">参与要求</h2>

      <div className="space-y-[24px]">
        {/* 脚本要求 */}
        <div className="flex items-start">
          <div className="mt-[4px] flex items-center">
            <span className="mr-[4px] text-[28px] text-[#FF3141]">*</span>
            <span className="text-[28px] text-[#303030]">脚本要求</span>
          </div>
          <div className="ml-[20px]">
            <div className="rounded-[8px] border border-[#FE6D45] px-[16px] py-[12px]">
              <span className="text-[28px] text-[#FE6D45]">查看脚本要求</span>
            </div>
          </div>
        </div>

        {/* 参考视频 */}
        <div className="flex flex-col">
          <div className="mb-[16px] flex items-center">
            <span className="mr-[4px] text-[28px] text-[#FF3141]">*</span>
            <span className="text-[28px] text-[#303030]">参考视频</span>
          </div>
          <div className="flex space-x-[12px]">
            {[1, 2, 3].map((item, index) => (
              <div key={index} className="relative h-[200px] w-[160px]">
                <div className="bg-gray-200 h-full w-full rounded-[8px]"></div>
                <div className="absolute left-0 top-0 h-full w-full rounded-[8px] bg-black bg-opacity-40"></div>
                <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
                  <div className="flex h-[47px] w-[47px] items-center justify-center rounded-full border border-[#BBBBBD] bg-[rgba(138,138,138,0.6)]">
                    <div className="ml-[4px] h-0 w-0 border-y-[8px] border-l-[16px] border-y-transparent border-l-white"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 内容要求 */}
        <div>
          <h2 className="mb-[16px] text-[30px] font-bold text-black">
            内容要求
          </h2>

          <div className="space-y-[24px]">
            <div className="flex justify-between">
              <div className="flex items-center">
                <span className="mr-[4px] text-[28px] text-[#FF3141]">*</span>
                <span className="text-[28px] text-[#303030]">交付要求</span>
              </div>
              <span className="text-[28px] text-[#6E6E6E]">视频</span>
            </div>

            <div className="flex justify-between">
              <div className="flex items-center">
                <span className="mr-[4px] text-[28px] text-[#FF3141]">*</span>
                <span className="text-[28px] text-[#303030]">TAP挂链</span>
              </div>
              <span className="text-[28px] text-[#6E6E6E]">必须</span>
            </div>

            <div className="flex justify-between">
              <div className="flex items-center">
                <span className="mr-[4px] text-[28px] text-[#FF3141]">*</span>
                <span className="text-[28px] text-[#303030]">内容数量</span>
              </div>
              <span className="text-[28px] text-[#6E6E6E]">1</span>
            </div>

            <div className="flex justify-between">
              <div className="flex items-center">
                <span className="mr-[4px] text-[28px] text-[#FF3141]">*</span>
                <span className="text-[28px] text-[#303030]">内容时长</span>
              </div>
              <span className="text-[28px] text-[#6E6E6E]">至少30s</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
