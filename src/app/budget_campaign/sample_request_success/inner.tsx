'use client'

import { useMounted } from '@/lib/hooks/useMounted'
import { Image } from '@/components/Image'
import successIcon from '@/../public/images/budget_campaign/sample_request/success.png'

const Inner = () => {
  const mounted = useMounted()

  return mounted ? (
    <div className="mb-[24px] w-[750px] pt-[50px]">
      {/* 内容区域 */}
      <div className="flex flex-col items-center px-[32px] pt-[96px] text-center">
        <Image src={successIcon} className="h-[160px] w-[160px]" />

        <span className="mt-[64px] text-[32px] font-bold text-black30">
          申请成功
        </span>

        <span className="mt-[24px] text-[28px] text-black30">
          您的样品订单已上传，商家审核大概7天，请耐心等待！
        </span>
      </div>
    </div>
  ) : null
}

export default Inner
