import { Field, FieldInstance } from 'react-vant'
import clearIcon from '@/../public/images/budget_campaign/fulfilment_submit/clear.png'
import { Image } from '@/components/Image'
import { useRef, useState } from 'react'

interface FormItemProps {
  name: string
  errorMessage: string
  onChange?: (value: string) => void
  readOnly?: boolean
}

export const FormItem = ({
  name,
  errorMessage,
  onChange,
  readOnly = false
}: FormItemProps) => {
  const fieldRef = useRef<FieldInstance>(null)

  const [theValue, setTheValue] = useState('')
  const [isFocused, setIsFocused] = useState(false)

  return (
    <div>
      <div className="flex h-[60px] w-[654px] items-center rounded-[30px] bg-background pr-[24px] text-gray6E">
        <Field
          ref={fieldRef}
          name={name}
          border={false}
          style={{
            backgroundColor: 'transparent',
            // @ts-ignore
            '--rv-input-text-color': '#6E6E6E'
          }}
          onChange={text => {
            setTheValue(text)
            onChange?.(text)
          }}
          onFocus={() => {
            if (readOnly) {
              return
            }
            setIsFocused(true)
          }}
          readOnly={readOnly}
        />
        <div
          onClick={() => {
            fieldRef.current?.clear()
          }}
        >
          <Image src={clearIcon} className="h-[24px] w-[24px]" />
        </div>
      </div>
      <div className="mt-[16px] text-[24px] font-normal leading-[34px] text-[#FF3141]">
        {isFocused && !theValue && errorMessage}
      </div>
    </div>
  )
}
