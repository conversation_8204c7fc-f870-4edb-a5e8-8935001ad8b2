'use client'

import { useState } from 'react'
import { Form, Toast } from 'react-vant'
import wrongIcon from '@/../public/images/budget_campaign/fulfilment_submit/wrong.png'
import { Image } from '@/components/Image'
import { FormItem } from './components/FormItem'
import { BottomActionsBar, PrimaryButton } from '@/components/BottomActionsBar'

interface FormData {
  videoLink: string
  adsCode: string
}

export default function FulfilmentSubmitInner() {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const onFinish = async (values: FormData) => {
    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      Toast('提交成功')
    } catch (error) {
      Toast('提交失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="relative mx-auto min-h-[100vh] w-[750px] overflow-hidden bg-[#F5F5F5]">
      {/* 主要内容区域 */}
      <div className="pt-[26px]">
        {/* 不合格标题 */}
        <div className="mb-[20px] px-[26px]">
          <h2 className="text-[30px] font-bold leading-[36px] text-[#020202]">
            履约内容#1（不合格）
          </h2>
        </div>

        {/* 错误信息卡片 */}
        <div className="mx-[26px] mb-[40px] rounded-[12px] bg-white p-[24px]">
          {/* 卡片标题 */}
          <div className="mb-[28px]">
            <h3 className="text-[28px] font-bold leading-[34px] text-[#303030]">
              视频优化后重新上传
            </h3>
          </div>

          {/* 错误原因列表 */}
          <div className="space-y-[28px]">
            <div className="flex items-center">
              <Image src={wrongIcon} className="mr-[22px] h-[32px] w-[32px]" />
              <span className="text-[26px] font-normal leading-[36px] text-[#6E6E6E]">
                视频时长至少30秒
              </span>
            </div>

            <div className="flex items-center">
              <Image src={wrongIcon} className="mr-[22px] h-[32px] w-[32px]" />
              <span className="text-[26px] font-normal leading-[36px] text-[#6E6E6E]">
                视频未挂Youpik商品链接
              </span>
            </div>

            <div className="flex items-start">
              <Image src={wrongIcon} className="mr-[22px] h-[32px] w-[32px]" />
              <span className="text-[26px] font-normal leading-[36px] text-[#6E6E6E]">
                其他：运营填写返回理由不超过二十个字
              </span>
            </div>
          </div>
        </div>

        {/* 表单区域 */}
        <div className="mx-[26px] rounded-[12px] bg-white px-[24px] py-[20px]">
          <Form onFinish={onFinish}>
            {/* 履约方式 */}
            <div className="mb-[32px]">
              <div className="mb-[16px] flex items-center justify-between">
                <div className="flex items-center">
                  <span className="mr-[8px] text-[28px] font-normal text-[#FF3141]">
                    *
                  </span>
                  <span className="text-[28px] font-normal leading-[40px] text-[#303030]">
                    履约方式
                  </span>
                </div>
                <div className="flex items-center">
                  <span className="mr-[8px] text-[26px] font-normal text-[#6E6E6E]">
                    视频
                  </span>
                </div>
              </div>
            </div>

            {/* TikTok视频链接 */}
            <div className="mb-[32px]">
              <div className="mb-[16px] flex items-center">
                <span className="mr-[8px] text-[28px] font-normal text-[#FF3141]">
                  *
                </span>
                <span className="text-[28px] font-medium leading-[32px] text-[#303030]">
                  TikTok视频链接
                </span>
              </div>

              <FormItem name="videoLink" errorMessage="请输入视频链接" />
            </div>

            {/* TikTok视频Ads Code */}
            <div className="mb-[32px]">
              <div className="mb-[16px] flex items-center">
                <span className="mr-[8px] text-[28px] font-normal text-[#FF3141]">
                  *
                </span>
                <span className="text-[28px] font-medium leading-[32px] text-[#303030]">
                  TikTok视频Ads Code
                </span>
              </div>

              <FormItem name="adsCode" errorMessage="请输入Ads Code" />

              <div className="mt-[16px] text-right">
                <span className="cursor-pointer text-[24px] font-normal leading-[34px] text-[#FE6D45]">
                  如何获取Ads Code？
                </span>
              </div>
            </div>
          </Form>
        </div>
      </div>

      {/* 底部固定区域 */}

      <BottomActionsBar>
        <PrimaryButton text="再次提交审核" onClick={() => {}} />
      </BottomActionsBar>
    </div>
  )
}
