'use client'

import React, { useEffect, useState } from 'react'
import <PERSON>Header from './components/StatusHeader'
import FulfillmentProgress from './components/FulfillmentProgress'
import FulfillmentProcess from './components/FulfillmentProcess'
import LogisticsTracking from './components/LogisticsTracking'
import ProductInfo from './components/ProductInfo'
import CreatorInfo from './components/CreatorInfo'
import AddressInfo from './components/AddressInfo'
import BottomActions from './components/BottomActions'
import PaymentInfo from './components/PaymentInfo'
import { useMounted } from '@/lib/hooks/useMounted'
import { useGrayBody } from '@/lib/hooks/useGrayBody'
import {
  OrderInfoVo,
  getOrderInfoByOrderNo
} from '@/app/api/api-uchoice/order/user/getOrderInfoByOrderNo/request'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import BankList from '@/components/BankList'
import { Bank } from '@/app/api/api-base/bank/list/request'

const OrderInner = () => {
  const mounted = useMounted()
  const routeParams = useRouteParams<{
    orderNo: string
  }>()

  useGrayBody()

  const [orderInfo, setOrderInfo] = useState<OrderInfoVo | null>(null)

  const [showBankList, setShowBankList] = useState(true)
  const [selectedBank, setSelectedBank] = useState<Bank | null>(null)

  const handleSelectBank = (bank: Bank) => {
    setSelectedBank(bank)
    console.log('选择的银行:', bank)
  }

  useEffect(() => {
    const fetchOrderInfo = async () => {
      const res = await getOrderInfoByOrderNo(routeParams.orderNo)
      if (res.code === 200) {
        setOrderInfo(res.result)
      }
    }
    fetchOrderInfo()
  }, [])

  return mounted ? (
    <div className="w-[750px] bg-background pt-[1px]">
      {/* 待履约状态 */}
      <StatusHeader />

      {/* 履约信息进度 */}
      <FulfillmentProgress />

      {/* 收款信息 */}
      <PaymentInfo />

      {/* 商单履约流程 */}
      <FulfillmentProcess />

      {/* 物流追踪 */}
      <LogisticsTracking orderInfo={orderInfo} />

      {/* 商品信息 */}
      <ProductInfo orderInfo={orderInfo} />

      {/* 达人账号 */}
      <CreatorInfo orderInfo={orderInfo} />

      {/* 收件地址 */}
      <AddressInfo orderInfo={orderInfo} />

      {/* 底部操作区 */}
      <BottomActions />

      <BankList
        visible={showBankList}
        onClose={() => setShowBankList(false)}
        onSelectBank={handleSelectBank}
        selectedBankId={selectedBank?.id}
      />
    </div>
  ) : null
}

export default OrderInner
