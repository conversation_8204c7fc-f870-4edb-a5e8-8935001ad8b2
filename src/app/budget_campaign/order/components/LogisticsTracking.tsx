'use client'

import React from 'react'
import { useRouter } from '@/lib/hooks/useRouter'
import arrowRightIcon from '@/../public/images/budget_campaign/order/arrow_right.png'
import copyIcon from '@/../public/images/budget_campaign/order/copy.png'
import { Image } from '@/components/Image'
import { OrderInfoVo } from '@/app/api/api-uchoice/order/user/getOrderInfoByOrderNo/request'
import { copyText } from '@/lib/client/utils'
import { WebViewToPages, WebviewEvents } from '@/lib/client/webview/events'
import { webview } from '@/lib/client/webview'

interface LogisticsTrackingProps {
  orderInfo: OrderInfoVo | null
}

const LogisticsTracking: React.FC<LogisticsTrackingProps> = ({ orderInfo }) => {
  const router = useRouter()

  if (!orderInfo || !orderInfo.expressDeliveryVo) {
    return null
  }

  const { expressDeliveryVo } = orderInfo

  // 判断是否显示物流信息
  const visible =
    expressDeliveryVo.trackingNumber || expressDeliveryVo.trackingNumberAdd

  if (!visible) {
    return null
  }

  // 获取快递公司
  const getCompany = () => {
    if (expressDeliveryVo.courierCompany) {
      return expressDeliveryVo.courierCompany
    } else if (expressDeliveryVo.courierCompanyAdd) {
      return expressDeliveryVo.courierCompanyAdd
    }
    return '-'
  }

  // 获取快递单号（优先使用增补的单号）
  const getTrackingNumber = () => {
    return (
      expressDeliveryVo.trackingNumberAdd ||
      expressDeliveryVo.trackingNumber ||
      ''
    )
  }

  // 判断是否为补发
  const isReShip = expressDeliveryVo.isLogisticsAdded !== false

  const company = getCompany()
  const trackingNumber = getTrackingNumber()

  // 处理点击查看详情
  const handleViewDetails = () => {
    // 跳转到物流详情页面
    webview?.send(WebviewEvents.toPage, {
      name: WebViewToPages.sampleOrderLogistics,
      params: {
        orderNo: orderInfo.orderNo
      }
    })
  }

  // 处理复制快递单号
  const handleCopyTrackingNumber = () => {
    if (trackingNumber) {
      copyText(trackingNumber)
    }
  }

  return (
    <div className="mx-[24px] mb-[20px]" onClick={handleViewDetails}>
      <div className="rounded-[8px] bg-white p-[24px]">
        {/* 标题和查看详情 */}
        <div className="mb-[32px] flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-[32px] font-bold text-black02">物流追踪</span>
            {isReShip && (
              <div className="ml-[12px] border border-primary px-[8px] text-[24px] text-primary">
                商家补发
              </div>
            )}
          </div>
          <div className="flex cursor-pointer items-center">
            <Image src={arrowRightIcon} className="h-[24px] w-[16px]" />
          </div>
        </div>

        {/* 物流承运商 */}
        <div className="mb-[30px]">
          <div className="mb-[4px] text-[28px] text-black02">物流承运商</div>
          <div className="text-[28px] text-black02">
            {company.length === 0 ? '-' : company}
          </div>
        </div>

        {/* 物流单号 */}
        <div>
          <div className="mb-[4px] text-[28px] text-black02">物流单号</div>
          <div className="flex items-center justify-between">
            <div className="flex-1 overflow-hidden text-ellipsis text-[28px] text-black02">
              {trackingNumber}
            </div>
            {trackingNumber.length > 0 && (
              <div className="ml-[12px]">
                <button
                  onClick={handleCopyTrackingNumber}
                  className="flex h-[32px] w-[32px] items-center justify-center"
                >
                  <Image
                    src={copyIcon}
                    className="h-[24px] w-[24px] cursor-pointer"
                  />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default LogisticsTracking
