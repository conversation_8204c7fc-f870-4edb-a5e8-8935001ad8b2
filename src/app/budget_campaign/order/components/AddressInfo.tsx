import React from 'react'
import { OrderInfoVo } from '@/app/api/api-uchoice/order/user/getOrderInfoByOrderNo/request'
import { isRegionVN, isLanguageEN } from '@/lib/utils'

interface AddressInfoProps {
  orderInfo: OrderInfoVo | null
}

const AddressInfo: React.FC<AddressInfoProps> = ({ orderInfo }) => {
  // 如果没有订单信息或者没有地址信息，则不显示组件
  if (!orderInfo || !orderInfo.memberAddress) {
    return null
  }

  const { memberAddress } = orderInfo

  // 构建联系信息字符串
  const contactInfo = `${memberAddress.fullName} (+${
    isRegionVN() ? '84' : '66'
  }) ${memberAddress.mobile}`

  // 构建地址字符串
  const getLocalizedName = (
    nameEn: string | undefined,
    localName: string | undefined
  ) => {
    return isLanguageEN() ? nameEn : localName
  }

  const addressParts = [
    memberAddress.addressDetail,
    memberAddress.postCode,
    getLocalizedName(memberAddress.districtNameEn, memberAddress.districtName),
    getLocalizedName(memberAddress.cityNameEn, memberAddress.cityName),
    getLocalizedName(memberAddress.provinceNameEn, memberAddress.provinceName)
  ].filter(Boolean) // 过滤掉空值

  const fullAddress = addressParts.join(', ')

  return (
    <div className="mx-[24px] mb-[20px]">
      <div className="rounded-[8px] bg-white p-[24px]">
        {/* 标题 */}
        <div className="mb-[32px] text-[32px] font-bold text-black02">
          收件地址
        </div>

        {/* 收件人信息 */}
        <div className="mb-[16px]">
          <div className="text-[26px] text-black30">{contactInfo}</div>
        </div>

        {/* 收件地址 */}
        <div>
          <div className="text-[26px] text-black30">{fullAddress}</div>
        </div>
      </div>
    </div>
  )
}

export default AddressInfo
