import React, { useState } from 'react'
import nodePreIcon from '@/../public/images/budget_campaign/order/node_pre.png'
import nodeNextIcon from '@/../public/images/budget_campaign/order/node_next.png'
import nodeCurrentIcon from '@/../public/images/budget_campaign/order/node_current.png'
import arrowDownIcon from '@/../public/images/budget_campaign/order/arrow_down.png'
import { Image } from '@/components/Image'

interface ProcessStep {
  status: 'completed' | 'current' | 'pending'
  title: string
}

interface FulfillmentProcessProps {
  steps?: ProcessStep[]
}

const FulfillmentProcess: React.FC<FulfillmentProcessProps> = ({
  steps = [
    { status: 'completed', title: '品牌商寄送样品' },
    { status: 'current', title: '达人发布履约视频+上传Ads Code' },
    { status: 'pending', title: '平台审核履约内容' },
    { status: 'pending', title: '达人上传收款信息' },
    { status: 'pending', title: 'Youpik打款' }
  ]
}) => {
  const [isExpanded, setIsExpanded] = useState(false)

  // 渲染步骤图标
  const renderStepIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Image
            src={nodePreIcon}
            alt="已完成"
            width={36}
            height={36}
            className="h-[36px] w-[36px]"
          />
        )
      case 'current':
        return (
          <Image
            src={nodeCurrentIcon}
            alt="进行中"
            width={36}
            height={36}
            className="h-[36px] w-[36px]"
          />
        )
      case 'pending':
      default:
        return (
          <Image
            src={nodeNextIcon}
            alt="待完成"
            width={36}
            height={36}
            className="h-[36px] w-[36px]"
          />
        )
    }
  }

  // 渲染连接线
  const renderConnectingLine = (
    currentIndex: number,
    currentStatus: string,
    nextStatus?: string
  ) => {
    if (currentIndex === steps.length - 1) return null

    // 连接线颜色规则：
    // 如果当前是已完成状态且下一个不是待完成，连接线是绿色
    // 如果当前是进行中状态或下一个是待完成状态，连接线是灰色
    let lineColor = '#B6B4B3' // 默认灰色
    if (currentStatus === 'completed' && nextStatus !== 'pending') {
      lineColor = '#21B26A' // 绿色
    }

    return (
      <div
        className="ml-[18px] h-[86px] w-[2px]"
        style={{ backgroundColor: lineColor }}
      ></div>
    )
  }

  return (
    <div className="mx-[24px] mb-[20px]">
      <div className="rounded-[8px] bg-white p-[24px]">
        {/* 标题 */}
        <div className="mb-[32px] text-[32px] font-bold text-black02">
          商单履约流程
        </div>

        {/* 流程步骤 */}
        <div className="relative">
          {steps.map((step, index) => (
            <React.Fragment key={index}>
              {/* 步骤项 */}
              <div className="flex items-center">
                {renderStepIcon(step.status)}
                <span
                  className={`ml-[20px] text-[26px] ${
                    step.status === 'pending' ? 'text-gray9A' : 'text-black30'
                  }`}
                >
                  {step.title}
                </span>
              </div>

              {/* 连接线 */}
              {renderConnectingLine(
                index,
                step.status,
                steps[index + 1]?.status
              )}
            </React.Fragment>
          ))}
        </div>

        {/* 展开按钮 */}
        <div
          className="flex cursor-pointer items-center justify-center pt-[24px]"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <span className="mr-[8px] text-[24px] text-gray8A">
            {isExpanded ? '收起' : '展开'}
          </span>
          <div
            className={`transition-transform${isExpanded ? 'rotate-180' : ''}`}
          >
            <Image src={arrowDownIcon} className="h-[14px] w-[20px]" />
          </div>
        </div>
      </div>
    </div>
  )
}

export default FulfillmentProcess
