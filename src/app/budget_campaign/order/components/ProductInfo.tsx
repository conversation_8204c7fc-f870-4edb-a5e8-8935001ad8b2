'use client'

import React, { useState } from 'react'
import { useRouter } from '@/lib/hooks/useRouter'
import { Image } from '@/components/Image'
import { OrderInfoVo } from '@/app/api/api-uchoice/order/user/getOrderInfoByOrderNo/request'
import { formatPrice, formatTime } from '@/lib/format'
import { copyText } from '@/lib/client/utils'
import { addToShowCase, checkAuth } from '@/lib/actions'
import copyIcon from '@/../public/images/budget_campaign/order/copy.png'
import arrowDownIcon from '@/../public/images/budget_campaign/order/arrow_down.png'
import arrowUpIcon from '@/../public/images/budget_campaign/order/arrow_up.png'

interface ListInfoProps {
  label: string
  right: React.ReactNode
}

const ListInfo: React.FC<ListInfoProps> = ({ label, right }) => {
  return (
    <div className="flex items-center justify-between pb-[28px]">
      <span className="text-[26px] text-black30">{label}</span>
      <div className="text-[26px] text-gray6E">
        {typeof right === 'string' ? <span>{right}</span> : right}
      </div>
    </div>
  )
}

interface ProductInfoProps {
  orderInfo: OrderInfoVo | null
}

const ProductInfo: React.FC<ProductInfoProps> = ({ orderInfo }) => {
  const router = useRouter()
  const [isExpand, setIsExpand] = useState(false)

  if (!orderInfo || !orderInfo.itemInfos || orderInfo.itemInfos.length === 0) {
    return null
  }

  const firstItem = orderInfo.itemInfos[0]

  // 处理商品点击
  const handleProductClick = () => {
    router.push(`/product/${firstItem.itemId}`)
  }

  // 处理添加橱窗
  const handleAddShowcase = async () => {
    checkAuth(() => {
      addToShowCase(
        {
          id: firstItem.itemId,
          link: null // 如果有商品链接，可以传入
        },
        'order_detail'
      )
    })
  }

  // 处理复制订单号
  const handleCopyOrderNo = () => {
    copyText(orderInfo.orderNo.toString())
  }

  // 格式化日期时间
  const formatDateTime = (timestamp?: number, defaultValue: string = '-') => {
    if (!timestamp) return defaultValue
    return formatTime(timestamp, 'DD/MM/YYYY')
  }

  return (
    <div className="mx-[24px] mb-[20px]">
      <div className="rounded-[8px] bg-white p-[24px]">
        {/* 商品信息区域 */}
        <div className="cursor-pointer" onClick={handleProductClick}>
          <div className="flex">
            {/* 商品图片 */}
            <div className="mr-[20px]">
              <Image
                src={firstItem.itemUrl}
                className="h-[176px] w-[176px] rounded-[4px] object-cover"
                withPlaceholder={true}
              />
            </div>

            {/* 商品信息 */}
            <div className="flex flex-1 flex-col justify-between">
              <div>
                {/* 商品标题 */}
                <div
                  className="mb-[12px] overflow-hidden text-[28px] text-black30"
                  style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}
                >
                  {firstItem.itemTitle}
                </div>

                {/* 价格 */}
                <div className="text-[26px] text-black30">
                  Price: {formatPrice(firstItem.unitFee || 0, true)}
                </div>
              </div>

              <div className="mt-[10px]">
                {/* 规格和数量 */}
                <div className="flex items-center justify-between">
                  <span className="mr-[12px] text-[24px] text-[#7C7777]">
                    {firstItem.specifications} x{firstItem.count}
                  </span>

                  {/* 添加橱窗按钮 */}
                  <button
                    onClick={e => {
                      e.stopPropagation()
                      handleAddShowcase()
                    }}
                    className="rounded-[2px] bg-primary px-[16px] py-[8px] text-[24px] text-white"
                  >
                    添加橱窗
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 订单详情信息 */}
        <div className="pt-[24px]">
          <ListInfo
            label="每件赚"
            right={
              <span className="font-bold text-[#FE6D45]">
                {formatPrice(firstItem.commissionFee || 0, true)}
              </span>
            }
          />

          <ListInfo
            label="佣金率"
            right={
              <span className="text-[#FE6D45]">{orderInfo.commissionRate}</span>
            }
          />

          <ListInfo
            label="订单"
            right={
              <div className="flex items-center">
                <span className="mr-[12px] text-[26px] text-gray6E">
                  {orderInfo.orderNo}
                </span>
                <button
                  onClick={handleCopyOrderNo}
                  className="flex h-[26px] w-[26px] items-center justify-center"
                >
                  <Image
                    src={copyIcon}
                    className="h-[26px] w-[26px] cursor-pointer"
                  />
                </button>
              </div>
            }
          />

          {/* 展开的详细信息 */}
          {isExpand && (
            <div>
              <ListInfo
                label="申请时间"
                right={formatDateTime(orderInfo.createTime)}
              />

              <ListInfo
                label="平台审核时间"
                right={formatDateTime(orderInfo.reviewTime)}
              />

              <ListInfo
                label="商家审核时间"
                right={formatDateTime(orderInfo.supplierReviewTime)}
              />

              <ListInfo
                label="商家发货时间"
                right={formatDateTime(orderInfo.deliverTime)}
              />
            </div>
          )}
        </div>

        {/* 展开/收缩按钮 */}
        <div
          className="flex cursor-pointer items-center justify-center"
          onClick={() => setIsExpand(!isExpand)}
        >
          <span className="mr-[8px] text-[24px] text-gray8A">
            {isExpand ? '收起' : '展开'}
          </span>
          <Image
            src={isExpand ? arrowUpIcon : arrowDownIcon}
            className="h-[14px] w-[20px]"
          />
        </div>
      </div>
    </div>
  )
}

export default ProductInfo
