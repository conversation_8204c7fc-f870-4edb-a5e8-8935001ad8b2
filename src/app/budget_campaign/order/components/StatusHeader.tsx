import React from 'react'
import statusDone from '@/../public/images/budget_campaign/order/status_done.png'
import statusError from '@/../public/images/budget_campaign/order/status_error.png'
import statusPending from '@/../public/images/budget_campaign/order/status_pending.png'
import { Image } from '@/components/Image'

const StatusHeader = () => {
  return (
    <div className="mx-[24px] mb-[20px] mt-[22px]">
      <div className="rounded-[8px] bg-white p-[24px]">
        <div className="flex items-start justify-between">
          {/* 文字内容 */}
          <div>
            <div className="mb-[8px] text-[32px] font-bold text-gray8A">
              待履约
            </div>
            <div className="text-[28px] text-black30">履约内容审核中</div>
          </div>

          {/* 等待状态图标 */}
          <div>
            <Image src={statusPending} className="h-[96px] w-[96px]" />
          </div>
        </div>
      </div>
    </div>
  )
}

export default StatusHeader
