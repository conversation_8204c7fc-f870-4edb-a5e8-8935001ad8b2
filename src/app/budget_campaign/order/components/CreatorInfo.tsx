'use client'

import React from 'react'
import { useRouter } from '@/lib/hooks/useRouter'
import { formatCount } from '@/lib/format'
import { OrderInfoVo } from '@/app/api/api-uchoice/order/user/getOrderInfoByOrderNo/request'
import arrowRightIcon from '@/../public/images/budget_campaign/order/arrow_right.png'
import { Image } from '@/components/Image'

interface CreatorInfoProps {
  orderInfo: OrderInfoVo | null
}

const newVersionTime = new Date(2024, 8, 5) // 注意：月份是从0开始的，所以8代表9月

const CreatorInfo: React.FC<CreatorInfoProps> = ({ orderInfo }) => {
  const router = useRouter()

  if (!orderInfo) {
    return null
  }

  // 判断是否可以点击查看销售数据证明
  const isDisabled =
    !orderInfo.salesDataVoucher ||
    new Date(orderInfo.createTime).getTime() < newVersionTime.getTime()

  // 处理点击事件
  const handleClick = () => {
    if (isDisabled) return

    // 跳转到销售证明页面
    // 注意：这里的路由路径需要根据实际项目调整
    router.push(
      `/sales-prove?unionId=${orderInfo.unionId}&nickname=${encodeURIComponent(
        orderInfo.nickname || ''
      )}&salesDataVoucher=${orderInfo.salesDataVoucher}&minAmount=${
        orderInfo.minAmount || 0
      }&maxAmount=${orderInfo.maxAmount || 0}&tier=${
        orderInfo.tier || 0
      }&updateTime=${orderInfo.salesDataVoucherTime || ''}`
    )
  }

  // 根据履约方式显示标签
  const renderPerformanceTags = () => {
    const tags: React.ReactElement[] = []

    // 如果包含直播 (2-直播 3-直播&视频)
    if ([2, 3].includes(orderInfo.performanceWay)) {
      tags.push(
        <div
          key="live"
          className="mr-[12px] rounded-[4px] bg-[#FE2C55] px-[8px] py-[4px] text-[22px] text-white"
        >
          直播
        </div>
      )
    }

    // 如果包含视频 (1-视频 3-直播&视频)
    if ([1, 3].includes(orderInfo.performanceWay)) {
      tags.push(
        <div
          key="video"
          className="rounded-[4px] bg-[#21B26A] px-[8px] py-[4px] text-[22px] text-white"
        >
          视频
        </div>
      )
    }

    return tags
  }

  // 显示销售额范围
  const renderSalesRange = () => {
    if (orderInfo.systemCrawlerGmv) {
      return `销售额范围 ${formatCount(orderInfo.systemCrawlerGmv)}`
    } else if (orderInfo.minAmount && orderInfo.minAmount > 0) {
      const maxText =
        orderInfo.maxAmount === 0
          ? '及以上'
          : ` ~ ${formatCount(orderInfo.maxAmount)}`
      return `销售额范围 ${formatCount(orderInfo.minAmount)}${maxText}`
    }
    return null
  }

  return (
    <div className="mx-[24px] mb-[20px]">
      <div className={`rounded-[8px] bg-white p-[24px]`} onClick={handleClick}>
        {/* 标题和箭头 */}
        <div className="mb-[32px] flex items-center justify-between">
          <span className="text-[32px] font-bold text-black02">达人账号</span>
          {/* TODO：要能点击吗？ */}
          {!isDisabled && (
            <Image src={arrowRightIcon} className="h-[24px] w-[16px]" />
          )}
        </div>

        {/* 达人信息 */}
        <div>
          <div className="flex">
            {/* 头像 */}
            <div className="mr-[16px]">
              <Image
                src={orderInfo.avatar}
                className="h-[104px] w-[104px] rounded-full object-cover"
              />
            </div>

            {/* 达人详情 */}
            <div className="flex-1">
              {/* 姓名和标签 */}
              <div className="mb-[8px] flex items-center">
                <span className="mr-[12px] text-[28px] text-black02">
                  {orderInfo.nickname || ''}
                </span>
                {renderPerformanceTags()}
              </div>

              {/* 粉丝数 */}
              <div className="mb-[8px] text-[28px] text-black02">
                粉丝数 {orderInfo.numberOfFans}
              </div>

              {/* 销售额范围 */}
              {renderSalesRange() && (
                <div className="text-[28px] text-black02">
                  {renderSalesRange()}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreatorInfo
