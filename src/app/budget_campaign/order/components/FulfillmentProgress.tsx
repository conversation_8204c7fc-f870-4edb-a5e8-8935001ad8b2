import React from 'react'
import arrowRightIcon from '@/../public/images/budget_campaign/order/arrow_right.png'
import { Image } from '@/components/Image'
import uploadBtnBg from '@/../public/images/budget_campaign/order/upload_btn_bg.png'

const FulfillmentProgress = () => {
  return (
    <div className="mx-[24px] mb-[20px]">
      <div className="rounded-[8px] bg-white p-[24px]">
        {/* 标题 */}
        <div className="mb-[32px] text-[32px] font-bold text-black02">
          履约信息（2/3）
        </div>

        {/* 履约内容#1 */}
        <div className="mb-[16px] flex h-[76px] items-center justify-between rounded-[8px] bg-background px-[24px]">
          <div className="flex items-center">
            <span className="text-[26px] text-black30">履约内容#1</span>
            <span className="ml-[48px] text-[26px] text-[#FF8D13]">审核中</span>
          </div>
          <div className="flex items-center">
            <span className="mr-[16px] text-[26px] text-black30">查看详情</span>
            <div className="h-[20px] w-[10px]">
              <Image src={arrowRightIcon} className="h-[24px] w-[16px]" />
            </div>
          </div>
        </div>

        {/* 履约内容#2 */}
        <div className="flex h-[76px] items-center justify-between rounded-[8px] bg-background px-[24px]">
          <div className="flex items-center">
            <span className="text-[26px] text-black30">履约内容#2</span>
            <span className="ml-[48px] text-[26px] text-[#FF8D13]">审核中</span>
          </div>
          <div className="flex items-center">
            <span className="mr-[16px] text-[26px] text-black30">查看详情</span>
            <div className="h-[20px] w-[10px]">
              <Image src={arrowRightIcon} className="h-[24px] w-[16px]" />
            </div>
          </div>
        </div>

        {/* 上传提示区域 */}
        <div className="relative mt-[16px] flex h-[88px] items-center justify-center rounded-[8px]">
          <Image
            src={uploadBtnBg}
            className="absolute h-full w-full rounded-[8px]"
          />
          <span className="relative z-10 text-[28px] text-primary">
            立即上传 &gt;
          </span>
        </div>
      </div>
    </div>
  )
}

export default FulfillmentProgress
