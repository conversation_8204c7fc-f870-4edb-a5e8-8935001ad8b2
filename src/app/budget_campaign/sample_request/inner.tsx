'use client'

import { useRouter } from 'next/navigation'
import React, { useState, useEffect } from 'react'

// 导入图片资源
import warningIcon from '@/../public/images/budget_campaign/sample_request/warning.png'
import locationIcon from '@/../public/images/budget_campaign/sample_request/address.png'
import cardBgIcon from '@/../public/images/budget_campaign/sample_request/card_bg.png'
import lineIcon from '@/../public/images/budget_campaign/sample_request/line.png'
import { useGrayBody } from '@/lib/hooks/useGrayBody'
import { Image } from '@/components/Image'
import { BottomActionsBar, PrimaryButton } from '@/components/BottomActionsBar'
import { Address } from './components/Address'
import {
  UChoiceMemberAddressVo,
  getAddressList
} from '@/app/api/api-uchoice/uChoice/member/address/getAddressList/request'
import { Loading } from 'react-vant'

export default function SampleRequestPage() {
  const router = useRouter()
  const [address, setAddress] = useState<
    UChoiceMemberAddressVo | null | 'loading'
  >('loading')

  useGrayBody()

  useEffect(() => {
    const fetchAddressList = async () => {
      const res = await getAddressList()
      if (res.code === 200) {
        setAddress(res.result?.find(item => item.isDefault) || null)
      }
    }
    fetchAddressList()
  }, [])

  return (
    <div className="flex w-[750px] flex-col items-center bg-background pt-[24px]">
      {/* 警告提示 */}
      <div className="flex h-[52px] w-[702px] items-center rounded-[8px] bg-[rgba(255,141,19,0.08)] pl-[16px]">
        <Image src={warningIcon} alt="warning" className="h-[24px] w-[24px]" />
        <span className="ml-[8px] text-[24px] text-[#FF8D13]">
          该账号粉丝数量≤2000，不可申样
        </span>
      </div>

      {/* 达人信息 */}
      <div
        className="mx-auto mt-[16px] h-[200px] w-[702px] rounded-[8px] bg-white p-[30px]"
        style={{
          backgroundImage: `url(${cardBgIcon.src})`,
          backgroundSize: 'cover'
        }}
      >
        <div className="mb-[12px] text-[28px] text-black">
          申样达人姓名：Kate
        </div>
        <div className="mb-[12px] text-[28px] text-black">粉丝数：2000</div>
        <div className="text-[28px] text-black">可履约方式：视频</div>
      </div>

      <div className="mt-[24px] w-[702px] rounded-[8px] bg-white">
        <Image src={lineIcon} alt="line" className="h-[10px] w-[702px]" />

        {address === 'loading' ? (
          <div className="flex h-[122px] w-full items-center justify-center">
            <Loading />
          </div>
        ) : (
          <Address
            address={address}
            onSelectedAddress={address => {
              setAddress(address)
            }}
          />
        )}

        {/* 分隔线 */}
        <div className="mx-auto h-[2px] w-full bg-background"></div>

        {/* 商品信息 */}
        <div className="mx-auto mt-[24px] flex px-[24px]">
          <div className="h-[200px] w-[200px] overflow-hidden rounded-[8px] bg-white">
            <Image src={''} alt="商品图片" className="h-[200px] w-[200px]" />
          </div>
          <div className="ml-[20px] flex-1">
            <div className="line-clamp-2 text-[24px] leading-[34px] text-black">
              商品商品商品商品商品商品商品商品商品商品
            </div>

            <div className="mt-[16px] flex h-[48px] w-[170px] items-center justify-center rounded-[8px] bg-background">
              <span className="text-[24px] text-black">达人佣金</span>
              <span className="ml-[4px] text-[24px] font-semibold text-black">
                18%
              </span>
            </div>

            <div className="mt-[6px] font-bold text-primary">
              <span className="text-[28px]">฿889.00</span>
              <span className="mx-[4px] text-[28px]">～</span>
              <span className="text-[28px]">2999.00</span>
            </div>
          </div>
        </div>

        {/* 规格和数量 */}
        <div className="mx-auto mt-[24px] px-[24px] pb-[24px]">
          <div className="mb-[12px] flex justify-between">
            <div className="text-[24px] text-black">规格</div>
            <div className="text-[24px] text-gray6E">xxxxxxxxx</div>
          </div>
          <div className="flex justify-between">
            <div className="text-[24px] text-black">数量</div>
            <div className="text-[24px] text-gray6E">x1</div>
          </div>
        </div>
      </div>

      <BottomActionsBar>
        <PrimaryButton text="申请样品" onClick={() => {}} />
      </BottomActionsBar>
    </div>
  )
}
