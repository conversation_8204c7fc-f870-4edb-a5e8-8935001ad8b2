import React from 'react'
import locationIcon from '@/../public/images/budget_campaign/sample_request/address.png'
import addressEditIcon from '@/../public/images/budget_campaign/sample_request/address_edit.png'
import arrowMoreIcon from '@/../public/images/budget_campaign/sample_request/arrow_more.png'
import { Image } from '@/components/Image'
import { UChoiceMemberAddressVo } from '@/app/api/api-uchoice/uChoice/member/address/getAddressList/request'
import { isRegionVN } from '@/lib/utils'
import { webview } from '@/lib/client/webview'
import { WebViewToPages, WebviewEvents } from '@/lib/client/webview/events'
import { toast } from '@/lib/client/toast'

interface AddressProps {
  address?: UChoiceMemberAddressVo | null
  onSelectedAddress?: (address: UChoiceMemberAddressVo) => void
}

export const Address: React.FC<AddressProps> = ({
  address,
  onSelectedAddress
}) => {
  const handleAddressSelect = () => {
    window.onAddressSelected = (address: UChoiceMemberAddressVo) => {
      if (onSelectedAddress) {
        onSelectedAddress(address)
      }
    }

    webview?.send(WebviewEvents.toPage, {
      name: WebViewToPages.addressList,
      params: {
        addressId: address?.id
      }
    })
  }

  // 有地址时显示完整地址信息
  if (address && address.id) {
    return (
      <div className={`mx-auto w-[702px]`}>
        {/* 地址信息卡片 */}
        <div className="bg-white touch-opacity" onClick={handleAddressSelect}>
          <div className="flex items-center px-[24px] py-[32px]">
            {/* 地址图标 */}
            <div className="mr-[24px]">
              <Image src={locationIcon} className="h-[80px] w-[80px]" />
            </div>

            {/* 地址详细信息 */}
            <div className="flex-1">
              {/* 姓名和电话行 */}
              <div className="flex items-center">
                <div className="truncate text-[32px] font-bold text-black30">
                  {address.fullName}
                </div>
                <div className="mx-[16px] h-[28px] w-[2px] bg-grayD9"></div>
                <div className="text-[26px] text-gray6E">
                  (+{isRegionVN() ? '84' : '66'}){address.mobile}
                </div>
              </div>

              {/* 详细地址 */}
              <div className="mt-[20px] text-[30px] text-black30">
                {address.addressDetail}
              </div>
            </div>

            {/* 分隔线 */}
            <div className="ml-[28px] h-[40px] w-[2px] bg-grayD9"></div>

            {/* 编辑按钮 */}
            <div
              className="p-[28px] touch-opacity"
              onClick={e => {
                e.stopPropagation()
                handleAddressSelect()
              }}
            >
              <Image src={addressEditIcon} className="h-[32px] w-[32px]" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 无地址时显示创建地址提示
  return (
    <div className={`mx-auto w-[702px]`}>
      {/* 创建地址卡片 */}
      <div
        className="h-[122px] w-[702px] bg-white touch-opacity"
        onClick={handleAddressSelect}
      >
        <div className="flex h-full items-center justify-between px-[24px]">
          <div className="flex items-center">
            <div className="mr-[24px]">
              <Image src={locationIcon} className="h-[80px] w-[80px]" />
            </div>
            <div className="text-[30px] text-gray9A">创建收货地址</div>
          </div>
          <Image src={arrowMoreIcon} className="h-[24px] w-[16px]" />
        </div>
      </div>
    </div>
  )
}
