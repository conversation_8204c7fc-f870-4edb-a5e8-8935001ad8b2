'use client'

import React, { use, useEffect, useState } from 'react'
import { Image } from '@/components/Image'
import CreatorsBar from '@/components/CreatorsBar'
import { accountList } from '@/app/api/api-uchoice/invite/code/accountList'
import { TtInviteCodeAccountVo } from '@/app/api/api-uchoice/invite/code/accountList'
import { webview } from '@/lib/client/webview'
import { BottomSafeArea } from '@/components/BottomSafeArea'
import { useMounted } from '@/lib/hooks/useMounted'

const QuotationIntention = () => {
  const mounted = useMounted()
  const [quotationPrice, setQuotationPrice] = useState('3000')
  const [creators, setCreators] = React.useState<TtInviteCodeAccountVo[]>([])
  const [selectCreator, setSelectCreator] =
    React.useState<TtInviteCodeAccountVo>()

  useEffect(() => {
    fetchAccountList()
  }, [])

  const fetchAccountList = async () => {
    const { code, result } = await accountList()
    if (code == 200) {
      setCreators(result)

      if (result.length > 0) {
        setSelectCreator(result[0])
      }
    }
  }

  return mounted ? (
    <div className="mx-auto w-[750px] bg-white">
      <div className="px-[24px] pt-[24px]">
        <CreatorsBar
          creators={creators}
          selectedUnionId={selectCreator?.unionId}
          onSelect={unionId => {
            setSelectCreator(creators.find(item => item.unionId == unionId))
          }}
        ></CreatorsBar>
      </div>

      {/* 数据统计区域 */}
      <div className="px-[40px]">
        {/* 粉丝量 */}
        <div className="flex h-[88px] w-full items-center justify-between border-b border-[#F7F9FC] bg-white">
          <span className="text-[28px] font-normal text-black">粉丝量</span>
          <span className="text-[28px] font-normal text-gray6E">21K</span>
        </div>

        {/* 月销 */}
        <div className="flex h-[88px] w-full items-center justify-between border-b border-[#F7F9FC] bg-white">
          <span className="text-[28px] font-normal text-black">月销</span>
          <span className="text-[28px] font-normal text-gray6E">4.1M</span>
        </div>

        {/* 平均播放量 */}
        <div className="flex h-[88px] w-full items-center justify-between border-b border-[#F7F9FC] bg-white">
          <span className="text-[28px] font-normal text-black">平均播放量</span>
          <span className="text-[28px] font-normal text-gray6E">90.8K</span>
        </div>

        {/* 互动率 */}
        <div className="flex h-[88px] w-full items-center justify-between border-b border-[#F7F9FC] bg-white">
          <span className="text-[28px] font-normal text-black">互动率</span>
          <span className="text-[28px] font-normal text-gray6E">20.9%</span>
        </div>

        {/* 合作报价 */}
        <div className="flex h-[88px] w-full items-center justify-between bg-white">
          <span className="text-[28px] font-normal text-black">合作报价</span>
          <div className="flex items-center">
            <input
              type="text"
              value={quotationPrice}
              onChange={e => setQuotationPrice(e.target.value)}
              className="w-[120px] border-none bg-transparent text-right text-[28px] font-normal text-black outline-none"
            />
            <button className="ml-[16px] h-[32px] w-[32px]">
              <Image
                src="/images/budget_campaign/quotation_intention/edit.png"
                className="size-[32px]"
              />
            </button>
          </div>
        </div>
      </div>

      {/* 提示文字 */}
      <div className="mt-[46px] px-[40px]">
        <p className="text-[24px] font-normal leading-[34px] text-gray8A">
          (请输入您每条视频的报价,供品牌参考)
        </p>
      </div>

      {/* 底部保存按钮 */}
      <div className="fixed bottom-0 left-1/2 w-[750px] -translate-x-1/2 bg-white px-[24px]">
        <button className="h-[80px] w-full rounded-[8px] bg-primary text-[28px] font-bold text-white">
          保存
        </button>
        <BottomSafeArea />
      </div>
    </div>
  ) : null
}

export default QuotationIntention
