'use client'

import { useState } from 'react'
import { Image } from '@/components/Image'
import { BottomSafeArea } from '@/components/BottomSafeArea'

// 自定义Radio组件
const Radio = ({ checked }: { value: string; checked: boolean }) => {
  return (
    <div className="flex items-center">
      <Image
        src={
          checked
            ? '/images/budget_campaign/checkbox_on.png'
            : '/images/budget_campaign/checkbox_off.png'
        }
        className="h-[36px] w-[36px]"
      />
    </div>
  )
}

// 自定义RadioGroup组件
const RadioGroup = ({
  children
}: {
  value: string
  onChange: (value: string) => void
  children: React.ReactNode
}) => {
  return <div className="flex flex-col">{children}</div>
}

const PaymentTypePage = ({
  onConfirm
}: {
  onConfirm: (type: string) => void
}) => {
  // 打款类型选择
  const [paymentType, setPaymentType] = useState<string>('personal')

  // 表单确认提交
  const handleSubmit = () => {
    onConfirm(paymentType)
  }

  // 点击整行触发单选框切换
  const handleRowClick = (value: string) => {
    setPaymentType(value)
  }

  return (
    <div className="flex flex-col bg-white">
      {/* 选择打款类型 */}
      <div className="flex-1 p-[24px]">
        <div className="mb-[20px] text-[30px] font-bold text-black">
          打款类型
        </div>

        <RadioGroup value={paymentType} onChange={val => setPaymentType(val)}>
          <div
            className="flex h-[88px] items-center justify-between border-b border-[#F7F9FC] bg-white"
            onClick={() => handleRowClick('personal')}
          >
            <span className="text-[30px] text-[#303030]">个人收款</span>
            <Radio value="personal" checked={paymentType === 'personal'} />
          </div>
          <div
            className="flex h-[88px] items-center justify-between bg-white"
            onClick={() => handleRowClick('company')}
          >
            <span className="text-[30px] text-[#303030]">公司收款</span>
            <Radio value="company" checked={paymentType === 'company'} />
          </div>
        </RadioGroup>
      </div>

      {/* 底部按钮 */}
      <div className="fixed inset-x-0 bottom-0 bg-white p-[24px]">
        <button
          className="flex h-[80px] w-full items-center justify-center rounded-[4px] border-none bg-[#FE6D45] text-[28px] font-bold text-white"
          onClick={handleSubmit}
        >
          确定
        </button>
        <BottomSafeArea />
      </div>
    </div>
  )
}

// 详细的收款信息表单组件
const PersonalInfoForm = ({ onBack }: { onBack: () => void }) => {
  const [gender, setGender] = useState<string>('male')

  // 处理上一步返回
  const handleBack = () => {
    onBack()
  }

  // 点击整行触发单选框切换
  const handleGenderClick = (value: string) => {
    setGender(value)
  }

  // 表单提交
  const handleSubmit = () => {
    // 提交表单逻辑
    alert('表单提交成功')
  }

  return (
    <div className="flex min-h-screen flex-col bg-white pb-[192px]">
      {/* 审核不通过提示 */}
      <div className="flex flex-col items-center pt-[24px]">
        <div className="mb-[24px] flex w-[702px] items-center rounded-[12px] bg-[#FFF1F1] px-[32px] py-[44px]">
          <div className="flex items-center">
            <Image
              src="/images/budget_campaign/payment/error.png"
              className="mr-[20px] h-[72px] w-[72px]"
            />
            <div className="flex-1">
              <div className="mb-[8px] text-[28px] font-medium text-[#FF3141]">
                审核不通过
              </div>
              <div className="text-[24px] text-[#FF3141]">
                身份证号码与银行卡号姓名不一致，请修改后重新提交。
              </div>
            </div>
          </div>
        </div>
      </div>
      {/*  */}
      {/* 表单内容 */}
      <div className="px-[24px] pt-[24px]">
        <div className="mb-[40px]">
          <div className="mb-[20px] text-[30px] text-[#303030]">
            <span className="mr-[4px] text-[#FF3141]">*</span>联系电话
          </div>
          <input
            type="text"
            className="h-[80px] w-full border-0 border-b border-[#F7F9FC] p-0 text-[30px] text-[#303030] outline-none"
            placeholder="请输入联系电话"
            defaultValue="123456789"
          />
        </div>

        <div className="mb-[40px]">
          <div className="mb-[20px] text-[30px] text-[#303030]">
            <span className="mr-[4px] text-[#FF3141]">*</span>性别
          </div>
          <div className="flex flex-col">
            <RadioGroup value={gender} onChange={val => setGender(val)}>
              <div
                className="flex h-[64px] items-center justify-between"
                onClick={() => handleGenderClick('male')}
              >
                <span className="text-[28px] text-[#303030]">先生</span>
                <Radio value="male" checked={gender === 'male'} />
              </div>
              <div
                className="flex h-[64px] items-center justify-between"
                onClick={() => handleGenderClick('female')}
              >
                <span className="text-[28px] text-[#303030]">女士</span>
                <Radio value="female" checked={gender === 'female'} />
              </div>
              <div
                className="flex h-[64px] items-center justify-between"
                onClick={() => handleGenderClick('miss')}
              >
                <span className="text-[28px] text-[#303030]">小姐</span>
                <Radio value="miss" checked={gender === 'miss'} />
              </div>
            </RadioGroup>
          </div>
        </div>

        <div className="mb-[40px]">
          <div className="mb-[20px] text-[30px] text-[#303030]">
            <span className="mr-[4px] text-[#FF3141]">*</span>身份证姓名
          </div>
          <input
            type="text"
            className="h-[80px] w-full border-0 border-b border-[#F7F9FC] p-0 text-[30px] text-[#303030] outline-none"
            placeholder="请输入身份证姓名"
          />
        </div>

        <div className="mb-[40px]">
          <div className="mb-[20px] text-[30px] text-[#303030]">
            <span className="mr-[4px] text-[#FF3141]">*</span>身份证居住地址
          </div>
          <input
            type="text"
            className="h-[80px] w-full border-0 border-b border-[#F7F9FC] p-0 text-[30px] text-[#303030] outline-none"
            placeholder="请输入身份证居住地址"
          />
        </div>

        <div className="mb-[40px]">
          <div className="mb-[20px] text-[30px] text-[#303030]">
            <span className="mr-[4px] text-[#FF3141]">*</span>身份证号码
          </div>
          <input
            type="text"
            className="h-[80px] w-full border-0 border-b border-[#F7F9FC] p-0 text-[30px] text-[#303030] outline-none"
            placeholder="请输入身份证号码"
          />
        </div>

        <div className="mb-[40px]">
          <div className="mb-[20px] text-[30px] text-[#303030]">
            <span className="mr-[4px] text-[#FF3141]">*</span>收款银行
          </div>
          <div className="flex h-[80px] items-center justify-between border-b border-[#F7F9FC] text-[30px] text-[#303030]">
            <span>请选择银行</span>
            <Image
              src="/images/common/arrow_down.png"
              className="h-[28px] w-[28px]"
              width={28}
              height={28}
            />
          </div>
        </div>

        <div className="mb-[40px]">
          <div className="mb-[20px] text-[30px] text-[#303030]">
            <span className="mr-[4px] text-[#FF3141]">*</span>银行卡号
          </div>
          <input
            type="text"
            className="h-[80px] w-full border-0 border-b border-[#F7F9FC] p-0 text-[30px] text-[#303030] outline-none"
            placeholder="请输入银行卡号"
          />
        </div>

        <div className="mb-[40px]">
          <div className="mb-[20px] text-[30px] text-[#303030]">
            <span className="mr-[4px] text-[#FF3141]">*</span>身份证照片
          </div>
          <div className="mb-[20px] flex h-[160px] w-[160px] items-center justify-center bg-[#F5F5F5]">
            <div className="flex h-[48px] w-[48px] items-center justify-center">
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 5V19"
                  stroke="#999999"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M5 12H19"
                  stroke="#999999"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
              </svg>
            </div>
          </div>
        </div>

        <div className="mb-[40px]">
          <div className="mb-[20px] text-[30px] text-[#303030]">
            <span className="mr-[4px] text-[#FF3141]">*</span>银行卡照片
          </div>
          <div className="mb-[20px] flex h-[160px] w-[160px] items-center justify-center bg-[#F5F5F5]">
            <div className="flex h-[48px] w-[48px] items-center justify-center">
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 5V19"
                  stroke="#999999"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M5 12H19"
                  stroke="#999999"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
              </svg>
            </div>
          </div>
        </div>

        <div className="mb-[40px]">
          <div className="mb-[20px] text-[30px] text-[#303030]">
            <span className="mr-[4px] text-[#FF3141]">*</span>
            营业执照（最多上传3张照片）
          </div>
          <div className="mb-[20px] flex h-[160px] w-[160px] items-center justify-center bg-[#F5F5F5]">
            <div className="flex h-[48px] w-[48px] items-center justify-center">
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 5V19"
                  stroke="#999999"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M5 12H19"
                  stroke="#999999"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* 底部按钮组 */}
      <div className="fixed inset-x-0 bottom-0 flex flex-col bg-white">
        <div className="flex justify-between p-[24px] pb-0">
          <button
            className="flex h-[78px] w-[198px] items-center justify-center rounded-[4px] border border-[#FE6D45] bg-white text-[28px] font-bold text-[#FE6D45]"
            onClick={handleBack}
          >
            上一步
          </button>
          <button className="flex h-[78px] w-[198px] items-center justify-center rounded-[4px] border border-[#FE6D45] bg-white text-[28px] font-bold text-[#FE6D45]">
            取消
          </button>
          <button
            className="flex h-[80px] w-[262px] items-center justify-center rounded-[4px] border-none bg-[#FE6D45] text-[28px] font-bold text-white"
            onClick={handleSubmit}
          >
            确定
          </button>
        </div>
        <BottomSafeArea />
      </div>
    </div>
  )
}

// 默认导出组件，根据状态决定显示哪个页面
export default function PaymentPage() {
  const [showForm, setShowForm] = useState(false)
  const [paymentType, setPaymentType] = useState<string>('personal')

  const handleConfirm = (type: string) => {
    setPaymentType(type)
    setShowForm(true)
  }

  const handleBack = () => {
    setShowForm(false)
  }

  if (showForm) {
    return <PersonalInfoForm onBack={handleBack} />
  }

  return <PaymentTypePage onConfirm={handleConfirm} />
}
