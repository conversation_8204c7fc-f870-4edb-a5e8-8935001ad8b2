'use client'

import { FC, useState } from 'react'
import { Image } from '@/components/Image'
import { px2rem } from '@/lib/client/utils'
import { webview } from '@/lib/client/webview'
import { BottomSafeArea } from '@/components/BottomSafeArea'
import { CommercialOrderH5DetailVo } from '../../api/api-uchoice/h5/commercial/order/detail/request'
import { formatPrice } from '@/lib/format'
import { BottomActionsBar, PrimaryButton } from '@/components/BottomActionsBar'
import checkboxOnIcon from '@/../public/images/budget_campaign/checkbox_on.png'
import checkboxOffIcon from '@/../public/images/budget_campaign/checkbox_off.png'
import { acceptCommercialOrder } from '@/app/api/api-uchoice/h5/commercial/order/accept/request'
import { loading } from '@/lib/client/loading'
import { useRouter } from '@/lib/hooks/useRouter'
import { useRouteParams } from '@/lib/hooks/useRouteParams'

interface BottomActionsProps {
  toRefetch?: () => void
  commercialOrderDetail?: CommercialOrderH5DetailVo
}

const BottomActions: FC<BottomActionsProps> = ({
  commercialOrderDetail,
  toRefetch
}) => {
  const routeParams = useRouteParams<{
    inviteCode: string
  }>()
  const router = useRouter()
  const [isChecked, setIsChecked] = useState(false)

  // 检查是否已过期
  const isExpired = commercialOrderDetail?.registrationEndTime
    ? new Date(commercialOrderDetail.registrationEndTime) < new Date()
    : false

  // 检查是否已接单
  const isAccepted = commercialOrderDetail?.acceptOrderFlag === 1

  if (isExpired) {
    return null
  }

  if (isAccepted) {
    return (
      <BottomActionsBar>
        <PrimaryButton text="已接单" onClick={() => {}} />
      </BottomActionsBar>
    )
  }

  const onAccept = async () => {
    loading.show()
    const res = await acceptCommercialOrder({
      expertInviteId: commercialOrderDetail?.expertInviteId
    })

    toRefetch?.()

    loading.hide()
    if (res.code === 200) {
      router.push(
        `/budget_campaign/join_success?inviteCode=${routeParams.inviteCode}`
      )
    }
  }

  return (
    <BottomActionsBar>
      <div className="flex items-center pb-[20px]">
        <div
          className="flex items-center"
          onClick={() => setIsChecked(!isChecked)}
        >
          <div className="relative mr-[10px] h-[36px] w-[36px]">
            <Image
              src={isChecked ? checkboxOnIcon : checkboxOffIcon}
              className="size-[36px]"
            />
          </div>
          <span className="text-[26px] text-black30">
            本人接受该商单合作报价且承诺按本商单需求交付内容
          </span>
        </div>
      </div>
      <button
        className={`flex h-[80px] w-[702px] items-center justify-center rounded-[4px] text-[28px] font-bold text-white ${
          isChecked ? 'bg-primary' : 'bg-grayD9'
        }`}
        disabled={!isChecked}
        onClick={() => {
          if (isChecked) {
            onAccept()
          }
        }}
      >
        <span className="mr-[4px]">接受邀请，</span>
        <span className="font-bold">
          {formatPrice(commercialOrderDetail?.budgetPerTalent, true)}
        </span>
        <span>商单等您来参与！</span>
      </button>
    </BottomActionsBar>
  )
}

export default BottomActions
