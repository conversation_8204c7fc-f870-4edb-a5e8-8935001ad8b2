'use client'

import { FC } from 'react'
import { Image } from '@/components/Image'
import { CommercialOrderH5DetailVo } from '../../api/api-uchoice/h5/commercial/order/detail/request'
import banner from '@/../public/images/budget_campaign/banner.png'
import bannerBlur from '@/../public/images/budget_campaign/banner_blur.png'
import { formatTime } from '@/lib/format'

interface BannerProps {
  commercialOrderDetail?: CommercialOrderH5DetailVo
}

const Banner: FC<BannerProps> = ({ commercialOrderDetail }) => {
  const displayValidUntil = formatTime(
    commercialOrderDetail?.registrationEndTime
  )

  // 检查是否已过期
  const isExpired = commercialOrderDetail?.registrationEndTime
    ? new Date(commercialOrderDetail.registrationEndTime) < new Date()
    : false

  return (
    <div className="relative w-full">
      <div className="w-full">
        <Image
          src={isExpired ? bannerBlur : banner}
          className="h-[300px] w-[750px]"
        />
      </div>

      {isExpired ? (
        /* 过期状态：只显示"本次活动已过期" */
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-[28px] font-medium text-white">
            本次活动已过期
          </div>
        </div>
      ) : (
        <>
          {/* 正常状态：显示邀请内容 */}
          <div className="absolute left-[32px] top-[38px] text-white">
            <div className="mb-[10px] text-[32px] font-bold">
              尊敬的{commercialOrderDetail?.displayName}
            </div>
            <div className="mb-[10px] text-[26px]">
              We sincerely invite you to participate
            </div>
            <div className="mb-[10px] text-[26px]">
              in the business order invitation of
            </div>
            <div className="text-[26px]">
              {commercialOrderDetail?.brandName}
            </div>
          </div>

          {/* 有效期 */}
          <div className="absolute bottom-[20px] left-[32px] text-[24px] text-gray8A">
            Valid until：{displayValidUntil}
          </div>
        </>
      )}
    </div>
  )
}

export default Banner
