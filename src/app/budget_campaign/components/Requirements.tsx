'use client'

import { FC } from 'react'
import SectionTitle from './SectionTitle'
import { CommercialOrderH5DetailVo } from '../../api/api-uchoice/h5/commercial/order/detail/request'
import { formatCount } from '@/lib/format'
import palyIcon from '@/../public/images/budget_campaign/pdp/play.png'
import { Image } from '@/components/Image'

interface RequirementsProps {
  commercialOrderDetail?: CommercialOrderH5DetailVo
}

const Requirements: FC<RequirementsProps> = ({ commercialOrderDetail }) => {
  const displayFollowerCount = commercialOrderDetail?.followerCount
  const displayContentCount = commercialOrderDetail?.contentCount
  const displayContentDurationSecond =
    commercialOrderDetail?.contentDurationSecond
  const displayTapLinkRequired = commercialOrderDetail?.tapLinkRequired
  const displayDeliveryContentType = commercialOrderDetail?.deliveryContentType
  const displayItemDesc = commercialOrderDetail?.itemDesc

  return (
    <div className="mb-[40px]" id="requirements-section">
      <SectionTitle title={'参与要求'} />
      <div className="space-y-[24px]">
        <div className="flex justify-between">
          <div className="flex items-center">
            <span className="mr-[4px] text-[28px] text-red">*</span>
            <span className="text-[28px] text-black30">达人要求</span>
          </div>
          <span className="text-[28px] text-gray6E">
            粉丝量≥{formatCount(displayFollowerCount)}
          </span>
        </div>

        {/* 脚本要求 */}
        <div className="flex items-start">
          <div className="mt-[4px] flex items-center">
            <span className="mr-[4px] text-[28px] text-red">*</span>
            <span className="text-[28px] text-black30">脚本要求</span>
          </div>
          <div className="ml-[20px]">
            <div className="rounded-[8px] border border-primary px-[16px] py-[12px]">
              <span className="text-[28px] text-primary">查看脚本要求</span>
            </div>
          </div>
        </div>

        {/* 参考视频 */}
        <div className="flex flex-col">
          <div className="mb-[16px] flex items-center">
            <span className="mr-[4px] text-[28px] text-red">*</span>
            <span className="text-[28px] text-black30">参考视频</span>
          </div>
          <div className="flex space-x-[12px]">
            {displayItemDesc?.videoResourceList &&
              displayItemDesc.videoResourceList.length > 0 &&
              displayItemDesc.videoResourceList
                .slice(0, 3)
                .map((videoUrl, index) => (
                  <div key={index} className="relative h-[200px] w-[160px]">
                    <img
                      src={videoUrl}
                      alt={`video${index + 1}`}
                      className="h-full w-full rounded-[8px] object-cover"
                    />
                    <div className="absolute left-0 top-0 h-full w-full rounded-[8px] bg-[rgba(0,0,0,0.4)]"></div>
                    <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
                      <Image src={palyIcon} className="size-[48px]" />
                    </div>
                  </div>
                ))}
          </div>
        </div>

        <div className="flex justify-between">
          <div className="flex items-center">
            <span className="mr-[4px] text-[28px] text-red">*</span>
            <span className="text-[28px] text-black30">交付要求</span>
          </div>
          <span className="text-[28px] text-gray6E">
            {displayDeliveryContentType === 0 ? '视频' : '视频'}
          </span>
        </div>

        <div className="flex justify-between">
          <div className="flex items-center">
            <span className="mr-[4px] text-[28px] text-red">*</span>
            <span className="text-[28px] text-black30">TAP挂链</span>
          </div>
          <span className="text-[28px] text-gray6E">
            {displayTapLinkRequired === 1 ? '必须' : '不必须'}
          </span>
        </div>

        <div className="flex justify-between">
          <div className="flex items-center">
            <span className="mr-[4px] text-[28px] text-red">*</span>
            <span className="text-[28px] text-black30">内容数量</span>
          </div>
          <span className="text-[28px] text-gray6E">{displayContentCount}</span>
        </div>

        <div className="flex justify-between">
          <div className="flex items-center">
            <span className="mr-[4px] text-[28px] text-red">*</span>
            <span className="text-[28px] text-black30">内容时长</span>
          </div>
          <span className="text-[28px] text-gray6E">
            至少{displayContentDurationSecond}s
          </span>
        </div>
      </div>
    </div>
  )
}

export default Requirements
