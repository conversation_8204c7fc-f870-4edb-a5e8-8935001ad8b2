'use client'

import { FC } from 'react'
import Image from 'next/image'

interface SectionTitleProps {
  title: string
  showArrow?: boolean
  onClick?: () => void
}

const SectionTitle: FC<SectionTitleProps> = ({
  title,
  showArrow = false,
  onClick
}) => {
  return (
    <div
      className="mb-[20px] flex items-center justify-between"
      onClick={onClick}
    >
      <h2 className="text-[30px] font-bold text-black30">{title}</h2>
      {showArrow && (
        <div className="flex h-[28px] w-[28px] items-center justify-center">
          <Image
            src="/images/budget_campaign/arrow_right.png"
            alt="arrow"
            width={28}
            height={28}
          />
        </div>
      )}
    </div>
  )
}

export default SectionTitle
