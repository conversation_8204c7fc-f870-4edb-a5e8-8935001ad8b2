'use client'

import { FC } from 'react'
import SectionTitle from './SectionTitle'
import { CommercialOrderH5DetailVo } from '../../api/api-uchoice/h5/commercial/order/detail/request'
import { Image } from '@/components/Image'
import { formatPrice } from '@/lib/format'
import { useRouter } from '@/lib/hooks/useRouter'
interface ProductInfoProps {
  commercialOrderDetail?: CommercialOrderH5DetailVo
}

const ProductInfo: FC<ProductInfoProps> = ({ commercialOrderDetail }) => {
  const router = useRouter()
  // 使用商单数据中的商品信息
  const displayItemVo = commercialOrderDetail?.itemVo
  return (
    <div
      className="mb-[40px] touch-opacity"
      id="product-section"
      onClick={() =>
        router.push(`/budget_campaign/pdp?id=${displayItemVo?.productId}`)
      }
    >
      <SectionTitle title={'活动商品'} showArrow />
      <div className="flex items-start">
        <div className="mr-[20px] h-[144px] w-[144px] rounded-[8px] bg-gray8A">
          {displayItemVo?.image && (
            <Image
              src={displayItemVo.image}
              className="h-[144px] w-[144px] rounded-[8px] object-cover"
            ></Image>
          )}
        </div>
        <div className="flex-1">
          <p className="mb-[16px] text-[24px] text-black30">
            {displayItemVo?.productName}
          </p>
          <div className="flex items-center">
            <span className="text-[28px] font-bold text-primary">
              {displayItemVo?.minPrice === displayItemVo?.maxPrice
                ? `${formatPrice(displayItemVo?.minPrice, true)}`
                : `${formatPrice(
                    displayItemVo?.minPrice,
                    true
                  )} ~ ${formatPrice(displayItemVo?.maxPrice, true)}`}
            </span>
          </div>

          {/* 达人佣金 */}
          <div className="mt-[16px] flex items-center">
            <div className="flex items-center rounded-[4px] bg-background px-[16px] py-[4px]">
              <span className="text-[24px] text-black30">达人佣金</span>
              <span className="ml-[4px] text-[24px] font-semibold text-black30">
                {(displayItemVo?.partnerCommissionPercent / 100).toFixed(0)}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductInfo
