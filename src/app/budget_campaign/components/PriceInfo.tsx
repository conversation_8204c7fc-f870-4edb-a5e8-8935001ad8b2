'use client'

import { FC } from 'react'
import SectionTitle from './SectionTitle'
import { CommercialOrderH5DetailVo } from '../../api/api-uchoice/h5/commercial/order/detail/request'

interface PriceInfoProps {
  title?: string
  showArrow?: boolean
  onClick?: () => void
  price?: string
  commercialOrderDetail?: CommercialOrderH5DetailVo
}

const PriceInfo: FC<PriceInfoProps> = ({
  title = '合作报价',
  showArrow = false,
  onClick,
  price = '฿2000',
  commercialOrderDetail
}) => {
  const displayPrice = commercialOrderDetail?.budgetPerTalent
    ? `฿${commercialOrderDetail.budgetPerTalent}`
    : price
  return (
    <div className="mb-[40px]" id="price-section">
      <SectionTitle title={title} showArrow={showArrow} onClick={onClick} />
      <p className="text-[28px] font-bold text-black30">
        本次合作报价为{displayPrice}
      </p>
    </div>
  )
}

export default PriceInfo
