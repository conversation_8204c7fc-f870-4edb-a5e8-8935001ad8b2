'use client'

import { FC } from 'react'
import SectionTitle from './SectionTitle'
import { CommercialOrderH5DetailVo } from '../../api/api-uchoice/h5/commercial/order/detail/request'
import { formatPrice } from '@/lib/format'
import { Image } from '@/components/Image'

interface BrandIntroProps {
  commercialOrderDetail?: CommercialOrderH5DetailVo
}

const BrandIntro: FC<BrandIntroProps> = ({ commercialOrderDetail }) => {
  return (
    <div className="mb-[40px]" id="brand-section">
      <SectionTitle title={'关于品牌'} />
      <div className="flex items-start">
        {commercialOrderDetail?.brandLogo && (
          <div className="mr-[20px] h-[128px] w-[128px] rounded-[8px]">
            <Image
              src={commercialOrderDetail?.brandLogo}
              className="h-[128px] w-[128px] rounded-[8px] object-cover"
            ></Image>
          </div>
        )}
        <div>
          <h3 className="mb-[10px] text-[28px] font-bold text-black30">
            {commercialOrderDetail?.brandName}
          </h3>
          <p className="text-[24px] text-black30">
            {commercialOrderDetail?.brandDesc}
          </p>
        </div>
      </div>

      {/* 合作报价 */}
      <div className="mt-[40px] border-t border-grayEE pt-[20px]">
        <SectionTitle title="合作报价" />
        <p className="text-[28px] font-bold text-black30">
          本次合作报价为
          <span className="text-[36px] font-bold text-primary">
            {formatPrice(commercialOrderDetail?.budgetPerTalent || 0, true)}
          </span>
        </p>
      </div>
    </div>
  )
}

export default BrandIntro
