import ImageWithPreload from '@/app/components/ImageWithPreload'
import styles from './index.module.scss'
import { i18n } from '@/lib/client/i18n'
import { formatSales } from '@/lib/format'

const Avatar = ({ item }) => {
  return item ? (
    <div className={styles.avatar_box}>
      <div className={styles.avatar}>
        <ImageWithPreload
          className={styles.avatar_img}
          src={item?.avatar || ''}
          imageStyle={{ borderRadius: '4px' }}
        />
      </div>
      <div>
        <div className={styles.nick_name}>{item?.nickname}</div>
        <div className={styles.followers}>
          {i18n.t('TiktokData.粉丝总量')}:{' '}
          {formatSales(item?.fansCount, '-', true)}
        </div>
      </div>
    </div>
  ) : null
}

export default Avatar
