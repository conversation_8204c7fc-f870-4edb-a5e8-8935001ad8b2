'use client'
import React, { useEffect, useState, useRef } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import icon_back from '@/../public/images/promotion/icon-back.png'
import ImageWithPreload from '@/app/components/ImageWithPreload'
import copy from '@/../public/images/promotion/copy.png'
import youpik_icon from '@/../public/images/promotion/youpik_icon.png'
import fire from '@/../public/images/promotion/fire.png'
import { rpxToPx } from '@/lib/client/utils'
import { Skeleton } from 'react-vant'
import { isLanguageEN, isRegionTH } from '@/lib/utils'
import { formatMinMax, formatPrice, formatThous, showEarn } from '@/lib/format'
import { copyText } from '@/lib/client/utils'
import { toast } from '@/lib/client/toast'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
const ProductIntro = ({ productItem }) => {
  return (
    <div className={styles.boxs}>
      {productItem ? (
        <>
          <div className={styles.intro_boxs}>
            <ImageWithPreload
              alt=""
              src={productItem.homeImgUrl}
              className={styles.home_img}
              width={100}
              height={100}
            ></ImageWithPreload>
            <div className={styles.product_content}>
              <div className={styles.title_boxs}>
                <div className={styles.title}>
                  {isLanguageEN() ? productItem?.nameEn : productItem?.name}
                </div>
                <div
                  className={styles.icon_copy}
                  onClick={e => {
                    e.stopPropagation()
                    statistic({
                      eventName: EventName.click_product_promotion_copy
                    })
                    copyText(
                      isLanguageEN() ? productItem?.nameEn : productItem?.name
                    )
                  }}
                >
                  {' '}
                  <ImageWithPreload
                    className={styles.icon_copy}
                    width={16}
                    height={16}
                    src={copy}
                  />
                </div>
              </div>
              <div className={styles.youpik_boxs}>
                <ImageWithPreload
                  width={18}
                  height={18}
                  src={youpik_icon}
                  imageStyle={{ width: rpxToPx(42), height: rpxToPx(44) }}
                />
                <div className={styles.youpik_high}>
                  {i18n.t('Promotion.youpikHighCom')}
                </div>
                <span className={styles.youpik_com}>{`${
                  productItem?.commissionRate
                    ? Math.floor(productItem?.commissionRate * 100)
                    : 0
                }%`}</span>
              </div>
              <div className={styles.sold_in}>
                <ImageWithPreload width={10} height={12} src={fire} />
                <div className={styles.sold_in_day}>
                  <span>{i18n.t('Promotion.solddays')} </span>
                  <span>
                    {productItem.salesForLast30Days
                      ? formatThous(productItem?.salesForLast30Days)
                      : 0}
                  </span>
                </div>
              </div>
              <div className={styles.price_box}>
                <div className={styles.price_high}>
                  {formatMinMax(
                    formatPrice(productItem?.minPrice, true),
                    formatPrice(productItem?.maxPrice, true)
                  )}
                </div>
                <span className={styles.price_com}>
                  {' '}
                  {i18n.t('Product.Earn')}{' '}
                  {showEarn(productItem.minEarn || 0, productItem.maxEarn || 0)}
                </span>
              </div>
            </div>
          </div>
          {productItem.maxEarnHistory && productItem.maxEarnHistory != 0 ? (
            <div className={styles.high_cooperator}>
              <span className={styles.high_cooperator}>
                {i18n.t('Promotion.historyHighCooperator')}
              </span>
              <span className={styles.high_cooperator_price}>
                {' '}
                {formatPrice(productItem.maxEarnHistory, true)}{' '}
              </span>
            </div>
          ) : null}
        </>
      ) : (
        <Skeleton avatar avatarSize={90}></Skeleton>
      )}
    </div>
  )
}

export default ProductIntro
