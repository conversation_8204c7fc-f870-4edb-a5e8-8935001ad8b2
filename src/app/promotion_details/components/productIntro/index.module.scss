.boxs {
    width: 100%;
    box-sizing: border-box;
    padding: 0 24px;
}

.intro_boxs {
    flex: 1;
    display: flex;
    // margin-top: 20px;
}

.product_content {
    flex: 1;
    margin-left: 18px;
    width: 200px;
}

.title_boxs {
    display: flex;
    align-items: center;

}

.home_img {
    width: 200px !important;
    height: 200px !important;
}

.title {
    font-size: 28px;
    color: #333;
    margin-right: 8px;
    height: 40px;

    line-height: 42px;
    /* 隐藏溢出部分的文本 */
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
}

.icon_copy {
    width: 32px !important;
    height: 32px !important;
}

.youpik_high {
    background-color: #F4393B;
    // border-radius: 2px;
    font-size: 24px;
    color: #FFFFFF;
    padding: 2px;

}

.youpik_com {
    border: 2px solid #f4393b;
    font-size: 24px;
    color: #f4393b;
    font-weight: bold;
    padding: 0 8px;

}

.youpik_boxs {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-top: 18px;
}

.sold_in {
    display: flex;
    align-items: center;
}

.sold_in_day {
    margin-left: 8px;
    font-size: 24px;
    color: #8a8a8a;
}

.sold_in {
    margin-top: 16px;
}

.price_box {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    flex-wrap: wrap;
}

.price_high {
    font-size: 26px;
    color: #333333;

}

.price_com {
    font-weight: bold;
    font-size: 28px;
    color: #FE6D45;

}

.high_cooperator {
    font-weight: normal;
    font-size: 28px;
    color: #6E6E6E;
    margin-top: 32px;

}

.high_cooperator_price {
    color: #FE6D45;
}