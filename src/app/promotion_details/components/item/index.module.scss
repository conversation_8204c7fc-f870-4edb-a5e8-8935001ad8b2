.item_box {
    margin-top: 32px;
    display: flex;
    justify-content: space-between;
}

.item_content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item_number {
    display: flex;
    justify-content: space-between;
}

.item_img {
    width: 160px !important;
    height: 200px !important;
    margin-right: 16px;
    border-radius: 8px;
    position: relative;
    z-index: 1;

}

.item_title_box {
    display: flex;
    flex-direction: column;
}

.icon_play_img {
    width: 64px !important;
    height: 64px !important;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -32px;
    margin-top: -32px;
    z-index: 2;
}

.item_title {
    box-sizing: border-box;
    overflow-wrap: break-word;
    font-weight: normal;
    font-size: 28px;
    color: #303030;
    color: #333;
    height: 40px;
    line-height: 42px;
    /* 隐藏溢出部分的文本 */
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;
    overflow: hidden;

}

.item_time {
    font-weight: normal;
    font-size: 24px;
    color: #8A8A8A;
    margin-top: 8px;

}