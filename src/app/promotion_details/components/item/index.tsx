'use client'
import React, { useEffect, useState, useRef } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import ImageWithPreload from '@/app/components/ImageWithPreload'
import icon_play_btn from '@/../public/images/promotion/icon_play_btn.png'
import { formatSales, formatTime } from '@/lib/format'
import { useRouter } from '@/lib/hooks/useRouter'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import Avatar from '../avatar'

const Item = ({ item, tabIndex }) => {
  const router = useRouter()

  return (
    <>
      <div
        className={styles.item_box}
        onClick={() => {
          if (tabIndex == 0) {
            statistic({
              eventName: EventName.click_promote_play,
              param: {
                videoId: item.videoId
              }
            })
          } else {
            statistic({
              eventName: EventName.click_my_promote_video,
              param: {
                videoId: item.videoId
              }
            })
          }
        }}
      >
        <div
          className={styles.item_img}
          onClick={() => {
            item.sourceUrl &&
              router.push(
                `https://www.tiktok.com/player/v1/${item.videoId}?id=${item.videoId}`
              )
          }}
        >
          {item.type == 1 && (
            <ImageWithPreload
              width={160}
              height={200}
              src={icon_play_btn}
              className={styles.icon_play_img}
            />
          )}
          <ImageWithPreload
            width={160}
            height={200}
            src={item?.coverImg}
            className={styles.item_img}
          />
        </div>
        <div className={styles.item_content}>
          <div className={styles.item_title_box}>
            <div className={styles.item_title}>
              {item.videoDescription
                ? item.videoDescription
                : i18n.t('Promotion.defaultTitle')}
            </div>
            {tabIndex === 0 && <Avatar item={item}></Avatar>}
          </div>
          <div className={styles.item_number}>
            <div className={styles.item_time}>
              <span>{i18n.t('Promotion.playVolume')}</span>{' '}
              <span>{formatSales(item.viewCount)}</span>
            </div>
            <div className={styles.item_time}>
              <span>{i18n.t('Promotion.numberOfLikes')}</span>{' '}
              <span>{formatSales(item.likeCount)}</span>
            </div>
          </div>
          <div className={styles.item_time}>
            <span>{i18n.t('Promotion.releaseTime')}</span>{' '}
            <span>{formatTime(item.videoCreateTime)}</span>
          </div>
        </div>
      </div>
    </>
  )
}

export default Item
