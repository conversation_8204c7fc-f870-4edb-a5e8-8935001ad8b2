'use client'
import React from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { formatPercentSign, formatPrice } from '@/lib/format'
const ItemBottom = ({ item }) => {
  return (
    <div className={styles.item_bottom}>
      <div className={styles.bottom_com_rate}>
        <span className={styles.rate_title}>
          {i18n.t('Product.佣金率')}: &nbsp;
        </span>
        <span className={styles.rate_price}>
          {formatPercentSign(item.commissionRate)}
        </span>
      </div>
      <div className={styles.line}></div>
      <div className={styles.bottom_com}>
        <span className={styles.rate_title}>
          {i18n.t('LowPrice.佣金')}: &nbsp;
        </span>
        <span className={styles.com_count}>
          {' '}
          {formatPrice(item.commission, true)}
        </span>
      </div>
    </div>
  )
}

export default ItemBottom
