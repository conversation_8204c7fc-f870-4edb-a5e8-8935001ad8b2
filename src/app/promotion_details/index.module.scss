.boxs {
    width: 750px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    flex: 1;
    // background: linear-gradient(180deg, #FFEDE4 0%, #FFFFFF 100%);
    // overflow-y: scroll;
    background: #f5f5f5;
}

.item_box {
    width: 750px;
    background: #F5F5F5;
    padding: 20px 24px 144px 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;


}

.history_box {
    margin-top: 38px;
}

.popular_promotional_title {
    font-weight: bold;
    font-size: 32px;
    color: #303030;

}

.history_btns {
    display: flex;
    margin-top: 32px;
    white-space: nowrap;
    overflow-x: auto;
}

.history_btns::-webkit-scrollbar {
    display: none;
}

.item {
    flex: 1;
    overflow: scroll;
}

.btn_bg {
    background-color: #fff;
    padding: 20px 0;
    box-sizing: border-box;
}

.bottom_line {
    width: 654px;
    height: 2px;
    background: #F5F5F5;
    margin-top: 32px;
}

.content {
    flex: 1;
    background: #FFFFFF;
    border-radius: 4px;
    padding: 24px;
    position: relative;
    padding-bottom: 104px;
}

.title {
    font-weight: bold;
    font-size: 32px;
    color: #303030;

}

.video_btns {
    display: flex;
    margin-top: 32px;

}

.video_btn {
    padding: 0 24px;
    height: 60px;
    background: #FFF0EC;
    border-radius: 2px;
    border: 2px solid #FE6D45;
    font-weight: normal;
    font-size: 28px;
    color: #FE6D45;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 24px;
}

.latest_btn {
    height: 60px;
    padding: 0 24px;
    background: #F5F5F5;
    border-radius: 2px;
    font-weight: normal;
    font-size: 28px;
    color: #6E6E6E;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 24px;
}

.btn_width {
    width: 32px;
}

.rePro_btn {
    width: 100%;
    padding: 0 24px;
    height: 180px;
    background: #fff;
    border-radius: 4px;
    padding-top: 24px;
    display: flex;
    justify-content: center;
    position: fixed;
    bottom: 0px;

    z-index: 9;
}

.resale_btn {
    padding: 0 24px;
    height: 80px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 28px;
    color: #FE6D45;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
    border: 2px solid #FE6D45;
    z-index: 9;
}

.similar_btn {
    padding: 0 24px;
    height: 80px;
    line-height: 80px;
    background: #FE6D45;
    border-radius: 4px;
    font-weight: bold;
    font-size: 28px;
    color: #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    z-index: 9;
}

.sigh_box {
    width: 90%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 24px;


}

.sigh {
    font-weight: normal;
    font-size: 24px;
    color: #9A9A9A;
    margin-left: 8px;
}

.fix_bottom {
    background-color: #fff;
}

.img_fire {
    width: 32px !important;
    height: 32px !important;
}

.hot_video_tip {
    font-weight: normal;
    font-size: 26px;
    color: #F94753;
}

.fire_tip {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.status_i18n {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80vh;
}