'use client'
import React, { useEffect, useState, useRef, Suspense } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import ProductIntro from './components/productIntro'
import Button from '../components/Button'
import Item from './components/item'
import ImageWithPreload from '../components/ImageWithPreload'
import sigh from '@/../public/images/promotion/sigh.png'
import { useRouter } from '@/lib/hooks/useRouter'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import {
  TopGrandientCard,
  TransparentNavPage
} from '@/components/TransparentNavPage'
import { StickyHeader } from '@/components/StickyHeader'
import {
  PromotionInfoDto,
  PromotionVideoListDto,
  PromotionVideoListParams
} from '../api/api-uchoice/tt/item/promotionInfo/dtos'
import { loading } from '@/lib/client/loading'
import {
  promotionInfo,
  promotionVideoList
} from '../api/api-uchoice/tt/item/promotionInfo/request'
import { ItemRank } from './components/item/components/itemRank'
import StatusView, { StatusViewType } from '../components/StatusView'
import { LoadMore } from '../components/LoadMore'
import { LineTabs } from '@/components/Tabs'
import ItemBottom from './components/itemBottom'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import BtnTabs from '../components/BtnTabs'
import { PullToRefresh } from '@/app/components/PullToRefresh'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import icon_fire from '@/../public/images/promotion/icon_fire.png'
import Avatar from './components/avatar'
interface Props {
  id: string
  productId: string
  tt_user_id: string
  isShowMypro: boolean
}
const Inner = () => {
  const [itemList, setItemList] = useState<PromotionVideoListDto[]>([])
  const videoSortByRef = useRef('view_count')
  const pageNoRef = useRef(1)
  const productIdRef = useRef('')
  const totalRef = useRef(0)
  const [status, setStatus] = useState(StatusViewType.loading)
  const noMoreRef = useRef(false)
  const routerParams = useRouteParams<Props>()
  const [tabIndex, setTabIndex] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)
  const mounted = useMounted()
  const typeRef = useRef(1)
  const videoTypeRef = useRef(0)
  const tiktokUserIdRef = useRef('')
  const [btnIndex, setBtnIndex] = useState(0)
  const [productItem, setProductItem] = useState<PromotionInfoDto>({
    hasHotVideo: true
  })
  const router = useRouter()
  const buttonRefs = useRef<(HTMLDivElement | null)[]>([])
  const [buttonWidths, setButtonWidths] = useState<number[]>([])
  const reIdRef = useRef(null)
  const hotBtns = [
    {
      title: i18n.t('Promotion.playVolume'),
      value: 'view_count',
      buryKey: EventName.click_promote_play_num
    },
    {
      title: i18n.t('Promotion.recentlyPublished'),
      value: 'video_create_time',
      buryKey: EventName.click_release_time
    }
  ]
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const recordsBtns = [
    {
      title: i18n.t('Promotion.highestReturn'),
      value: 'commission',
      buryKey: EventName.click_high_income,
      id: 0
    },
    {
      title: i18n.t('Promotion.recentlyPublished'),
      value: 'video_create_time',
      buryKey: EventName.click_recently_publish,
      id: 1
    },
    {
      title: i18n.t('Promotion.videoOnly'),
      value: 'view_count',
      buryKey: EventName.click_video_only,
      id: 2
    }
  ]
  useEffect(() => {
    productIdRef.current = routerParams?.productId
    tiktokUserIdRef.current = routerParams?.tt_user_id
  }, [routerParams])

  const loadMore = () => {
    if (noMoreRef.current) return
    fetchVideoItems()
  }
  useEffect(() => {
    fetchItems()
  }, [])
  useEffect(() => {
    const timer = setTimeout(() => {
      const widths = buttonRefs.current.map(ref => ref?.offsetWidth || 0)
      setButtonWidths(widths)
    }, 0)

    return () => clearTimeout(timer)
  }, [recordsBtns])
  const handleRefresh = async () => {
    pageNoRef.current = 1
    await fetchVideoItems()
    await fetchItems()
  }
  const fetchItems = async () => {
    const { code, result } = await promotionInfo({
      productId: routerParams?.productId || '',
      tiktokUserId: tiktokUserIdRef?.current
    })

    if (code === 200 && result) {
      setProductItem(result)
    }
  }
  const scrollLeft = id => {
    if (containerRef.current) {
      if (id == 0) {
        containerRef.current.scrollTo({
          left: 0,
          behavior: 'smooth'
        })
        return
      } else if (id == 2 && id != reIdRef.current) {
        containerRef.current.scrollBy({
          left: buttonWidths[id] / 2,
          behavior: 'smooth'
        })
      }
    }
    reIdRef.current = id
  }
  const fetchVideoItems = async () => {
    if (pageNoRef.current > 1) loading.show({ userInteractive: true })

    loading.show()
    const params: PromotionVideoListParams = {
      pageNo: pageNoRef.current,
      pageSize: 10,
      productId: productIdRef.current,
      type: typeRef.current == 1 ? 0 : videoTypeRef.current,
      sortBy: typeRef.current == 1 ? '' : videoSortByRef.current,
      promotionType: typeRef.current == 2 ? 1 : 2,
      tiktokUserId: /^\d+$/.test(tiktokUserIdRef?.current)
        ? tiktokUserIdRef?.current
        : '',
      sortDirection: 'desc'
    }
    const { code, result } = await promotionVideoList(params)
    loading.hide()

    if (code === 200) {
      const list = result.list
      let isMore = result.total === list.length || list.length === 0
      noMoreRef.current = isMore

      if (result.list.length > 0) {
        pageNoRef.current == 1
          ? setItemList([...result.list])
          : setItemList(itemList.concat(result.list))
        totalRef.current = result.total
        setStatus(
          result.total === 0 && pageNoRef.current === 1
            ? StatusViewType.empty
            : StatusViewType.loading
        )
      } else {
        setStatus(StatusViewType.empty)
      }

      pageNoRef.current++
    }
  }
  const onTabChange = (index: number) => {
    setItemList([])
    if (index == 0) {
      videoSortByRef.current = 'view_count'
    }
    statistic({
      eventName:
        index == 0
          ? EventName.click_poplular_promotion
          : EventName.click_my_promotion
    })
    pageNoRef.current = 1
    typeRef.current = index + 1
    setBtnIndex(0)
    setTabIndex(index)
    fetchVideoItems()
  }
  const myRecordsBtneTabs = item => {
    scrollLeft(item?.id)

    item.id != 2
      ? (videoSortByRef.current = item.value)
      : (videoSortByRef.current = videoSortByRef.current)
    setItemList([])
    if (item.id == 2) {
      if (videoTypeRef.current == 1) {
        videoTypeRef.current = 0
      } else {
        videoTypeRef.current = 1
      }
    } else {
      setBtnIndex(item.id)
    }

    pageNoRef.current = 1
    statistic({ eventName: item.buryKey })

    fetchVideoItems()
  }

  return mounted ? (
    <TransparentNavPage
      title={i18n.t('Promotion.promotionShop')}
      transparent={true}
      hide={!webview}
    >
      <div className={styles.boxs}>
        <TopGrandientCard>
          <div
            onClick={() => {
              statistic({
                eventName: EventName.click_product_promotion_detail,
                param: {
                  id: productItem?.id
                }
              })
              router.push(`/product/${productItem?.id}`)
            }}
          >
            <ProductIntro productItem={productItem} />
          </div>
        </TopGrandientCard>

        <div className={styles.item_box}>
          <div className={styles.content}>
            <StickyHeader zIndex="3">
              <div className={styles.btn_bg}>
                {routerParams.isShowMypro ? (
                  <LineTabs
                    titles={[
                      i18n.t('Promotion.popularPromotionalVideos'),

                      i18n.t('Promotion.myPromotionRecord')
                    ]}
                    onChange={onTabChange}
                    index={tabIndex}
                  ></LineTabs>
                ) : (
                  <div className={styles.popular_promotional_title}>
                    {i18n.t('Promotion.popularPromotionalVideos')}
                  </div>
                )}
                {tabIndex === 0 ? null : (
                  // <div className={styles.video_btns}>
                  //   {hotBtns.map((item, index) => (
                  //     <div key={index}>
                  //       <Button
                  //         onClick={() => {
                  //           videoSortByRef.current = item.value
                  //           setItemList([])
                  //           pageNoRef.current = 1
                  //           fetchVideoItems()
                  //           statistic({ eventName: item.buryKey })
                  //         }}
                  //         title={item.title}
                  //         className={
                  //           videoSortByRef.current == item.value
                  //             ? styles.video_btn
                  //             : styles.latest_btn
                  //         }
                  //       ></Button>
                  //       <div className={styles.btn_width}></div>
                  //     </div>
                  //   ))}
                  // </div>
                  <div className={styles.history_box}>
                    <Avatar item={productItem}></Avatar>
                    <div className={styles.history_btns} ref={containerRef}>
                      {recordsBtns.map((item, index) => (
                        <div key={index}>
                          <BtnTabs
                            ref={el => (buttonRefs.current[index] = el)}
                            btnIndex={btnIndex}
                            item={item}
                            onClick={() => {
                              myRecordsBtneTabs(item)
                            }}
                            className={
                              btnIndex == item.id ||
                              (videoTypeRef.current == 1 && item.id == 2)
                                ? styles.video_btn
                                : styles.latest_btn
                            }
                          ></BtnTabs>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </StickyHeader>
            {!productItem?.hasHotVideo && tabIndex === 0 ? (
              <div className={styles.fire_tip}>
                <ImageWithPreload
                  width={16}
                  height={16}
                  src={icon_fire}
                  className={styles.img_fire}
                />
                <div className={styles.hot_video_tip}>
                  <div>{i18n.t('Promotion.hotVideoTip1')}</div>
                  <div>{i18n.t('Promotion.hotVideoTip2')}</div>
                </div>
              </div>
            ) : null}
            {itemList.length > 0 ? (
              <PullToRefresh onRefresh={handleRefresh}>
                <>
                  {itemList.map((item, index) => {
                    return (
                      <div key={index}>
                        <ItemRank index={index}>
                          <Item item={item} tabIndex={tabIndex}></Item>
                        </ItemRank>
                        {tabIndex === 1 && (
                          <ItemBottom item={item}></ItemBottom>
                        )}
                        <div className={styles.bottom_line}></div>
                      </div>
                    )
                  })}
                </>
              </PullToRefresh>
            ) : (
              <div className="pt-[100px]">
                <StatusView status={status}></StatusView>
              </div>
            )}
            {itemList.length > 0 && (
              <div className={styles.fix_bottom}>
                <div className={styles.sigh_box}>
                  <ImageWithPreload width={12} height={12} src={sigh} />
                  <span className={styles.sigh}>
                    {i18n.t('Promotion.videoDataSource')}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
        <LoadMore
          onLoadMore={loadMore}
          noMore={noMoreRef.current && itemList.length > 0}
        ></LoadMore>
        <div className={styles.rePro_btn}>
          <Button
            onClick={async () => {
              statistic({
                eventName: EventName.click_product_promotion_resale,
                param: {
                  id: productItem?.id
                }
              })

              router.push(`/tiktok_promotion_help`)
            }}
            title={i18n.t('Promotion.resaleTutorial')}
            className={styles.resale_btn}
          ></Button>
          <Button
            onClick={async () => {
              statistic({
                eventName: EventName.click_product_promotion_income,
                param: {
                  id: productItem?.id
                }
              })
              await webview?.send(WebviewEvents.launch, {
                url: 'tiktok://'
              })
            }}
            title={i18n.t('Promotion.resaleToDoubleMyIncome')}
            className={styles.similar_btn}
          ></Button>
        </div>
      </div>
    </TransparentNavPage>
  ) : null
}

export default Inner
