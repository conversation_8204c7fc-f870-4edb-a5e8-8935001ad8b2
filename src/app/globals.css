@tailwind base;

@layer base {
  html {
    @apply text-black;
    @apply text-[32px];
    @apply bg-white;
  }
}

@tailwind components;
@tailwind utilities;


:root {
  --uchoice-navbar-height: 2.2rem;
  --uchoice-primary: #FE6D45;
  --uchoice-black: #303030;
  --uchoice-background: #F4F5F7;
}

* {
  -webkit-user-select: none; /* Chrome, Safari, Opera */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* 非前缀版本, 目前被广泛支持 */

  -webkit-touch-callout: none; /* iOS Safari */
}

img {
  pointer-events: none; /* 阻止图片被选中 */
}

/* antd的预览图片组件，图片间距为0 */
.adm-image-viewer-slides-inner > * {
  /* margin-right: 0 !important; */
}

.rv-image-preview__close-icon--top-right {
  top: 100px !important;
}

.tag-cloud-item1 {
  background-color: #FE6D45;
  border-radius: 4px;
  font-size: 48px !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.tag-cloud-item2 {
  background-color: #FEA78F;
  border-radius: 4px;
  font-size: 28px !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.tag-cloud-item3 {
  background-color: #FFC5B5;
  border-radius: 4px;
  font-size: 28px !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

a.adm-picker-header-button {
  color: #FE6D45;
}

body.gray {
  min-height: 100vh;
  background-color: var(--uchoice-background);
}

body.black {
  min-height: 100vh;
  background-color: #140801;
}

@media(max-width: 768px) {
  html {
    font-size: calc(100vw / 750 * 40);
  }
}

@media (min-width: 768px) {
  html {
    max-width: 750px;
    margin: 0 auto;
    font-size: 52px !important;
  }
}

.rv-popup--bottom {
  left: auto !important;
  width: 750px !important;
}