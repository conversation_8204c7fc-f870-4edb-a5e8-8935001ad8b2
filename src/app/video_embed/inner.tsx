'use client'
import React, { useEffect, useState, useRef } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import ImageWithPreload from '@/app/components/ImageWithPreload'
import icon_play_btn from '@/../public/images/promotion/icon_play_btn.png'
import TikTokEmbed from './components/videoEmbed'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { useRouter } from '@/lib/hooks/useRouter'
import { WebviewEvents } from '@/lib/client/webview/events'
import { webview } from '@/lib/client/webview'
import { useMounted } from '@/lib/hooks/useMounted'
import { TransparentNavPage } from '@/components/TransparentNavPage'
interface Props {
  videoId: string
  productId: string
  productImage: string
  productName: string
  productEarn: string
  productLink: string
}

const VideoEmbedBg = () => {
  const mounted = useMounted()
  const [modalVisible, setModalVisible] = useState(true)
  const routerParams = useRouteParams<Props>()
  const router = useRouter()
  const [videoId, setVideoId] = useState('')

  useEffect(() => {
    setVideoId(routerParams.videoId)
  }, [routerParams])
  const onCloseModal = () => {
    webview && webview?.send(WebviewEvents.back)
    !webview && router.back()
    setModalVisible(false)
  }

  return mounted ? (
    <TikTokEmbed
      videoId={videoId}
      productInfo={
        routerParams.productImage
          ? {
              imageUrl: routerParams.productImage,
              name: routerParams.productName,
              earn: routerParams.productEarn,
              link: routerParams.productLink
            }
          : undefined
      }
    ></TikTokEmbed>
  ) : null
}

export default VideoEmbedBg
