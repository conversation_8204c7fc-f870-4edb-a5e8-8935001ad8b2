.modal_box {
    width: 100%;
    height: 100vh;
    background-color: #121111;
    position: relative;
    z-index: 66;
    // border: 1px solid #121111;
}

.tiktok_box {
    width: 100%;
    height: 100vh;

}

.tiktok-embed iframe {}

.custom_embed {
    width: 750px !important;
    height: 100vh !important;
    margin-top: 88px auto !important;
}

.custom_embed>section::before {
    height: 100vh !important;
    background-image: none !important;
    background-color: #fff;
}

.custom_embed iframe {
    height: 100vh !important;
    max-height: 100vh !important;
    z-index: 88;
}

.custom_embed .css-vptvdy {
    min-width: 100% !important;
    width: 100% !important;
    height: 100vh !important;
}



.item_img {
    width: 204px !important;
    height: 204px !important;
}

.title {
    font-size: 20px;
    font-weight: normal;
    color: #303030;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-top: 8px;
}

.earn_box {
    font-weight: normal;
    font-size: 20px;
    color: #9A9A9A;
    margin-top: 4px;

}

.earn_price {

    font-weight: normal;
    font-size: 20px;
    color: #303030;

}

.add_btn {
    width: 192px;
    height: 56px;
    background: #FE6D45;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: normal;
    font-size: 24px;
    color: #FFFFFF;
    margin-top: 8px;


}

.title_box {
    padding: 0 8px;
}

.floating_window {
    padding: 2px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    border-radius: 4px;
    width: 208px;
    // height: 382px;
    position: absolute;
    bottom: 140px;
    right: 24px;
    z-index: 99;
    padding-bottom: 8px;
}

.icon_close_img_box {
    width: 140px !important;
    height: 140px !important;
    position: absolute;
    left: 24px;
    top: 24px;
    z-index: 999;
}

.icon_close_img {
    width: 40px !important;
    height: 40px !important;
    position: absolute;
    left: 24px;
    top: 24px;
    z-index: 999;

}

.icon_close_box_ios {
    width: 140px !important;
    height: 140px !important;
    position: absolute;
    left: 48px;
    top: 48px;
    z-index: 999;
}

.icon_close_box_ios {
    width: 40px !important;
    height: 40px !important;
    position: absolute;
    left: 48px;
    top: 48px;
    z-index: 999;
}

.video_boxs {
    width: 750px;
    height: 100%;
    padding-top: 188px;
    box-sizing: border-box;
}