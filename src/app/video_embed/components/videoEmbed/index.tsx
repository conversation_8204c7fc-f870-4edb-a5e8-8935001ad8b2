import React, { useEffect, useState } from 'react'
import styles from './index.module.scss'
import ImageWithPreload from '@/app/components/ImageWithPreload'
import icon_back from '@/../public/images/showcase/img_1_vi_en.png'
import icon_close from '@/../public/images/promotion/icon_close.png'
import Button from '@/app/components/Button'
import { i18n } from '@/lib/client/i18n'
import { isIOS, rpxToPx } from '@/lib/client/utils'
import { webview } from '@/lib/client/webview'
import { formatPrice } from '@/lib/format'
import gray_close from '@/../public/images/common/gray_close.png'
import { Image } from '@/components/Image'
import { statistic } from '@/lib/statistic'
import { addToShowCase, checkAuth } from '@/lib/actions'
import { EventName } from '@/lib/statistic/const'

interface ProductInfo {
  imageUrl: string
  name: string
  earn: string
  link: string
}

const TikTokEmbed = ({
  videoId,
  productInfo
}: {
  videoId: string
  productInfo?: ProductInfo
}) => {
  const [floatVisible, setFloatVisible] = useState(true)

  return (
    <div>
      {productInfo && floatVisible && (
        <div className={styles.floating_window}>
          <ImageWithPreload
            width={204}
            height={204}
            src={productInfo.imageUrl}
            className={styles.item_img}
          />
          <div className={styles.title_box}>
            <div className={styles.title}>{productInfo.name}</div>
            <div className={styles.earn_box}>
              {i18n.t('Product.Earn')}:{' '}
              <span className={styles.earn_price}>{productInfo.earn}</span>
            </div>
          </div>
          <Button
            onClick={() => {
              statistic({
                eventName: EventName.video_add_to_showcase
              })
              // 这里缺少商品id参数，无法上报事件统计
              // @ts-ignore
              checkAuth(() => addToShowCase(productInfo))
            }}
            title={i18n.t('Promotion.add')}
            className={styles.add_btn}
          ></Button>
          <div
            className="absolute right-[-16px] top-[-16px]"
            onClick={() => setFloatVisible(false)}
          >
            <Image className="size-[32px]" src={gray_close}></Image>
          </div>
        </div>
      )}

      <div className="h-screen w-screen">
        <iframe
          onClick={event => event.preventDefault()}
          src={`https://www.tiktok.com/player/v1/${videoId}?id=${videoId}`}
          width="100%"
          height="100%"
          allow="autoplay; encrypted-media; fullscreen"
          allowFullScreen
        ></iframe>
      </div>
    </div>
  )
}

export default TikTokEmbed
