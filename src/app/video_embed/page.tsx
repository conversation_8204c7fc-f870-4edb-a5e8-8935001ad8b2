import { metadataTemplate, inApp, isIOS } from '@/lib/server/utils'
import VideoEmbedBg from './inner'
import { redirect } from 'next/navigation'

// 服务端的 generateMetadata 函数
export async function generateMetadata() {
  if (inApp()) {
    return metadataTemplate({
      title: '',
      nativeNavBarTitle: '',
      showNativeNavBar: true
    })
  }

  return metadataTemplate()
}

// 修改后的 Index 页面，处理 videoId 重定向
export default async function Index({ searchParams }) {
  const videoId = searchParams?.videoId

  // 如果存在 videoId 参数，重定向到指定的 TikTok 播放器 URL
  if (videoId && isIOS()) {
    redirect(`https://www.tiktok.com/player/v1/${videoId}?id=${videoId}`)
  }

  // 没有 videoId 则返回默认页面
  return <VideoEmbedBg />
}
