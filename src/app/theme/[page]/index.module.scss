.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  -ms-overflow-style: none;
  scrollbar-width: none;
  overflow-y: scroll;
  position: relative;
}

.container_h5 {
  width: 100%;
  height: 100vh;
  overflow-y: scroll;
  display: flex;
  flex-direction: column;
  //  background: #ffc4e6;

}

.container::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.theme_content {
  display: flex;
  flex-direction: column;
  flex: 1;
  border-radius: 12px 12px 0px 0px;
  z-index: 1;
  box-sizing: border-box;


}

.tab_fixed {
  width: 100%;
  z-index: 999;
  margin-top: -5px;
}

.tab_ios_fixed {
  width: 100%;
  z-index: 999;
  margin-top: -15px;
}

.theme_content_three {
  background: #ffc4e6;
  border-image: linear-gradient(180deg,
      rgba(255, 255, 255, 1),
      rgba(230, 235, 245, 0)) 1 1;
}

.theme_content_one {
  background: #beeaff;
  border-image: linear-gradient(180deg,
      rgba(255, 255, 255, 1),
      rgba(230, 235, 245, 0)) 1 1;
}

.theme_content_two {
  background: #c4ccff;
  border-image: linear-gradient(180deg,
      rgba(255, 255, 255, 1),
      rgba(230, 235, 245, 0)) 1 1;
}


.divide {
  width: 100%;
  height: 2px;
  background: #fff;
  z-index: 9999;
  position: absolute;
}

.item_height {
  height: 24px
}