'use client'

import React, { useEffect, useRef, useState } from 'react'
import styles from './index.module.scss'
import ThemeHead from './components/theme_head'
import ThemeItem from './components/theme_item'
import ThemeTab from './components/TabCard'
import { useMounted } from '@/lib/hooks/useMounted'
import { getWebItemListByPage } from '@/app/api/api-uchoice/rankings/h5/getRankingInfo'
import { getBannerPage } from '@/app/api/api-uchoice/home/<USER>'
import { isIOS, scaledPx } from '@/lib/client/utils'
import { isLanguageEN } from '@/lib/utils'
import { StickyHeader } from '@/components/StickyHeader'
import { useRouteParams } from '@/lib/hooks/useRouteParams'

const Theme = () => {
  const mounted = useMounted()
  const [itemInfo, setItemInfo] = useState<any>([])
  const [itemInfos, setItemInfos] = useState<any>([])
  const [hasMore, setHasMore] = useState(true)
  const [activeTab, setActiveTab] = useState<any>(1)
  const [tabArr, setTabArr] = useState<any>([])
  const [isProductCategory, setIsProductCategory] = useState<any>(false)
  const routeParams = useRouteParams<any>()

  const idRef = useRef<any>('')
  const lang: any =
    typeof localStorage !== 'undefined'
      ? localStorage.getItem('language')
      : 'en'
  useEffect(() => {
    if (routeParams && routeParams?.week) {
      const { week } = routeParams
      if (week == 'next') {
        setActiveTab(3)
      } else if (week == 'current') {
        setActiveTab(2)
      } else {
        setActiveTab(1)
      }
    }
  }, [activeTab])
  useEffect(() => {
    getHomeList()
  }, [])
  const getHomeList = () => {
    getBannerPage({ type: '4' }).then((res: any) => {
      if (res.code == 200 && res.success) {
        let list = res.result.map(item => {
          return {
            ...item,
            label: item.title,
            value: item.id,
            desc: isLanguageEN() ? item.summaryEn : item.summaryTh
          }
        })
        idRef.current = list[0]?.id
        webItemListByPage(list[0]?.id)
        setTabArr(list)
      }
    })
  }
  const handleTabClick = async (item, index) => {
    await setItemInfos([])
    await setActiveTab(index)
    await setItemInfo([])
    idRef.current = item.id
    webItemListByPage(item.id)
    const currentURL = window.location.href
    const hasIdParam = currentURL.includes('index=')
    let newURL

    if (hasIdParam) {
      newURL = currentURL.replace(/index=\d*/, `index=${index}`)
    } else if (!currentURL.includes('?')) {
      newURL = `${currentURL}?index=${index - 1}`
    } else {
      newURL = `${currentURL}&index=${index - 1}`
    }

    window.history.pushState(null, '', newURL)
  }
  const webItemListByPage = (id?, categoryId?) => {
    getWebItemListByPage({
      id: id
    }).then(res => {
      if (res.code == 200 && res.result) {
        let list: any = []
        setIsProductCategory(res.result.isProductCategory)
        if (!res.result.isProductCategory) {
          list = res?.result?.ttaItems
        } else {
          if (categoryId) {
            list = res?.result?.categoryItems.filter(
              item => item.id == categoryId
            ).ttaItemVos
          } else {
            list = res?.result?.categoryItems[0].ttaItemVos
          }
          setItemInfos(res?.result?.categoryItems)
        }
        if (list.length == 0) {
          setHasMore(false) // 如果没有更多数据了，设置 hasMore 为 false
        }
        if (list.length > 0) {
          setItemInfo([...list])
          setHasMore(true)
        }
      } else {
        setHasMore(false)
      }
    })
  }
  // 做分页的时候的处理逻辑现在暂不做分页
  const fetchData = () => {}
  const getClassBg = () => {
    const classNames = [styles.theme_content]

    if (activeTab == 1) {
      classNames.push(styles.theme_content_one)
    }

    if (activeTab == 2) {
      classNames.push(styles.theme_content_two)
    }

    if (activeTab == 3) {
      classNames.push(styles.theme_content_three)
    }

    return classNames.join(' ')
  }
  const handleCategoryClick = (item, index) => {
    setItemInfo(item.ttaItemVos)
  }
  return (
    <div>
      {mounted && (
        <div
          className={styles.container}
          style={{
            background:
              activeTab == '1'
                ? '#beeaff'
                : activeTab == '2'
                ? '#c4ccff'
                : '#ffc4e6'
          }}
        >
          <div className={styles.fix_head}>
            {tabArr?.length > 0 && (
              <ThemeHead
                handleTabClick={handleTabClick}
                activeTab={activeTab}
                tabArr={tabArr}
              ></ThemeHead>
            )}
          </div>
          {!isProductCategory && tabArr.length > 0 && (
            <div
              className={styles.divide}
              style={{
                top: scaledPx(203)
              }}
            ></div>
          )}
          <div
            className={getClassBg()}
            style={{
              paddingTop: isProductCategory ? 0 : scaledPx(24)
            }}
          >
            {isProductCategory && itemInfos.length > 0 ? (
              <StickyHeader zIndex="3">
                <div
                  className={`${
                    isIOS() ? styles.tab_ios_fixed : styles.tab_fixed
                  } ${getClassBg()}`}
                >
                  <ThemeTab
                    handleTabClick={handleCategoryClick}
                    categoryInfo={itemInfos}
                  ></ThemeTab>
                </div>
              </StickyHeader>
            ) : null}

            <ThemeItem
              itemInfo={itemInfo}
              fetchData={fetchData}
              hasMore={hasMore}
              activeTab={activeTab}
            ></ThemeItem>
          </div>
        </div>
      )}
    </div>
  )
}

export default Theme
