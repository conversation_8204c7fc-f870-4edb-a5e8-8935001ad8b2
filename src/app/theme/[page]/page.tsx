import { metadataTemplate, inApp } from '@/lib/server/utils'
import { isLanguageEN } from '@/lib/utils'
import Inner from './inner'
import { bannerInfo } from '@/app/api/api-uchoice/home/<USER>/fetch'
import React from 'react'
interface Props {
  params: any
  searchParams: {
    index: string
  }
}

export async function generateMetadata({
  params,
  searchParams: { index }
}: Props) {
  if (inApp()) {
    return metadataTemplate()
  }
  const banners = await bannerInfo()

  let bannerItem = banners[index || 0]

  const title = bannerItem?.title || ''
  const description = isLanguageEN()
    ? bannerItem?.summaryEn
    : bannerItem?.summaryTh
  const icon = 'https://file.uchoice.pro/app/res/tt_rank_banner.png'

  return metadataTemplate({ title, description, icon })
}

export default async function Index() {
  return <Inner />
}
