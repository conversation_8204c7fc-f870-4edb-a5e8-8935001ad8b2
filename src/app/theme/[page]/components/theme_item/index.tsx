import React, { useEffect, useState } from 'react'
import styles from './index.module.scss'
import InfiniteScroll from 'react-infinite-scroll-component'
import Image from 'next/image'
import SaleBottom from '@/app/components/SaleBottom'
import { useRouter } from '@/lib/hooks/useRouter'
import { useMounted } from '@/lib/hooks/useMounted'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { i18n } from '@/lib/client/i18n'
import HandlerOnceTap from '@/lib/handlerOnceTap'
import { formatPrice } from '@/lib/format'

const themeSort = index => {
  const sortImgArr = [
    {
      id: '1',
      style_bg: 'top_imgs_one',
      style_title: 'top_title_one',
      style_num: 'top_num_one'
    },
    {
      id: '2',
      style_bg: 'top_imgs_two',
      style_title: 'top_title_two',
      style_num: 'top_num_two'
    },
    {
      id: '3',
      style_bg: 'top_imgs_three',
      style_title: 'top_title_three',
      style_num: 'top_num_three'
    },
    {
      id: '4',
      style_bg: 'top_imgs_four',
      style_title: 'top_title_four',
      style_num: 'top_num_four'
    }
  ]
  const originalIndex = index + 1
  const sortIndex: any = Math.min(originalIndex, 4)
  return sortImgArr.map((item, index) =>
    sortIndex == item.id ? (
      <div
        className={`${styles.top_imgs} ${styles[item.style_bg]}`}
        key={index}
      >
        <div className={`${styles.top_title} ${styles[item.style_title]}`}>
          Top
        </div>
        <div className={`${styles.top_sort} ${styles[item.style_title]}`}>
          {originalIndex}
        </div>
      </div>
    ) : null
  )
}

const ThemeItem = ({ itemInfo, fetchData, hasMore, activeTab }) => {
  const router = useRouter()
  const mounted = useMounted()
  const styleBg = () => {
    const classNames: any = [styles.theme_bg]

    if (activeTab == 1) {
      classNames.push(styles.theme_bg_one)
    }

    if (activeTab == 2) {
      classNames.push(styles.theme_bg_two)
    }

    if (activeTab == 3) {
      classNames.push(styles.theme_bg_three)
    }

    return classNames.join(' ')
  }
  useEffect(() => {
    document.body.addEventListener(
      'touchmove',
      function (e) {
        const target = e.target as HTMLElement // 将 e.target 断言为 HTMLElement 类型
        if (
          target &&
          target instanceof HTMLElement &&
          target.closest('.item_boxs')
        ) {
          e.preventDefault()
        }
      },
      { passive: false }
    )
  }, [])
  return (
    <div className={styles.item_box}>
      {hasMore && itemInfo.length == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.loading} dark></StatusView>
        </div>
      )}
      {!hasMore && itemInfo.length == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.empty} dark></StatusView>
        </div>
      )}
      {mounted && itemInfo && itemInfo.length > 0 && (
        <div className={styles.item_boxs}>
          {itemInfo.map((item, index) => {
            return (
              <div
                className={styles.item_container}
                key={index}
                onClick={() => {
                  HandlerOnceTap(() => {
                    router.push(`${window.location.origin}/product/${item.id}`)
                  })
                }}
              >
                <div className={styles.item}>
                  {themeSort(index)}
                  <div className={styles.item_content}>
                    <Image
                      src={item.image}
                      alt="title"
                      width={192}
                      height={192}
                      className={styles.item_img}
                    />
                    <div className={styles.item_desc}>
                      <div className={styles.item_pruductName}>
                        {item.productName}
                      </div>
                      <div className={styles.item_prices}>
                        <div className={styles.item_comm}>
                          {' '}
                          {i18n.t('LowPrice.佣金')}:
                          <span className={styles.item_comm_num}>
                            {item.commissionRate}
                          </span>
                        </div>
                        <div className={styles.item_earn}>
                          {i18n.t('LowPrice.Earn')}{' '}
                          <span className={styles.item_earn_num}>{`${
                            item.minEarn === item.maxEarn
                              ? formatPrice(item.maxEarn, true)
                              : `${formatPrice(
                                  item.minEarn,
                                  true
                                )}~${formatPrice(item.maxEarn, true)}`
                          }`}</span>
                        </div>
                      </div>
                      {/* 公开佣金 */}
                      <div className={styles.public_commission}>
                        <div className={styles.commission_img}>
                          <span style={{ marginTop: '3px' }}>
                            {item.commissionDiff}
                          </span>
                        </div>
                        <span className={styles.plan_commission}>
                          {i18n.t('LowPrice.公开佣金')}{' '}
                          {item.planCommissionPercent
                            ? item.planCommissionPercent
                            : 0}{' '}
                        </span>
                      </div>
                      <div className={styles.item_sold_btn}>
                        <div className={styles.item_price}>
                          <span className={styles.item_price_num}>
                            {`${
                              item.maxPrice === item.minPrice
                                ? formatPrice(item.minPrice, true)
                                : `${formatPrice(
                                    item.minPrice,
                                    true
                                  )}~${formatPrice(item.maxPrice, true)}`
                            }`}
                          </span>{' '}
                        </div>

                        <div className={styles.item_btn}>
                          {i18n.t('LowPrice.FreeSample')}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <SaleBottom item={item} styleBg={styleBg()}></SaleBottom>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default ThemeItem
