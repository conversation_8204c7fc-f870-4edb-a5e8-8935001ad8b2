.item_container {
  // display: flex;
  // flex-direction: column;
  // flex: 1;
  background-color: #fff;
  margin-bottom: 12px;
  box-sizing: border-box;
  position: relative;
  border-radius: 4px;


}

.item {
  display: flex;
  flex: 1;
  padding: 24px;
  background-color: #fff;
  margin-bottom: 12px;
  box-sizing: border-box;
  position: relative;
  border-radius: 4px;

}

.item_box {
  flex: 1;
  // overflow-y: auto;
  // -webkit-overflow-scrolling: touch;
  margin-left: 24px;
  margin-right: 24px;
}

.item_boxs {
  flex: 1;
  margin-bottom: 124px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.load_box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 250px;
}

.item_desc {
  flex: 1;
}

.top_imgs {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 72px;
  height: 80px;
  box-sizing: border-box;
  padding: 2px 0 16px 0;
  position: absolute;
  top: 0
}

.top_imgs_one {
  background-image: url('../../../../../../public/images/theme/icon_top1.png');
  background-size: cover;
  background-size: 72px 80px;
}

.top_imgs_two {
  background-image: url('../../../../../../public/images/theme/icon_top2.png');
  background-size: cover;
  background-size: 72px 80px;
}

.top_imgs_three {
  background-image: url('../../../../../../public/images/theme/icon_top3.png');
  background-size: cover;
  background-size: 72px 80px;
}

.top_imgs_four {
  background-image: url('../../../../../../public/images/theme/icon_top4.png');
  background-size: cover;
  background-size: 72px 80px;
}

.top_title {
  font-size: 24px;
  font-weight: normal;
  color: #b46f27;
}

.top_title_one {
  color: #b46f27;
}

.top_title_two {
  color: #1c3e56;
}

.top_title_three {
  color: #792e2f;
}

.top_title_four {
  color: #4d5a6d;
}

.top_sort {
  font-size: 28px;
  font-weight: bold;
  margin-top: -8px;
}

.top_sort_one {
  color: #b46f27;
}

.top_sort_two {
  color: #1c3e56;
}

.top_sort_three {
  color: #792e2f;
}

.top_sort_three {
  color: #792e2f;
}

.top_sort_four {
  color: #4d5a6d;
}

.item_content {
  display: flex;
  flex: 1;
  justify-content: space-between;
}

.item_pruductName {
  height: 80px;
  font-size: 28px;
  font-weight: 400;
  color: #303030;
  line-height: 42px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;

}

.item_prices {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 8px 0;
}

.item_img {
  width: 192px;
  height: 192px;
  margin-right: 20px;
}

.serial_num {
  font-size: 28px;
  font-weight: bold;
  color: #6a5322;
  line-height: 32px;
}

.sold_img {
  width: 20px;
  height: 24px;
  margin-right: 8px;
}

.stock {
  font-size: 22px;
  font-weight: normal;
  color: #303030;
  line-height: 22px;
}

.item_price {
  font-size: 24px;
  font-weight: 600;
  color: #303030;
  line-height: 36px;
}

.item_comm {
  height: 32px;
  background: rgba(197, 68, 93, 0.1);
  border-radius: 2px;
  padding: 0 8px;
  font-size: 22px;
  font-weight: normal;
  color: #c5445d;
}

.item_sold_btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 18px;
}

.item_earn {
  font-size: 24px;
  font-weight: 400;
  color: #fe6d45;
  line-height: 36px;
}

.item_earn_num {
  font-size: 24px;
  font-weight: 400;
  color: #fe6d45;
  line-height: 36px;
  font-weight: bold;
}

.item_comm_num {
  font-size: 22px;
  font-weight: 400;
  color: #c5445d;
  line-height: 24px;
  font-weight: bold;
}

.item_sold {
  display: flex;
}

.stock {
  font-size: 22px;
  font-weight: normal;
  color: #303030;
  line-height: 22px;
}

.sold {
  font-size: 22px;
  font-weight: normal;
  color: #303030;
  line-height: 22px;
}

.item_btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 24px;
  height: 48px;
  background: #fe6d45;
  border-radius: 2px;
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
}

.commission_img {
  min-height: 28px;
  min-width: 60px;
  display: flex;
  // flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  background-image: url('../../../../../../public/images/hot/icon_house.png');
  background-size: cover;
  background-size: 100% 100%;
  box-sizing: border-box;
  font-size: 22px;
  font-weight: normal;
  color: #FFFFFF;

}

.public_commission {
  display: flex;
  align-items: center;
}

.plan_commission {
  font-size: 22px;
  font-weight: normal;
  color: #9a9a9a;
  margin-left: 8px;
  text-decoration: line-through;
}

.theme_bg {
  display: flex;
  justify-content: space-between;
  align-items: start;
  padding: 14px 24px;
  // height: 100px;
  border-radius: 0px 0px 4px 4px;
}

.theme_bg_three {
  background: linear-gradient(180deg, #ffe9ef 0%, #ffffff 100%);
}

.theme_bg_two {
  background: linear-gradient(180deg, #ece9ff 0%, #ffffff 100%);
}

.theme_bg_one {
  background: linear-gradient(180deg, #e5f7ff 0%, #ffffff 100%);
}

.loadingStatus {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}