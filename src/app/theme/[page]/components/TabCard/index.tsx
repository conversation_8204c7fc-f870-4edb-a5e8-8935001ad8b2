import React, { useEffect, useState } from 'react'
import { Tabs } from 'react-vant'
import './index.scss'
import styles from './index.module.scss'
import { px2rem } from '@/lib/client/utils'

const CommonTab = ({ handleTabClick, categoryInfo }) => {
  const lang: any = localStorage.getItem('language')
  return (
    <div className={styles.tab_box}>
      <Tabs
        defaultActive={0}
        swipeThreshold={1}
        lineWidth={px2rem(32)}
        duration={800}
        scrollspy={false}
        onClickTab={async event => {
          let tabIndex = await event.index
          const item: any = (await categoryInfo) ? categoryInfo[tabIndex] : ''
          handleTabClick(item)
        }}
      >
        {(categoryInfo || []).map((item: any, index: any) => (
          <Tabs.TabPane
            key={index}
            title={lang == 'en' ? item.categoryNameEn : item.categoryNameTh}
            titleClass={styles.tab_title}
          >
            {item.id}
          </Tabs.TabPane>
        ))}
      </Tabs>
    </div>
  )
}

export default CommonTab
