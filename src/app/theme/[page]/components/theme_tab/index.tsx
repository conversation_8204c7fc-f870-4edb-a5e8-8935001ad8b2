import React, { useEffect, useState } from 'react'
import styles from './index.module.scss'
import { Tabs } from 'antd-mobile'
import './index.scss'
import { rpxToPx } from '@/lib/client/utils'
const ThemeTab = ({ handleTabClick, tabArr, activeTab }) => {
  return (
    <div className={styles.tab_container}>
      <div className={styles.tab_buttons}>
        <Tabs
          defaultActiveKey={tabArr[activeTab - 1].id || tabArr[0].id}
          style={{
            '--title-font-size': `${rpxToPx(28)}`,
            '--active-line-color': '#303030'
          }}
          onChange={key => {
            let item = tabArr.filter(item => item.id == key)[0]
            let index = tabArr.findIndex(item => item.id == key)

            handleTabClick(item, index + 1)
          }}
        >
          {(tabArr || []).map(item => (
            <Tabs.Tab title={item.label} key={item.id} />
          ))}
        </Tabs>
      </div>
    </div>
  )
}

export default ThemeTab
