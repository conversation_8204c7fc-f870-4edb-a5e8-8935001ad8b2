import React, { useEffect, useMemo, useState } from 'react'
import Image from 'next/image'
import icon_back from '@/../public/images/low_price/icon_back.png'
import icon_share from '@/../public/images/low_price/icon_share.png'
import styles from './index.module.scss'
import ThemeTab from '../theme_tab'
import icon_one_left from '@/../public/images/theme/icon_one_left.png'
import icon_one_right from '@/../public/images/theme/icon_one_right.png'
import icon_two_left from '@/../public/images/theme/icon_two_left.png'
import icon_two_right from '@/../public/images/theme/icon_two_right.png'
import icon_three_left from '@/../public/images/theme/icon_three_left.png'
import icon_three_right from '@/../public/images/theme/icon_three_right.png'
import { webview } from '@/lib/client/webview'
import { px2rem } from '@/lib/client/utils'
import { useMounted } from '@/lib/hooks/useMounted'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useRouter } from '@/lib/hooks/useRouter'
import { useWindowScroll } from '@/lib/hooks/useWindowScroll'
import { useIntersectionObserver } from '@/lib/hooks/useIntersectionObserver'

const themeArr = [
  {
    icon_left: icon_one_left,
    icon_right: icon_one_right,
    title: '',
    desc: '',
    title_style: 'title_one',
    desc_style: 'desc_one'
  },
  {
    icon_left: icon_two_left,
    icon_right: icon_two_right,
    title: '',
    desc: '',
    title_style: 'title_two',
    desc_style: 'desc_two'
  },
  {
    icon_left: icon_three_left,
    icon_right: icon_three_right,
    title: '',
    desc: '',
    title_style: 'title_three',
    desc_style: 'desc_three'
  }
]
const ThemeStyle = (activeTab, tabArr) => {
  for (let i in themeArr) {
    for (let j in tabArr) {
      if (i == j) {
        themeArr[i].title = tabArr[j].title
        themeArr[i].desc = tabArr[j].desc
      }
    }
  }

  return themeArr.map((item, index) =>
    activeTab == index + 1 ? (
      <div key={index} className={styles.icons_box}>
        <div className={styles.icons_imgs}>
          <Image src={item.icon_left} alt="title" className={styles.sign_img} />
          <span className={`${styles[item.title_style]} ${styles['title']}`}>
            {item.title}
          </span>
          <Image
            src={item.icon_right}
            alt="title"
            className={styles.sign_img}
          />
        </div>
        {item.desc && (
          <div className={`${styles[item.desc_style]}`}>
            <div className={styles.desc}>{item.desc}</div>
          </div>
        )}
      </div>
    ) : null
  )
}

const ThemeHead = ({ activeTab, handleTabClick, tabArr }) => {
  const mounted = useMounted()
  const router = useRouter()
  const { ref, isLeave } = useIntersectionObserver()

  const headBg = () => {
    switch (activeTab) {
      case 1:
        return styles.head_bg_1
      case 2:
        return styles.head_bg_2
      case 3:
        return styles.head_bg_3
      default:
        return null
    }
  }

  return mounted ? (
    <div
      className={`${styles['head_bg']}  ${headBg()}`}
      style={{
        paddingTop: webview ? `${webview?.getData().topSafeArea}px` : '12px',
        backgroundColor: isLeave ? '#fff' : ''
        // position:isLeave?'relative':'fixed'
      }}
    >
      <div ref={ref}></div>
      <div
        className={styles.fix_tab}
        style={{
          backgroundColor: isLeave ? '#fff' : '',
          paddingTop: webview ? `${webview?.getData().topSafeArea}px` : '0',
          marginTop: webview ? '' : `${isLeave ? '38px' : '44px'}`
        }}
      >
        <div className={webview ? styles.title_box : styles.title_box_h5}>
          {webview && (
            <div
              onClick={() => {
                router.back()
              }}
            >
              {' '}
              <Image src={icon_back} alt="icon" className={styles.icon_back} />
            </div>
          )}
          <ThemeTab
            handleTabClick={handleTabClick}
            tabArr={tabArr}
            activeTab={activeTab}
          ></ThemeTab>

          {webview && (
            <div
              onClick={() =>
                webview?.send(WebviewEvents.shareUrl, {
                  url: window.location.href
                })
              }
            >
              <Image
                src={icon_share}
                alt="icon"
                className={styles.icon_share}
              />
            </div>
          )}
        </div>
      </div>

      {ThemeStyle(activeTab, tabArr)}
    </div>
  ) : null
}

export default ThemeHead
