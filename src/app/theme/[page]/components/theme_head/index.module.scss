.head_bg {
  width: 750px;
  height: 406px;
  box-sizing: border-box;
  padding: 24px 0 20px 0;
  overflow: hidden;
}

.head_bg_1 {
  background-image: url('../../../../../../public/images/theme/theme1.png');
  background-size: cover;
  background-size: 750px 406px;
}

.head_bg_2 {
  background-image: url('../../../../../../public/images/theme/theme2.png');
  background-size: cover;
  background-size: 750px 406px;
}

.head_bg_3 {
  background-image: url('../../../../../../public/images/theme/theme3.png');
  background-size: cover;
  background-size: 750px 406px;
}


.title_box {
  display: flex;
  flex: 1;
  justify-content: space-between;
  align-items: center;
}

.title_box_h5 {
  display: flex;
  flex: 1;
  justify-content: space-between;
  align-items: center;
  // margin-top: 30px;
  margin-left: 24px;
}

.title_name {
  font-size: 52px;
  font-weight: bold;
  color: #ff7431;
  background: linear-gradient(180deg, #ffe8dc 0%, #fe6d45 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.icon_back {
  width: 40px;
  height: 40px;
}

.icon_share {
  width: 40px;
  height: 40px;
}

.title {
  font-size: 36px;
  font-weight: bold;
  color: #ffffff;
}

.icons_imgs {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 24px;
  margin-bottom: 24px;
}

.sign_img {
  width: 44px;
  height: 88px;
}

.icons_box {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 128px;
  padding: 0 24px;
}

.desc {
  display: inline;
  max-height: 42px;
  line-height: 42px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;

}

.title_three {
  font-size: 52px;
  font-weight: bold;
  color: #410519;
  line-height: 60px;
}

.desc_three {
  display: flex;
  justify-content: center;
  // align-items: center;
  font-size: 26px;
  font-weight: normal;
  color: #410519;
  // height: 48px;
  background: linear-gradient(270deg,
      rgba(205, 55, 157, 0) 0%,
      rgba(195, 14, 137, 0.15) 51%,
      rgba(205, 55, 157, 0) 100%);
  border: 1px solid;
  border-image: linear-gradient(270deg,
      rgba(244, 247, 251, 0.15),
      rgba(255, 244, 251, 0.8),
      rgba(230, 235, 245, 0.15)) 1 1;
}

.title_two {
  font-size: 52px;
  font-weight: bold;
  color: #410519;
}

.desc_two {
  display: flex;
  justify-content: center;
  // align-items: center;
  font-size: 26px;
  color: #410519;
  // height: 48px;
  background: linear-gradient(270deg,
      rgba(87, 53, 212, 0) 0%,
      rgba(14, 31, 195, 0.15) 51%,
      rgba(87, 53, 212, 0) 100%);
  border: 1px solid;
  border-image: linear-gradient(270deg,
      rgba(244, 247, 251, 0.1),
      rgba(244, 244, 255, 0.8),
      rgba(230, 235, 245, 0.1)) 1 1;
}

.title_one {
  font-size: 52px;
  font-weight: bold;
  color: #052641;
  line-height: 60px;
}

.desc_one {
  display: flex;
  justify-content: center;
  // align-items: center;
  font-size: 26px;
  color: #052641;
  // height: 48px;
  background: linear-gradient(270deg,
      rgba(48, 172, 209, 0.01) 0%,
      rgba(14, 153, 195, 0.2) 51%,
      rgba(48, 172, 209, 0.01) 100%);
  border: 1px solid;
  border-image: linear-gradient(270deg,
      rgba(244, 247, 251, 0.15),
      rgba(244, 249, 255, 0.9),
      rgba(230, 235, 245, 0.15)) 1 1;
}

.fix_tab {
  position: fixed;
  z-index: 99999;
  padding: 0 24px;
  width: 750px;
  top: 0;

}