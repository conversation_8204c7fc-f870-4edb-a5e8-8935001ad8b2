import { i18n } from '@/lib/client/i18n'
import { AppColors } from '@/lib/const'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { isRegionTH } from '@/lib/utils'
import React, { useEffect, useState } from 'react'
import rank_selection_top_right from '@/../public/images/ranking/rank_selection_top_right.png'
import rank_selection_bottom_left from '@/../public/images/ranking/rank_selection_bottom_left.png'
import { Tabs } from 'antd-mobile'
import { Image } from '@/components/Image'
import './index.css'

interface Props {
  onChange(id: number): void
}

export const RanksBar = ({ onChange }: Props) => {
  const params = useRouteParams<{ index: string }>()
  const [selectedId, setSelectedId] = useState(1)
  const [ranks, setRanks] = useState<{ id: number; name: string }[]>([])

  useEffect(() => {
    const ranks = [
      { id: 1, name: i18n.t('Product.Most sample requests') },
      { id: 4, name: i18n.t('Product.Most sold units') }
    ]

    if (isRegionTH()) {
      ranks.splice(
        1,
        0,
        { id: 2, name: i18n.t('Product.Most add wishlists') },
        { id: 3, name: i18n.t('Product.Most add showcase') }
      )
    }

    setRanks(ranks)
  }, [])

  useEffect(() => {
    if (params?.index && params.index !== '0') {
      beforeSelectId(Number(params?.index) + 1, true)
    }
  }, [])

  const beforeSelectId = (id, initial = false) => {
    console.log('beforeSelectId', id, typeof id)
    setSelectedId(id)

    if (!initial) {
      statistic({
        eventName: [
          EventName.ranking_type_1,
          EventName.ranking_type_2,
          EventName.ranking_type_3,
          EventName.ranking_type_4
        ][id - 1]
      })

      onChange(id)
    }
  }

  return (
    <div className="ranks-bar category-tabs reletive flex h-[60px] w-[750px] items-center bg-[#140801] pr-[24px]">
      <Tabs
        style={{
          // @ts-ignore
          '--active-line-height': 0
        }}
        type="jumbo"
        background="rgba(0,0,0,0)"
        activeKey={`${selectedId}`}
        onChange={id => {
          beforeSelectId(Number(id))
        }}
      >
        {ranks.map(({ id, name }) => (
          <Tabs.Tab
            key={id}
            title={
              <div className="relative ml-[24px] min-w-[120px]">
                <div
                  className="flex h-[60px] items-center justify-center rounded-[30px] border-[2px] px-[20px] text-[24px]"
                  style={{
                    border: id === selectedId ? '#9E702B' : '#664D3B',
                    background:
                      id === selectedId
                        ? 'linear-gradient( 270deg, #FBD090 0%, #FAEFD0 51%, #F9CD90 100%)'
                        : 'linear-gradient( 270deg, #3C2C1F 0%, #534837 51%, #392C1F 100%)',
                    color: id === selectedId ? '#674118' : '#F6EBCE'
                  }}
                >
                  {name}
                </div>
                {id !== selectedId && (
                  <>
                    <div className="absolute right-0 top-0 h-[32px] w-[84px]">
                      <Image
                        src={rank_selection_top_right}
                        className="h-[32px] w-[84px]"
                      ></Image>
                    </div>
                    <div className="absolute bottom-0 left-0 h-[32px] w-[84px]">
                      <Image
                        src={rank_selection_bottom_left}
                        className="h-[32px] w-[84px]"
                      ></Image>
                    </div>
                  </>
                )}
              </div>
            }
          ></Tabs.Tab>
        ))}
      </Tabs>
    </div>
  )
}
