import {
  GetTapRankingListType,
  TapRankItemVo
} from '@/app/api/api-uchoice/rankings/getTapRankingList/dtos'
import { Rank } from '@/app/components/Rank'
import { Earn } from '@/components/Earn'
import { Image } from '@/components/Image'
import { YoupikHighCom } from '@/components/YoupikHighCom'
import { makeSureInApp, resizeImageUrl } from '@/lib/client/utils'
import {
  formatCount,
  formatPrice,
  formatSales,
  formatThous
} from '@/lib/format'
import React, { useState } from 'react'
import { Trans } from 'react-i18next'
import icon_fire from '@/../public/images/ranking/icon_fire.png'
import like from '@/../public/images/ranking/like.png'
import rank_item_cell from '@/../public/images/ranking/rank_item_cell.png'
import { loading } from '@/lib/client/loading'
import { updateMemberCollect } from '@/app/api/api-uchoice/tt/h5/updateMemberCollect'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useRouter } from '@/lib/hooks/useRouter'
import { i18n } from '@/lib/client/i18n'
import { Notice } from '../Notice'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { addToShowCase, checkAuth } from '@/lib/actions'
import { TapRankItemInfoVo } from '@/app/api/api-uchoice/rankings/getTapRankingListV2/dtos'

interface Props {
  index: number
  item: TapRankItemInfoVo
}

export const ItemCell = ({ index, item }: Props) => {
  const onBuyShareLink = e => {
    e.stopPropagation()

    statistic({
      eventName: EventName.buy_share_link,
      param: {
        id: item.id
      }
    })

    window.open(item.affiliateShareLink)
  }

  return (
    <>
      <div
        onClick={onBuyShareLink}
        className="relative mt-[10px] flex items-center justify-center bg-[#140801] pb-[4px]  "
        id={`top_${item.rn}`}
      >
        <Image src={rank_item_cell} className="h-[280px] w-[702px] "></Image>
        <div className="absolute top-[26px] w-[662px] rounded-[16px] bg-white p-[20px]">
          <div className="flex">
            <img
              src={resizeImageUrl(item.image, 180)}
              className="size-[180px] rounded-[8px]"
            ></img>
            <div className="flex flex-1 flex-col justify-between overflow-hidden pl-[20px]">
              <div className="">
                <div className="truncate text-[28px] text-black">
                  {item.productName}
                </div>
                <div className="mt-[8px] flex items-start text-[24px] text-[#fe6d45]">
                  <Image
                    src={icon_fire}
                    className="mt-[2px] h-[28px] w-[28px]"
                  ></Image>
                  <div>
                    {i18n.t('Ranking.30日销售超过', {
                      num: formatSales(item?.salesForLast30Days)
                    })}
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap  items-center justify-between">
                <div className="text-[26px] font-bold text-black">
                  {formatPrice(item.minPrice, true)}
                </div>
                <div
                  className="flex h-[56px] items-center justify-center rounded-[4px] border-2 border-primary bg-primary px-[20px] touch-opacity"
                  onClick={onBuyShareLink}
                >
                  <span className="text text-center text-[24px] font-bold text-white">
                    {i18n.t('Ranking.立即抢购')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute left-[20px] top-[9px]">
          {index < 30 && <Rank index={index}></Rank>}
        </div>
      </div>
    </>
  )
}
