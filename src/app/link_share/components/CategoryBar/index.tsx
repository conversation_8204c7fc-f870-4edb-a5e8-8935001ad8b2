import { StickyHeader } from '@/components/StickyHeader'
import React, { useEffect, useState } from 'react'
import category_collapse_on from '@/../public/images/ranking/category_collapse_on.png'
import category_collapse_off from '@/../public/images/ranking/category_collapse_off.png'
import { Image } from '@/components/Image'
import { makeBodyScrollable, px2rem } from '@/lib/client/utils'
import { isLanguageEN } from '@/lib/utils'
import { TtCustomCategory } from '@/app/api/api-uchoice/rankings/getTapRankingList/dtos'
import { Tabs } from 'antd-mobile'
import './index.css'
import { RanksBar } from '../RanksBar'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { webview } from '@/lib/client/webview'

interface Props {
  categories: TtCustomCategory[]
  onChange(it): void
}

export const CategoryBar = ({ categories, onChange }: Props) => {
  const [expanded, setExpanded] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const params = useRouteParams<{ categoryId: string }>()

  useEffect(() => {
    if (categories.length > 0 && params?.categoryId) {
      setSelectedIndex(
        categories.findIndex(item => item.id === Number(params?.categoryId))
      )
    }
  }, [categories])

  useEffect(() => {
    onChange(categories[selectedIndex])
  }, [selectedIndex])

  const makeExpanded = (value: boolean) => {
    setExpanded(value)
    makeBodyScrollable(!value)
  }

  return (
    <StickyHeader height={px2rem(102 + 20)}>
      <div className="">
        <div className="relative h-[102px] bg-[#140801]">
          <div className="reletive">
            <div
              className="category-tabs reletive absolute    flex h-[85px] w-[750px] items-center bg-[#140801] pr-[80px]"
              style={{ top: `${!webview ? '24px' : 0}` }}
            >
              <Tabs
                style={{
                  // @ts-ignore
                  '--active-line-height': 0
                }}
                type="jumbo"
                background="rgba(0,0,0,0)"
                activeKey={`${selectedIndex}`}
                onChange={index => {
                  setSelectedIndex(Number(index))
                  makeExpanded(false)
                }}
              >
                {categories.map(({ id, name, nameEn }, index) => (
                  <Tabs.Tab key={index} title={name}></Tabs.Tab>
                ))}
              </Tabs>
              <div
                className="absolute right-0 top-[14px] touch-opacity"
                onClick={() => {
                  makeExpanded(!expanded)
                }}
              >
                <Image
                  src={expanded ? category_collapse_on : category_collapse_off}
                  className="h-[62px] w-[102px]"
                ></Image>
              </div>
            </div>
          </div>

          {expanded && (
            <div
              className="absolute inset-x-0 top-[120px] z-10 h-screen bg-[rgba(0,0,0,0.4)]"
              onClick={() => makeExpanded(false)}
            >
              <div
                className="rounded-b-[20px] px-[28px] pt-[28px]"
                style={{
                  background:
                    'linear-gradient( 180deg, #140801 0%, #675948 100%)'
                }}
              >
                <div className="flex flex-wrap">
                  {categories.map(({ id, name, nameEn }, index) => (
                    <div
                      className="flex pb-[28px] pr-[28px]"
                      key={`${id}_${index}`}
                      onClick={() => {
                        setSelectedIndex(index)
                        makeExpanded(false)
                      }}
                    >
                      <div
                        className="flex h-[60px] items-center justify-center rounded-[30px] border-2 px-[34px]"
                        style={{
                          background:
                            index === selectedIndex
                              ? 'linear-gradient( 270deg, #FBD090 0%, #FAEFD0 51%, #F9CD90 100%)'
                              : 'linear-gradient( 270deg, #3C2C1F 0%, #534837 51%, #392C1F 100%)',
                          borderColor:
                            index === selectedIndex ? '#9E702B' : '#664D3B'
                        }}
                      >
                        <span
                          className="text-[26px]"
                          style={{
                            fontWeight:
                              index === selectedIndex ? 'bold' : 'normal',
                            color:
                              index === selectedIndex ? '#674118' : '#F6EBCE'
                          }}
                        >
                          {isLanguageEN() ? nameEn : name}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
        {/* <div className="h-[22px] w-full bg-[#140801]"></div> */}
      </div>
    </StickyHeader>
  )
}
