'use client'

import {
  TransparentNavPage,
  TopTransparentCard
} from '@/components/TransparentNavPage'
import { useEffect, useRef, useState } from 'react'
import { Image } from '@/components/Image'
import ranking_header_bg from '@/../public/images/ranking/rank_title_bg.png'
import rank_title_left from '@/../public/images/ranking/rank_title_left.png'
import rank_title_right from '@/../public/images/ranking/rank_title_right.png'
import { Ranks } from './components/Ranks'
import { CategoryBar } from './components/CategoryBar'
import { webview } from '@/lib/client/webview'
import { ItemCell } from './components/ItemCell'
import { useBlackBody } from '@/lib/hooks/useGrayBody'
import { ShareLinkItemCategory } from '../api/api-uchoice/rankings/getTapRankingList/dtos'
import StatusView, { StatusViewType } from '../components/StatusView'
import { useRequest } from 'ahooks'
import { i18n } from '@/lib/client/i18n'
import { isLanguageEN, isRegionTH, isRegionVN } from '@/lib/utils'
import {
  getShareLinkItemCategory,
  getTtaCategoryInfo
} from '../api/api-mall/tt/item/categoryInfo'
import { getShareLinkItemListByPage } from '../api/api-uchoice/rankings/getTapRankingListV2/request'
import { isIOS } from '@/lib/client/utils'
import styles from './index.module.scss'
import { InfiniteScroll, PullToRefresh } from 'antd-mobile'
import { loading } from '@/lib/client/loading'
import { cancelAllRequests, cancelRequest } from '@/lib/client/request'

export function Inner() {
  useBlackBody()

  const [categoryId, setCategoryId] = useState(0)
  const [categoryName, setCategoryName] = useState(
    i18n.t('Ranking.tiktok今日热卖榜')
  )
  const [finished, setFinished] = useState<boolean>(false)
  const pageNoRef = useRef(1)
  const totalRef = useRef(1)
  const [categoryList, setCategoryList] = useState<ShareLinkItemCategory[]>([])
  const [items, setItems] = useState<any>([])
  const nodataRef = useRef(false)

  const fetchCategoryList = async () => {
    if (categoryList.length === 0) {
      let categories: ShareLinkItemCategory[] = []
      if (categories.length === 0) {
        const categoriesRes = await getShareLinkItemCategory()
        if (categoriesRes.code === 200) {
          categories = categoriesRes.result
        }
      }

      categories = [
        {
          name: isRegionTH() ? 'ทั้งหมด' : 'Tất cả',
          nameEn: i18n.t('Ranking.recommend'),
          id: 0
        },
        ...categories
      ]
      const categoriesNew = categories.map((item, index) => {
        return {
          ...item,
          name: isLanguageEN() ? item.nameEn : item.name
        }
      })
      setCategoryList(categoriesNew)
    }
  }
  const fetchTapRankingList = async (category_Id?) => {
    cancelRequest('getShareLinkItemListByPage')

    setFinished(items.length == totalRef.current && totalRef.current != 0)
    nodataRef.current =
      (items.length == totalRef.current && totalRef.current != 0) ||
      pageNoRef.current > 10
    if (
      (finished && categoryList.length === 0) ||
      nodataRef.current ||
      pageNoRef.current > 10
    )
      return
    const params: any = {
      pageNo: pageNoRef.current,
      pageSize: '10',
      categoryId: category_Id || category_Id === 0 ? category_Id : categoryId,
      highCommission: false
    }
    try {
      const { code, result } = await getShareLinkItemListByPage(params)
      if (code === 200) {
        if (result?.list.length > 0) {
          pageNoRef.current == 1
            ? setItems([...result.list])
            : setItems(items.concat(result.list))
          totalRef.current = result.total
          if (nodataRef.current) {
            return
          }
          pageNoRef.current++
        } else {
          setFinished(true)
        }
      }
    } finally {
    }
  }
  useEffect(() => {
    fetchCategoryList()
  }, [])
  const InfiniteScrollContent = ({ hasMore }: { hasMore?: boolean }) => {
    return (
      <>
        {hasMore ? (
          <div className={styles.no_more}>Loading...</div>
        ) : (
          <div className={styles.no_more}>
            {i18n.t('TiktokData.无更多数据')}
          </div>
        )}
      </>
    )
  }
  const resetParams = () => {
    pageNoRef.current = 1
    setItems([])
    totalRef.current = 0
  }
  return (
    <TransparentNavPage
      hide={!webview}
      hideTitleWhenTransparent
      title={i18n.t('Ranking.TikTok今日巨划算')}
      theme="light"
    >
      <div>
        {!webview ? (
          <div className={styles.nav_box}>
            {i18n.t('Ranking.TikTok今日巨划算')}
          </div>
        ) : null}

        <div className="w-[750px] bg-[#140801]">
          <div className="relative">
            <div className="h-[100px] w-[750px] bg-[#140801]"></div>
            {!isIOS() && (
              <div className="absolute left-0 top-0 w-full bg-black"></div>
            )}
          </div>
          <Image
            src={ranking_header_bg}
            className="h-[220px] w-[750px]"
          ></Image>
          <div className="absolute top-[80px] h-[220px] w-[750px]">
            <TopTransparentCard>
              <div className="flex items-center justify-center pt-[60px]">
                <Image
                  src={rank_title_left}
                  className="h-[76px] w-[36px]"
                ></Image>
                <span className="mx-[24px] text-[40px] font-bold leading-[48px] text-[#FFDBB2]">
                  {categoryName && categoryId != 0
                    ? categoryName
                    : i18n.t('Ranking.tiktok今日热卖榜')}
                </span>
                <Image
                  src={rank_title_right}
                  className="h-[76px] w-[36px]"
                ></Image>
              </div>
            </TopTransparentCard>
          </div>
        </div>

        {/* @ts-ignore */}
        {categoryList.length > 0 && (
          <CategoryBar
            categories={categoryList}
            onChange={it => {
              setCategoryName(it.name)
              setCategoryId(it.id)
              resetParams()
              fetchTapRankingList(it.id)
            }}
          ></CategoryBar>
        )}

        <div className="bg-[#140801] px-[24px]">
          {!finished && items.length == 0 ? (
            <div className="pt-[200px]">
              <StatusView status={StatusViewType.loading} dark></StatusView>
            </div>
          ) : items.length === 0 ? (
            <div className="pt-[200px]">
              <StatusView status={StatusViewType.empty} dark></StatusView>
            </div>
          ) : (
            <PullToRefresh
              onRefresh={async () => {
                pageNoRef.current = 1
                totalRef.current = 0
                fetchTapRankingList(categoryId)
              }}
            >
              {items?.map((item, index) => (
                <ItemCell
                  key={`${index}_${item.id}`}
                  index={index}
                  item={item}
                ></ItemCell>
              ))}
              <InfiniteScroll
                loadMore={fetchTapRankingList}
                hasMore={!finished}
              >
                <InfiniteScrollContent hasMore={!finished} />
              </InfiniteScroll>
            </PullToRefresh>
          )}
        </div>
      </div>
    </TransparentNavPage>
  )
}
