.score_page_container {
  min-height: 100vh;
  background-color: #F2F2F2;
  position: relative;
  overflow-y: scroll;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;

  .padding_top_container {
    width: 750px;
    height: 88px;
    background-color: #ffffff;
  }

  .bg_score_scontainer {
    position: absolute;
    left: 0;
    right: 0;
    width: 750px;
    height: 660px;
  }

  .score_content_container {
    position: absolute;
    left: 0;
    right: 0;
    top: 128px;

    .update_label {
      font-size: 26px;
      color: #FFFFFF;
      line-height: 36px;
      margin-bottom: 18px;
      margin-right: 24px;
      text-align: right;
    }
  }
}
