/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import React, { useEffect, useRef, useState } from 'react'
import { i18n } from '@/lib/client/i18n'
import dayjs from 'dayjs'
import Image from 'next/image'
import bg_score from '@/../public/images/fmcg/score/bg_page.png'
import bg_page_short from '@/../public/images/fmcg/score/bg_page_short.png'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import ScoreCard from './components/ScoreCard'
import ScoreListCard from './components/ScoreListCard'
import { webview } from '@/lib/client/webview'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { getSellerCampaignPointsSummary } from '@/app/api/api-uchoice/seller/campaigns/pointsSummary'
import { rpxToPx } from '@/lib/client/utils'
import styles from './index.module.scss'

interface ScorePageProps {
  id: string
}

function getQueryParams() {
  if (typeof window === 'undefined') {
    return {}
  }
  const search = window.location.search.substring(1)
  return Object.fromEntries(new URLSearchParams(search))
}

const ScorePage: React.FC<ScorePageProps> = ({ id }) => {
  const ref = useRef<any>()
  const [isLeave, setIsLeave] = useState(false)
  const [pointsSummary, setPointsSummary] = useState<any>({})

  useEffect(() => {
    let intersectionObserver: IntersectionObserver | null = null
    setTimeout(() => {
      if (ref.current) {
        // @ts-ignore
        intersectionObserver?.disconnect()

        intersectionObserver = new IntersectionObserver(
          entries => {
            entries.forEach(entry => {
              setIsLeave(!entry.isIntersecting)
            })
          },
          {
            threshold: 0
          }
        )
        intersectionObserver.observe(ref.current)
      }
    }, 500)

    return () => {
      intersectionObserver?.disconnect()
    }
  }, [])

  const requestSellerCampaignPointsSummary = async () => {
    const res: any = await getSellerCampaignPointsSummary(
      id,
      getQueryParams().dataVersion || Date.now().toString()
    )
    if (res.code === 200 && res.result) {
      setPointsSummary(res.result)
    }
  }

  useEffect(() => {
    requestSellerCampaignPointsSummary()
  }, [])

  return (
    <TransparentNavPage
      title={i18n.t('Activity.积分明细')}
      theme={isLeave ? 'dark' : 'light'}
      titleTheme={isLeave ? 'dark' : 'light'}
      backgroundColor={isLeave ? 'white' : 'transparent'}
      showBackButton
      showContactUsButton={!!webview}
      onShared={() =>
        statistic({ eventName: EventName.rankinghit_score_share })
      }
    >
      <div className={styles.score_page_container}>
        <div ref={ref}></div>
        <div className={styles.bg_score_scontainer}>
          <Image src={webview ? bg_score : bg_page_short} alt="" className={webview ? styles.bg_score : styles.bg_score_short}></Image>
        </div>
        <div className={styles.score_content_container} style={{ top: webview ? webview?.getData().topSafeArea + rpxToPx(128) : rpxToPx(128) }}>
          <ScoreCard pointsSummary={pointsSummary}></ScoreCard>
          {pointsSummary.dataUpdateTime && <div className={styles.update_label}>
            {i18n.t('Activity.数据更新')}
            {dayjs(pointsSummary.dataUpdateTime).format(
              'HH:mm:ss DD/MM/YYYY'
            )}
          </div>}
          <ScoreListCard id={id}></ScoreListCard>
        </div>
      </div>
    </TransparentNavPage>
  )
}

export default ScorePage
