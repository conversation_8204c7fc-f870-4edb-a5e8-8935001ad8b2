/* eslint-disable @next/next/no-img-element */
import React, { useMemo } from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import classNames from 'classnames'
import dayjs from 'dayjs'
import icon_play from '@/../public/images/fmcg/score/icon_play.png'
import icon_tip from '@/../public/images/fmcg/score/icon_tip.png'
import icon_calendar from '@/../public/images/fmcg/score/icon_calendar.png'
import icon_house from '@/../public/images/fmcg/score/icon_house.png'
import icon_invite_user from '@/../public/images/fmcg/score/icon_invite_user.png'
import icon_eye from '@/../public/images/fmcg/score/icon_eye.png'
import icon_note from '@/../public/images/fmcg/score/icon_note.png'
import placeholder from '@/../public/images/common/placeholder.png'
import { formatNumberWithCommas } from '@/lib/utils'
import { Dialog } from 'react-vant'
import styles from './index.module.scss'

const ScoreListItem = ({ item }) => {
  const [thumbError, setThumbError] = React.useState(false)
  const [avatarError, setAvatarError] = React.useState(false)

  const tagName = useMemo(() => {
    const { sourceType } = item
    if (sourceType === 1) {
      return i18n.t('Activity.播放量获得')
    }
    if (sourceType === 2) {
      return i18n.t('Activity.发布天数')
    }
    if (sourceType === 3) {
      return i18n.t('Activity.视频出单')
    }
    if (sourceType === 4) {
      return i18n.t('Activity.橱窗出单')
    }
    if (sourceType === 5) {
      return i18n.t('Activity.邀请达人')
    }
    if (sourceType === 6) {
      return i18n.t('Activity.邀请任务')
    }
    return ''
  }, [item])

  const tagBackground = useMemo(() => {
    const { sourceType } = item
    if (sourceType === 1) {
      return 'rgba(71,139,255,0.1)'
    }
    if (sourceType === 2) {
      return 'rgba(34,179,94,0.1)'
    }
    if (sourceType === 3) {
      return 'rgba(254,109,69,0.1)'
    }
    if (sourceType === 4) {
      return 'rgba(254,109,69,0.1)'
    }
    if (sourceType === 5) {
      return 'rgba(255,20,77,0.1)'
    }
    if (sourceType === 6) {
      return 'rgba(255,20,77,0.1)'
    }
    return ''
  }, [item])

  const tagColor = useMemo(() => {
    const { sourceType } = item
    if (sourceType === 1) {
      return '#478BFF'
    }
    if (sourceType === 2) {
      return '#22B35E'
    }
    if (sourceType === 3) {
      return '#FE6D45'
    }
    if (sourceType === 4) {
      return '#FE6D45'
    }
    if (sourceType === 5) {
      return '#FF144D'
    }
    if (sourceType === 6) {
      return '#FF144D'
    }
    return ''
  }, [item])

  const handleShowInvalidAlert = () => {
    Dialog.alert({
      title: i18n.t('Activity.已失效'),
      message: i18n.t('Activity.积分因视频隐藏/删除，订单取消/退货而扣除'),
      confirmButtonText: i18n.t('Activity.确定'),
      confirmButtonColor: '#FE6D45'
    })
  }

  return (
    <div className={classNames({ [styles.score_list_item_container]: true, [styles.item_disabled]: item.isExpired })}>
      <div className={styles.top_container}>
        <div
          className={styles.tag_container}
          style={{ background: tagBackground, color: tagColor }}
        >
          {tagName}
        </div>
        <div className={styles.status_container}>
          {!item.isExpired && item.odds > 1 && (
            <div className={styles.scale}>
              {i18n.t('Activity.按x倍计入', { scale: item.odds })}
            </div>
          )}
          {item.isExpired && (
            <div className={styles.invalid_container}>
              <div className={styles.invalid_label}>
                {i18n.t('Activity.已失效')}
              </div>
              <div onClick={handleShowInvalidAlert}>
                <Image
                  src={icon_tip}
                  alt=""
                  className={styles.icon_tip}
                ></Image>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className={styles.content_container}>
        <div className={styles.left_container}>
          {(item.sourceType === 1 || item.sourceType === 3) && (
            <div className={styles.thumbnail_container}>
              {!thumbError ? (
                <img
                  className={styles.thumbnail}
                  src={item.imageUrl}
                  onError={() => {
                    setThumbError(true)
                  }}
                ></img>
              ) : (
                <Image
                  src={placeholder}
                  className={styles.thumbnail}
                  alt=""
                ></Image>
              )}
              {/* <Image
                src={icon_play}
                alt=""
                className={styles.icon_play}
              ></Image> */}
            </div>
          )}
          {item.sourceType === 2 && (
            <Image
              src={icon_calendar}
              alt=""
              className={styles.icon_calendar}
            ></Image>
          )}
          {item.sourceType === 4 && (
            <Image
              src={icon_house}
              alt=""
              className={styles.icon_calendar}
            ></Image>
          )}
          {item.sourceType === 5 &&
            (!avatarError ? (
              <img
                src={item.imageUrl}
                alt=""
                className={styles.icon_avatar}
                onError={() => {
                  setAvatarError(true)
                }}
              ></img>
            ) : (
              <Image src={placeholder} className={styles.icon_avatar} alt="" />
            ))}
          {item.sourceType === 6 && (
            <Image
              src={icon_invite_user}
              alt=""
              className={styles.icon_calendar}
            ></Image>
          )}
        </div>
        <div className={styles.right_container}>
          {(item.sourceType === 1 || item.sourceType === 3) && (
            <div className={styles.title}>{item.description || ''}</div>
          )}
          {item.sourceType === 2 && (
            <div className={styles.title_normal}>
              {i18n.t('Activity.累计发布活动视频达到x天', {
                days: item.metric
              })}
            </div>
          )}
          {item.sourceType === 4 && (
            <div className={styles.title_normal}>
              {i18n.t('Activity.账号橱窗出单')}
            </div>
          )}
          {item.sourceType === 5 && (
            <div className={styles.title_normal}>
              {i18n.t('Activity.邀请x参加活动', {
                inviteName: item.description || ''
              })}
            </div>
          )}
          {item.sourceType === 6 && (
            <div className={styles.title_normal}>
              {i18n.t('Activity.完成邀请任务')}
            </div>
          )}
          {(item.sourceType === 1 || item.sourceType === 3) && (
            <div className={styles.time}>
              {i18n.t('Activity.发布时间')}：
              {dayjs(item.lastUpdateTime).format('DD/MM/YYYY')}
            </div>
          )}
          {item.sourceType === 2 && (
            <div className={styles.time}>
              {i18n.t('Activity.达成时间')}：
              {dayjs(item.lastUpdateTime).format('DD/MM/YYYY')}
            </div>
          )}
          {item.sourceType === 5 && (
            <div className={styles.time}>
              {i18n.t('Activity.完成时间')}：
              {dayjs(item.lastUpdateTime).format('DD/MM/YYYY')}
            </div>
          )}
          {item.sourceType === 6 && (
            <div className={styles.time}>
              {i18n.t('Activity.完成时间')}：
              {dayjs(item.lastUpdateTime).format('DD/MM/YYYY')}
            </div>
          )}
          <div className={styles.right_footer_container}>
            <div>
              {item.sourceType === 1 && (
                <div className={styles.stat_container}>
                  <Image
                    src={icon_eye}
                    alt=""
                    className={styles.stat_icon}
                  ></Image>
                  <div className={styles.stat_label}>
                    {formatNumberWithCommas(parseFloat(item.metric || 0))}
                  </div>
                </div>
              )}
              {(item.sourceType === 3 || item.sourceType === 4) && (
                <div className={styles.stat_container}>
                  <Image
                    src={icon_note}
                    alt=""
                    className={styles.stat_icon}
                  ></Image>
                  <div className={styles.stat_label}>
                    {formatNumberWithCommas(parseFloat(item.metric || 0))}
                    {i18n.t('Activity.订单')}
                  </div>
                </div>
              )}
            </div>
            <div
              className={styles.score}
              style={{ color: item.isExpired ? '#ABABAB' : '#303030' }}
            >
              +{formatNumberWithCommas(parseFloat(item.pointsChange || 0))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ScoreListItem
