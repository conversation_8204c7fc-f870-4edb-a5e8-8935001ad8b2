.score_list_item_container {
  padding: 24px;

  .top_container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .tag_container {
      border-radius: 4px;
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 14px;
    }

    .tag {
      font-size: 24px;
      line-height: 34px;
    }

    .status_container {
      display: flex;
      align-items: center;

      .scale {
        font-size: 24px;
        color: #FF9900;
        line-height: 34px;
      }

      .invalid_container {
        display: flex;
        align-items: center;

        .invalid_label {
          font-size: 24px;
          color: #ABABAB;
          line-height: 34px;
        }

        .icon_tip {
          width: 32px;
          height: 32px;
          margin-left: 8px;
        }
      }
    }
  }

  .content_container {
    display: flex;

    .left_container {
      .thumbnail_container {
        position: relative;
        width: 120px;
        height: 160px;
        margin-right: 24px;

        .thumbnail {
          position: absolute;
          top: 0;
          left: 0;
          width: 120px;
          height: 160px;
          border-radius: 8px;
        }

        .icon_play {
          width: 32px;
          height: 32px;
          position: absolute;
          top: 64px;
          left: 44px;
        }
      }

      .icon_calendar {
        width: 100px;
        height: 100px;
        margin-right: 40px;
      }

      .icon_avatar {
        width: 100px;
        height: 100px;
        border-radius: 50px;
        margin-right: 24px;
      }
    }

    .right_container {
      flex: 1;
      display: flex;
      flex-direction: column;

      .title {
        font-size: 28px;
        color: #303030;
        line-height: 40px;
        white-space: nowrap; 
        overflow: hidden; 
        text-overflow: ellipsis;
        width: 512px;
        margin-bottom: 12px;
      }

      .title_normal {
        font-size: 28px;
        color: #303030;
        line-height: 40px;
        margin-bottom: 12px;
      }

      .time {
        font-size: 28px;
        color: #8A8A8A;
        line-height: 40px;
        margin-bottom: 28px;
      }

      .right_footer_container {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .score {
          font-weight: bold;
          font-size: 40px;
          color: #303030;
          line-height: 48px;
        }

        .stat_container {
          display: flex;
          align-items: center;

          .stat_icon {
            width: 32px;
            height: 32px;
            margin-right: 8px;
          }

          .stat_label {
            font-size: 24px;
            color: #303030;
            line-height: 28px;
          }
        }
      }
    }
  }
}

.item_disabled {
  opacity: 0.6;
}