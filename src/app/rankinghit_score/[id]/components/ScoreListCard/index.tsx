/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState } from 'react'
import { InfiniteScroll } from 'antd-mobile'
import { i18n } from '@/lib/client/i18n'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import ScoreListItem from '../ScoreListItem'
import { getSellerCampaignPointsDetail } from '@/app/api/api-uchoice/seller/campaigns/pointsDetails'
import styles from './index.module.scss'

const pageSize = 10

const InfiniteScrollContent = ({ hasMore }: { hasMore?: boolean }) => {
  return (
    <>
      {hasMore ? (
        <div className={styles.no_more}>Loading...</div>
      ) : (
        <div className={styles.no_more}>{i18n.t('TiktokData.无更多数据')}</div>
      )}
    </>
  )
}

function getQueryParams() {
  if (typeof window === 'undefined') {
    return {}
  }
  const search = window.location.search.substring(1)
  return Object.fromEntries(new URLSearchParams(search))
}

const ScoreListCard = ({ id }) => {
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const fetchLoadingRef = useRef(false)
  const [pointsDetails, setPointsDetails] = useState<any>([])
  const pageNoRef = useRef<number>(1)
  const [total, setTotal] = useState<number>(0)

  const requestSellerCampaignPointsDetails = async () => {
    if (fetchLoadingRef.current) {
      return
    }
    if (total === pointsDetails.length && pointsDetails.length !== 0) {
      return
    }
    try {
      setFetchLoading(true)
      fetchLoadingRef.current = true
      const params = {
        pageNo: pageNoRef.current,
        pageSize
      }
      const res: any = await getSellerCampaignPointsDetail(
        id,
        getQueryParams().dataVersion || Date.now().toString(),
        params
      )
      // const res = {
      //   code: 200,
      //   message: 'string',
      //   result: {
      //     list: [
      //       {
      //         description: '视频标题视频标题视频标题视频标题视频标题视频标题视频标题视频标题',
      //         imageUrl:
      //           'https://cdn.jsdelivr.net/gh/andy7076/static_assets/images/banner.png',
      //         isExpired: true,
      //         lastUpdateTime: 123456,
      //         metric: '2,950',
      //         odds: 5,
      //         pointsChange: '+2950',
      //         sourceType: 5
      //       },
      //       {
      //         description: '视频标题视频标题视频标题视频标题视频标题视频标题视频标题视频标题',
      //         imageUrl:
      //           'https://cdn.jsdelivr.net/gh/andy7076/static_assets/images/banner.png',
      //         isExpired: false,
      //         lastUpdateTime: 123456,
      //         metric: '2,950',
      //         odds: 5,
      //         pointsChange: '+2950',
      //         sourceType: 4
      //       },
      //       {
      //         description: '视频标题视频标题视频标题视频标题视频标题视频标题视频标题视频标题',
      //         imageUrl:
      //           'https://cdn.jsdelivr.net/gh/andy7076/static_assets/images/banner.png',
      //         isExpired: false,
      //         lastUpdateTime: 123456,
      //         metric: '2,950',
      //         odds: 5,
      //         pointsChange: '+2950',
      //         sourceType: 3
      //       },
      //       {
      //         description: '视频标题视频标题视频标题视频标题视频标题视频标题视频标题视频标题',
      //         imageUrl:
      //           'https://cdn.jsdelivr.net/gh/andy7076/static_assets/images/banner.png',
      //         isExpired: false,
      //         lastUpdateTime: 123456,
      //         metric: '2,950',
      //         odds: 5,
      //         pointsChange: '+2950',
      //         sourceType: 2
      //       },
      //       {
      //         description: '视频标题视频标题视频标题视频标题视频标题视频标题视频标题视频标题',
      //         imageUrl:
      //           'https://cdn.jsdelivr.net/gh/andy7076/static_assets/images/banner.png',
      //         isExpired: false,
      //         lastUpdateTime: 123456,
      //         metric: '2,950',
      //         odds: 5,
      //         pointsChange: '+2950',
      //         sourceType: 1
      //       }
      //     ],
      //     total: 5
      //   },
      //   success: true
      // }
      setFetchLoading(false)
      fetchLoadingRef.current = false
      if (res.code === 200 && res.result) {
        const { total, list } = res.result
        setPointsDetails(pointsDetails.concat(list))
        setTotal(total)
        pageNoRef.current += 1
      }
    } catch (error) {
      setFetchLoading(false)
      fetchLoadingRef.current = false
    }
  }

  useEffect(() => {
    requestSellerCampaignPointsDetails()
  }, [])

  return (
    <div className={styles.score_list_card_container}>
      {pointsDetails &&
        pointsDetails.length !== 0 &&
        pointsDetails.map((item, index) => {
          return (
            <div key={index}>
              <ScoreListItem item={item}></ScoreListItem>
              {<div className={styles.line}></div>}
            </div>
          )
        })}
      {fetchLoading && total === 0 && (
        <StatusView
          style={{ padding: '48px 0' }}
          status={StatusViewType.loading}
        ></StatusView>
      )}
      {!fetchLoading && total === 0 && (
        <StatusView
          style={{ padding: '48px 0' }}
          status={StatusViewType.empty}
        ></StatusView>
      )}
      {pointsDetails.length !== 0 && (
        <InfiniteScroll
          loadMore={requestSellerCampaignPointsDetails}
          hasMore={total !== pointsDetails.length}
        >
          <InfiniteScrollContent hasMore={total !== pointsDetails.length} />
        </InfiniteScroll>
      )}
    </div>
  )
}

export default ScoreListCard
