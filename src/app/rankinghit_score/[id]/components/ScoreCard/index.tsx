import React from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import icon_tip from '@/../public/images/fmcg/score/icon_tip.png'
import icon_gold from '@/../public/images/fmcg/score/icon_gold.png'
import { formatNumberWithCommas } from '@/lib/utils'
import { useRouter } from '@/lib/hooks/useRouter'
import styles from './index.module.scss'

const ScoreCard = ({ pointsSummary }) => {
  const router = useRouter()

  const handleToRules = () => {
    router.push(`/activity/goojodoq_rule`)
  }

  return (
    <div className={styles.score_card_container}>
      <div className={styles.title_container}>
        <div className={styles.title}>
          {i18n.t('Activity.x活动积分', {
            displayName: pointsSummary.displayName || ''
          })}
        </div>
        <div className={styles.rules_container} onClick={handleToRules}>
          <div className={styles.rules_label}>
            {i18n.t('Activity.积分规则')}
          </div>
          <div>
            <Image src={icon_tip} alt="" className={styles.icon_tip}></Image>
          </div>
        </div>
      </div>
      <div className={styles.score_container}>
        <Image src={icon_gold} alt="" className={styles.icon_gold}></Image>
        <div className={styles.score}>
          {formatNumberWithCommas(parseFloat(pointsSummary.totalPoints || 0))}
        </div>
      </div>
      {/* <div
        className={styles.desc}
        dangerouslySetInnerHTML={{
          __html: i18n.t('Activity.x积分因视频隐藏/删除，订单取消/退货而扣除', {
            score: formatNumberWithCommas(
              parseFloat(pointsSummary.cancelledPoints || 0)
            )
          })
        }}
      ></div> */}
    </div>
  )
}

export default ScoreCard
