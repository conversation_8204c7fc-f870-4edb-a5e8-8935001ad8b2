import React from 'react'
import Image from 'next/image'
import { i18n } from '@/lib/client/i18n'
import icon_back from '@/../public/images/fmcg/score/icon_back.png'
import icon_contact from '@/../public/images/fmcg/score/icon_contact.png'
import styles from './index.module.scss'

const ScoreHeader = () => {
  return <div className={styles.score_header_container}>
    <div><Image src={icon_back} alt="" className={styles.icon_back}></Image></div>
    <div className={styles.title}>{i18n.t('Activity.积分明细')}</div>
    <div><Image src={icon_contact} alt="" className={styles.icon_contact}></Image></div>
  </div>
}

export default ScoreHeader
