.goods_containter {
  display: flex;
  flex-direction: column;
  height: 100vh;
  // background: #c4cdff;
  border-radius: 12px 12px 0px 0px;
}

.goods_content {
  flex: 1;
  background: #c4cdff;
  border-radius: 12px 12px 0px 0px;
  margin-top: 480px;
  padding-top: 12px;
}

.goods_content_vi {
  flex: 1;
  background: #c4cdff;
  border-radius: 12px 12px 0px 0px;
  margin-top: 540px;
  padding-top: 12px;
}

.divide {
  width: 750px;
  height: 2px;
  background-color: #fff;
}

.goods_container {
  min-height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #c4cdff;
}

.fix_head {
  position: fixed;
  top: 0;
  z-index: 999;
}