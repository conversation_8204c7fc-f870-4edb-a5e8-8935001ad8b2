import { metadataTemplate, inApp } from '@/lib/server/utils'
import Inner from './inner'
import React from 'react'
import { i18nS } from '@/lib/server/i18n'
interface Props {
  params: {
    id: string
  }
}

export async function generateMetadata() {
  if (inApp()) {
    return metadataTemplate({ title: i18nS.t('LowPrice.BestProducts') })
  }
  const title = i18nS.t('LowPrice.BestProducts')
  const description =
    i18nS.t('LowPrice.SalesThreshold') + i18nS.t('LowPrice.Addtoshowcase')
  const icon =
    'https://file.uchoice.pro/public/img/231219/ddd.png?x-oss-process=style/jpg'

  return metadataTemplate({ title, description, icon })
}

export default async function Index() {
  return <Inner />
}
