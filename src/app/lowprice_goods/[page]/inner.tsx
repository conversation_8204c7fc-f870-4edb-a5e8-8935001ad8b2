'use client'
import React, { useState, useEffect } from 'react'
import GoodsHead from './components/lowprice_head'
import styles from './index.module.scss'
import PriceTab from './components/price_tab'
import { getWebItemListByPage } from '@/app/api/api-uchoice/tt/item/highItem'
import GoodsItem from './components/goodsItem'
import { i18n } from '@/lib/client/i18n'
import { isRegionTH } from '@/lib/utils'
import { useMounted } from '@/lib/hooks/useMounted'

const LowPriceGoods = () => {
  const [pageNo, setPageNo] = useState(1)
  const [itemInfo, setItemInfo] = useState<any>([])
  const [hasMore, setHasMore] = useState(true)
  const [total, setTotal] = useState(0)
  const [activeTab, setActiveTab] = useState(1)
  const mounted = useMounted()
  const [params, setParams] = useState<any>(
    isRegionTH()
      ? { minPrice: 0, maxPrice: 50 }
      : { minPrice: 0, maxPrice: 20000 }
  )
  const priceTab = [
    {
      id: 1,
      priceRange: isRegionTH()
        ? i18n.t('LowPrice.Below50')
        : i18n.t('LowPrice.BelowVN50')
    },
    {
      id: 2,
      priceRange: isRegionTH()
        ? i18n.t('LowPrice.Below99')
        : i18n.t('LowPrice.BelowVN99')
    },
    {
      id: 3,
      priceRange: isRegionTH()
        ? i18n.t('LowPrice.Below199')
        : i18n.t('LowPrice.BelowVN199')
    }
  ]
  useEffect(() => {
    webItemListByPage()
  }, [])
  const handleTabClick = (index, item) => {
    setActiveTab(index)
    let paramsObj = params
    if (item.id == 1) {
      paramsObj = isRegionTH()
        ? { minPrice: 0, maxPrice: 50 }
        : { minPrice: 0, maxPrice: 20000 }
      setParams(
        isRegionTH()
          ? { minPrice: 0, maxPrice: 50 }
          : { minPrice: 0, maxPrice: 20000 }
      )
    } else if (item.id == 2) {
      paramsObj = isRegionTH()
        ? { minPrice: 50, maxPrice: 99 }
        : { minPrice: 20000, maxPrice: 30000 }
      setParams(
        isRegionTH()
          ? { minPrice: 50, maxPrice: 99 }
          : { minPrice: 20000, maxPrice: 30000 }
      )
    } else if (item.id == 3) {
      paramsObj = isRegionTH()
        ? { minPrice: 99, maxPrice: 199 }
        : { minPrice: 30000, maxPrice: 40000 }
      setParams(
        isRegionTH()
          ? { minPrice: 99, maxPrice: 199 }
          : { minPrice: 30000, maxPrice: 40000 }
      )
    }
    setItemInfo([])
    setPageNo(1)
    webItemListByPage(1, paramsObj)
  }
  const fetchData = () => {
    if (pageNo != 1 && total == itemInfo.length) {
      return
    }
    setPageNo(pageNo + 1)
    webItemListByPage(pageNo + 1)
  }
  const webItemListByPage = (page?, param?) => {
    getWebItemListByPage({
      pageNo: page ? page : pageNo,
      pageSize: '20',
      ...(param ? param : params)
    }).then(res => {
      if (res.code == 200) {
        const list: any = res?.result?.list
        if (list.length == 0 || list.length < 20) {
          setHasMore(false) // 如果没有更多数据了，设置 hasMore 为 false
        }
        if (list.length > 0) {
          setTotal(res.result.total)
          page == 1
            ? setItemInfo([...list])
            : setItemInfo(itemInfo.concat(list))
        }
      }
    })
  }
  return (
    mounted && (
      <div className={styles.goods_containter}>
        <div className={styles.fix_head}>
          <GoodsHead></GoodsHead>
          <div className={styles.divide}></div>
          <div className={styles.goods_tab}>
            <PriceTab
              tabArr={priceTab}
              handleTabClick={handleTabClick}
              activeTab={activeTab}
            ></PriceTab>
          </div>
        </div>
        <div
          className={
            isRegionTH() ? styles.goods_content : styles.goods_content_vi
          }
        >
          <GoodsItem
            itemInfo={itemInfo}
            fetchData={fetchData}
            hasMore={hasMore}
          ></GoodsItem>
        </div>
      </div>
    )
  )
}
export default LowPriceGoods
