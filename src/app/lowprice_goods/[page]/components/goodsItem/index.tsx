import React from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import Image from 'next/image'
import styles from './index.module.scss'
import SaleBottom from '@/app/components/SaleBottom'
import HandlerOnceTap from '@/lib/handlerOnceTap'
import { useRouter } from '@/lib/hooks/useRouter'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { formatPrice } from '@/lib/format'
import { checkAuth, addToShowCase } from '@/lib/actions'

const GoodsItem = ({ itemInfo, fetchData, hasMore }) => {
  const router = useRouter()
  const mounted = useMounted()

  return (
    <div className={styles.goods_item_box}>
      {hasMore && itemInfo.length == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.loading}></StatusView>
        </div>
      )}
      {!hasMore && itemInfo.length == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.empty}></StatusView>
        </div>
      )}
      {mounted && itemInfo && itemInfo.length > 0 && (
        <InfiniteScroll
          dataLength={itemInfo.length}
          next={fetchData}
          hasMore={hasMore}
          loader={
            <div className={styles.load_box}>{i18n.t('Common.Loading')}</div>
          }
          endMessage={
            itemInfo.length > 3 ? (
              <div className={styles.load_box}>{i18n.t('Common.Empty')}</div>
            ) : null
          }
        >
          {itemInfo.map((item, i) => {
            return (
              <div className={styles.item_container} key={i}>
                <div className={styles.item_content}>
                  <div className={styles.item_img}>
                    <Image
                      unoptimized={true}
                      src={item.image}
                      alt="title"
                      width={240}
                      height={240}
                      className={styles.item_img}
                    />
                  </div>

                  <div className={styles.item_right}>
                    <div className={styles.item_pruductName}>
                      {item.productName}
                    </div>
                    <div className={styles.item_comm}>
                      <div className={styles.item_comm_rate}>
                        {' '}
                        {i18n.t('LowPrice.佣金')}{' '}
                        <span className={styles.item_comm_num}>
                          {item.commissionRate}
                        </span>
                      </div>
                      <div className={styles.item_sample}>
                        {' '}
                        {i18n.t('LowPrice.FreeSample')}
                      </div>
                    </div>
                    <div className={styles.item_earn_box}>
                      <div className={styles.item_price}>
                        <span className={styles.item_price_num}>
                          {`${
                            item.maxPrice === item.minPrice
                              ? formatPrice(item.minPrice, true)
                              : `${formatPrice(
                                  item.minPrice,
                                  true
                                )}~ ${formatPrice(item.maxPrice, true)}`
                          }`}
                        </span>
                      </div>
                      <div className={styles.item_earn}>
                        {i18n.t('Product.Earn')}:{' '}
                        <span className={styles.item_earn_num}>{`${
                          item.minEarn === item.maxEarn
                            ? `${formatPrice(item.maxEarn, true)}`
                            : `${formatPrice(
                                item.minEarn,
                                true
                              )}~ ${formatPrice(item.maxEarn, true)}`
                        }`}</span>
                      </div>
                    </div>
                    <div className={styles.item_btns}>
                      <div
                        className={styles.btns_add_case}
                        onClick={() => {
                          HandlerOnceTap(() => {
                            checkAuth(() =>
                              addToShowCase(item, 'low_price_products')
                            )
                          }),
                            statistic({
                              eventName: EventName.lowprice_goods_add_showcase,
                              param: {
                                id: item.id
                              }
                            })
                        }}
                      >
                        {i18n.t('LowPrice.AddShowcase')}
                      </div>
                      <div
                        className={styles.btns_fee_sample}
                        onClick={() => {
                          HandlerOnceTap(() => {
                            router.push(
                              `${window.location.origin}/product/${item.id}`
                            )
                          })
                        }}
                      >
                        {i18n.t('LowPrice.GetFreeSample')}
                      </div>
                    </div>
                  </div>
                </div>
                <div className={styles.item_bottom}>
                  <SaleBottom item={item}></SaleBottom>
                </div>
              </div>
            )
          })}
        </InfiniteScroll>
      )}
      {itemInfo.length > 3 && itemInfo.length < 20 && (
        <div className={styles.noMore}>{i18n.t('Common.Empty')}</div>
      )}
    </div>
  )
}

export default GoodsItem
