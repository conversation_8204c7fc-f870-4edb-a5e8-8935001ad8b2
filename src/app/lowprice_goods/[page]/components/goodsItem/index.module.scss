.goods_item_box {
  flex: 1;
  padding: 12px 24px;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
}

.goods_item_box::-webkit-scrollbar {
  display: none;
}

.item_content {
  display: flex;
  padding: 20px 24px;
}

.item_img {
  width: 240px;
  height: 240px;
  border-radius: 4px;
  margin-right: 20px;
}

.item_right {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.item_pruductName {
  height: 84px;
  font-size: 28px;
  font-weight: normal;
  color: #303030; 
  line-height: 42px;
  text-overflow: ellipsis;
   display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item_container {
  background: #ffffff;
  border-radius: 4px;
  margin-bottom: 24px;
}

/* Webkit 浏览器（如 Chrome 和 Safari）的滚动条样式 */

.item_comm {
  display: flex;
  align-items: center;
  margin-top: 16px;
}

.item_comm_rate {
  height: 32px;
  background: rgba(197, 68, 93, 0.1);
  border-radius: 2px;
  padding: 0 8px;
  font-size: 22px;
  font-weight: normal;
  color: #c5445d;
}

.item_comm_num {
  font-weight: bold;
}

.item_sample {
  font-size: 22px;
  font-weight: normal;
  color: #303030;
  line-height: 22px;
  margin-left: 20px;
}

.item_earn_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.item_price_num {
  font-size: 24px;
  font-weight: bold;
  color: #303030;
  line-height: 36px;
}

.item_earn {
  font-size: 24px;
  font-weight: normal;
  color: #fe6d45;
  line-height: 36px;
}

.item_btns {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 8px;
}

.btns_add_case {
  // height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 8px;
  font-size: 24px;
  font-weight: normal;
  color: #fe6d45;
  border-radius: 2px;
  border: 1px solid #fe6d45;
}

.btns_fee_sample {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 8px;
  // height: 48px;
  background: #fe6d45;
  border-radius: 2px;
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  margin-left: 18px;
}
.load_box{
  padding-top: 48px;
  display: flex;
 align-items: center;
 justify-content: center;
}
.loadingStatus{
 background: #c4cdff;
  display: flex;
  flex: 1;
  justify-content:center;
  align-items: center;
  padding-top: 150px;
}