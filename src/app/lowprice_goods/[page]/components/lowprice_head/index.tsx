import React from 'react'
import Image from 'next/image'
import icon_back from '@/../public/images/low_price/icon_back.png'
import icon_share from '@/../public/images/low_price/icon_share.png'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { webview } from '@/lib/client/webview'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useRouter } from '@/lib/hooks/useRouter'
import { useMounted } from '@/lib/hooks/useMounted'

const HighHead = () => {
  const router = useRouter()
  const mounted = useMounted()
  return (
    mounted && (
      <div
        className={styles.head_bg}
        style={{
          paddingTop: webview ? `${webview?.getData().topSafeArea}px` : '24px'
        }}
      >
        {webview && (
          <div className={styles.title_box}>
            <div
              onClick={() => {
                router.back()
              }}
            >
              {' '}
              <Image src={icon_back} alt="icon" className={styles.icon_back} />
            </div>
            {/* <span className={styles.title}>
              {i18n.t('LowPrice.lowPriceGoodies')}
            </span> */}
            <div
              onClick={() =>
                webview?.send(WebviewEvents.shareUrl, {
                  url: window.location.href
                })
              }
            >
              <Image
                src={icon_share}
                alt="icon"
                className={styles.icon_share}
              />
            </div>
          </div>
        )}
        <div className={styles.icons_imgs}>
          <span className={styles.title_name}>
            {i18n.t('LowPrice.BestProducts')}
          </span>
          <div className={styles.title_name_sales}>
            <span className={styles.title_name_text}>
              {' '}
              {i18n.t('LowPrice.SalesThreshold')}
            </span>
          </div>
          <span className={styles.title_name}>
            {i18n.t('LowPrice.Addtoshowcase')}
          </span>
        </div>
      </div>
    )
  )
}

export default HighHead
