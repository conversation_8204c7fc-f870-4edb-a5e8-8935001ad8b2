/*
 * @Author: <PERSON>
 * @Date: 2024-04-10 11:32:35
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-15 16:56:05
 * @Desc 忘记密码页面
 */
'use client'

import { checkCode } from '@/app/api/api-base/sms/uChoiceValidCode'
import { sendCode } from '@/app/api/api-base/sms/uChoiceSendCode'
import { Input } from '@/components/Input'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { i18n } from '@/lib/client/i18n'
import { loading as Loading } from '@/lib/client/loading'
import { toast } from '@/lib/client/toast'
import { webview } from '@/lib/client/webview'
import { AppColors } from '@/lib/const'
import useCounter from '@/lib/hooks/useCounter'
import { useRouter } from '@/lib/hooks/useRouter'
import { Button } from 'antd-mobile'
import { Suspense, useCallback, useEffect, useState } from 'react'
import './index.scss'

export const Inner = () => {
  const [phone, setPhone] = useState('')
  const [code, setCode] = useState('')
  const [disabled, setDisabled] = useState(true)
  const [loading, setLoading] = useState(false)
  const [sended, setSended] = useState(false)
  const counter = useCounter()
  const router = useRouter()

  useEffect(() => {
    if (sended) return
    const _disabled = phone.trim().length === 0
    if (disabled !== _disabled) {
      setDisabled(_disabled)
    }
  }, [disabled, phone, sended])

  useEffect(() => {
    if (counter.time === 0) {
      setSended(false)
    }
  }, [counter.time])

  const onChangeMobile = useCallback((value: string) => setPhone(value), [])

  const onChangeCode = useCallback((value: string) => setCode(value), [])

  const onSendCode = useCallback(async () => {
    const sendEnd = () => {
      setLoading(false)
      setDisabled(false)
      setSended(false)
    }

    try {
      if (disabled) return
      setLoading(true)
      const params = {
        mobile: phone,
        type: 4
      }
      const data = await sendCode(params)
      setLoading(false)
      if (!data.success) {
        toast.error(data.message)
        return
      }
      toast.success(i18n.t('Common.Success'))
      setSended(true)
      counter.start()
    } catch (error) {
      console.log(error)
      error && error instanceof Error && toast.error(error.message)
      sendEnd()
    }
  }, [counter, disabled, phone])

  const onSubmit = useCallback(async () => {
    try {
      Loading.show()
      const params = {
        mobile: phone,
        type: 4,
        validCode: code
      }
      const res = await checkCode(params)
      Loading.hide()
      if (!res.success) return
      router.push(
        `/login/setNewPassword?mobile=${phone}&validToken=${res.result}`
      )
    } catch (error) {
      console.log(error)
      Loading.hide()
    }
  }, [code, phone, router])

  const onSubmitClick = useCallback(() => {
    if (code.trim().length === 0) {
      toast(i18n.t('Login.please_enter_code'))
      return
    }
    onSubmit()
  }, [code, onSubmit])

  return (
    <TransparentNavPage
      title={i18n.t('Login.set_password')}
      transparent={false}
      hide={!webview}
    >
      <div className="forgot-page">
        {!webview && (
          <p className="page-title">{i18n.t('Login.set_password')}</p>
        )}
        <p className="tip">
          {i18n.t('Login.We_will_send_a_one_time_SMS_code_your_phone')}
        </p>
        <p className="inupt-top-title">{i18n.t('Login.mobile_number')}</p>
        <div className="input-wrapper">
          <span>🇹🇭 +66</span>
          <Input
            placeholder={i18n.t('Login.mobile_number')}
            onChange={onChangeMobile}
            value={phone}
            fontSize="0.8rem"
            type="text"
            className="input"
            placeholderColor={AppColors.grayAB}
            color={AppColors.black2D}
            maxLength={10}
          />
        </div>
        <div className="input-wrapper code-input-wrapper">
          <Input
            placeholder={i18n.t('Login.sms_code')}
            onChange={onChangeCode}
            value={code}
            fontSize="0.65rem"
            placeholderColor={AppColors.placeholder}
            color={AppColors.fontBlack}
            className="input code-input"
          />
          <div className="block" />
          <Button
            className="send-text-button"
            onClick={onSendCode}
            loading={loading}
            disabled={disabled}
          >
            {sended ? `${counter.time}s` : i18n.t('Login.send')}
          </Button>
        </div>
        <button className="submit-button" onClick={onSubmitClick}>
          {i18n.t('Login.verify_code')}
        </button>
      </div>
    </TransparentNavPage>
  )
}
