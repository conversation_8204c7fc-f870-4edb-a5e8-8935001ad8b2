.forgot-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #fff;
  align-items: center;

  .back-button {
    width: 40px;
    height: 40px;
    margin-top: 112px;
    align-self: self-start;
    margin-left: 24px;

    .icon {
      width: 100%;
      height: 100%;
    }
  }

  .page-title {
    color: #020202;
    font-weight: bold;
    font-size: 40px;
    margin-top: 88px;
  }

  .tip {
    color: #303030;
    font-size: 44px;
    margin-top: 44px;
    text-align: left;
    width: 594px;
  }

  .inupt-top-title {
    color: #8A8A8A;
    font-size: 26px;
    margin: 96px 0 0 24px;
    align-self: flex-start;
  }

  .input-wrapper {
    display: flex;
    align-items: center;
    padding: 16px 30px;
    border-bottom: 1px solid #BBBBBD;
    width: calc(100% - 48px);
    margin: 28px 0 20px 0;
    font-weight: bold;
    font-size: 32px;
    color: #2E2D2D;

    .input {
      flex: 1;
      padding: 0 8px;
      font-weight: normal;
    }

    .code-input {
      font-weight: normal;
    }
  }

  .code-input-wrapper {
    margin-top: 10px;

    .block {
      width: 1px;
      height: 36px;
      background-color: #BBBBBD;
      margin: 0 32px;
    }

    .send-text-button {
      width: 70px;
      border: 0;
      outline: none;
      padding: 0;
      color: #FE6D45;
      font-size: 28px;
      font-weight: normal;
    }
  }

  .submit-button {
    width: calc(100% - 48px);
    height: 84px;
    background-color: #FE6D45;
    color: #fff;
    font-size: 32px;
    margin-top: 140px;
    border-radius: 4px;
  }
}
