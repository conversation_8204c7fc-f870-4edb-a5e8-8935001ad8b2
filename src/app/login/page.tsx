/*
 * @Author: <PERSON>
 * @Date: 2024-04-09 09:43:18
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-18 17:42:21
 * @Desc 登陆页面
 */

'use client'

import uchoice_logo from '@/../public/images/login/uchoice_logo.png'
import { i18n } from '@/lib/client/i18n'
import { useMounted } from '@/lib/hooks/useMounted'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { useRouter } from '@/lib/hooks/useRouter'
import Image from 'next/image'
import { Suspense, useCallback, useEffect, useState } from 'react'
import LoginByPassword from './components/LoginByPassword'
import LoginBySMS from './components/LoginBySMS'
import './index.scss'
import { DEFAULT_AFTER_LOGIN_URL } from '@/lib/const'

const enum LOGIN_TYPE {
  /** 密码登陆 */
  pwd = 1,
  /** 验证码登陆 */
  sms = 2,
  /** 其他 */
  other = 3
}

const TOKEN_STORAGE_KEY = 'access_token'

const LoginPage = () => {
  const mounted = useMounted()
  const router = useRouter()
  const routerParams = useRouteParams<{ redirectTo?: string }>()
  const [loginType, setLoginType] = useState(LOGIN_TYPE.pwd)

  useEffect(() => {
    localStorage.setItem('after_login_url', routerParams.redirectTo || '')
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const onTriggerLoginType = useCallback(() => {
    setLoginType(loginType === LOGIN_TYPE.pwd ? LOGIN_TYPE.sms : LOGIN_TYPE.pwd)
  }, [loginType])

  const onLoginSuccess = (token: string) => {
    localStorage.setItem(TOKEN_STORAGE_KEY, token)
    const url = routerParams.redirectTo || DEFAULT_AFTER_LOGIN_URL
    router.push(url)
  }

  return mounted ? (
    <div className="login-page">
      <Image className="logo" src={uchoice_logo} alt="" />
      {loginType === LOGIN_TYPE.pwd ? (
        <LoginByPassword onLoginSuccess={onLoginSuccess} />
      ) : (
        <LoginBySMS onLoginSuccess={onLoginSuccess} />
      )}
      <span className="trigger-login-type" onClick={onTriggerLoginType}>
        {i18n.t(
          loginType === LOGIN_TYPE.pwd
            ? 'Login.login_with_sms'
            : 'Login.login_with_password'
        )}
      </span>
    </div>
  ) : null
}

const page = () => (
  <Suspense>
    <LoginPage />
  </Suspense>
)

export default page
