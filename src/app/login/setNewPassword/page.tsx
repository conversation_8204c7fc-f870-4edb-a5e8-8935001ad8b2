/*
 * @Author: <PERSON>
 * @Date: 2024-04-10 14:12:44
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-15 19:26:24
 * @Desc 设置密码页面
 */
'use client'

import uchoice_logo from '@/../public/images/login/uchoice_logo.png'
import { updatePassword } from '@/app/api/api-uchoice/uChoice/account/updatePassword'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { i18n } from '@/lib/client/i18n'
import { toast } from '@/lib/client/toast'
import { webview } from '@/lib/client/webview'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { useRouter } from '@/lib/hooks/useRouter'
import Image from 'next/image'
import { Suspense, useCallback, useState } from 'react'
import { Toast, ToastReturnType } from 'react-vant'
import PwdInput from '../components/PwdInput'
import './index.scss'

const SetNewPasswordPage = () => {
  const [password, setPassword] = useState('')
  const [password2, setPassword2] = useState('')
  const routerParams = useRouteParams<{
    mobile?: string
    validToken?: string
  }>()
  const router = useRouter()

  const handleSign = async () => {
    let loading: ToastReturnType | null = null
    try {
      const reg = /(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-zf]{6,}/
      if (!reg.test(password)) {
        toast(i18n.t('Login.minnum_6'))
        return
      }
      if (password !== password2) {
        toast(i18n.t('Login.the_two_passwords_are_inconsistent'))
        return
      }
      loading = Toast.loading(i18n.t('Common.Loading'))
      const obj = {
        mobile: routerParams.mobile || '',
        newPassword: password,
        momentToken: routerParams.validToken || ''
      }
      await updatePassword(obj)
      loading?.clear()
      Toast.success({
        message: i18n.t('Common.Success'),
        duration: 1000,
        onClose() {
          router.push(`/login`)
        }
      })
    } catch (error) {
      console.log(error)
      loading?.clear()
    }
  }

  const onChangePwd = useCallback((value: string) => setPassword(value), [])
  const onChangePwd2 = useCallback((value: string) => setPassword2(value), [])

  return (
    <TransparentNavPage title="" transparent={false} hide={!webview}>
      <div className="set-new-pwd-page">
        <Image className="logo" src={uchoice_logo} alt="" />
        <PwdInput
          onchange={onChangePwd}
          placeholder={i18n.t('Login.password')}
          value={password}
        />
        <PwdInput
          onchange={onChangePwd2}
          placeholder={i18n.t('Login.new_password')}
          value={password2}
        />
        <p className="tip">{i18n.t('Login.minnum_6')}</p>
        <button onClick={handleSign} className="submit-button">
          {i18n.t('Login.next')}
        </button>
      </div>
    </TransparentNavPage>
  )
}

const page = () => (
  <Suspense>
    <SetNewPasswordPage />
  </Suspense>
)

export default page
