/*
 * @Author: <PERSON>
 * @Date: 2024-04-10 18:41:27
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-11 14:46:37
 * @Desc 密码输入框
 */
import eyeCloseIcon from '@/../public/images/login/eye_close_icon.png'
import eyeOpenIcon from '@/../public/images/login/eye_open_icon.png'
import { Input } from '@/components/Input'
import { i18n } from '@/lib/client/i18n'
import { toast } from '@/lib/client/toast'
import { AppColors } from '@/lib/const'
import Image from 'next/image'
import { useCallback, useState } from 'react'
import './index.scss'

const PwdInput = (props: {
  placeholder?: string
  onchange: (value: string) => void
  value: string
}) => {
  const { onchange, value, placeholder } = props

  const [isEyeOpen, setIsEyeOpen] = useState(false)

  const onTriggerEye = useCallback(() => {
    setIsEyeO<PERSON>(!isEyeOpen)
  }, [isEyeOpen])

  const onPasswordBlur = useCallback(() => {
    const reg = /(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-zf]{6,}/
    if (!reg.test(value)) {
      toast(i18n.t('Login.minnum_6'))
      return
    }
  }, [value])

  return (
    <div className="input-wrapper">
      <Input
        onBlur={onPasswordBlur}
        placeholder={placeholder}
        onChange={onchange}
        value={value}
        fontSize="0.8rem"
        placeholderColor={AppColors.placeholder}
        color={AppColors.fontBlack}
        className="input"
        type={isEyeOpen ? 'text' : 'password'}
      />
      <button onClick={onTriggerEye}>
        <Image
          src={isEyeOpen ? eyeOpenIcon : eyeCloseIcon}
          alt=""
          className="eye-icon"
        />
      </button>
    </div>
  )
}

export default PwdInput
