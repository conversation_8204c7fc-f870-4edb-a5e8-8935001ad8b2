/*
 * @Author: <PERSON>
 * @Date: 2024-04-10 16:28:03
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-15 15:27:34
 * @Desc 密码登陆
 */
'use client'

import eyeCloseIcon from '@/../public/images/login/eye_close_icon.png'
import eyeOpenIcon from '@/../public/images/login/eye_open_icon.png'
import phoneIcon from '@/../public/images/login/phone_icon.png'
import pwdIcon from '@/../public/images/login/pwd_icon.png'
import { Input } from '@/components/Input'
import { i18n } from '@/lib/client/i18n'
import { toast } from '@/lib/client/toast'
import { AppColors } from '@/lib/const'
import usePasswordLogin from '@/lib/hooks/usePasswordLogin'
import { useRouter } from '@/lib/hooks/useRouter'
import Image from 'next/image'
import { useCallback, useState } from 'react'
import './index.scss'

const LoginByPassword = ({
  onLoginSuccess
}: {
  onLoginSuccess: (token: string) => void
}) => {
  const [phone, setPhone] = useState('')
  const [pwd, setPwd] = useState('')
  const [isEyeOpen, setIsEyeOpen] = useState(false)
  const router = useRouter()
  const onPasswordLogin = usePasswordLogin()

  const onChangePhone = useCallback((value: string) => setPhone(value), [])

  const onChangePwd = useCallback((value: string) => setPwd(value), [])

  const onTriggerEye = useCallback(() => {
    setIsEyeOpen(!isEyeOpen)
  }, [isEyeOpen])

  const onLogin = async () => {
    try {
      const token = await onPasswordLogin(phone, pwd)
      if (!token) return
      onLoginSuccess(token)
    } catch (error) {
      console.log(error)
    }
  }

  const handleLoginClick = useCallback(() => {
    if (phone.trim().length === 0) {
      toast(i18n.t('Login.please_enter_account'))
      return
    }
    if (pwd.trim().length === 0) {
      toast(i18n.t('Login.please_enter_password'))
      return
    }
    onLogin()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [phone, pwd])

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const nav2Forgot = useCallback(() => router.push(`/login/forgot`), [])

  return (
    <div className="login-by-password">
      <div className="input-wrapper">
        <Image src={phoneIcon} alt="" className="input-icon" />
        <Input
          placeholder={i18n.t('Login.phone')}
          onChange={onChangePhone}
          value={phone}
          fontSize="0.8rem"
          placeholderColor={AppColors.placeholder}
          color={AppColors.fontBlack}
          className="input"
          maxLength={10}
        />
      </div>
      <div className="input-wrapper">
        <Image src={pwdIcon} alt="" className="input-icon" />
        <Input
          placeholder={i18n.t('Login.password')}
          onChange={onChangePwd}
          value={pwd}
          fontSize="0.8rem"
          placeholderColor={AppColors.placeholder}
          color={AppColors.fontBlack}
          className="input"
          type={isEyeOpen ? 'text' : 'password'}
        />
        <button onClick={onTriggerEye}>
          <Image
            src={isEyeOpen ? eyeOpenIcon : eyeCloseIcon}
            alt=""
            className="eye-icon"
          />
        </button>
        <div className="block" />
        <span className="forgot-text" onClick={nav2Forgot}>
          {i18n.t('Login.forgot')}
        </span>
      </div>
      <button className="login-submit-button" onClick={handleLoginClick}>
        {i18n.t('Login.login')}
      </button>
    </div>
  )
}

export default LoginByPassword
