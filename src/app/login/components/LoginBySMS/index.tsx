/*
 * @Author: <PERSON>
 * @Date: 2024-04-10 16:30:47
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-15 18:25:08
 * @Desc 验证码登陆
 */
import smsCodeIcon from '@/../public/images/login/sms_code_icon.png'
import smsPhoneIcon from '@/../public/images/login/sms_phone_icon.png'
import { checkCode } from '@/app/api/api-base/sms/uChoiceValidCode'
import { passwordLogin } from '@/app/api/api-uaa/oauth/token'
import { sendCode } from '@/app/api/api-base/sms/uChoiceSendCode'
import { Input } from '@/components/Input'
import { i18n } from '@/lib/client/i18n'
import { loading as Loading } from '@/lib/client/loading'
import { toast } from '@/lib/client/toast'
import { AppColors } from '@/lib/const'
import useCounter from '@/lib/hooks/useCounter'
import { But<PERSON> } from 'antd-mobile'
import Image from 'next/image'
import { useCallback, useEffect, useRef, useState } from 'react'
import './index.scss'
import { useRouter } from '@/lib/hooks/useRouter'

const enum LOGIN_TYPE {
  login = 2,
  register = 1
}

const LoginBySMS = ({
  onLoginSuccess
}: {
  onLoginSuccess: (token: string) => void
}) => {
  const [phone, setPhone] = useState('')
  const [code, setCode] = useState('')
  const [disabled, setDisabled] = useState(true)
  const [loading, setLoading] = useState(false)
  const [sended, setSended] = useState(false)
  const counter = useCounter()
  const type = useRef(LOGIN_TYPE.login)
  const router = useRouter()

  useEffect(() => {
    if (sended) return
    const _disabled = phone.trim().length === 0
    if (disabled !== _disabled) {
      setDisabled(_disabled)
    }
  }, [disabled, phone, sended])

  useEffect(() => {
    if (counter.time === 0) {
      setSended(false)
    }
  }, [counter.time])

  const onChangePhone = useCallback((value: string) => setPhone(value), [])

  const onChangeCode = useCallback((value: string) => setCode(value), [])

  const onSendCode = useCallback(async () => {
    const sendEnd = () => {
      setLoading(false)
      setDisabled(false)
      setSended(false)
    }

    try {
      if (disabled) return
      setLoading(true)
      const params = {
        mobile: phone,
        type: type.current
      }
      const data = await sendCode(params)
      setLoading(false)
      // 未注册账号
      if (data.code === 31004) {
        // 重新发送验证码 type为1-注册
        type.current = LOGIN_TYPE.register
        setSended(true)
        onSendCode()
        return
      }
      if (!data.success) {
        if (type.current === LOGIN_TYPE.register) {
          toast.error(data.message)
        }
        return
      }
      toast.success(i18n.t('Common.Success'))
      setSended(true)
      counter.start()
    } catch (error) {
      console.log(error)
      sendEnd()
    }
  }, [counter, disabled, phone])

  const onLoginSubmit = useCallback(async () => {
    try {
      if (phone.trim().length === 0) {
        toast(i18n.t('Login.please_enter_mobile'))
        return
      }
      if (code.trim().length === 0) {
        toast(i18n.t('Login.please_enter_code'))
        return
      }
      Loading.show()
      if (type.current === LOGIN_TYPE.register) {
        // 注册校验验证码
        const params = {
          mobile: phone,
          type: type.current,
          validCode: code
        }
        const data = await checkCode(params)
        Loading.hide()
        if (data.code !== 200) return
        router.push(
          `/login/setPassword?mobile=${phone}&validToken=${data.result}&from=register&prefix=66`
        )
      } else {
        const form = `grant_type=tt_mobile_code&mobile=${phone}&code=${code}`
        const data = await passwordLogin(form)
        Loading.hide()
        onLoginSuccess(data.result.access_token || '')
      }
    } catch (error) {
      Loading.hide()
      error && error instanceof Error && toast.error(error.message)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code, phone])

  return (
    <div className="login-by-sms">
      <div className="input-wrapper">
        <Image src={smsPhoneIcon} alt="" className="input-icon" />
        <Input
          placeholder={i18n.t('Login.phone')}
          onChange={onChangePhone}
          value={phone}
          fontSize="0.8rem"
          placeholderColor={AppColors.placeholder}
          color={AppColors.fontBlack}
          className="input"
          maxLength={10}
        />
      </div>
      <div className="input-wrapper">
        <Image src={smsCodeIcon} alt="" className="input-icon" />
        <Input
          placeholder={i18n.t('Login.sms_code')}
          onChange={onChangeCode}
          value={code}
          fontSize="0.8rem"
          placeholderColor={AppColors.placeholder}
          color={AppColors.fontBlack}
          className="input"
        />
        <div className="block" />
        <Button
          className="send-text-button"
          onClick={onSendCode}
          loading={loading}
          disabled={disabled}
        >
          {sended ? `${counter.time}s` : i18n.t('Login.send')}
        </Button>
      </div>
      <p className="tip">
        {i18n.t('Login.未注册的手机号验证通过后将自动注册')}
      </p>
      <button className="login-submit-button" onClick={onLoginSubmit}>
        {i18n.t('Login.验证并登录')}
      </button>
    </div>
  )
}

export default LoginBySMS
