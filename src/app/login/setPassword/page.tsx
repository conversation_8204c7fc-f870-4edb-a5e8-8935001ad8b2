/*
 * @Author: <PERSON>
 * @Date: 2024-04-12 16:32:10
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-18 17:42:08
 * @Desc 设置密码页面
 */
'use client'

import uchoice_logo from '@/../public/images/login/uchoice_logo.png'
import { registerMember } from '@/app/api/api-uchoice/uChoice/account/registerMember'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import { i18n } from '@/lib/client/i18n'
import { loading } from '@/lib/client/loading'
import { toast } from '@/lib/client/toast'
import { webview } from '@/lib/client/webview'
import usePasswordLogin from '@/lib/hooks/usePasswordLogin'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { useRouter } from '@/lib/hooks/useRouter'
import Image from 'next/image'
import qs from 'qs'
import { Suspense, useCallback, useState } from 'react'
import PwdInput from '../components/PwdInput'
import './index.scss'
import { DEFAULT_AFTER_LOGIN_URL } from '@/lib/const'

interface RouterParams {
  /**
   * 手机号
   */
  mobile?: string
  /**
   * 临时Token
   */
  validToken?: string
  /**
   * 手机号前缀, 默认值: 66
   */
  prefix?: string
  /**
   * 用户来源(APP/H5/PC)
   */
  source?: string
  redirectTo?: string
}

const SetPassword = () => {
  const [pwd, setPwd] = useState('')
  const router = useRouter()
  const routerParams = useRouteParams<RouterParams>()
  const onPasswordLogin = usePasswordLogin()

  const onChangePwd = useCallback((value: string) => {
    setPwd(value)
  }, [])

  /**
   * 注册
   */
  const onRegisterMember = async () => {
    try {
      loading.show()
      const {
        mobile = '',
        validToken = '',
        prefix = '66',
        source = 'H5'
      } = routerParams
      const params = {
        mobile,
        momentToken: validToken,
        prefix,
        source,
        password: pwd
      }
      const data = await registerMember(qs.stringify(params))
      loading.hide()
      if (data.code === 200) {
        // 登录
        const res = await onPasswordLogin(mobile, pwd)
        if (!res) return
        localStorage.setItem('access_token', res || '')
        const redirectTo = localStorage.getItem('after_login_url')
        const url = redirectTo || DEFAULT_AFTER_LOGIN_URL
        router.push(url)
      }
    } catch (error) {
      console.log(error)
      loading.hide()
    }
  }

  const handleSign = useCallback(() => {
    const reg = /(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-zf]{6,}/
    if (!reg.test(pwd.trim())) {
      toast(i18n.t('Login.minnum_6'))
      return
    }
    if (pwd.trim().length === 0) {
      toast(i18n.t('Login.please_enter_password'))
      return
    }
    onRegisterMember()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pwd])

  return (
    <TransparentNavPage title="" transparent={false} hide={!webview}>
      <div className="set-pwd-page">
        <Image className="logo" src={uchoice_logo} alt="" />
        <PwdInput
          onchange={onChangePwd}
          placeholder={i18n.t('Login.password')}
          value={pwd}
        />
        <p className="tip">
          {i18n.t('Login.PasswordMustHaveAtLastSixCharacters')}
        </p>
        <button onClick={handleSign} className="submit-button">
          {i18n.t('Login.next')}
        </button>
      </div>
    </TransparentNavPage>
  )
}

const page = () => (
  <Suspense>
    <SetPassword />
  </Suspense>
)

export default page
