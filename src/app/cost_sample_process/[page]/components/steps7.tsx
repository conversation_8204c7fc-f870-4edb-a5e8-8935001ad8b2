import { i18n } from '@/lib/client/i18n'
import styles from '../index.module.scss'
import Image from 'next/image'
import LangsImg from './langs_img'

const StepSeven = ({ imgIndex }) => {
  const arr = [i18n.t('Promotion.costTips7')]

  return (
    <div className={styles.stepOne_content}>
      <div className={styles.stepOne_left}>
        <div className={styles.title}>{i18n.t('Promotion.step7')}</div>
        {arr.map((item, index) => (
          <div className={styles.direction} key={index}>
            {item}
          </div>
        ))}
      </div>
      <div className={styles.stepOne_right}>
        <LangsImg imgIndex={imgIndex}></LangsImg>
      </div>
    </div>
  )
}

export default StepSeven
