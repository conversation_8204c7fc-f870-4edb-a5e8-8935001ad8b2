import { i18n } from '@/lib/client/i18n'
import styles from '../index.module.scss'
import Image from 'next/image'
import LangsImg from './langs_img'

const StepFour = ({ imgIndex }) => {
  const arr = [i18n.t('Promotion.costTips5')]

  return (
    <div className={styles.stepTwo_content}>
      <div className={styles.stepTwo_left}>
        <LangsImg imgIndex={imgIndex}></LangsImg>
      </div>
      <div className={styles.stepTwo_right}>
        <div className={styles.title}>{i18n.t('Promotion.step4')}</div>
        {arr.map(
          (item, index) =>
            item && (
              <div className={styles.direction} key={index}>
                {item}
              </div>
            )
        )}
      </div>
    </div>
  )
}

export default StepFour
