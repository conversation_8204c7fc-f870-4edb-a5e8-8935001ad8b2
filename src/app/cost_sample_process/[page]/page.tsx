'use client'
import { TransparentNavPage } from '@/components/TransparentNavPage'
import React, { useEffect, useRef, useState } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import { useRouter } from '@/lib/hooks/useRouter'
import Image from 'next/image'
import StepOne from './components/steps1'
import StepTwo from './components/steps2'
import StepThree from './components/steps3'
import StepFour from './components/steps4'
import StepFive from './components/steps5'
import StepSix from './components/steps6'
import StepSeven from './components/steps7'
import styles from './index.module.scss'
import { webview } from '@/lib/client/webview'

const SampleGuide = () => {
  const mounted = useMounted()
  const router = useRouter()
  const [showBtn, setShowBtn] = useState<boolean>(false)
  const collectionIdRef = useRef(null)

  return (
    mounted && (
      <TransparentNavPage
        title={i18n.t('Promotion.costSampleTheme')}
        transparent={false}
        hide={!webview}
      >
        <div className={styles.container}>
          <div className={styles.tilte_box}>
            <span className={styles.vertical_line}></span>
            <div>
              <span className={styles.title_text}>
                {i18n.t('Promotion.costSampleTitle')}
              </span>
              <span className={styles.red_title}>
                {i18n.t('Promotion.costSampleRedTitle')}
              </span>
            </div>
          </div>
          <StepOne imgIndex={1} />
          <div className={styles.arrow_img}>
            <Image
              alt=""
              unoptimized
              src="/images/showcase/arrow_1.png"
              className={styles.arrow_img}
              width={135}
              height={116}
            ></Image>
          </div>
          <StepTwo imgIndex={2} />
          <div className={styles.arrow_img}>
            <Image
              unoptimized
              alt=""
              src="/images/showcase/arrow_2.png"
              className={styles.arrow_img}
              width={135}
              height={116}
            ></Image>
          </div>
          <StepThree imgIndex={3} />
          <div className={styles.arrow_img}>
            <Image
              alt=""
              unoptimized
              src="/images/showcase/arrow_1.png"
              className={styles.arrow_img}
              width={135}
              height={116}
            ></Image>
          </div>
          <StepFour imgIndex={4} />
          <div className={styles.arrow_img}>
            <Image
              alt=""
              src="/images/showcase/arrow_2.png"
              className={styles.arrow_img}
              width={135}
              height={116}
            ></Image>
          </div>
          <StepFive imgIndex={5} />
          <div className={styles.arrow_img}>
            <Image
              alt=""
              src="/images/showcase/arrow_1.png"
              className={styles.arrow_img}
              width={135}
              height={116}
            ></Image>
          </div>
          <StepSix imgIndex={6} />
          {/* <div className={styles.arrow_img}>
            <Image
              alt=""
              src="/images/showcase/arrow_1.png"
              className={styles.arrow_img}
              width={135}
              height={116}
            ></Image>
          </div> */}
          {/* <StepSeven imgIndex={6} /> */}
          <div className={styles.all_made_end}>
            <span className={styles.horizontal_line}></span>
            &nbsp;&nbsp;
            {i18n.t('Promotion.allmadeforcreator')}
            &nbsp;&nbsp;
            <span className={styles.horizontal_line}></span>
          </div>
        </div>
      </TransparentNavPage>
    )
  )
}

export default SampleGuide
