import React from 'react'
import { metadataTemplate, inApp } from '@/lib/server/utils'
import Image from 'next/image'
import image1 from '@/../public/images/sources/TAP GMV No. 1 in Jan. 2025.png'
import image2 from '@/../public/images/sources/MCN No. 1 in 2024.png'
import image3 from '@/../public/images/sources/TAP No. 1 in 2024.png'

interface Props {
  params: {
    id: string
  }
}

const posters = {
  1: {
    image: image1,
    shareImage: `${process.env.NEXT_PUBLIC_URL}/images/sources/TAP GMV No. 1 in Jan. 2025.png`,
    shareTitle: 'TAP GMV No. 1 in Jan. 2025'
  },
  2: {
    image: image2,
    shareImage: `${process.env.NEXT_PUBLIC_URL}/images/sources/MCN No. 1 in 2024.png`,
    shareTitle: 'MCN No. 1 in 2024'
  },
  3: {
    image: image3,
    shareImage: `${process.env.NEXT_PUBLIC_URL}/images/sources/TAP No. 1 in 2024.png`,
    shareTitle: 'TAP No. 1 in 2024'
  }
}

export async function generateMetadata({ params }: Props) {
  if (inApp()) {
    return metadataTemplate({
      title: posters[params.id].shareTitle,
      showNativeNavBar: true,
      showNativeNavBarShareButton: true
    })
  }

  const icon = `${process.env.NEXT_PUBLIC_URL}/images/sources/${
    posters[params.id].shareImage
  }`

  return metadataTemplate({ title: posters[params.id].shareTitle, icon })
}

export default async function Index(props: Props) {
  return (
    <Image
      layout="responsive"
      objectFit="contain"
      objectPosition="top"
      src={posters[props.params.id].image}
      alt={''}
    ></Image>
  )
}
