import { useMounted } from '@/lib/hooks/useMounted'
import { getGoAppBarHeight } from '../GoAppBar'
import { getNavHeight } from '../TransparentNavPage'
import { ReactNode } from 'react'

interface Props {
  children: ReactNode | ReactNode[]
  height?: number
  zIndex?: string
}

export const FixedHeader = ({ children, height, zIndex }: Props) => {
  const mounted = useMounted()

  return mounted ? (
    <div
      id="sticky-header"
      style={{
        top: `${getGoAppBarHeight()}px`,
        position: 'fixed',
        zIndex: zIndex ? zIndex : 2,
        height: height || 'auto'
      }}
    >
      {children}
    </div>
  ) : null
}
