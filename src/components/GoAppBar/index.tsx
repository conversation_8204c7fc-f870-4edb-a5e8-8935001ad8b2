/* eslint-disable @next/next/no-img-element */
'use client'
import { i18n } from '@/lib/client/i18n'
import { isPreviewMode, makeSureInApp, scaledPx } from '@/lib/client/utils'
import { webview } from '@/lib/client/webview'
import { useMounted } from '@/lib/hooks/useMounted'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { usePathname } from 'next/navigation'
import { ypkClient, ypkH5 } from '@/lib/ypk'

export const GoAppBar = () => {
  const mounted = useMounted()
  const pathName = usePathname()

  return mounted &&
    !webview &&
    !isPreviewMode() &&
    !pathName.includes('poster/') &&
    !pathName.includes('account_deactivation') &&
    !pathName.includes('download_url') &&
    !pathName.includes('link_share') &&
    !ypkClient &&
    !ypkH5 ? (
    <div
      id="go-app-bar"
      className="sticky top-0 z-[99999] flex w-full items-center justify-between bg-gray6E px-[24px] py-[8px]"
    >
      <div className="flex items-center">
        <img
          src="/images/logo/logo.png"
          className="size-[64px] rounded-[12px]"
        ></img>
        <span className="ml-[10px] text-[26px] text-white">
          {i18n.t('Common.openAppTips')}
        </span>
      </div>
      <div
        className="flex h-[50px] items-center justify-center rounded-[4px] bg-primary px-[24px] touch-opacity"
        onClick={() => {
          statistic({ eventName: EventName.open_app_bar })
          makeSureInApp()
        }}
      >
        <span className="text-white">{i18n.t('Common.OpenApp')}</span>
      </div>
    </div>
  ) : null
}

export const getGoAppBarHeight = () => {
  return !webview && !isPreviewMode() ? scaledPx(40) : 0
}
