.category {
    position: relative;

    .category_btn_box {
        height: 98px;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

.category_btn_item_box {
    height: 98px;
    overflow: hidden;
}

.category_btn_comm {
    display: flex;
    align-items: center;
    padding: 0 24px;
    padding-left: 24px;
    padding-right: 24px;
    height: 60px;
    border-radius: 30px;
    margin-right: 28px;
    font-size: 26px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.btn_title {
    overflow: hidden;
    text-overflow: ellipsis
}

.category_btn_item {
    background: #F4F5F7;
}

.category_btn_item_active {
    background: #FE6D4525;
    border-radius: 30px;
    border: 2px solid #FE6D45;
    color: #FE6D45;
}

.serial_img {
    width: 14px;
    height: 8px;
    margin-left: 8px;
}

.category_btn {
    display: flex;
    padding-left: 24px;
    height: 70px;
    overflow: hidden;
    // z-index: 3000;
}

.mask_box {
    width: 100%;
    height: 100vh;
    background-color: #00000050;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}