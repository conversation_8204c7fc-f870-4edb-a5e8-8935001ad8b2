'use client'
import React, {
  useEffect,
  useImperativeHandle,
  useState,
  forwardRef
} from 'react'
import styles from './index.module.scss'
import { makeBodyScrollable, rpxToPx } from '@/lib/client/utils'
import { isLanguageEN } from '@/lib/utils'
import FilterPopUp from '../FilterPopUp'
import Image from 'next/image'
import asc from '@/../public/images/talent/asc.png'
import desc from '@/../public/images/talent/desc.png'
import { getGoAppBarHeight } from '@/components/GoAppBar'
import { getLastDate } from '@/app/api/api-uchoice/tiktok/item/getLastDate'
import MonthlySales from '@/components/FilterPopUp/components/MonthlySales'
import DateFilter from '@/components/FilterPopUp/components/DateFilter'
import ShopCategories from '@/components/FilterPopUp/components/ShopCategories'
import { DayTimestampsToLabels } from '@/components/FilterPopUp/components/DateFilter/date'
import CheckedList from '@/components/FilterPopUp/components/CheckedList'
import { getCustomCategory } from '@/app/api/api-uchoice/tiktok/category/getCustomCategory'
import { loading } from '@/lib/client/loading'
import { i18n } from '@/lib/client/i18n'

const CategoryBtns = (
  {
    btnsArr = [],
    setCategoryArrs,
    onCheckedItem,
    handleTabBtns,
    rankType = '1',
    tabIndex,
    handleCloseModal
  },
  ref
) => {
  const [activeIndex, setActiveIndex] = useState(-1)
  const [tabsCon, setTabsCon] = useState<any>(null)
  const [visiblePopUp, setVisiblePopUp] = useState(false)
  const [dayArr, setdayArr] = useState<any>(null)
  const [lastTimeArr, setLastTimeArr] = useState<any>(null)
  const [categoryArr, setCategoryArr] = useState([])

  useEffect(() => {
    makeBodyScrollable(!visiblePopUp)
  }, [visiblePopUp])

  useEffect(() => {
    handleTabTime()
    handleCampaignRankingsInfo()
    console.log('tabIndex', tabIndex)
  }, [tabIndex])
  const customgoryParam = () => {
    switch (tabIndex) {
      case '6':
        return '3'
      case '2':
        return '4'
      case '3':
        return '1'
      case '4':
        return '2'
      default:
        return '1'
    }
  }
  const handleCampaignRankingsInfo = async () => {
    loading.show({ userInteractive: true })
    const res: any = await getCustomCategory({
      type: customgoryParam()
    })
    if (res.code == 200 && res.result) {
      let data = res.result.map(item => {
        return {
          value: item.id,
          label: isLanguageEN() ? item.nameEn : item.name
        }
      })
      data.unshift({
        label: i18n.t('HotSpot.全部'),
        value: ''
      })

      setCategoryArr(data)
    }
    loading.hide()
  }
  const handleTabTime = () => {
    getLastDate({ rankType: tabIndex }).then(res => {
      console.log(tabIndex, '3456')
      if (res.code == 200 && res.result) {
        setLastTimeArr(res.result)
        if (res.result.dayLastTime) {
          let dayTemp = DayTimestampsToLabels(res.result.dayLastTime)[0]
          setdayArr(DayTimestampsToLabels(res.result.dayLastTime))
          console.log(dayTemp, btnsArr, 'dayTemp', btnsArr.length)
          if (btnsArr.length > 0) {
            const newCategoryArr: any = btnsArr.map((item: any) => {
              return {
                label:
                  item?.value == '3' || item?.value == '5'
                    ? dayTemp.label
                    : item.label,
                value: item.value
              }
            })
            setCategoryArrs(newCategoryArr)
          }
        }
      }
    })
  }
  const closeModal = () => {
    setVisiblePopUp(false)
    setActiveIndex(-1)
  }
  const height = getGoAppBarHeight() + 98 + 88
  // const height = getGoAppBarHeight() + getNavHeight() + 98 + 88
  // console.log(height, 'height')
  useImperativeHandle(ref, () => ({
    closeModal
  }))
  const onCheckedItemBtn = e => {
    const newCategoryArrs: any = btnsArr.map((item: any) => {
      return {
        label: tabsCon == item.value ? e.label : item.label,
        value: item.value
      }
    })
    setCategoryArrs(newCategoryArrs)
    onCheckedItem(e)
    setActiveIndex(-1)
  }
  //按钮显示的内容
  const handleBtnCon = btnsIndex => {
    //1商品分类2按销量排序3日期4月销量额5昨日
    switch (btnsIndex) {
      case '1':
        return (
          <ShopCategories
            categoryArr={categoryArr}
            onCheckedItem={onCheckedItemBtn}
            status="category"
          />
        )
      case '2':
        return (
          <ShopCategories onCheckedItem={onCheckedItemBtn} status="sales" />
        )
      case '3':
        return (
          <DateFilter
            onCheckedItem={onCheckedItemBtn}
            lastTimeArr={lastTimeArr}
          />
        )
      case '4':
        return <MonthlySales onCheckedItem={onCheckedItemBtn} />
      case '5':
        return (
          <div>
            <CheckedList arrList={dayArr} onCheckedItem={onCheckedItemBtn} />
          </div>
        )
      default:
        return null
    }
  }
  return (
    <div className={styles.category}>
      <div className={styles.category_btn_box}>
        <div className={styles.category_btn}>
          {(btnsArr || []).map((item: any, index) => {
            return (
              <div className={styles.category_btn_item_box} key={index}>
                <div
                  className={`${
                    index === activeIndex
                      ? `${styles.category_btn_item_active} ${styles.category_btn_comm}`
                      : `${styles.category_btn_item} ${styles.category_btn_comm}`
                  }`}
                  key={index}
                  onClick={() => {
                    setActiveIndex(index)
                    setTabsCon(item.value ? item.value : item)
                    handleTabBtns(item)
                    setVisiblePopUp(true)
                    if (activeIndex == index) {
                      setVisiblePopUp(false)
                      handleCloseModal()
                      setActiveIndex(-1)
                    }
                  }}
                >
                  <span className={styles.btn_title}>{item.label}</span>
                  <Image
                    src={index === activeIndex ? asc : desc}
                    alt="title"
                    className={styles.serial_img}
                  />
                </div>
              </div>
            )
          })}
        </div>
      </div>
      <FilterPopUp visible={visiblePopUp}>{handleBtnCon(tabsCon)}</FilterPopUp>
      {visiblePopUp && (
        <div
          className={styles.mask_box}
          style={{ top: rpxToPx(height) }}
          onClick={() => {
            setVisiblePopUp(false)
            handleCloseModal()
            setActiveIndex(-1)
          }}
        ></div>
      )}
    </div>
  )
}

export default forwardRef(CategoryBtns)
