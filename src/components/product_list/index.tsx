'use client'
import React, { useEffect, useState, useRef } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { itemRankList } from '@/app/api/api-uchoice/tiktok/item/itemRankList'
import CategoryBtns from '@/components/CategoryBtns'
import TalentHead from '@/components/TalentHead'
import ItemList from './components/item_list'
import { makeBodyScrollable } from '@/lib/client/utils'
import { loading } from '@/lib/client/loading'
import { getLastDate } from '@/app/api/api-uchoice/tiktok/item/getLastDate'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { StickyHeader } from '@/components/StickyHeader'
import TalentTab from '@/components/TalentTab'

let cancelRequests = []

const headTabs = [
  {
    label: i18n.t('Rank.热销商品榜'),
    value: '1'
  },
  {
    label: i18n.t('Rank.潜力爆品榜'),
    value: '2'
  },
  {
    label: i18n.t('Rank.持续好货榜'),
    value: '3'
  }
]
const hotBtnsArr: any = () => [
  {
    label: i18n.t('Rank.商品分类'),
    value: '1'
  },
  {
    label: i18n.t('Rank.日榜'),
    value: '5'
  }
]
const topBtnsArr: any = () => [
  {
    label: i18n.t('Rank.商品分类'),
    value: '1'
  }
]
const bestBtnsArr: any = () => [
  {
    label: i18n.t('Rank.商品分类'),
    value: '1'
  },
  {
    label: i18n.t('Rank.日榜'),
    value: '5'
  }
]

const ProductList = () => {
  const mounted = useMounted()
  const closeModalRef = useRef()
  const [pageNo, setPageNo] = useState(1)
  const [total, setTotal] = useState(0)
  const [params, setParams] = useState({})
  const [itemList, setItemList] = useState([])
  const [hasMore, setHasMore] = useState(true)
  const [categoryBtnIndex, setCategoryBtnIndex] = useState(0)
  const tabIndex = useRef('1')
  const [defaultTime, setDefaultTime] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [categoryArrs, setCategoryArrs] = useState(hotBtnsArr())
  const routeParams = useRouteParams()

  useEffect(() => {
    if (routeParams && routeParams['tabIndex']) {
      const urlParams = new URLSearchParams(window.location.search)
      urlParams.delete('tabIndex')
      window.history.replaceState(
        {},
        '',
        `${window.location.pathname}?${urlParams.toString()}`
      )

      setTimeout(() => {
        onChangeTabs(routeParams['tabIndex'])
      }, 1000)
    }
  }, [])
  useEffect(() => {
    if (tabIndex.current != '3') {
      handleTabTime()
    }
  }, [tabIndex.current])
  const handleTabTime = () => {
    loading.show({ userInteractive: true })
    getLastDate({ rankType: tabIndex.current }).then(res => {
      if (res.code == 200 && res.result) {
        if (res.result.dayLastTime) {
          let dayTemp = res.result.dayLastTime[0]
          onLoadRefresh(null, null, dayTemp)
          setDefaultTime(dayTemp)
        }
      }
    })
  }
  const handleTabBtns = item => {
    setCategoryBtnIndex(item.value)
    makeBodyScrollable(false)
  }
  const onLoadRefresh = async (page?, paramsQuery?, dayTemp?) => {
    page ? setPageNo(page) : setPageNo(1)
    loading.show({ userInteractive: true })
    setIsLoading(true)
    let paramQuery: any = {}
    if (tabIndex.current != '3') {
      paramQuery = {
        dateStartTimestamp: dayTemp ? dayTemp : defaultTime,
        dateType: 1
      }
    } else {
      paramQuery = {
        dateStartTimestamp: '',
        dateType: ''
      }
    }
    paramQuery = {
      ...params,
      ...paramQuery,
      type: tabIndex.current || 1,
      pageNo: page ? page : 1,
      pageSize: 10,
      ...(paramsQuery && { ...paramsQuery })
    }
    setParams(paramQuery)

    const requestP = new Promise(resove => {
      itemRankList({ ...paramQuery }).then(res => {
        resove(res)
      })
    })

    const cancelP = new Promise(resove => {
      // @ts-ignore
      cancelRequests.push(() => resove(null))
    })

    Promise.race([requestP, cancelP]).then((res: any) => {
      console.log(requestP, 'requestP')
      if (res) {
        loading.hide()
        setIsLoading(false)
        if (res.code == 200) {
          setTotal(res.result.total)
          let list = res.result.list
          if (list.length == 0 && paramQuery.pageNo == 1) {
            setItemList([])
          }

          if (list.length > 0) {
            let newList = page ? [...itemList, ...list] : list
            setItemList(newList)
            setHasMore(true)
          }
        }
      }
    })
  }

  const fetchData = () => {
    if (total == itemList.length) {
      setHasMore(false)
      return
    }

    let page = pageNo + 1
    setPageNo(page)
    // 模拟异步加载数据
    setTimeout(() => {
      onLoadRefresh(page, null, null)
    }, 10)
  }

  const onCheckedItem = item => {
    let paramsQuery: any = {
      ...params,
      pageNo: 1
    }
    if (categoryBtnIndex == 1) {
      paramsQuery = {
        ...paramsQuery,
        customCategoryId: item.value
      }
    }
    if (categoryBtnIndex == 5) {
      paramsQuery = {
        ...paramsQuery,
        dateStartTimestamp: item.value
      }
    }

    setParams(paramsQuery)
    onLoadRefresh(null, paramsQuery)

    setTimeout(() => {
      // @ts-ignore
      closeModalRef.current.closeModal()
      makeBodyScrollable(true)
    }, 300)
  }
  const onChangeTabs = e => {
    makeBodyScrollable(true)
    // @ts-ignore
    cancelRequests.every(req => req())
    cancelRequests = []
    let paramsQuery: any = {
      ...params,
      type: e,
      pageNo: 1,
      customCategoryId: ''
    }
    setItemList([])
    setHasMore(true)
    if (e == '3') {
      paramsQuery = {
        ...paramsQuery,
        dateStartTimestamp: '',
        dateType: ''
      }
      onLoadRefresh(null, paramsQuery)
    }
    setParams(paramsQuery)
    tabIndex.current = e
    if (e == '1') {
      setCategoryArrs(hotBtnsArr())
    } else if (e == '2') {
      setCategoryArrs(bestBtnsArr())
    } else {
      setCategoryArrs(topBtnsArr())
    }
    // @ts-ignore
    closeModalRef.current.closeModal()
  }
  const handleCloseModal = () => {
    makeBodyScrollable(true)
  }
  return mounted ? (
    <div className={styles.fix_boxs}>
      <StickyHeader>
        <div className={styles.fix_container}>
          <TalentHead>
            <TalentTab
              activeKey={tabIndex.current}
              items={headTabs}
              onChangeTabs={onChangeTabs}
            />
          </TalentHead>

          <CategoryBtns
            rankType="1"
            btnsArr={categoryArrs}
            setCategoryArrs={setCategoryArrs}
            handleCloseModal={handleCloseModal}
            tabIndex={tabIndex.current}
            handleTabBtns={handleTabBtns}
            ref={closeModalRef}
            onCheckedItem={onCheckedItem}
          ></CategoryBtns>
        </div>
      </StickyHeader>
      <div className={styles.store_box}>
        <ItemList
          tabIndex={tabIndex.current}
          isLoading={isLoading}
          hasMore={hasMore}
          itemList={itemList}
          fetchData={fetchData}
          total={total}
        />
      </div>
    </div>
  ) : null
}

export default ProductList
