import { i18n } from '@/lib/client/i18n'
import { Mask } from 'antd-mobile'
import React, { ReactNode } from 'react'
import happy_alert_mask from '@/../public/images/common/happy_alert_mask.png'
import { Image } from '../Image'

interface AlertButton {
  title?: string
  onClick?: () => void
}

interface AlertProps {
  visible: boolean
  title: ReactNode
  content: ReactNode
  confirmBtn?: AlertButton
  onMaskClick?: () => void
}

export const HappyAlert = ({
  visible,
  title,
  content,
  confirmBtn,
  onMaskClick
}: AlertProps) => {
  return (
    <Mask visible={visible} disableBodyScroll>
      <div
        className="flex h-screen w-screen items-center justify-center"
        onClick={onMaskClick}
      >
        <div className="relative pb-[170px]">
          <div className="mt-[170px] flex w-[602px] flex-col justify-center rounded-[32px] bg-white px-[96px] pb-[70px] pt-[156px]">
            <span className="text-center text-[52px] font-bold text-black">
              {title}
            </span>
            <div className="mt-[24px] text-[28px] text-gray6E">{content}</div>
            <div
              className="mt-[64px] flex h-[80px] w-full items-center justify-center rounded-[8px] touch-opacity"
              style={{
                background:
                  'linear-gradient( 20deg, #FF4F98 0%, #FF2D2A 59%, #FF2F2A 63%, #FF772F 100%)'
              }}
              onClick={confirmBtn?.onClick}
            >
              <span className="text-[32px] font-bold text-white">
                {confirmBtn?.title || i18n.t('Common.Confirm')}
              </span>
            </div>
          </div>
          <div className="absolute top-0 h-[481px] w-[602px]">
            <Image
              src={happy_alert_mask}
              className="h-[481px] w-[602px]"
            ></Image>
          </div>
        </div>
      </div>
    </Mask>
  )
}
