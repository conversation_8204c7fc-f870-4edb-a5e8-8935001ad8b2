import React from 'react'
import icon_divider from '@/../public/images/similar/icon_divider.png'
import styles from './index.module.scss'
import { formatPrice } from '@/lib/format'
import { i18n } from '@/lib/client/i18n'
import Image from 'next/image'

interface Props {
  earn: string
}

export const Earn = ({ earn }: Props) => {
  return (
    <div className={styles.price_container}>
      <div className={styles.earn_container}>
        <div className={styles.earn_label}>{i18n.t('Product.Earn')}</div>
        <Image
          quality={100}
          src={icon_divider}
          alt="earn_divider"
          className={styles.earn_divider}
        ></Image>
        <div className={styles.earn_value_container}>
          <div className={styles.earn_value}>{earn}</div>
        </div>
      </div>
    </div>
  )
}
