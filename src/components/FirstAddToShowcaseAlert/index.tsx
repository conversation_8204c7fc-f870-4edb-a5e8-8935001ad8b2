'use client'
import React, { useLayoutEffect, useState } from 'react'
import right from '@/../public/images/common/right.png'
import wrong from '@/../public/images/common/wrong.png'
import first_alert_en from '@/../public/images/product/detail/first_alert_en.png'
import first_alert_th from '@/../public/images/product/detail/first_alert_th.png'
import first_alert_vn from '@/../public/images/product/detail/first_alert_vn.png'
import { Image } from '@/components/Image'
import { Alert } from '@/components/Alert'
import { i18n } from '@/lib/client/i18n'
import { TransComponent } from '@/app/components/Trans'
import { useMounted } from '@/lib/hooks/useMounted'

export class FirstAddToShowcaseAlertManager {
  static _onAdd = () => {}
  static show(onAdd: () => void) {}
}

export const FirstAddToShowcaseAlert = () => {
  const [visible, setVisible] = useState(false)
  const mounted = useMounted()

  useLayoutEffect(() => {
    FirstAddToShowcaseAlertManager.show = onAdd => {
      setVisible(true)
      FirstAddToShowcaseAlertManager._onAdd = onAdd
    }
  }, [])

  return mounted ? (
    <Alert
      visible={visible}
      content={
        <div className="flex w-full flex-col items-center">
          <TransComponent
            en={
              <Image
                className="h-[454px] w-[240px] rounded-[8px]"
                src={first_alert_en}
              ></Image>
            }
            th={
              <Image
                className="h-[454px] w-[240px] rounded-[8px]"
                src={first_alert_th}
              ></Image>
            }
            vi={
              <Image
                className="h-[454px] w-[240px] rounded-[8px]"
                src={first_alert_vn}
              ></Image>
            }
          ></TransComponent>
          <span className="mb-[44px] mt-[24px] text-center text-[30px] text-black">
            {i18n.t('Sample.请认准【YOUPIK】高佣链接')}
          </span>
          <div className="flex w-full items-center justify-between">
            <Image src={right} className="size-[32px]"></Image>
            <div className="flex flex-1 pl-[18px]">
              <span className="line-clamp-0 text-ellipsis break-words text-[26px] text-black">
                {i18n.t('Sample.DOs弹窗处点击add showcase')}
              </span>
            </div>
          </div>
          <div className="my-[40px] flex w-full items-center justify-between">
            <Image src={wrong} className="size-[32px]"></Image>
            <div className="flex flex-1 pl-[18px]">
              <span className="line-clamp-0 text-ellipsis break-words text-[26px] text-black">
                {i18n.t('Sample.DONTs弹窗处点击view more后add showcase')}
              </span>
            </div>
          </div>
        </div>
      }
      confirmBtn={{
        title: i18n.t('Sample.我已知晓'),
        onClick: () => {
          setVisible(false)
          FirstAddToShowcaseAlertManager._onAdd()
        }
      }}
    ></Alert>
  ) : null
}
