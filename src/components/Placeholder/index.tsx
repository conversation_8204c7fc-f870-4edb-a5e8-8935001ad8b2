import React from 'react'
import { Image } from '@/components/Image'
import { i18n } from '@/lib/client/i18n'

interface PlaceholderProps {
  type: 'search' | 'no-results-found' | 'none'
}

const Placeholder = ({ type }: PlaceholderProps) => {
  const data = {
    search: {
      image: '/images/placeholders/search_products.png',
      title: '',
      desc: ''
    },
    'no-results-found': {
      image: '/images/placeholders/no_results_found.png',
      title: i18n.t('Search.搜索无结果'),
      desc: i18n.t('Search.请尝试其他关键词')
    }
  }

  return type != 'none' ? (
    <div className="flex w-[500px] flex-col items-center">
      <Image src={data[type].image} className="h-[280px] w-[400px]"></Image>
      <span className="mt-[40px] w-full break-words text-center text-[30px] font-bold leading-[42px] text-black">
        {data[type].title}
      </span>
      <span className="mt-[20px] w-full break-words text-center text-[28px] leading-[40px] text-[#6E6E6E]">
        {data[type].desc}
      </span>
    </div>
  ) : null
}

export { Placeholder }
export type { PlaceholderProps }
