'use client'
import { i18n } from '@/lib/client/i18n'
import { toast } from '@/lib/client/toast'
import React, { useState } from 'react'
import { JsonView, allExpanded, defaultStyles } from 'react-json-view-lite'
import 'react-json-view-lite/dist/index.css'
import copy from '@/../public/images/common/copy.png'
import arrow_down from '@/../public/images/hotspot/icon_arrow_down.png'
import close from '@/../public/images/common/modal_close.png'
import { Image } from '@/components/Image'
import { copyText } from '@/lib/client/utils'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { webview } from '@/lib/client/webview'
import { useMounted } from '@/lib/hooks/useMounted'

export const JSONView = ({ json }: { json: any }) => {
  const mounted = useMounted()
  const { apilog } = useRouteParams<{ apilog?: string }>()
  const [visible, setVisible] = useState(true)
  const [expand, setExpand] = useState(false)

  return mounted && visible && !!apilog ? (
    <>
      <div
        className=" relative flex justify-center bg-[#eeeeee] px-[24px] py-[12px] text-black"
        style={{
          paddingTop: webview ? webview?.getData().topSafeArea + 44 : 0
        }}
      >
        {expand ? (
          <div
            className="flex items-center touch-opacity"
            onClick={() => copyText(JSON.stringify(json))}
          >
            <span>复制</span>
            <Image src={copy} className="ml-[4px] size-[32px]"></Image>
          </div>
        ) : (
          <div
            className="flex items-center touch-opacity"
            onClick={() => setExpand(!expand)}
          >
            <span>查看接口响应</span>
            <Image
              src={arrow_down}
              className="ml-[4px] h-[12px] w-[20px]"
            ></Image>
          </div>
        )}
        <div
          className="absolute bottom-0 right-0 flex items-center p-[12px] touch-opacity"
          onClick={() => setVisible(false)}
        >
          <Image src={close} className="ml-[4px] size-[20px]"></Image>
        </div>
      </div>
      {expand && (
        <JsonView
          data={json}
          shouldExpandNode={allExpanded}
          style={defaultStyles}
        />
      )}
    </>
  ) : null
}
