'use client'
import React, { ReactNode, useMemo } from 'react'
import { Image } from '@/components/Image'
import { useMounted } from '@/lib/hooks/useMounted'
import styles from './index.module.css'
import { useRouter } from '@/lib/hooks/useRouter'
import { webview } from '@/lib/client/webview'
import { isIOS, px2rem, scaledPx } from '@/lib/client/utils'
import { WebviewEvents } from '@/lib/client/webview/events'
import { useWindowScroll } from '@/lib/hooks/useWindowScroll'
import { useGrayBody } from '@/lib/hooks/useGrayBody'
import { useWebviewBackground } from '@/lib/hooks/useWebviewBackground'
import { ChangeLanguageButton } from '@/app/components/ChangeLanguageButton'
import { useIntersectionObserver } from '@/lib/hooks/useIntersectionObserver'

const navHeight = 44

interface TopGrandientCardProps {
  children: ReactNode | ReactNode[]
  gradientColors?: string[]
}

/**
 * 页面上面第一个渐变色卡片
 */
export const TopGrandientCard = ({
  children,
  gradientColors = ['#ffede4', 'rgba(255,255,255,0)']
}: TopGrandientCardProps) => {
  useWebviewBackground('#FFEDE4')

  return (
    <div className="bg-white">
      {/* 这个24是padding */}
      <div
        className={styles.top_grandient_card_content}
        style={{
          backgroundImage: `linear-gradient(180deg, ${gradientColors[0]} 0%, ${gradientColors[1]} 100%)`,
          paddingTop: webview
            ? `${webview?.getData().topSafeArea + scaledPx(navHeight)}px`
            : `${scaledPx(navHeight)}px`
        }}
      >
        {children}
      </div>
    </div>
  )
}

interface TopTransprantCardProps {
  children: ReactNode | ReactNode[]
}

export const TopTransparentCard = ({ children }: TopTransprantCardProps) => {
  return (
    <div
      style={{
        paddingTop: webview ? 0 : `${scaledPx(navHeight)}px`
      }}
    >
      {children}
    </div>
  )
}

interface TransparentNavPageProps {
  children?: ReactNode | ReactNode[]
  rightChildren?: ReactNode | ReactNode[]
  title?: ReactNode | ReactNode[]
  transparent?: boolean
  hide?: boolean
  showShareButton?: boolean
  showContactUsButton?: boolean
  showBackButton?: boolean
  onShared?(): void
  theme?: 'light' | 'dark'
  titleTheme?: 'light' | 'dark'
  withOutBottomSafeArea?: boolean
  langBtn?: boolean
  langItems?: any[]
  langChange?: (lang: string) => void
  langCurrent?: string
  langSelect?: boolean
  className?: {}
  titleClass?: {}
  hideTitleWhenTransparent?: boolean
  backgroundColor?: string
}

/**
 * 渐变色导航栏
 */
export const TransparentNavPage = ({
  children,
  rightChildren,
  title = '',
  transparent = true,
  hide = false,
  showShareButton = false,
  showContactUsButton = false,
  showBackButton = true,
  langBtn = false,
  onShared,
  theme = 'dark',
  titleTheme = 'dark',
  className = {},
  titleClass = {},
  withOutBottomSafeArea = false,
  hideTitleWhenTransparent = false,
  backgroundColor
}: TransparentNavPageProps) => {
  const mounted = useMounted()

  const position = useWindowScroll()

  const { ref, isLeave } = useIntersectionObserver()

  const router = useRouter()

  const onBack = () => {
    router.back()
  }

  // const isLeave = useMemo(() => position > 0, [position])
  const isDesktopBrowser = () => {
    const userAgent = navigator.userAgent
    return (
      /Windows|Macintosh|Linux/.test(userAgent) &&
      !/Mobile|Android|iPhone|iPad/.test(userAgent)
    )
  }
  const renderNav = () => {
    return (
      <div
        id={hide ? 'nav:hide' : 'nav'}
        className={`fixed z-10 w-[750px] ${className}`}
        style={{
          backgroundColor: backgroundColor || (isLeave
            ? 'white'
            : `rgba(255, 255, 255, ${transparent ? 0 : 1})`),
          transition: isIOS() ? 'background-color 0.2s ease' : 'none',
          borderBottom: `${!isLeave || langBtn ? 'none' : '1px solid #F5F5F5'}`
        }}
      >
        <div
          style={{
            height: webview ? `${webview?.getData().topSafeArea}px` : 0
          }}
        ></div>
        <div
          className="relative px-[4px]"
          style={{
            height: `${langBtn && isDesktopBrowser() ? scaledPx(10) : scaledPx(navHeight)
              }px`
          }}
        >
          {/* 标题 */}
          <div className="absolute inset-0 flex h-full w-[750px] items-center justify-center overflow-hidden px-[20%]">
            {title && (
              <span
                className={`truncate text-[36px] font-bold ${titleTheme === 'dark' ? 'text-black' : 'text-white'} ${titleClass}`}
                style={
                  hideTitleWhenTransparent
                    ? {
                      opacity: isLeave ? 1 : 0
                    }
                    : {}
                }
              >
                {title}
              </span>
            )}
          </div>

          <div className="absolute inset-0 flex h-full w-[750px] items-center justify-between">
            {/* 返回按钮 */}
            {webview && (
              <div
                className="flex w-[180px] px-[24px] py-[10px] touch-opacity"
                onClick={() => {
                  showBackButton && onBack()
                }}
                style={{
                  opacity: showBackButton ? 1 : 0
                }}
              >
                <Image
                  className="size-[44px]"
                  src={
                    isLeave
                      ? '/images/navbar/back.png'
                      : theme === 'light'
                        ? '/images/navbar/back_white.png'
                        : '/images/navbar/back.png'
                  }
                ></Image>
              </div>
            )}

            <div className="flex w-[750px] justify-end px-[24px] py-[10px]">
              {/* 分享按钮 */}
              {showShareButton && webview && (
                <div
                  className="touch-opacity"
                  onClick={() => {
                    webview?.send(WebviewEvents.shareUrl, {
                      url: window.location.href
                    })

                    onShared?.()
                  }}
                >
                  <Image
                    className="size-[40px]"
                    src={
                      isLeave
                        ? '/images/navbar/share.png'
                        : theme === 'light'
                          ? '/images/navbar/share_white.png'
                          : '/images/navbar/share.png'
                    }
                  ></Image>
                </div>
              )}

              {showContactUsButton && <div className="w-[34px]"></div>}

              {/* 客服按钮 */}
              {showContactUsButton && (
                <div
                  className="touch-opacity"
                  onClick={() => webview?.send(WebviewEvents.contactUs)}
                >
                  <Image
                    className="size-[40px]"
                    src={
                      isLeave
                        ? '/images/navbar/cs.png'
                        : theme === 'light'
                          ? '/images/navbar/cs_white.png'
                          : '/images/navbar/cs.png'
                    }
                  ></Image>
                </div>
              )}
              {/* 切换语言按钮 */}
              {!webview && langBtn && (
                <div className="touch-opacity">
                  <ChangeLanguageButton></ChangeLanguageButton>
                </div>
              )}
              {rightChildren}
            </div>
          </div>
        </div>
      </div>
    )
  }
  const NotransparentHeight = () => {
    if (langBtn && isDesktopBrowser()) {
      return `${scaledPx(10)}px`
    } else if (webview) {
      return `${scaledPx(navHeight)}px`
    } else if (hide) {
      return 0
    } else {
      return `${scaledPx(navHeight)}px`
    }
  }
  return mounted ? (
    <div>
      {!hide && renderNav()}
      {/* <div className=" break-words">
        {decodeURIComponent(window.location.search)}
      </div>
      <br />
      <div className=" break-words">{JSON.stringify(webview?.getData())}</div> */}
      {!transparent && (
        <div
          style={{
            paddingTop: webview
              ? `${webview?.getData().topSafeArea + scaledPx(navHeight)}px`
              : 0,
            height: NotransparentHeight(),

            backgroundColor: `${langBtn && isDesktopBrowser() ? '#000' : ''}`
          }}
        ></div>
      )}
      <div ref={ref}></div>
      {children}
      {!withOutBottomSafeArea && (
        <div
          style={{
            height: webview ? `${px2rem(webview?.getData().bottomSafeArea)}` : 0
          }}
        ></div>
      )}
    </div>
  ) : null
}

export const getNavHeight = () => {
  return webview
    ? webview?.getData().topSafeArea + scaledPx(navHeight)
    : document.getElementById('nav')
      ? scaledPx(navHeight)
      : 0
}
