import { Radio as AntRadio, RadioProps } from 'antd-mobile'
import styles from './index.module.css'
import { Image } from '@/components/Image'

const Radio = (props: RadioProps) => {
  return (
    <AntRadio
      {...props}
      icon={checked =>
        checked ? (
          <div className={styles.radio}>
            <Image
              src="/images/common/radio_on.png"
              className="size-[32px]"
            ></Image>
            <span className={styles.label}>{props.children}</span>
          </div>
        ) : (
          <div className={styles.radio}>
            <Image
              src="/images/common/radio_off.png"
              className="size-[32px]"
            ></Image>
            <span className={`${styles.label} ${styles.label_gray}`}>
              {props.children}
            </span>
          </div>
        )
      }
    >
      {''}
    </AntRadio>
  )
}

export { Radio }
