import { px2rem } from '@/lib/client/utils'
import { BottomSafeArea } from '../BottomSafeArea'
import { useEffect, useRef, useState } from 'react'

interface BottomActionsBarProps {
  children: React.ReactNode
  withBorder?: boolean
}

export const BottomActionsBar = ({
  children,
  withBorder = true
}: BottomActionsBarProps) => {
  const [childrenHeight, setChildrenHeight] = useState(0)
  const contentRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const updateHeight = () => {
      if (contentRef.current) {
        const height = contentRef.current.offsetHeight
        setChildrenHeight(height)
      }
    }

    // 初始计算高度
    updateHeight()

    // 监听窗口大小变化
    window.addEventListener('resize', updateHeight)

    // 使用 ResizeObserver 监听内容变化
    const resizeObserver = new ResizeObserver(updateHeight)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }

    return () => {
      window.removeEventListener('resize', updateHeight)
      resizeObserver.disconnect()
    }
  }, [children])

  return (
    <div>
      <div className="pt-[24px]"></div>
      <div style={{ height: px2rem(childrenHeight) }}></div>
      <BottomSafeArea />

      <div
        ref={contentRef}
        className="fixed bottom-0 left-0 w-full bg-white px-[24px] pt-[24px]"
        style={{
          borderTop: withBorder ? '1px solid #F5F5F5' : 'none'
        }}
      >
        {children}
        <BottomSafeArea />
      </div>
    </div>
  )
}

export const PrimaryButton = ({
  text,
  onClick
}: {
  text: string
  onClick: () => void
}) => {
  return (
    <div
      className="flex h-[80px] w-[702px] items-center justify-center rounded-[4px] border-none bg-primary touch-opacity"
      onClick={onClick}
    >
      <span className="text-[32px] font-bold text-white">{text}</span>
    </div>
  )
}
