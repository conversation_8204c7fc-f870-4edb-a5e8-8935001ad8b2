'use client'
import React, { useEffect, useState } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { rpxToPx } from '@/lib/client/utils'
import remind from '@/../public/images/talent/remind.png'
import Image from 'next/image'
import CheckedList from '../CheckedList'

const MonthlySales = ({ onCheckedItem }) => {
  const monthlyArr = [
    { label: i18n.t('HotSpot.全部'), value: '0' },
    { label: i18n.t('Rank.头部达人 >1000w'), value: '1' },
    { label: i18n.t('Rank.肩部达人 100-1000w'), value: '2' },
    { label: i18n.t('Rank.中腰部达人 10-100w'), value: '3' },
    { label: i18n.t('Rank.小达人 3-10w'), value: '4' },
    { label: i18n.t('Rank.尾部达人 <3w'), value: '5' }
  ]
  return (
    <div className={styles.monthlySales_box}>
      <div className={styles.remind_box}>
        <Image src={remind} className={styles.remind_img} alt="title" />
        <span>{i18n.t('Rank.近30天达人直播带货或视频带货GMV')}</span>
      </div>
      <CheckedList onCheckedItem={onCheckedItem} arrList={monthlyArr} />
    </div>
  )
}

export default MonthlySales
