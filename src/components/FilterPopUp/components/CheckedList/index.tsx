'use client'
import React, { useEffect, useState } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { rpxToPx } from '@/lib/client/utils'
import checked from '@/../public/images/talent/checked.png'
import Image from 'next/image'

const CheckedList = ({ arrList, onCheckedItem, isLine = '' }) => {
  const [activeIndex, setActiveIndex] = useState(-1)
  return (
    <div className={styles.checkedList_box}>
      {(arrList || []).map((item, index) => {
        return (
          <div
            key={index}
            onClick={() => {
              onCheckedItem(item), setActiveIndex(index)
            }}
            style={{ borderBottom: isLine ? '1px solid #eee' : 0 }}
            className={styles.checkedList_item_box}
          >
            <div
              className={`${
                index == activeIndex
                  ? `${styles.checkedList_item_active} ${styles.checkedList_item_comm}`
                  : `${styles.checkedList_item} ${styles.checkedList_item_comm}`
              }`}
            >
              {item.label ? item.label : item}
            </div>
            {index == activeIndex && (
              <Image
                src={checked}
                className={styles.checked_img}
                alt="checked"
              />
            )}
          </div>
        )
      })}
    </div>
  )
}

export default CheckedList
