'use client'
import React, { use, useEffect, useState } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { rpxToPx } from '@/lib/client/utils'
import CheckedList from '../CheckedList'
import {
  WeekTimestampsToLabels,
  MonthTimestamps,
  DayTimestampsToLabels
} from './date'

const DateFilter = ({ onCheckedItem, lastTimeArr }) => {
  const [activeIndex, setActiveIndex] = useState(1)
  const [weekArr, setWeekArr] = useState([])
  const [monthArr, setMonthArr] = useState([])
  const [dayArr, setDayArr] = useState([])
  useEffect(() => {
    if (lastTimeArr) {
      if (lastTimeArr.monthLastTime) {
        setMonthArr(MonthTimestamps(lastTimeArr.monthLastTime))
      }
      if (lastTimeArr.dayLastTime) {
        setDayArr(DayTimestampsToLabels(lastTimeArr.dayLastTime))
      }
      if (lastTimeArr.weekLastTime) {
        setWeekArr(WeekTimestampsToLabels(lastTimeArr.weekLastTime))
      }
    }
  }, [])
  const dataArr = [
    { label: i18n.t('Rank.日榜'), value: '1' },
    { label: i18n.t('Rank.周榜'), value: '2' },
    { label: i18n.t('Rank.月榜'), value: '3' }
  ]

  return (
    <div className={styles.date_box}>
      <div className={styles.date_list}>
        {dataArr.map((item, index) => {
          return (
            <div
              key={item.value}
              onClick={() => {
                setActiveIndex(index + 1)
              }}
              className={`${
                index + 1 == activeIndex
                  ? `${styles.date_list_item_active} ${styles.date_list_item_comm}`
                  : `${styles.date_list_item} ${styles.date_list_item_comm} `
              }`}
            >
              {item.label}
            </div>
          )
        })}
      </div>
      <div className={styles.date_box}>
        <CheckedList
          onCheckedItem={onCheckedItem}
          isLine="1"
          arrList={
            activeIndex == 1 ? dayArr : activeIndex == 2 ? weekArr : monthArr
          }
        ></CheckedList>
      </div>
    </div>
  )
}

export default DateFilter
