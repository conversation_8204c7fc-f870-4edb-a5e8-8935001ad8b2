//一天
function DayTimestampsToLabels(timestamps) {
  const convertedDates: any = []

  timestamps.forEach(timestamp => {
    const date = new Date(timestamp)
    const day = date.getDate() // 获取日期
    const month = date.getMonth() + 1 // 获取月份（注意：月份从0开始）
    const year = date.getFullYear() // 获取年份的最后两位

    // 将数字转换为两位数的字符串
    const formattedDay = day < 10 ? '0' + day : day
    const formattedMonth = month < 10 ? '0' + month : month
    const formattedYear = year < 10 ? '0' + year : year

    const label = `${formattedDay}/${formattedMonth}/${formattedYear}` // 格式化日期范围的字符串
    const value = timestamp // 时间戳作为值

    const obj = { label, value } // 创建包含 label 和 value 的对象
    convertedDates.push(obj)
  })

  return convertedDates
}
//一月
function MonthTimestamps(timestamps) {
  const convertedDates: any = []

  timestamps.forEach(timestamp => {
    const date = new Date(timestamp)
    const month = date.getMonth() + 1 // 获取月份（注意：月份从0开始）
    const year = date.getFullYear() // 获取年份的最后两位

    // 将数字转换为两位数的字符串
    const formattedMonth = month < 10 ? '0' + month : month
    const formattedYear = year < 10 ? '0' + year : year

    const label = `${formattedMonth}/${formattedYear}` // 格式化日期范围的字符串
    const value = timestamp // 时间戳作为值

    const obj = { label, value } // 创建包含 label 和 value 的对象
    convertedDates.push(obj) // 将对象添加到数组中
  })

  return convertedDates
}
//一周
function WeekTimestampsToLabels(timestamps) {
  const convertedDates: any = []

  timestamps.forEach(timestamp => {
    const startDate = new Date(timestamp)
    const endDate = new Date(startDate)
    endDate.setDate(endDate.getDate() + 6) // 将开始日期增加6天得到结束日期
    const startDay = startDate.getDate() // 获取开始日期的日期
    const startMonth = startDate.getMonth() + 1 // 获取开始日期的月份
    const endDay = endDate.getDate() // 获取结束日期的日期
    const endMonth = endDate.getMonth() + 1 // 获取结束日期的月份

    // 将年份转换为最后两位
    const startYear = startDate.getFullYear()
    const endYear = endDate.getFullYear()

    // 将数字转换为两位数的字符串
    const formattedStartDay = startDay < 10 ? '0' + startDay : startDay
    const formattedStartMonth = startMonth < 10 ? '0' + startMonth : startMonth
    const formattedEndDay = endDay < 10 ? '0' + endDay : endDay
    const formattedEndMonth = endMonth < 10 ? '0' + endMonth : endMonth
    const formattedStartYear = startYear < 10 ? '0' + startYear : startYear
    const formattedEndYear = endYear < 10 ? '0' + endYear : endYear

    const label = `${formattedStartDay}/${formattedStartMonth}/${formattedStartYear}-${formattedEndDay}/${formattedEndMonth}/${formattedEndYear}` // 格式化日期范围的字符串
    const value = timestamp // 时间戳作为值

    const obj = { label, value } // 创建包含label和value的对象
    convertedDates.push(obj) // 将对象添加到数组中
  })
  return convertedDates
}
function getDateType(dateString) {
  // 正则表达式模式匹配日期字符串
  const dayPattern = /^\d{2}\/\d{2}\/\d{4}$/ // 匹配类似 '3/28' 的格式
  const weekPattern = /^\d{2}\/\d{2}\/\d{4}-\d{2}\/\d{2}\/\d{4}$/ // 匹配类似 '3/28-3/29' 的格式
  const monthPattern = /^\d{2}\/\d{4}$/ // 匹配类似 '2023-4' 的格式

  if (dayPattern.test(dateString)) {
    return 'day' // 如果匹配到 dayPattern，则返回 'day'
  } else if (weekPattern.test(dateString)) {
    return 'week' // 如果匹配到 weekPattern，则返回 'week'
  } else if (monthPattern.test(dateString)) {
    return 'month' // 如果匹配到 monthPattern，则返回 'month'
  } else {
    return 'unknown' // 如果无法匹配任何模式，则返回 'unknown'
  }
}

export {
  WeekTimestampsToLabels,
  MonthTimestamps,
  DayTimestampsToLabels,
  getDateType
}
