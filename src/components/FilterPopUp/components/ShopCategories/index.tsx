'use client'
import React, { useEffect, useState } from 'react'
import styles from './index.module.scss'
import { rpxToPx } from '@/lib/client/utils'
import CheckedList from '../CheckedList'
import { i18n } from '@/lib/client/i18n'

const ShopCategories = ({ status, onCheckedItem, categoryArr = [] }) => {
  const saleSort = [
    { label: i18n.t('TiktokData.按销量排序'), value: 'sales' },
    { label: i18n.t('TiktokData.按销售额排序'), value: 'gmv' }
  ]

  return (
    <div className={styles.monthlySales_box}>
      <CheckedList
        arrList={status == 'category' ? categoryArr : saleSort}
        onCheckedItem={onCheckedItem}
      />
    </div>
  )
}

export default ShopCategories
