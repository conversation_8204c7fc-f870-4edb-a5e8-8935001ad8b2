/* eslint-disable @next/next/no-img-element */
'use client'
import { isPreviewMode, px2rem, scaledPx } from '@/lib/client/utils'
import { webview } from '@/lib/client/webview'
import { useMounted } from '@/lib/hooks/useMounted'
import { useMemo } from 'react'
import float_btn from '@/../public/images/coupon1212/float_btn.png'
import { Image } from '../Image'
import { useRouter } from '@/lib/hooks/useRouter'
import { FloatingBubble } from 'antd-mobile'
import './index.css'
import { usePathname } from 'next/navigation'

export const CounponFloatButton = () => {
  const mounted = useMounted()
  const router = useRouter()
  const pathName = usePathname()

  const visible = useMemo(() => {
    const startTime = new Date('2024-12-09T00:00:00').getTime()
    const endTime = new Date('2024-12-12T23:59:59').getTime()
    const now = Date.now()
    return now >= startTime && now <= endTime
  }, [])

  return mounted &&
    visible &&
    !pathName.includes('account_deactivation') &&
    !pathName.includes('download_url') &&
    !pathName.includes('coupon1212') ? (
    <FloatingBubble
      axis="xy"
      magnetic="x"
      defaultOffset={{
        x: 0,
        y: -100
      }}
      style={{
        '--background': 'transparent',
        '--initial-position-bottom': '0px',
        '--initial-position-right': '0px',
        '--edge-distance': '0px',
        '--border-radius': '0',
        '--size': px2rem(140)
      }}
      onClick={() => router.push('/activity/coupon1212/page')}
    >
      <div>
        <div className="animate-zoom">
          <Image src={float_btn} className="size-[140px]"></Image>
        </div>
      </div>
    </FloatingBubble>
  ) : null
}

export const getGoAppBarHeight = () => {
  return !webview && !isPreviewMode() ? scaledPx(40) : 0
}
