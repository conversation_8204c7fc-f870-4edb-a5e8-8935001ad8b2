import { Mask } from 'antd-mobile'
import React, { ReactNode } from 'react'

interface AlertButton {
  title: string
  onClick?: () => void
}

interface AlertProps {
  visible: boolean
  title?: ReactNode
  content: ReactNode
  cancelBtn?: AlertButton
  confirmBtn?: AlertButton
  onMaskClick?: () => void
}

export const Alert = ({
  visible,
  title,
  content,
  cancelBtn,
  confirmBtn,
  onMaskClick
}: AlertProps) => {
  return (
    <Mask visible={visible} disableBodyScroll>
      <div
        className="flex  h-screen w-screen items-center justify-center"
        onClick={onMaskClick}
      >
        <div className="flex w-[602px] flex-col items-center rounded-[8px] bg-white px-[36px] pb-[44px] pt-[36px]">
          {title && (
            <div className="pb-[56px]">
              {typeof title === 'string' ? (
                <div className="flex items-center">
                  <span className="ml-[12px] text-[36px] font-bold text-black30">
                    {title}
                  </span>
                </div>
              ) : (
                title
              )}
            </div>
          )}
          {typeof content === 'string' ? (
            <div className="mb-[70px] text-[32px] text-black30">{content}</div>
          ) : (
            content
          )}
          <div className="flex w-full justify-between">
            {cancelBtn && (
              <div
                className=" h-[80px] flex-1 rounded-[4px] border-2  border-grayD9 text-center text-[28px] leading-[80px] text-black30 touch-opacity"
                onClick={e => {
                  e.stopPropagation()
                  cancelBtn.onClick && cancelBtn.onClick()
                }}
              >
                {cancelBtn.title}
              </div>
            )}
            {cancelBtn && confirmBtn && <div className="w-[18px]"></div>}
            {confirmBtn && (
              <div
                className=" h-[80px] flex-1 rounded-[4px] border-2  border-primary bg-primary text-center text-[28px] leading-[80px] text-white  touch-opacity"
                onClick={e => {
                  e.stopPropagation()
                  confirmBtn.onClick && confirmBtn.onClick()
                }}
              >
                {confirmBtn.title}
              </div>
            )}
          </div>
        </div>
      </div>
    </Mask>
  )
}
