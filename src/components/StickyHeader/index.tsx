import { useMounted } from '@/lib/hooks/useMounted'
import { getGoAppBarHeight } from '../GoAppBar'
import { getNavHeight } from '../TransparentNavPage'
import { ReactNode } from 'react'

interface Props {
  children: ReactNode | ReactNode[]
  height?: string
  zIndex?: string
  withoutNavHeight?: boolean
}

export const StickyHeader = ({
  children,
  height,
  zIndex,
  withoutNavHeight
}: Props) => {
  const mounted = useMounted()

  return mounted ? (
    <div
      id="sticky-header"
      style={{
        top: `${
          getGoAppBarHeight() + (withoutNavHeight ? 0 : getNavHeight())
        }px`,
        position: 'sticky',
        zIndex: zIndex ? zIndex : 2,
        height: height || 'auto'
      }}
    >
      {children}
    </div>
  ) : null
}
