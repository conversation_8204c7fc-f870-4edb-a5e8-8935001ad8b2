'use client'

import React, { useEffect, useRef, useState } from 'react'
import Plyr from 'plyr'
import modal_circle_close from '@/../public/images/common/modal_circle_close.png'
import { Image } from '../Image'
import { webview } from '@/lib/client/webview'

export const videoPreviewer = {
  play: (src: string) => {},
  close: () => {}
}

const VideoPreviewer = () => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const playerRef = useRef<Plyr>()
  const [url, setUrl] = useState('')

  useEffect(() => {
    videoPreviewer.play = (src: string) => {
      setUrl(src)
    }

    videoPreviewer.close = () => {
      setUrl('')
    }
  }, [])

  useEffect(() => {
    if (url.length > 0 && videoRef.current) {
      videoRef.current.src = url
      videoRef.current.load()
      videoRef.current.play()
    } else {
      videoRef.current?.pause()
    }
  }, [url, videoRef.current])

  useEffect(() => {
    if (!videoRef.current) return

    const defaultOptions: Plyr.Options = {
      controls: [
        'play',
        'progress',
        'current-time',
        'mute',
        'volume',
        'fullscreen'
      ],
      hideControls: false,
      i18n: {
        restart: '重新播放'
      }
    }

    playerRef.current = new Plyr(videoRef.current, defaultOptions)

    return () => {
      if (playerRef.current) {
        playerRef.current.destroy()
      }
    }
  }, [])

  return url.length > 0 ? (
    <div className="fixed left-0 top-0 z-50 flex h-[100vh] w-screen touch-none items-center justify-center bg-black/90">
      <div className="flex flex-col items-center justify-center">
        <video
          ref={videoRef}
          playsInline
          className="max-h-[75vh] max-w-[90vw]"
          autoPlay
          controls
          disablePictureInPicture
        >
          <source type="video/mp4" />
          <source type="video/webm" />
          <source type="video/ogg" />
          <source type="video/quicktime" />
        </video>

        <div
          className="mt-[24px]"
          onClick={() => {
            videoPreviewer.close()
          }}
        >
          <Image src={modal_circle_close} className="h-[56px] w-[58px]"></Image>
        </div>
      </div>
    </div>
  ) : null
}

export default VideoPreviewer
