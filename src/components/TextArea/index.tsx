import { TextArea as AntTextArea, TextAreaProps } from 'antd-mobile'
import styles from './index.module.css'
import { runes } from 'runes2'

const TextArea = (props: TextAreaProps) => {
  return (
    <div className={styles.wrap}>
      <AntTextArea
        {...props}
        style={{
          '--color': '#303030',
          '--placeholder-color': '#9A9A9A',
          '--font-size': '0.6rem'
        }}
        autoSize={{ minRows: 6, maxRows: 6 }}
        maxLength={props.maxLength}
      />
      <div className={styles.count}>
        {runes(props.value || '').length || 0}/{props.maxLength}
      </div>
    </div>
  )
}

export { TextArea }
