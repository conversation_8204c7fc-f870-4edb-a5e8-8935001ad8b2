import { ReactNode } from 'react'
import { Context } from './context'
import { useForm } from './useForm'
import type { FormInstance, FormInstanceCallbacks } from './useForm'

interface FromProps extends FormInstanceCallbacks {
  form?: FormInstance
  children: ReactNode
}

const Form = ({ form, onSubmit, onChange, children }: FromProps) => {
  const formSelf = useForm(form)
  formSelf.onSubmit = onSubmit
  formSelf.onChange = onChange

  return <Context.Provider value={formSelf}>{children}</Context.Provider>
}

export { Form }
