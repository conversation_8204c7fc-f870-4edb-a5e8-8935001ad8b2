import { useRef } from 'react'

interface FormData {
  [props: string]: any
}

interface FormInstance extends FormInstanceCallbacks {
  setFieldValue(name: string, value?: any): void
  getFieldsValue(): FormData
  submit(): void
}

interface FormInstanceCallbacks {
  onSubmit?(formData: FormData): void
  onChange?(formData: FormData): void
}

let formData: FormData = {}

const useForm: (form?: FormInstance) => FormInstance = form => {
  const ref = useRef<FormInstance>()

  if (!ref.current) {
    if (!form) {
      ref.current = {
        setFieldValue: (name, value) => {
          const _formData = { ...formData }
          _formData[name] = value
          formData = _formData
          ref.current?.onChange?.(formData)
        },
        getFieldsValue: () => formData,
        submit: () => ref.current?.onSubmit?.(formData)
      }
    } else {
      ref.current = form
    }
  }

  return ref.current
}

export { useForm }
export type { FormInstance, FormInstanceCallbacks, FormData }
