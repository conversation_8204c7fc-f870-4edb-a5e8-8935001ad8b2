import {
  ReactElement,
  cloneElement,
  useContext,
  useEffect,
  useState
} from 'react'
import styles from './index.module.css'
import { Context } from '../context'
import type { FormInstance } from '../useForm'

interface FieldProps {
  name: string
  label?: string
  required?: boolean
  noPaddingBottom?: boolean
  children: ReactElement
}

const Field = ({
  name,
  label,
  required,
  noPaddingBottom,
  children
}: FieldProps) => {
  const { setFieldValue } = useContext(Context) as FormInstance
  const [value, onChange] = useState()

  useEffect(() => {
    setFieldValue(name, children.props.defaultValue)
  }, [children.props.defaultValue])

  return (
    <div
      className={
        noPaddingBottom
          ? styles.main
          : `${styles.main} ${styles.main_padding_bottom}`
      }
    >
      {label && (
        <div className={styles.label}>
          {required && <span className={styles.label_required}>*</span>}
          <span>{label}</span>
        </div>
      )}

      {cloneElement(children, {
        value,
        onChange(value: any) {
          onChange(value)
          setFieldValue(name, value)
          children.props.onChange?.call(this, arguments)
        }
      })}
    </div>
  )
}

export { Field }
