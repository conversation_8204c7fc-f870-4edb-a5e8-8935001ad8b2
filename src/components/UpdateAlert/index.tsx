'use client'

import { i18n } from '@/lib/client/i18n'
import { Mask } from 'antd-mobile'
import React, { useEffect } from 'react'
import update_alert_mask from '@/../public/images/common/update_alert_mask.png'
import { Image } from '../Image'
import { makeSureInApp } from '@/lib/client/utils'

export const updateAlert = {
  show: () => {}
}

const UpdateAlert = () => {
  const [visible, setVisible] = React.useState(false)

  useEffect(() => {
    updateAlert.show = () => {
      setVisible(true)
    }
  }, [])

  return (
    <Mask visible={visible} disableBodyScroll>
      <div
        className="flex h-screen w-screen items-center justify-center"
        onClick={() => setVisible(false)}
      >
        <div className="relative pb-[170px]">
          <div className="mt-[170px] flex w-[602px] flex-col justify-center rounded-[32px] bg-white px-[96px] pb-[40px] pt-[120px]">
            {/* <span className="text-center text-[52px] font-bold text-black">
              {'title'}
            </span> */}
            <div className="mt-[24px] text-[28px] text-gray6E">
              {i18n.t('Common.使用该功能请先升级APP版本！轻松提升销量！')}
            </div>
            <div
              className="mt-[64px] flex h-[80px] w-full items-center justify-center rounded-[8px] touch-opacity"
              style={{
                background:
                  'linear-gradient( 20deg, #FF4F98 0%, #FF2D2A 59%, #FF2F2A 63%, #FF772F 100%)'
              }}
              onClick={() => {
                setVisible(false)
                makeSureInApp()
              }}
            >
              <span className="text-[32px] font-bold text-white">
                {i18n.t('Common.立即升级')}
              </span>
            </div>

            <div className="mt-[24px] flex justify-center">
              <span
                className="text-[24px] text-grayCC"
                onClick={() => setVisible(false)}
              >
                {i18n.t('Common.Cancel')}
              </span>
            </div>
          </div>
          <div className="absolute top-0 h-[260px] w-[602px]">
            <Image
              src={update_alert_mask}
              className="h-[260px] w-[602px]"
            ></Image>
          </div>
        </div>
      </div>
    </Mask>
  )
}

export default UpdateAlert
