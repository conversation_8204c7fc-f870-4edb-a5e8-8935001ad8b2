import NextImage from 'next/image'

interface Props {
  src?: any
  alt?: string
  className: string
  width?: number
  height?: number
  withPlaceholder?: boolean
}

const Image = ({
  className,
  src,
  alt = '',
  withPlaceholder = false
}: Props) => {
  const width = className.includes('size')
    ? Number(className.match(/size-\[([\d|.]+)px/)?.[1])
    : Number(className.match(/w-\[([\d|.]+)px/)?.[1])

  const height = className.includes('size')
    ? Number(className.match(/size-\[([\d|.]+)px/)?.[1])
    : Number(className.match(/h-\[([\d|.]+)px/)?.[1])
  return src ? (
    <div className={className}>
      <NextImage
        className={className}
        quality={100}
        src={src}
        width={width}
        height={height}
        alt={alt}
        sizes={`${width / 2}px`}
        placeholder={withPlaceholder ? 'blur' : 'empty'}
        blurDataURL={'/images/common/placeholder.png'}
        unoptimized={typeof src === 'string'}
      ></NextImage>
    </div>
  ) : (
    <div className={className}></div>
  )
}

export { Image }
