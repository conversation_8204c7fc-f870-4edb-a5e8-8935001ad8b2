.box {

   margin: 0 24px;
   background: #FFFFFF;
   border-radius: 12px;
   box-sizing: border-box;
}

.content_top {
   display: flex;
   padding-bottom: 34px;

}

.category_box {
   display: flex;
   align-items: center;

}

.avatar_img {
   width: 120px;
   height: 120px;
   border-radius: 50% !important;
   margin-right: 24px;
}

.title {
   font-weight: bold;
   font-size: 30px;
   color: #303030;
}

.tiktokId {
   font-weight: 400;
   font-size: 26px;
   color: #6E6E6E;
   line-height: 36px;
}

.content_top_line {
   border-bottom: #eeeeee solid 2px;
}

.category {
   display: inline-block;
   text-align: center;
   line-height: 32px;
   margin-top: 12px;
   padding: 0 8px;
   font-weight: 400;
   font-size: 22px;
   color: #EF8100;
   border-radius: 4px;
   background-color: #EF810025;
}

.content_bottom {
   display: flex;
   padding: 24px 84px;
   justify-content: space-between;
   align-items: center;
}

.fans_box {
   display: flex;
   flex-direction: column;
   justify-content: center;
   align-items: center;
}

.fans_num {
   height: 38px;
   font-weight: normal;
   font-size: 32px;
   color: #303030;
}

.fans_title {
   height: 34px;
   font-weight: 400;
   font-size: 24px;
   color: #6E6E6E;
   margin-top: 12px;
}