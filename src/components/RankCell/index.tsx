'use client'
import React, { useState, useEffect } from 'react'
import styles from './index.module.scss'
import { i18n } from '@/lib/client/i18n'
import { Image } from '@/components/Image'
import { WithRankNum } from '@/app/tiktok_data/components/rankNum'
import { useRouter } from '@/lib/hooks/useRouter'
import HandlerOnceTap from '@/lib/handlerOnceTap'
import { rpxToPx } from '@/lib/client/utils'
import { formatSales, formatPrice, formatCount } from '@/lib/format'

const RankCell = ({ item, index, tabsIndex }) => {
  const router = useRouter()
  const handleClickDetail = item => {
    router.push(`/tiktok_data/kol/${item.anchorId}`)
  }
  return (
    <div
      className={styles.box}
      onClick={() => {
        HandlerOnceTap(() => handleClickDetail(item))
      }}
    >
      <div className={styles.content_top_line}>
        <WithRankNum index={index}>
          <div className={styles.content_top}>
            <Image
              src={item?.avatar || ''}
              alt=""
              className={`size-[120px]  ${styles.avatar_img}`}
            />
            <div className={styles.dec}>
              <div className={styles.title}>{item.nickname}</div>
              <div className={styles.tiktokId}>{item.displayName}</div>
              {tabsIndex == '5'
                ? item.productCategoryNameList &&
                  item.productCategoryNameList.length > 0 && (
                    <div className={styles.category_box}>
                      <div className={styles.category}>
                        {item.productCategoryNameList[0]}
                      </div>
                      {item.productCategoryNameList.length > 2 && (
                        <div
                          className={styles.category}
                          style={{ marginLeft: '8px' }}
                        >
                          ...
                        </div>
                      )}
                    </div>
                  )
                : item.categoryName && (
                    <div className={styles.category}>{item.categoryName}</div>
                  )}
            </div>
          </div>
        </WithRankNum>
      </div>
      <div className={styles.content_bottom}>
        {tabsIndex == '5' ? (
          <div className={styles.fans_box}>
            <div className={styles.fans_num}>
              {item.followerCount ? formatSales(item.followerCount) : '--'}
            </div>
            <div className={styles.fans_title}>
              {i18n.t('TiktokData.粉丝总量')}
            </div>
          </div>
        ) : (
          <div className={styles.fans_box}>
            <div className={styles.fans_num}>
              {item.risingPowder ? formatSales(item.risingPowder) : '--'}
            </div>
            <div className={styles.fans_title}>
              {i18n.t('TiktokData.粉丝增量')}
            </div>
          </div>
        )}
        {tabsIndex == '5' ? (
          <div className={styles.fans_box}>
            <div className={styles.fans_num}>
              {item.gmv ? formatSales(item.gmv) : '--'}
            </div>
            <div className={styles.fans_title}>
              {i18n.t('TiktokData.总销售额')}
            </div>
          </div>
        ) : (
          <div className={styles.fans_box}>
            <div className={styles.fans_num}>
              {item.followerCount ? formatSales(item.followerCount) : '--'}
            </div>
            <div className={styles.fans_title}>
              {i18n.t('TiktokData.粉丝总量')}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default RankCell
