'use client'

import {
  Category<PERSON>ell,
  PreviewComponentList,
  GlobalSetting,
  Component
} from '@youpik/mobile_components'
import { JumpContent } from '@youpik/mobile_components/dist/src/typing'
import '@youpik/mobile_components/dist/index.css'
import { I18n } from '@youpik/mobile_components'
// import { currentLanguage } from '@/lib/client/i18n'

// I18n.changeLanguage(currentLanguage())

export { CategoryCell, PreviewComponentList, I18n }
export type { GlobalSetting, Component, JumpContent }
