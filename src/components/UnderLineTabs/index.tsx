import { Tabs } from 'antd-mobile'
import React from 'react'
import './index.css'
import { useMounted } from '@/lib/hooks/useMounted'
import { AppColors } from '@/lib/const'

interface Props {
  index: number
  onChange(index: number): void
  titles: string[]
}

export const UnderLineTabs = ({ index, onChange, titles }: Props) => {
  const mounted = useMounted()

  return (
    <div className="under-line-tabs">
      <Tabs
        activeKey={`${index}`}
        onChange={value => {
          onChange(Number(value))
        }}
        activeLineMode="fixed"
        style={{
          '--fixed-active-line-width': '1.5rem',
          '--active-line-height': '0.15rem',
          '--active-line-border-radius': '0px',
          '--active-line-color': mounted
            ? AppColors.primary
            : 'rgba(0, 0, 0, 0)',
          '--active-title-color': AppColors.black,
          '--content-padding': '0'
        }}
      >
        {titles.map((title, i) => (
          <Tabs.Tab title={title} key={`${i}`}></Tabs.Tab>
        ))}
      </Tabs>
    </div>
  )
}
