import styles from './index.module.css'

interface Props {
  title: string
  onConfirm(): void
  disabled?: boolean
}

const ConfirmButton = ({ title, onConfirm, disabled }: Props) => {
  return (
    <div
      className={disabled ? `${styles.btn} ${styles.btn_disabled}` : styles.btn}
      onClick={() => !disabled && onConfirm?.()}
    >
      <span className={styles.btn_text}>{title}</span>
    </div>
  )
}

export { ConfirmButton }
