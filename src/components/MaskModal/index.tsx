import React, { useState } from 'react'
import { Mask } from 'antd-mobile'
import styles from './index.module.scss'
import { i18n } from '@/lib/client/i18n'

const MaskModal = ({ title, modalVisible, handleCancel, handleConfirm }) => {
  return (
    <>
      <Mask visible={modalVisible}>
        <div className={styles.box}>
          <div className={styles.content}>
            <div className={styles.title}>{title}</div>
            <div className={styles.btns}>
              <div className={styles.cancel_btn} onClick={handleCancel}>
                {i18n.t('Common.Cancel')}
              </div>
              <div className={styles.confirm_btn} onClick={handleConfirm}>
                {i18n.t('Common.Confirm')}
              </div>
            </div>
          </div>
        </div>
      </Mask>
    </>
  )
}
export default MaskModal
