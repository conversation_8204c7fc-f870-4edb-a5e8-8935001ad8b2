/* eslint-disable @next/next/no-img-element */
import React from 'react'
import { Popup as AntPopup } from 'antd-mobile'
import { px2rem } from '@/lib/client/utils'
import { webview } from '@/lib/client/webview'
import { i18n } from '@/lib/client/i18n'

export interface PopupProps {
  visible: boolean
  toggle(): void
  onConfirm?(): void
  children: React.ReactNode
}

export const Popup = ({ visible, toggle, onConfirm, children }: PopupProps) => {
  return (
    <AntPopup
      visible={visible}
      onMaskClick={toggle}
      bodyStyle={{
        borderTopLeftRadius: px2rem(8),
        borderTopRightRadius: px2rem(8)
      }}
    >
      <div
        className="p-[24px] pt-0"
        onTouchMove={event => {
          // antd内部会监听这个事件但是它处理的又有问题导致卡顿，所以组织它监听
          event.stopPropagation()
        }}
      >
        <div className="pt-[80px]">{children}</div>
        <div className="bg-white">
          <div
            onClick={onConfirm}
            className="mb-[24px] flex h-[80px] w-[702px] items-center justify-center rounded-[4px] bg-primary touch-opacity"
          >
            <span className="text-[32px] text-white">
              {i18n.t('Common.Confirm')}
            </span>
          </div>

          <div
            style={{
              height: webview
                ? `${px2rem(webview?.getData().bottomSafeArea)}`
                : 0
            }}
          ></div>
        </div>
        <div
          className="absolute right-0 top-0 z-10 p-[24px] touch-opacity"
          onClick={toggle}
        >
          <img
            className="right-0 size-[32px]"
            src="/images/common/modal_close.png"
          ></img>
        </div>
      </div>
    </AntPopup>
  )
}
