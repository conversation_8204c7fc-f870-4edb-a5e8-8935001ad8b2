import React from 'react'
import youpik_earn_pre from '@/../public/images/product/detail/youpik_earn_pre.png'
import { Image } from '../Image'
import { i18n } from '@/lib/client/i18n'

interface Props {
  commission: string
}

export const YoupikHighCom = ({ commission }: Props) => {
  return (
    <div className="flex items-center">
      <Image src={youpik_earn_pre} className="h-[36px] w-[38px]"></Image>
      <div className="flex h-[36px] items-center rounded-r-[2px] bg-[#F4393B] pl-[6px] pr-[4px]">
        <span className="text-[24px] leading-[36px] text-white">
          {i18n.t('Product.Youpik High Com.')}
        </span>
        <div className="ml-[8px] h-[28px] rounded-[2px] bg-white px-[10px] text-[26px] font-bold leading-[26px] text-[#F4393B]">
          {commission.replace('%', '')}%
        </div>
      </div>
    </div>
  )
}
