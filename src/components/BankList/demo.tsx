'use client'
import React, { useState } from 'react'
import BankList from './index'
import { Bank } from '@/app/api/api-base/bank/list/request'
import { rpxToPx } from '@/lib/client/utils'

export default function BankListDemo() {
  const [showBankList, setShowBankList] = useState(false)
  const [selectedBank, setSelectedBank] = useState<Bank | null>(null)

  const handleSelectBank = (bank: Bank) => {
    setSelectedBank(bank)
    console.log('选择的银行:', bank)
  }

  return (
    <div className="bg-gray-50 min-h-screen" style={{ padding: rpxToPx(32) }}>
      <div className="mx-auto max-w-md">
        <h1
          className="text-gray-900 text-center font-bold"
          style={{
            marginBottom: rpxToPx(96),
            fontSize: rpxToPx(48)
          }}
        >
          银行选择组件演示
        </h1>

        <div
          className="bg-white shadow-sm"
          style={{
            borderRadius: rpxToPx(16),
            padding: rpxToPx(48)
          }}
        >
          <div style={{ marginBottom: rpxToPx(32) }}>
            <label
              className="text-gray-700 block font-medium"
              style={{
                fontSize: rpxToPx(28),
                marginBottom: rpxToPx(16)
              }}
            >
              选择银行
            </label>
            <button
              className="border-gray-300 hover:bg-gray-50 w-full border bg-white text-left transition-colors"
              style={{
                padding: rpxToPx(24),
                borderRadius: rpxToPx(12),
                fontSize: rpxToPx(32)
              }}
              onClick={() => setShowBankList(true)}
            >
              <span
                className={selectedBank ? 'text-gray-900' : 'text-gray-500'}
              >
                {selectedBank ? selectedBank.name : '点击选择银行'}
              </span>
            </button>
          </div>

          {selectedBank && (
            <div
              className="bg-blue-50 border-blue-200 border"
              style={{
                marginTop: rpxToPx(48),
                padding: rpxToPx(32),
                borderRadius: rpxToPx(12)
              }}
            >
              <h3
                className="text-blue-900 font-medium"
                style={{
                  fontSize: rpxToPx(36),
                  marginBottom: rpxToPx(24)
                }}
              >
                已选择银行信息
              </h3>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: rpxToPx(16)
                }}
              >
                <div style={{ fontSize: rpxToPx(28) }}>
                  <span className="text-gray-700 font-medium">银行名称：</span>
                  <span className="text-gray-900">{selectedBank.name}</span>
                </div>
                {selectedBank.code && (
                  <div style={{ fontSize: rpxToPx(28) }}>
                    <span className="text-gray-700 font-medium">
                      银行代码：
                    </span>
                    <span className="text-gray-900">{selectedBank.code}</span>
                  </div>
                )}
                {selectedBank.swiftCode && (
                  <div style={{ fontSize: rpxToPx(28) }}>
                    <span className="text-gray-700 font-medium">
                      SWIFT代码：
                    </span>
                    <span className="text-gray-900">
                      {selectedBank.swiftCode}
                    </span>
                  </div>
                )}
                {selectedBank.type && (
                  <div style={{ fontSize: rpxToPx(28) }}>
                    <span className="text-gray-700 font-medium">
                      银行类型：
                    </span>
                    <span className="text-gray-900">
                      {selectedBank.type === 1 ? '泰国本地银行' : '跨境银行'}
                    </span>
                  </div>
                )}
                {selectedBank.isPayout && (
                  <div style={{ fontSize: rpxToPx(28) }}>
                    <span className="text-gray-700 font-medium">
                      支持转账：
                    </span>
                    <span className="text-gray-900">
                      {selectedBank.isPayout === '1' ? '是' : '否'}
                    </span>
                  </div>
                )}
              </div>

              <button
                className="text-blue-600 hover:text-blue-800"
                style={{
                  marginTop: rpxToPx(32),
                  fontSize: rpxToPx(28)
                }}
                onClick={() => setSelectedBank(null)}
              >
                清除选择
              </button>
            </div>
          )}
        </div>

        <div
          className="bg-yellow-50 border-yellow-200 border"
          style={{
            marginTop: rpxToPx(48),
            padding: rpxToPx(32),
            borderRadius: rpxToPx(12)
          }}
        >
          <h4
            className="text-yellow-800 font-medium"
            style={{
              fontSize: rpxToPx(32),
              marginBottom: rpxToPx(16)
            }}
          >
            功能说明：
          </h4>
          <ul
            className="text-yellow-700"
            style={{
              fontSize: rpxToPx(28),
              display: 'flex',
              flexDirection: 'column',
              gap: rpxToPx(8)
            }}
          >
            <li>• 点击按钮打开底部弹窗</li>
            <li>• 自动获取银行列表数据</li>
            <li>• 支持选中状态显示</li>
            <li>• 显示银行详细信息</li>
            <li>• 选择后自动关闭弹窗</li>
          </ul>
        </div>
      </div>

      <BankList
        visible={showBankList}
        onClose={() => setShowBankList(false)}
        onSelectBank={handleSelectBank}
        selectedBankId={selectedBank?.id}
      />
    </div>
  )
}
