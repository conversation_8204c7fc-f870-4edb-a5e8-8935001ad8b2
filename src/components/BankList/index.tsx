'use client'
import React, { useEffect, useState } from 'react'
import { Popup } from 'react-vant'
import { Loading } from 'react-vant'
import { bankList, Bank } from '@/app/api/api-base/bank/list/request'
import { toast } from 'react-hot-toast'
import { BottomSafeArea } from '../BottomSafeArea'
import { AppColors } from '@/lib/const'

interface BankListProps {
  visible: boolean
  onClose: () => void
  onSelectBank?: (bank: Bank) => void
  selectedBankId?: number
}

const BankList: React.FC<BankListProps> = ({
  visible,
  onClose,
  onSelectBank,
  selectedBankId
}) => {
  const [banks, setBanks] = useState<Bank[]>([])
  const [loading, setLoading] = useState(false)

  // 获取银行列表
  const fetchBankList = async () => {
    try {
      setLoading(true)
      const response = await bankList()
      setBanks(response.result || [])
    } catch (error) {
      console.error('获取银行列表失败:', error)
      toast.error('获取银行列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (visible) {
      fetchBankList()
    }
  }, [visible])

  // 选择银行
  const handleSelectBank = (bank: Bank) => {
    onSelectBank?.(bank)
    onClose()
  }

  return (
    <Popup
      visible={visible}
      onClose={onClose}
      position="bottom"
      round
      closeable
      className="max-h-[80vh]"
    >
      <div className="px-[24px] pb-[24px]">
        {/* 标题 */}
        <div className="sticky top-0 bg-white py-[24px] text-center">
          <h3 className="text-[36px] font-medium text-black02">选择银行</h3>
        </div>

        {/* 银行列表 */}
        <div className="max-h-96 overflow-y-auto">
          {loading ? (
            <div className="flex justify-center py-8">
              <Loading size="24px" />
            </div>
          ) : (
            <div className="space-y-0">
              {banks.map(bank => (
                <div
                  key={bank.id}
                  className={`flex h-[88px] cursor-pointer items-center border-b border-background last:border-b-0`}
                  onClick={() => handleSelectBank(bank)}
                >
                  <div className="flex-1">
                    <div
                      className="text-[24px]"
                      style={{
                        color:
                          selectedBankId === bank.id
                            ? AppColors.primary
                            : AppColors.black
                      }}
                    >
                      {bank.name}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <BottomSafeArea />
      </div>
    </Popup>
  )
}

export default BankList
