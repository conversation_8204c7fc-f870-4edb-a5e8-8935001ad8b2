'use client'
import React, { useEffect, useState } from 'react'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import Image from 'next/image'
import icon_back from '@/../public/images/talent/icon_back.png'
import { useRouter } from '@/lib/hooks/useRouter'
import { ChildrenProps } from '@/lib/types/common'
import HandlerOnceTap from '@/lib/handlerOnceTap'
import { rpxToPx } from '@/lib/client/utils'

const TalentHead = ({ children }: ChildrenProps) => {
  const router = useRouter()
  return (
    <div className={styles.talent_box}>
      {/* <div
        className={styles.image}
        onClick={() => {
          HandlerOnceTap(() => {
            router.back()
          })
        }}
      >
        <Image
          alt=""
          src={icon_back}
          className={styles.icon_back}
          width={rpxToPx(44)}
          height={rpxToPx(44)}
        />
      </div> */}

      {children}
    </div>
  )
}

export default TalentHead
