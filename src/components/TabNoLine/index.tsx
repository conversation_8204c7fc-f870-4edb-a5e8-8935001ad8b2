'use client'
import React, { useState, useEffect } from 'react'
import styles from './index.module.scss'
import { i18n } from '@/lib/client/i18n'
import Image from 'next/image'
import { Tabs } from 'react-vant'
import './index.scss'

const TalentNoLine = ({ items, onCategoryTabs }) => {
  return (
    <div className={styles.no_line_tab}>
      <Tabs defaultActive={0} onChange={onCategoryTabs}>
        {items.map(item => (
          <Tabs.TabPane key={item.value} title={item.label}></Tabs.TabPane>
        ))}
      </Tabs>
    </div>
  )
}

export default TalentNoLine
