import styles from './index.module.scss'
const InsertColoredText = ({ text, className, children }) => {
  const renderMessage = () => {
    const parts = text.split('{{word}}')

    return (
      <div className={className}>
        {Array.isArray(children) ? (
          parts.map((part, index) => (
            <span key={index}>
              {part}
              {index < children.length && children[index]}
            </span>
          ))
        ) : (
          <span className={className}>
            {parts[0] || ''}
            {children}
            {parts[1] || ''}
          </span>
        )}
      </div>
    )
  }

  return <>{renderMessage()}</>
}

export default InsertColoredText
