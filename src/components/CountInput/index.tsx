/* eslint-disable @next/next/no-img-element */
import { AppColors } from '@/lib/const'
import React from 'react'

interface Props {
  value: number
  onChange: (value: number) => void
  min?: number
  max?: number
  warning?: boolean
}

export const CountInput = ({
  value,
  onChange,
  min = 1,
  max = Infinity,
  warning = false
}: Props) => {
  return (
    <div className="flex items-center">
      <div onClick={() => onChange(Math.max(value - 1, min))}>
        <img
          src={
            value == min
              ? '/images/common/input/decrease_disabled.png'
              : '/images/common/input/decrease.png'
          }
          className="size-[52px] touch-opacity"
        />
      </div>
      <input
        className="w-[66px] text-center text-[28px]"
        style={{
          color: warning ? 'red' : AppColors.black
        }}
        value={value}
        onChange={e => {
          let value = parseInt(e.target.value, 10) // 尝试将输入转换为十进制数
          // 确保value是一个数字且介于1和max之间，否则根据条件调整value
          if (isNaN(value) || value < 1) {
            value = 1 // 如果value不是数字或小于1，设置value为1
          } else if (value > max) {
            value = max // 如果value超过最大值，设置value为max
          }
          onChange(value)
        }}
      ></input>
      <div onClick={() => onChange(Math.min(value + 1, max))}>
        <img
          src={
            value == max
              ? '/images/common/input/increase_disabled.png'
              : '/images/common/input/increase.png'
          }
          className="size-[52px] touch-opacity"
        />
      </div>
    </div>
  )
}
