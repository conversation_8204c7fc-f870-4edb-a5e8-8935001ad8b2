'use client'
import { Image } from '@/components/Image'
import logo from '@/../public/images/download/logo.png'
import gift_icon from '@/../public/images/download/gift.png'
import earn_icon from '@/../public/images/download/earn.png'
import data_icon from '@/../public/images/download/data.png'
import { makeSureInApp } from '@/lib/client/utils'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'

export const JoinUs = ({ afterJoin }: { afterJoin?: () => void }) => {
  const mounted = useMounted()

  return mounted ? (
    <div>
      <div className="flex flex-col items-center justify-center pt-[126px]">
        <Image className="size-[144px]" src={logo}></Image>

        <div className="mt-[32px] flex flex-col justify-center">
          <span className="text-center text-[32px] text-black30">
            {i18n.t('Activity.选品上uChoice pro')}
          </span>
          <span className=" text-center text-[32px] text-black30">
            {i18n.t('Activity.选品快人一步')}
          </span>
        </div>

        <div
          className="mt-[88px] flex h-[80px] w-[520px] items-center justify-center rounded-[4px] bg-primary touch-opacity"
          onClick={() => {
            makeSureInApp(undefined, true)

            afterJoin?.()
          }}
        >
          <span className="text-[32px] font-bold text-white">
            {i18n.t('Activity.JOIN NOW!')}
          </span>
        </div>
      </div>

      <div className="mt-[160px] pl-[122px] pr-[48px]">
        <div className="mb-[88px] flex items-center">
          <Image src={gift_icon} className="size-[108px]"></Image>
          <span className="ml-[18px] flex-1 text-[32px] text-black30">
            {i18n.t('Activity.热门爆品')}
          </span>
        </div>
        <div className="mb-[88px] flex items-center">
          <Image src={earn_icon} className="size-[108px]"></Image>
          <span className="ml-[18px] flex-1 text-[32px] text-black30">
            {i18n.t('Activity.专业商品数据分析')}
          </span>
        </div>
        <div className="mb-[88px] flex items-center">
          <Image src={data_icon} className="size-[108px]"></Image>
          <span className="ml-[18px] flex-1 text-[32px] text-black30">
            {i18n.t('Activity.独家高佣商品')}
          </span>
        </div>
      </div>
    </div>
  ) : null
}
