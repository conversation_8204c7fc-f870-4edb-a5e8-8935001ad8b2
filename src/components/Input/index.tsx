import { Input as AntInput, InputProps } from 'antd-mobile'
import React from 'react'

const Input = ({
  fontSize = '1rem',
  color = '#303030',
  placeholderColor = '#9A9A9A',
  ...props
}: {
  fontSize?: string
  color?: string
  placeholderColor?: string
} & InputProps) => {
  return (
    <AntInput
      {...props}
      style={{
        '--color': color,
        '--placeholder-color': placeholderColor,
        '--font-size': fontSize
      }}
    ></AntInput>
  )
}

export { Input }
