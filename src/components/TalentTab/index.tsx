'use client'
import React, { useState, useEffect } from 'react'
import styles from './index.module.scss'
import './index.scss'
import { Tabs } from 'antd-mobile'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'

const TalentTab = ({ items, onChangeTabs, activeKey }) => {
  useEffect(() => {
    toStatistic(activeKey)
  }, [])
  const toStatistic = e => {
    switch (e) {
      case '1':
        statistic({
          eventName: EventName.hot_sales
        })
        break
      case '2':
        statistic({
          eventName: EventName.potential_bestsellers
        })
        break
      case '3':
        statistic({
          eventName: EventName.top_rated
        })
        break
      case '4':
        statistic({
          eventName: EventName.store_list
        })
        break
      case '5':
        statistic({
          eventName: EventName.E_commerce_creator
        })
        break
      case '6':
        statistic({
          eventName: EventName.fan_growth_rank
        })
        break
    }
  }

  return (
    <div className={styles.talent_tab}>
      <Tabs
        activeKey={activeKey}
        activeLineMode="fixed"
        // style={{
        //   '--active-line-color': '#FE6D45',
        //   '--active-line-height': '4px'
        // }}
        onChange={e => {
          onChangeTabs(e)
          toStatistic(e)
        }}
      >
        {items.map(item => (
          <Tabs.Tab key={item.value} title={item.label}></Tabs.Tab>
        ))}
      </Tabs>
    </div>
  )
}

export default TalentTab
