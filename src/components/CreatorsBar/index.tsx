'use client'
import React, { useRef } from 'react'
import Creators from './components/Creators'
import { Skeleton } from 'react-vant'

interface Creator {
  /**
   * 头像
   */
  avatar: string
  /**
   * 用户名称
   */
  displayName: string
  /**
   * unionId
   */
  unionId: string
}

interface Props {
  creators: Creator[]
  selectedUnionId?: string
  onSelect(unionId: string): void
}

const CreatorsBar = (props: Props) => {
  const carouselRef = useRef<HTMLDivElement>(null)

  const onItemClick = item => {
    window.scrollTo(0, 0)
    props.onSelect(item.unionId)

    // TODO
  }

  const scrollToItem = (index: number) => {
    if (!carouselRef.current) return
    const item = carouselRef.current.children[index] as HTMLElement
    if (item) {
      const offsetLeft = item.offsetLeft
      const containerWidth = carouselRef.current.offsetWidth
      const itemWidth = item.offsetWidth
      carouselRef.current.scrollTo({
        left: offsetLeft - (containerWidth - itemWidth) / 2,
        behavior: 'smooth'
      })
    }
  }

  return props.creators?.length > 0 ? (
    <div className="flex overflow-x-scroll hide-scrollbar" ref={carouselRef}>
      {props.creators.map((item, index) => (
        <Creators
          key={index}
          item={item}
          selectedUnionId={props.selectedUnionId}
          onItemClick={item => {
            onItemClick(item)
            scrollToItem(index)
          }}
        />
      ))}
    </div>
  ) : (
    <Skeleton avatar avatarSize={90}></Skeleton>
  )
}

export default CreatorsBar
