'use client'
import React from 'react'
import { Image } from '@/components/Image'
import { AppColors } from '@/lib/const'

const Creators = ({ item, onItemClick, selectedUnionId }) => {
  return (
    <div
      className="mr-[48px] rounded-[50%] border-[3px]"
      style={{
        borderColor:
          selectedUnionId == item.unionId ? AppColors.primary : 'transparent'
      }}
      onClick={() => onItemClick(item)}
    >
      <Image
        src={item?.avatar}
        className="size-[104px] rounded-[50%]"
        withPlaceholder
      ></Image>
    </div>
  )
}

export default Creators
