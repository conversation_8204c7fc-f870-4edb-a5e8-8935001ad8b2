'use client'
import React, { useEffect, useState, useRef } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import TalentHead from '@/components/TalentHead'
import CategoryBtns from '@/components/CategoryBtns'
import { makeBodyScrollable, rpxToPx, wait } from '@/lib/client/utils'
import { loading } from '@/lib/client/loading'
import TalentTab from '@/components/TalentTab'
import { useRouteParams } from '@/lib/hooks/useRouteParams'
import { getTiktokAnchorRank } from '@/app/api/api-uchoice/tiktok/anchor/getTiktokAnchorProductRank/request'
import ItemList from './components/item_list'
import { getDateType } from '@/components/FilterPopUp/components/DateFilter/date'
import { getLastDate } from '@/app/api/api-uchoice/tiktok/item/getLastDate'
import { StickyHeader } from '@/components/StickyHeader'

const headTabs = [
  {
    label: i18n.t('Rank.带货达人榜'),
    value: '5'
  },
  {
    label: i18n.t('Rank.涨粉达人榜'),
    value: '6'
  }
]

const bringGoodsArr: any = () => [
  {
    label: i18n.t('Rank.带货类目'),
    value: '1'
  },
  {
    label: i18n.t('Rank.月销量额'),
    value: '4'
  },
  {
    label: i18n.t('Rank.日期'),
    value: '3'
  }
]
const talentArr: any = () => [
  {
    label: i18n.t('Rank.达人类目'),
    value: '1'
  },
  {
    label: i18n.t('Rank.日期'),
    value: '3'
  }
]

const RankList = () => {
  const mounted = useMounted()
  const closeModalRef = useRef()
  const [pageNo, setPageNo] = useState(1)
  const [total, setTotal] = useState<any>(null)
  const [params, setParams] = useState({})
  const [itemList, setItemList] = useState<any>([])
  const [hasMore, setHasMore] = useState(true)
  const [categoryBtnIndex, setCategoryBtnIndex] = useState(0)
  const tabIndex = useRef('5')
  const [isLoading, setIsLoading] = useState(true)
  const [categoryArrs, setCategoryArrs] = useState(bringGoodsArr())
  const routeParams = useRouteParams()
  const defaultTime = useRef(null)
  useEffect(() => {
    handleTabTime()

    // 获取 URL 中的查询参数
    if (routeParams && routeParams['tabIndex']) {
      const urlParams = new URLSearchParams(window.location.search)

      setTimeout(() => {
        onChangeTabs(routeParams['tabIndex'] == 1 ? '5' : '6')
        urlParams.delete('tabIndex')
        window.history.replaceState(
          {},
          '',
          `${window.location.pathname}?${urlParams.toString()}`
        )
      }, 3000)
    }
  }, [])

  const handleTabTime = () => {
    loading.show({ userInteractive: true })
    getLastDate({ rankType: tabIndex.current }).then(res => {
      if (res.code == 200 && res.result) {
        if (res.result.dayLastTime) {
          console.log('dayTemp', res.result.dayLastTime)
          let dayTemp = res.result.dayLastTime[0]
          onLoadRefresh(null, null, dayTemp)
          defaultTime.current = dayTemp
        }
      }
    })
  }
  const onChangeTabs = e => {
    setCategoryArrs(e === '5' ? bringGoodsArr() : talentArr())
    tabIndex.current = e
    const paramsQuery = {
      // ...params,
      pageNo: 1,
      rankType: e == 5 ? 1 : 2,
      customCategoryId: '',
      maxGmv: '',
      minGmv: '',
      timeStamp: defaultTime.current
    }
    setParams(paramsQuery)
    setItemList([])
    // @ts-ignore
    closeModalRef.current.closeModal()
    makeBodyScrollable(true)
    onLoadRefresh(null, paramsQuery)
  }
  const handleTabBtns = item => {
    setCategoryBtnIndex(item.value)
    makeBodyScrollable(false)
  }
  const onLoadRefresh = async (page?, paramsQuery?, dayTemp?) => {
    page ? setPageNo(page) : setPageNo(1)
    loading.show({ userInteractive: true })
    setIsLoading(true)
    const paramQuerys = {
      rankType: tabIndex.current == '6' ? 2 : 1,
      ...params,
      pageNo: page ? page : 1,
      pageSize: 10,
      timeStamp: dayTemp ? dayTemp : defaultTime.current,
      dateType: 1,
      ...(paramsQuery && { ...paramsQuery })
    }
    setParams(paramQuerys)
    getTiktokAnchorRank({ ...paramQuerys }).then(res => {
      loading.hide()
      setIsLoading(false)
      setTotal(res.result.total)
      if (res.code == 200) {
        let list = res.result.list
        if (list.length == 0 && paramQuerys.pageNo == 1) {
          setItemList([])
        }

        if (list.length > 0) {
          let newList = page ? [...itemList, ...list] : list
          setItemList(newList)
          setHasMore(true)
        }
      }
    })
  }

  const fetchData = () => {
    if (total == itemList.length) {
      setHasMore(false)
      return
    }
    let page = pageNo + 1
    const qaueryParams = {
      ...params,
      pageNo: page
    }
    console.log(page, 'pageNo')
    setPageNo(page)

    // 模拟异步加载数据
    setTimeout(() => {
      onLoadRefresh(page, qaueryParams)
    }, 1000)
  }

  const onCheckedItem = item => {
    let paramsQuery: any = {
      ...params,
      pageNo: 1
    }
    if (categoryBtnIndex == 1) {
      paramsQuery = {
        ...paramsQuery,
        customCategoryId: item.value
      }
    }
    if (categoryBtnIndex == 3) {
      const dateType = getDateType(item.label)
      if (dateType == 'day') {
        paramsQuery = {
          ...paramsQuery,
          dateType: 1,
          timeStamp: item.value
        }
      } else if (dateType == 'month') {
        paramsQuery = {
          ...paramsQuery,
          dateType: 3,
          timeStamp: item.value
        }
      } else if (dateType == 'week') {
        paramsQuery = {
          ...paramsQuery,
          dateType: 2,
          timeStamp: item.value
        }
      }
    }
    if (categoryBtnIndex == 4) {
      let maxGmv: any = ''
      let minGmv: any = ''

      switch (item.value) {
        case '0':
          maxGmv = ''
          minGmv = ''
          break
        case '1':
          maxGmv = ''
          minGmv = '1000000'
          break
        case '2':
          maxGmv = '1000000'
          minGmv = '100000'
          break
        case '3':
          maxGmv = '100000'
          minGmv = '10000'
          break
        case '4':
          maxGmv = '10000'
          minGmv = '3000'
          break
        case '5':
          maxGmv = '3000'
          minGmv = ''
          break
        default:
          break
      }
      paramsQuery = {
        ...paramsQuery,
        maxGmv: maxGmv,
        minGmv: minGmv
      }
    }
    setItemList([])
    setParams(paramsQuery)
    onLoadRefresh(null, paramsQuery)
    console.log(paramsQuery, 'paramsQuery')
    setTimeout(() => {
      // @ts-ignore
      closeModalRef.current.closeModal()
      makeBodyScrollable(true)
    }, 300)
  }
  const handleCloseModal = () => {
    makeBodyScrollable(true)
  }
  return mounted ? (
    <>
      <StickyHeader>
        <div
          className={styles.fix_boxs}
          // style={{ paddingTop: webview ? rpxToPx(getNavHeight()) : 0 }}
        >
          <TalentHead>
            <TalentTab
              items={headTabs}
              onChangeTabs={onChangeTabs}
              activeKey={tabIndex.current}
            />
          </TalentHead>
          <CategoryBtns
            btnsArr={categoryArrs}
            setCategoryArrs={setCategoryArrs}
            handleCloseModal={handleCloseModal}
            handleTabBtns={handleTabBtns}
            ref={closeModalRef}
            tabIndex={tabIndex.current}
            onCheckedItem={onCheckedItem}
          ></CategoryBtns>
        </div>
      </StickyHeader>
      <div className={styles.store_box}>
        <ItemList
          isLoading={isLoading}
          tabIndex={tabIndex.current}
          hasMore={hasMore}
          itemList={itemList}
          fetchData={fetchData}
          total={total}
        />
      </div>
    </>
  ) : null
}

export default RankList
