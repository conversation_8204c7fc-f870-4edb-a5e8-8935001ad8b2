import React, { useEffect, useState } from 'react'
import styles from './index.module.scss'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useRouter } from '@/lib/hooks/useRouter'
import { useMounted } from '@/lib/hooks/useMounted'
import StatusView, { StatusViewType } from '@/app/components/StatusView'
import { i18n } from '@/lib/client/i18n'
import RankCell from '@/components/RankCell'

const ItemList = ({
  itemList,
  fetchData,
  hasMore,
  total,
  tabIndex,
  isLoading
}) => {
  const router = useRouter()
  const mounted = useMounted()
  useEffect(() => {}, [hasMore, isLoading, total])
  return (
    <div className={styles.item_box}>
      {!isLoading && total == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.empty}></StatusView>
        </div>
      )}
      {isLoading && itemList.length == 0 && (
        <div className={styles.loadingStatus}>
          {' '}
          <StatusView status={StatusViewType.loading}></StatusView>
        </div>
      )}
      {mounted && itemList && itemList.length > 0 && (
        <InfiniteScroll
          dataLength={itemList.length}
          next={fetchData}
          hasMore={hasMore}
          loader={
            <div className={styles.noMore}>{i18n.t('Common.Loading')}</div>
          }
          endMessage={
            itemList.length == total ? (
              <div className={styles.noMore}>{i18n.t('Common.Empty')}</div>
            ) : null
          }
        >
          {itemList.map((item, index) => {
            return (
              <div className={styles.store_item_box} key={index}>
                <RankCell
                  item={item}
                  index={index}
                  tabsIndex={tabIndex}
                ></RankCell>
              </div>
            )
          })}
        </InfiniteScroll>
      )}
    </div>
  )
}

export default ItemList
