'use client'
import React, { useEffect, useState, useRef } from 'react'
import { useMounted } from '@/lib/hooks/useMounted'
import { i18n } from '@/lib/client/i18n'
import styles from './index.module.scss'
import { getLastDate } from '@/app/api/api-uchoice/tiktok/item/getLastDate'
import { getGoAppBarHeight } from '@/components/GoAppBar'
import CategoryBtns from '@/components/CategoryBtns'
import ItemList from './components/item_list'
import { sellerRankList } from '@/app/api/api-uchoice/tiktok/seller/itemList/request'
import { loading } from '@/lib/client/loading'
import { getDateType } from '@/components/FilterPopUp/components/DateFilter/date'
import { statistic } from '@/lib/statistic'
import { EventName } from '@/lib/statistic/const'
import { StickyHeader } from '@/components/StickyHeader'
import { makeBodyScrollable } from '@/lib/client/utils'

const RankList = () => {
  const mounted = useMounted()
  const btnsArr: any = () => [
    {
      label: i18n.t('Rank.商品分类'),
      value: '1'
    },
    {
      label: i18n.t('TiktokData.Sort'),
      value: '2'
    },
    {
      label: i18n.t('Rank.日期'),
      value: '3'
    }
  ]
  const closeModalRef = useRef()
  const [pageNo, setPageNo] = useState(1)
  const [total, setTotal] = useState(0)
  const [params, setParams] = useState({})
  const [itemList, setItemList] = useState([])
  const [hasMore, setHasMore] = useState(true)
  const height = getGoAppBarHeight()
  const [categoryBtnIndex, setCategoryBtnIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [defaultTime, setDefaultTime] = useState(null)
  const [isScrollList, setIsScrollList] = useState(true)
  const [categoryArrs, setCategoryArrs] = useState(btnsArr())

  useEffect(() => {
    handleTabTime()
    statistic({
      eventName: EventName.store_list
    })
  }, [])
  const handleTabTime = () => {
    loading.show({ userInteractive: true })
    getLastDate({ rankType: '4' }).then(res => {
      if (res.code == 200 && res.result) {
        if (res.result.dayLastTime) {
          let dayTemp = res.result.dayLastTime[0]
          onLoadRefresh(null, null, dayTemp)
          setDefaultTime(dayTemp)
        }
      }
    })
  }
  const handleTabBtns = item => {
    setCategoryBtnIndex(item.value)
    makeBodyScrollable(false)
  }
  const onCheckedItem = item => {
    let paramsQuery: any = {
      ...params,
      pageNo: 1
    }
    if (categoryBtnIndex == 1) {
      paramsQuery = {
        ...paramsQuery,
        customCategoryId: item.value
      }
    }
    //按销量排序
    if (categoryBtnIndex == 2) {
      paramsQuery = {
        ...paramsQuery,
        sortDirection: 'desc',
        sortBy: item.value
      }
    }
    if (categoryBtnIndex == 3) {
      const dateType = getDateType(item.label)
      if (dateType == 'day') {
        paramsQuery = {
          ...paramsQuery,
          dateType: 1,
          dateStartTimestamp: item.value
        }
      } else if (dateType == 'month') {
        paramsQuery = {
          ...paramsQuery,
          dateType: 3,
          dateStartTimestamp: item.value
        }
      } else if (dateType == 'week') {
        paramsQuery = {
          ...paramsQuery,
          dateType: 2,
          dateStartTimestamp: item.value
        }
      }
    }
    setParams(paramsQuery)
    onLoadRefresh(null, paramsQuery)

    setTimeout(() => {
      // @ts-ignore
      closeModalRef.current.closeModal()
      makeBodyScrollable(true)
    }, 300)
  }

  const onLoadRefresh = async (page?, paramsQuery?, dayTemp?) => {
    loading.show({ userInteractive: true })
    setIsLoading(true)
    page ? setPageNo(pageNo + 1) : setPageNo(1)
    const paramQuery = {
      ...params,
      pageNo: page ? page : 1,
      pageSize: 10,
      dateStartTimestamp: dayTemp ? dayTemp : defaultTime,
      dateType: 1,
      type: 1,
      ...(paramsQuery && { ...paramsQuery })
    }
    setParams(paramQuery)
    handleSellerRankList(paramQuery, page)
  }
  const handleSellerRankList = (paramQuery, page) => {
    sellerRankList({ ...paramQuery }).then(res => {
      loading.hide()
      setIsLoading(false)
      if (res.code == 200) {
        setTotal(res.result.total)
        let list = res.result.list
        if (list.length == 0 && paramQuery.pageNo == 1) {
          setItemList([])
        }
        if (list.length > 0) {
          let newList: any = page ? [...itemList, ...list] : list
          setItemList(newList)
          setHasMore(true)
        }
      }
    })
  }
  const fetchData = () => {
    if (total == itemList.length) {
      setHasMore(false)
      return
    }

    let page = pageNo + 1
    setPageNo(page)
    // 模拟异步加载数据
    setTimeout(() => {
      onLoadRefresh(page)
    }, 1000)
  }
  const handleCloseModal = () => {
    makeBodyScrollable(true)
  }
  return mounted ? (
    <div className={styles.store_box}>
      <StickyHeader>
        <div className={styles.fix_boxs}>
          <CategoryBtns
            handleCloseModal={handleCloseModal}
            btnsArr={categoryArrs}
            setCategoryArrs={setCategoryArrs}
            tabIndex="4"
            rankType="2"
            handleTabBtns={handleTabBtns}
            ref={closeModalRef}
            onCheckedItem={onCheckedItem}
          ></CategoryBtns>
        </div>
      </StickyHeader>
      <div className={styles.store_box}>
        <ItemList
          hasMore={hasMore}
          itemList={itemList}
          fetchData={fetchData}
          isLoading={isLoading}
          total={total}
        />
      </div>
    </div>
  ) : null
}

export default RankList
