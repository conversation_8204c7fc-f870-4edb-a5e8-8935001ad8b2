/* eslint-disable @next/next/no-img-element */
import { Image } from '@/components/Image'
import video_play from '@/../public/images/common/video_play.png'
import { useEffect, useState } from 'react'
import { uploadGetCoverImg } from '@/app/api/api-base/upload/getCoverImg/request'

export const Video = ({ resourceId }: { resourceId: string }) => {
  const [previewUrl, setPreviewUrl] = useState<string>('')

  useEffect(() => {
    if (resourceId) {
      pool()
    }
  }, [resourceId])

  const pool = async () => {
    const getPreviewUrl = async (count: number) => {
      if (count >= 6) return
      const res = await uploadGetCoverImg(resourceId)
      if (res.code === 200) {
        setPreviewUrl(res.result)
      } else {
        await new Promise(resolve => setTimeout(resolve, 500))
        await getPreviewUrl(count + 1)
      }
    }
    getPreviewUrl(0)
  }

  return (
    <>
      <img
        src={previewUrl}
        className="absolute h-[154px] w-[154px] rounded-[16px] object-cover"
      />
      <div className="absolute flex h-[154px] w-[154px] items-center justify-center rounded-[16px] bg-[rgba(0,0,0,0.4)]">
        <Image src={video_play} className="size-[32px]"></Image>
      </div>
    </>
  )
}
