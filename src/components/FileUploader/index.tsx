/* eslint-disable @next/next/no-img-element */
'use client'

import React, { useRef, useState } from 'react'
import camera from '@/../public/images/common/camera.png'
import gray_close from '@/../public/images/common/gray_close.png'
import { Image } from '@/components/Image'
import { toast } from '@/lib/client/toast'
import { videoPreviewer } from '@/components/VideoPreviewer'
import { ImageViewer } from 'antd-mobile'
import { i18n } from '@/lib/client/i18n'
import { uploadFile } from '@/lib/client/upload'
import { Video } from './components'
import { isIOS } from '@/lib/client/utils'

export interface UploadedData {
  resourceId: string
  url: string
  isVideo: boolean
}

interface Props {
  data?: UploadedData
  onUploaded?: (data?: UploadedData) => void
  onRemove?: (data: UploadedData) => void
}

export const FileUploader: React.FC<Props> = ({
  data,
  onUploaded,
  onRemove
}) => {
  const inputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    console.log('File changed:', event.target.files?.[0])

    const file = event.target.files?.[0]
    if (file!.size > 50 * 1024 * 1024) {
      toast(i18n.t('Feedback.不超过50MB'))
      return
    }

    const res = await uploadFile('FEEDBACK', file!)

    if (res) {
      onUploaded?.({
        ...res,
        isVideo: file!.type.startsWith('video/')
      })
      // Reset the file input
      if (inputRef.current) {
        inputRef.current.value = ''
      }
    }
  }

  const onSelectFile = () => {
    inputRef.current?.click()
  }

  return (
    <>
      {data ? (
        <div
          className="relative h-[154px] w-[154px]"
          onClick={async () => {
            if (!data.isVideo) {
              ImageViewer.show({
                image: data.url
              })
            } else {
              videoPreviewer.play(data.url)
            }
          }}
        >
          {data.isVideo ? (
            <Video resourceId={data.resourceId}></Video>
          ) : (
            <img
              src={data.url}
              className="absolute h-[154px] w-[154px] rounded-[16px] object-cover"
            />
          )}
          <div
            className="absolute right-[-16px] top-[-16px] size-[32px]"
            onClick={event => {
              event.stopPropagation()
              onRemove?.(data)
            }}
          >
            <Image className="size-[32px]" src={gray_close}></Image>
          </div>
        </div>
      ) : (
        <div
          className="flex h-[154px] w-[154px] cursor-pointer items-center justify-center rounded-[16px] bg-background"
          onClick={onSelectFile}
        >
          <Image className="size-[64px]" src={camera}></Image>
          <input
            ref={inputRef}
            id="file-input"
            type="file"
            accept={isIOS() ? 'image/*,video/*' : 'application/octet-stream'}
            capture={undefined}
            onChange={handleFileChange}
            className="hidden"
          />
        </div>
      )}
    </>
  )
}

export const FileUploaders = ({
  onChangeUploadedDatas
}: {
  onChangeUploadedDatas: (datas: UploadedData[]) => void
}) => {
  const [uploadedDatas, setUploadedDatas] = useState<UploadedData[]>([])

  const handleFileChange = (newData: UploadedData | undefined) => {
    if (uploadedDatas.length < 6) {
      console.log('File added:', newData)
      setUploadedDatas(prev => [...prev, newData!])
      onChangeUploadedDatas([...uploadedDatas, newData!])
    }
  }

  const handleFileRemove = (oldData: UploadedData) => {
    setUploadedDatas(prev => prev.filter(f => f !== oldData))
    onChangeUploadedDatas(uploadedDatas.filter(f => f !== oldData))
  }

  return (
    <div className="grid grid-cols-4 gap-y-[24px]">
      {uploadedDatas.map((data, index) => (
        <FileUploader
          key={`${index}_${data.resourceId}`}
          data={data}
          onUploaded={theData => {
            handleFileChange(theData)
          }}
          onRemove={handleFileRemove}
        />
      ))}
      {uploadedDatas.length < 6 && (
        <FileUploader onUploaded={handleFileChange} />
      )}
    </div>
  )
}
