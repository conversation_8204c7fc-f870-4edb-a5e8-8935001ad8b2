#!/bin/sh

# 获取暂存区中的文件（安全处理空格和特殊字符）
staged_files=$(git diff --cached --name-only -z -- '*.js' '*.jsx' '*.ts' '*.tsx' | tr '\0' '\n')

if [ -n "$staged_files" ]; then
    # 检查 ?? 运算符
    has_nullish_coalescing=$(echo "$staged_files" | grep -n -e '\?\?')
    
    if [ -n "$has_nullish_coalescing" ]; then
        echo "❌ 代码中使用了禁止的空值合并运算符 (??):"
        # 明确打印找到的内容
        echo "$has_nullish_coalescing"
        exit 1
    fi

    # 检查 .replaceall( 方法
    has_replace_all=$(echo "$staged_files" | grep -n -e '\.replaceall(')
    
    if [ -n "$has_replace_all" ]; then
        echo "❌ 代码中使用了禁止的 replaceall 方法（应为 replaceAll）:"
        echo "$has_replace_all"
        exit 1
    fi
fi

exit 0