{"name": "uchoice-ssr-h5", "version": "0.1.0", "private": true, "scripts": {"stop-servers": "ps aux | grep -E '/bin/node.*/next dev' | grep -v 'grep' | awk '{print $2}' | xargs kill -9", "dev": "next dev --port 3100", "build": "next build --no-lint", "build:analyze:": "ANALYZE=true next build", "start": "next start", "prepare": "husky install", "lint": "next lint", "ln": "sh scripts/link.sh", "unln": "sh scripts/unlink.sh"}, "dependencies": {"@antv/g2plot": "^2.4.31", "@sentry/nextjs": "^7.99.0", "@youpik/mobile_components": "^0.0.5", "ahooks": "^3.8.0", "ali-oss": "^6.22.0", "antd-mobile": "^5.32.0", "axios": "^1.4.0", "classnames": "^2.3.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "html-react-parser": "^5.1.7", "i18next": "^23.4.5", "nanoid": "^4.0.2", "next": "14.1.4", "plyr": "^3.7.8", "qs": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-i18next": "^13.2.0", "react-infinite-scroll-component": "^6.1.0", "react-json-view-lite": "^1.5.0", "react-player": "^2.16.0", "react-pull-to-refresh": "^2.0.1", "react-vant": "^3.3.3", "runes2": "^1.1.2", "sass": "^1.69.5", "typescript": "5.1.6"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@types/node": "20.4.5", "@types/qs": "^6.9.7", "@types/react": "18.2.17", "@types/react-dom": "18.2.7", "autoprefixer": "^10.4.14", "eslint": "8.45.0", "eslint-config-next": "13.4.12", "eslint-config-prettier": "^8.9.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-tailwindcss": "^3.13.0", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^13.2.3", "postcss": "^8.4.27", "postcss-pxtorem": "^6.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.4.1", "tailwindcss": "^3.4.6", "webpack-bundle-analyzer": "^4.10.1"}}