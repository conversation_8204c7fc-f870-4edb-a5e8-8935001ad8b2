/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}'
  ],
  theme: {
    extend: {
      colors: {
        primary: '#FE6D45',
        secondary: '#C5445D',
        black: '#303030',
        black30: '#303030',
        black33: '#333333',
        black02: '#020202',
        gray6E: '#6E6E6E',
        gray9A: '#9A9A9A',
        grayAB: '#ABABAB',
        grayCC: '#CCCCCC',
        grayD9: '#D9D9D9',
        grayEE: '#EEEEEE',
        gray8A: '#8A8A8A',
        background: '#F5F5F5',
        link: '#478BFF',

        white: '#FFFFFF',
        red: '#FE2C55',
        gray: '#6E6E6E',
        orange: 'orange',
        yellow: 'yellow',
        green: 'green',
        blue: 'blue'
      },
      spacing: {}
    }
  },
  plugins: [require('./tailwind-aliases.js')]
}
