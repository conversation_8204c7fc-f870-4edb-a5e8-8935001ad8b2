# 接口定义

src/app/api

# 接口参数定义

src/app/api/dtos

# 路由

- page.tsx
- 区分路由参数和query参数
- 用useNavigation跳转

# 分享解析

- meta

# 组件使用

- 渐变导航栏
- 图片组件
- loading
- toast
- 文字其中变色 InsertColoredText
- 文字插入 i18n.t('Showcase.Sold/Month', {
                  sold: item.salesForLast30DaysStr || ''
                })

# 工具类

- 分client、server和公共

# 国际化

i18n.t()
resources/translations

# CSS

index.module.css

# 启动

yarn dev

# 服务端debug

浏览器打开chrome://inspect/#other
设置4个端口

# RN交互

# 埋点

```
// 组件的方式
<Statistic data={{ eventName: EventName.payment_truemoney }}></Statistic>
```

```
// 方法的方式
statistic({
    eventName: EventName.social_jump,
    param: {
        from,
        linkType,
        linkText
    }
})
```

# 写入本地token

NEXT_PUBLIC_TOKEN=''

# 取消请求
cancelRequest('getShareLinkItemListByPage') 取消同名请求
cancelAllRequests(). 取消所有请求

# modal情况禁止滚动
makeBodyScrollable

# 运行
npm run dev
