#!/bin/bash

lib_dir="youpik_mobile_components"
lib="@youpik/mobile_components"
path="$PWD"
project=$(basename "$path")
parent_project=$(dirname "$path")

yarn

# yarn link命令后面不带参数，就会把当前这个文件夹软链到~/.config/yarn/link目录下，这一步其实只需要操作一次就行
cd "$parent_project/$lib_dir"
yarn link
cd "$parent_project/$lib_dir/node_modules/react"
yarn link
cd "$parent_project/$lib_dir/node_modules/react-dom"
yarn link

# 删掉自带的这三个文件夹
rm -rf "node_modules/$lib"
rm -rf node_modules/react
rm -rf node_modules/react-dom

# yarn link后面带了参数，就会去~/.config/yarn/link目录下找到对应名字的文件夹，软链到当前的node_modules根目录下
cd "$parent_project/$project"
yarn link "$lib"
yarn link react
yarn link react-dom


