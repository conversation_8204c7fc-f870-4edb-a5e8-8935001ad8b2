# 1. 构建基础镜像
FROM node:18.17.1-alpine3.18 AS base

ARG TARGET_NODE_ENV=production

ENV NODE_ENV=production \
  APP_PATH=/app \
  NEXT_SHARP_PATH=/app/node_modules/sharp

WORKDIR $APP_PATH

RUN apk add --no-cache libc6-compat

# 拷贝当前目录下的所有文件(除了.dockerignore里排除的)，都拷贝到工作目录下
COPY . .

RUN mv .env.${TARGET_NODE_ENV} .env.production && npx next build --no-lint
RUN cd public && node /app/script-optimize-image-in-the-folder.js
RUN cd .next/static/media && node /app/script-optimize-image-in-the-folder.js

EXPOSE 3000

CMD ["npm", "start"]
